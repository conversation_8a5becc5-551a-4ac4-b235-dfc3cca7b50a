import React, { useState, useEffect } from 'react';
import BrowserOnly from '@docusaurus/BrowserOnly';

// Demo data for recommendations
const demoRecommendation = {
  id: 'demo-1',
  title: 'Stripe Payment Processing',
  description: 'Accept payments online with Stripe\'s developer-friendly APIs and comprehensive payment solutions.',
  features: [
    'Accept 135+ payment methods',
    'Global payment processing',
    'Advanced fraud protection',
    'Real-time analytics dashboard'
  ],
  pricing: 'Starting at 2.9% + 30¢ per transaction',
  matchScore: 92,
  url: 'https://stripe.com',
  admeshLink: 'https://useadmesh.com/redirect/stripe-demo'
};

const demoRecommendations = [
  {
    id: 'demo-1',
    title: 'Stripe Payment Processing',
    description: 'Accept payments online with developer-friendly APIs.',
    features: ['135+ payment methods', 'Global processing', 'Fraud protection'],
    pricing: '2.9% + 30¢ per transaction',
    matchScore: 92,
    url: 'https://stripe.com',
    admeshLink: 'https://useadmesh.com/redirect/stripe-demo'
  },
  {
    id: 'demo-2',
    title: 'Vercel Deployment Platform',
    description: 'Deploy your frontend applications with zero configuration.',
    features: ['Instant deployments', 'Global CDN', 'Automatic HTTPS'],
    pricing: 'Free tier available',
    matchScore: 88,
    url: 'https://vercel.com',
    admeshLink: 'https://useadmesh.com/redirect/vercel-demo'
  },
  {
    id: 'demo-3',
    title: 'Figma Design Tool',
    description: 'Collaborative interface design tool for teams.',
    features: ['Real-time collaboration', 'Design systems', 'Prototyping'],
    pricing: 'Free for personal use',
    matchScore: 85,
    url: 'https://figma.com',
    admeshLink: 'https://useadmesh.com/redirect/figma-demo'
  }
];

const handleClick = (adId, admeshLink) => {
  console.log('Demo click:', adId, admeshLink);
  // In real implementation, this would open the link
};

export const OneLineAdDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0' }}>
    <h4>One Line Ad Demo</h4>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshSimpleAd } = require('admesh-ui-sdk');
        return (
          <AdMeshSimpleAd
            recommendation={demoRecommendation}
            variation="question"
            showPoweredBy={true}
            onClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const ProductCardDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0' }}>
    <h4>Product Card Demo</h4>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshProductCard } = require('admesh-ui-sdk');
        return (
          <AdMeshProductCard
            recommendation={demoRecommendation}
            showFeatures={true}
            showPricing={true}
            showMatchScore={true}
            onClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const ConversationSummaryDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0' }}>
    <h4>Conversation Summary Demo</h4>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshConversationSummary } = require('admesh-ui-sdk');
        return (
          <AdMeshConversationSummary
            recommendations={demoRecommendations}
            conversationSummary="Based on our conversation about payment processing and deployment solutions, here are some recommended tools that could help with your project."
            showTopRecommendations={3}
            onRecommendationClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const CitationDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0' }}>
    <h4>Citation Demo</h4>
    <p>For payment processing, many developers choose Stripe¹ due to its comprehensive API and global reach.</p>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshCitationUnit } = require('admesh-ui-sdk');
        return (
          <AdMeshCitationUnit
            recommendation={demoRecommendation}
            citationNumber={1}
            showSource={true}
            onClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const FloatingRecommendationsDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0', position: 'relative', height: '200px' }}>
    <h4>Floating Recommendations Demo</h4>
    <p>This demo shows how floating recommendations appear contextually. In a real implementation, these would appear based on user behavior and conversation context.</p>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshAutoRecommendationWidget } = require('admesh-ui-sdk');
        return (
          <AdMeshAutoRecommendationWidget
            recommendations={demoRecommendations.slice(0, 2)}
            trigger="manual"
            autoShow={true}
            position="bottom-right"
            onRecommendationClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const SidebarDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0' }}>
    <h4>Sidebar Demo</h4>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshSidebar } = require('admesh-ui-sdk');
        return (
          <AdMeshSidebar
            recommendations={demoRecommendations}
            title="Recommended Tools"
            maxDisplayed={3}
            onRecommendationClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const LayoutDemo = () => (
  <div style={{ padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px', margin: '10px 0' }}>
    <h4>Layout Demo</h4>
    <BrowserOnly fallback={<div>Loading demo...</div>}>
      {() => {
        const { AdMeshLayout } = require('admesh-ui-sdk');
        return (
          <AdMeshLayout
            recommendations={demoRecommendations}
            autoLayout={true}
            maxDisplayed={3}
            showMatchScores={true}
            showFeatures={true}
            onProductClick={handleClick}
          />
        );
      }}
    </BrowserOnly>
  </div>
);

export const AllDemosContainer = () => (
  <div>
    <OneLineAdDemo />
    <ProductCardDemo />
    <ConversationSummaryDemo />
    <CitationDemo />
    <FloatingRecommendationsDemo />
    <SidebarDemo />
    <LayoutDemo />
  </div>
);

export default AllDemosContainer;
