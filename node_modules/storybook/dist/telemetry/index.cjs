"use strict";
var ra = Object.create;
var $e = Object.defineProperty;
var na = Object.getOwnPropertyDescriptor;
var sa = Object.getOwnPropertyNames;
var oa = Object.getPrototypeOf, ia = Object.prototype.hasOwnProperty;
var i = (t, e) => $e(t, "name", { value: e, configurable: !0 });
var aa = (t, e) => () => (t && (e = t(t = 0)), e);
var S = (t, e) => () => (e || t((e = { exports: {} }).exports, e), e.exports), ca = (t, e) => {
  for (var r in e)
    $e(t, r, { get: e[r], enumerable: !0 });
}, gn = (t, e, r, n) => {
  if (e && typeof e == "object" || typeof e == "function")
    for (let s of sa(e))
      !ia.call(t, s) && s !== r && $e(t, s, { get: () => e[s], enumerable: !(n = na(e, s)) || n.enumerable });
  return t;
};
var T = (t, e, r) => (r = t != null ? ra(oa(t)) : {}, gn(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  e || !t || !t.__esModule ? $e(r, "default", { value: t, enumerable: !0 }) : r,
  t
)), da = (t) => gn($e({}, "__esModule", { value: !0 }), t);

// ../node_modules/picocolors/picocolors.js
var vn = S((nl, tr) => {
  var ut = process || {}, bn = ut.argv || [], dt = ut.env || {}, ua = !(dt.NO_COLOR || bn.includes("--no-color")) && (!!dt.FORCE_COLOR || bn.
  includes("--color") || ut.platform === "win32" || (ut.stdout || {}).isTTY && dt.TERM !== "dumb" || !!dt.CI), la = /* @__PURE__ */ i((t, e, r = t) => (n) => {
    let s = "" + n, o = s.indexOf(e, t.length);
    return ~o ? t + pa(s, e, r, o) + e : t + s + e;
  }, "formatter"), pa = /* @__PURE__ */ i((t, e, r, n) => {
    let s = "", o = 0;
    do
      s += t.substring(o, n) + r, o = n + e.length, n = t.indexOf(e, o);
    while (~n);
    return s + t.substring(o);
  }, "replaceClose"), xn = /* @__PURE__ */ i((t = ua) => {
    let e = t ? la : () => String;
    return {
      isColorSupported: t,
      reset: e("\x1B[0m", "\x1B[0m"),
      bold: e("\x1B[1m", "\x1B[22m", "\x1B[22m\x1B[1m"),
      dim: e("\x1B[2m", "\x1B[22m", "\x1B[22m\x1B[2m"),
      italic: e("\x1B[3m", "\x1B[23m"),
      underline: e("\x1B[4m", "\x1B[24m"),
      inverse: e("\x1B[7m", "\x1B[27m"),
      hidden: e("\x1B[8m", "\x1B[28m"),
      strikethrough: e("\x1B[9m", "\x1B[29m"),
      black: e("\x1B[30m", "\x1B[39m"),
      red: e("\x1B[31m", "\x1B[39m"),
      green: e("\x1B[32m", "\x1B[39m"),
      yellow: e("\x1B[33m", "\x1B[39m"),
      blue: e("\x1B[34m", "\x1B[39m"),
      magenta: e("\x1B[35m", "\x1B[39m"),
      cyan: e("\x1B[36m", "\x1B[39m"),
      white: e("\x1B[37m", "\x1B[39m"),
      gray: e("\x1B[90m", "\x1B[39m"),
      bgBlack: e("\x1B[40m", "\x1B[49m"),
      bgRed: e("\x1B[41m", "\x1B[49m"),
      bgGreen: e("\x1B[42m", "\x1B[49m"),
      bgYellow: e("\x1B[43m", "\x1B[49m"),
      bgBlue: e("\x1B[44m", "\x1B[49m"),
      bgMagenta: e("\x1B[45m", "\x1B[49m"),
      bgCyan: e("\x1B[46m", "\x1B[49m"),
      bgWhite: e("\x1B[47m", "\x1B[49m"),
      blackBright: e("\x1B[90m", "\x1B[39m"),
      redBright: e("\x1B[91m", "\x1B[39m"),
      greenBright: e("\x1B[92m", "\x1B[39m"),
      yellowBright: e("\x1B[93m", "\x1B[39m"),
      blueBright: e("\x1B[94m", "\x1B[39m"),
      magentaBright: e("\x1B[95m", "\x1B[39m"),
      cyanBright: e("\x1B[96m", "\x1B[39m"),
      whiteBright: e("\x1B[97m", "\x1B[39m"),
      bgBlackBright: e("\x1B[100m", "\x1B[49m"),
      bgRedBright: e("\x1B[101m", "\x1B[49m"),
      bgGreenBright: e("\x1B[102m", "\x1B[49m"),
      bgYellowBright: e("\x1B[103m", "\x1B[49m"),
      bgBlueBright: e("\x1B[104m", "\x1B[49m"),
      bgMagentaBright: e("\x1B[105m", "\x1B[49m"),
      bgCyanBright: e("\x1B[106m", "\x1B[49m"),
      bgWhiteBright: e("\x1B[107m", "\x1B[49m")
    };
  }, "createColors");
  tr.exports = xn();
  tr.exports.createColors = xn;
});

// ../node_modules/walk-up-path/dist/cjs/index.js
var Sn = S((lt) => {
  "use strict";
  Object.defineProperty(lt, "__esModule", { value: !0 });
  lt.walkUp = void 0;
  var In = require("path"), fa = /* @__PURE__ */ i(function* (t) {
    for (t = (0, In.resolve)(t); t; ) {
      yield t;
      let e = (0, In.dirname)(t);
      if (e === t)
        break;
      t = e;
    }
  }, "walkUp");
  lt.walkUp = fa;
});

// ../node_modules/zod/lib/helpers/util.js
var Fe = S((C) => {
  "use strict";
  Object.defineProperty(C, "__esModule", { value: !0 });
  C.getParsedType = C.ZodParsedType = C.objectUtil = C.util = void 0;
  var lr;
  (function(t) {
    t.assertEqual = (s) => s;
    function e(s) {
    }
    i(e, "assertIs"), t.assertIs = e;
    function r(s) {
      throw new Error();
    }
    i(r, "assertNever"), t.assertNever = r, t.arrayToEnum = (s) => {
      let o = {};
      for (let a of s)
        o[a] = a;
      return o;
    }, t.getValidEnumValues = (s) => {
      let o = t.objectKeys(s).filter((c) => typeof s[s[c]] != "number"), a = {};
      for (let c of o)
        a[c] = s[c];
      return t.objectValues(a);
    }, t.objectValues = (s) => t.objectKeys(s).map(function(o) {
      return s[o];
    }), t.objectKeys = typeof Object.keys == "function" ? (s) => Object.keys(s) : (s) => {
      let o = [];
      for (let a in s)
        Object.prototype.hasOwnProperty.call(s, a) && o.push(a);
      return o;
    }, t.find = (s, o) => {
      for (let a of s)
        if (o(a))
          return a;
    }, t.isInteger = typeof Number.isInteger == "function" ? (s) => Number.isInteger(s) : (s) => typeof s == "number" && isFinite(s) && Math.
    floor(s) === s;
    function n(s, o = " | ") {
      return s.map((a) => typeof a == "string" ? `'${a}'` : a).join(o);
    }
    i(n, "joinValues"), t.joinValues = n, t.jsonStringifyReplacer = (s, o) => typeof o == "bigint" ? o.toString() : o;
  })(lr || (C.util = lr = {}));
  var Nn;
  (function(t) {
    t.mergeShapes = (e, r) => ({
      ...e,
      ...r
      // second overwrites first
    });
  })(Nn || (C.objectUtil = Nn = {}));
  C.ZodParsedType = lr.arrayToEnum([
    "string",
    "nan",
    "number",
    "integer",
    "float",
    "boolean",
    "date",
    "bigint",
    "symbol",
    "function",
    "undefined",
    "null",
    "array",
    "object",
    "unknown",
    "promise",
    "void",
    "never",
    "map",
    "set"
  ]);
  var va = /* @__PURE__ */ i((t) => {
    switch (typeof t) {
      case "undefined":
        return C.ZodParsedType.undefined;
      case "string":
        return C.ZodParsedType.string;
      case "number":
        return isNaN(t) ? C.ZodParsedType.nan : C.ZodParsedType.number;
      case "boolean":
        return C.ZodParsedType.boolean;
      case "function":
        return C.ZodParsedType.function;
      case "bigint":
        return C.ZodParsedType.bigint;
      case "symbol":
        return C.ZodParsedType.symbol;
      case "object":
        return Array.isArray(t) ? C.ZodParsedType.array : t === null ? C.ZodParsedType.null : t.then && typeof t.then == "function" && t.catch &&
        typeof t.catch == "function" ? C.ZodParsedType.promise : typeof Map < "u" && t instanceof Map ? C.ZodParsedType.map : typeof Set < "\
u" && t instanceof Set ? C.ZodParsedType.set : typeof Date < "u" && t instanceof Date ? C.ZodParsedType.date : C.ZodParsedType.object;
      default:
        return C.ZodParsedType.unknown;
    }
  }, "getParsedType");
  C.getParsedType = va;
});

// ../node_modules/zod/lib/ZodError.js
var mt = S((Y) => {
  "use strict";
  Object.defineProperty(Y, "__esModule", { value: !0 });
  Y.ZodError = Y.quotelessJson = Y.ZodIssueCode = void 0;
  var Zn = Fe();
  Y.ZodIssueCode = Zn.util.arrayToEnum([
    "invalid_type",
    "invalid_literal",
    "custom",
    "invalid_union",
    "invalid_union_discriminator",
    "invalid_enum_value",
    "unrecognized_keys",
    "invalid_arguments",
    "invalid_return_type",
    "invalid_date",
    "invalid_string",
    "too_small",
    "too_big",
    "invalid_intersection_types",
    "not_multiple_of",
    "not_finite"
  ]);
  var _a = /* @__PURE__ */ i((t) => JSON.stringify(t, null, 2).replace(/"([^"]+)":/g, "$1:"), "quotelessJson");
  Y.quotelessJson = _a;
  var Be = class t extends Error {
    static {
      i(this, "ZodError");
    }
    get errors() {
      return this.issues;
    }
    constructor(e) {
      super(), this.issues = [], this.addIssue = (n) => {
        this.issues = [...this.issues, n];
      }, this.addIssues = (n = []) => {
        this.issues = [...this.issues, ...n];
      };
      let r = new.target.prototype;
      Object.setPrototypeOf ? Object.setPrototypeOf(this, r) : this.__proto__ = r, this.name = "ZodError", this.issues = e;
    }
    format(e) {
      let r = e || function(o) {
        return o.message;
      }, n = { _errors: [] }, s = /* @__PURE__ */ i((o) => {
        for (let a of o.issues)
          if (a.code === "invalid_union")
            a.unionErrors.map(s);
          else if (a.code === "invalid_return_type")
            s(a.returnTypeError);
          else if (a.code === "invalid_arguments")
            s(a.argumentsError);
          else if (a.path.length === 0)
            n._errors.push(r(a));
          else {
            let c = n, l = 0;
            for (; l < a.path.length; ) {
              let p = a.path[l];
              l === a.path.length - 1 ? (c[p] = c[p] || { _errors: [] }, c[p]._errors.push(r(a))) : c[p] = c[p] || { _errors: [] }, c = c[p],
              l++;
            }
          }
      }, "processError");
      return s(this), n;
    }
    static assert(e) {
      if (!(e instanceof t))
        throw new Error(`Not a ZodError: ${e}`);
    }
    toString() {
      return this.message;
    }
    get message() {
      return JSON.stringify(this.issues, Zn.util.jsonStringifyReplacer, 2);
    }
    get isEmpty() {
      return this.issues.length === 0;
    }
    flatten(e = (r) => r.message) {
      let r = {}, n = [];
      for (let s of this.issues)
        s.path.length > 0 ? (r[s.path[0]] = r[s.path[0]] || [], r[s.path[0]].push(e(s))) : n.push(e(s));
      return { formErrors: n, fieldErrors: r };
    }
    get formErrors() {
      return this.flatten();
    }
  };
  Y.ZodError = Be;
  Be.create = (t) => new Be(t);
});

// ../node_modules/zod/lib/locales/en.js
var fr = S((pr) => {
  "use strict";
  Object.defineProperty(pr, "__esModule", { value: !0 });
  var ne = Fe(), O = mt(), wa = /* @__PURE__ */ i((t, e) => {
    let r;
    switch (t.code) {
      case O.ZodIssueCode.invalid_type:
        t.received === ne.ZodParsedType.undefined ? r = "Required" : r = `Expected ${t.expected}, received ${t.received}`;
        break;
      case O.ZodIssueCode.invalid_literal:
        r = `Invalid literal value, expected ${JSON.stringify(t.expected, ne.util.jsonStringifyReplacer)}`;
        break;
      case O.ZodIssueCode.unrecognized_keys:
        r = `Unrecognized key(s) in object: ${ne.util.joinValues(t.keys, ", ")}`;
        break;
      case O.ZodIssueCode.invalid_union:
        r = "Invalid input";
        break;
      case O.ZodIssueCode.invalid_union_discriminator:
        r = `Invalid discriminator value. Expected ${ne.util.joinValues(t.options)}`;
        break;
      case O.ZodIssueCode.invalid_enum_value:
        r = `Invalid enum value. Expected ${ne.util.joinValues(t.options)}, received '${t.received}'`;
        break;
      case O.ZodIssueCode.invalid_arguments:
        r = "Invalid function arguments";
        break;
      case O.ZodIssueCode.invalid_return_type:
        r = "Invalid function return type";
        break;
      case O.ZodIssueCode.invalid_date:
        r = "Invalid date";
        break;
      case O.ZodIssueCode.invalid_string:
        typeof t.validation == "object" ? "includes" in t.validation ? (r = `Invalid input: must include "${t.validation.includes}"`, typeof t.
        validation.position == "number" && (r = `${r} at one or more positions greater than or equal to ${t.validation.position}`)) : "start\
sWith" in t.validation ? r = `Invalid input: must start with "${t.validation.startsWith}"` : "endsWith" in t.validation ? r = `Invalid input\
: must end with "${t.validation.endsWith}"` : ne.util.assertNever(t.validation) : t.validation !== "regex" ? r = `Invalid ${t.validation}` :
        r = "Invalid";
        break;
      case O.ZodIssueCode.too_small:
        t.type === "array" ? r = `Array must contain ${t.exact ? "exactly" : t.inclusive ? "at least" : "more than"} ${t.minimum} element(s)` :
        t.type === "string" ? r = `String must contain ${t.exact ? "exactly" : t.inclusive ? "at least" : "over"} ${t.minimum} character(s)` :
        t.type === "number" ? r = `Number must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "greater tha\
n "}${t.minimum}` : t.type === "date" ? r = `Date must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "gre\
ater than "}${new Date(Number(t.minimum))}` : r = "Invalid input";
        break;
      case O.ZodIssueCode.too_big:
        t.type === "array" ? r = `Array must contain ${t.exact ? "exactly" : t.inclusive ? "at most" : "less than"} ${t.maximum} element(s)` :
        t.type === "string" ? r = `String must contain ${t.exact ? "exactly" : t.inclusive ? "at most" : "under"} ${t.maximum} character(s)` :
        t.type === "number" ? r = `Number must be ${t.exact ? "exactly" : t.inclusive ? "less than or equal to" : "less than"} ${t.maximum}` :
        t.type === "bigint" ? r = `BigInt must be ${t.exact ? "exactly" : t.inclusive ? "less than or equal to" : "less than"} ${t.maximum}` :
        t.type === "date" ? r = `Date must be ${t.exact ? "exactly" : t.inclusive ? "smaller than or equal to" : "smaller than"} ${new Date(
        Number(t.maximum))}` : r = "Invalid input";
        break;
      case O.ZodIssueCode.custom:
        r = "Invalid input";
        break;
      case O.ZodIssueCode.invalid_intersection_types:
        r = "Intersection results could not be merged";
        break;
      case O.ZodIssueCode.not_multiple_of:
        r = `Number must be a multiple of ${t.multipleOf}`;
        break;
      case O.ZodIssueCode.not_finite:
        r = "Number must be finite";
        break;
      default:
        r = e.defaultError, ne.util.assertNever(t);
    }
    return { message: r };
  }, "errorMap");
  pr.default = wa;
});

// ../node_modules/zod/lib/errors.js
var ht = S((F) => {
  "use strict";
  var ka = F && F.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(F, "__esModule", { value: !0 });
  F.getErrorMap = F.setErrorMap = F.defaultErrorMap = void 0;
  var Mn = ka(fr());
  F.defaultErrorMap = Mn.default;
  var Dn = Mn.default;
  function Ta(t) {
    Dn = t;
  }
  i(Ta, "setErrorMap");
  F.setErrorMap = Ta;
  function Ia() {
    return Dn;
  }
  i(Ia, "getErrorMap");
  F.getErrorMap = Ia;
});

// ../node_modules/zod/lib/helpers/parseUtil.js
var hr = S((I) => {
  "use strict";
  var Sa = I && I.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(I, "__esModule", { value: !0 });
  I.isAsync = I.isValid = I.isDirty = I.isAborted = I.OK = I.DIRTY = I.INVALID = I.ParseStatus = I.addIssueToContext = I.EMPTY_PATH = I.makeIssue =
  void 0;
  var Ea = ht(), Ln = Sa(fr()), Ca = /* @__PURE__ */ i((t) => {
    let { data: e, path: r, errorMaps: n, issueData: s } = t, o = [...r, ...s.path || []], a = {
      ...s,
      path: o
    };
    if (s.message !== void 0)
      return {
        ...s,
        path: o,
        message: s.message
      };
    let c = "", l = n.filter((p) => !!p).slice().reverse();
    for (let p of l)
      c = p(a, { data: e, defaultError: c }).message;
    return {
      ...s,
      path: o,
      message: c
    };
  }, "makeIssue");
  I.makeIssue = Ca;
  I.EMPTY_PATH = [];
  function Pa(t, e) {
    let r = (0, Ea.getErrorMap)(), n = (0, I.makeIssue)({
      issueData: e,
      data: t.data,
      path: t.path,
      errorMaps: [
        t.common.contextualErrorMap,
        // contextual error map is first priority
        t.schemaErrorMap,
        // then schema-bound map if available
        r,
        // then global override map
        r === Ln.default ? void 0 : Ln.default
        // then global default map
      ].filter((s) => !!s)
    });
    t.common.issues.push(n);
  }
  i(Pa, "addIssueToContext");
  I.addIssueToContext = Pa;
  var mr = class t {
    static {
      i(this, "ParseStatus");
    }
    constructor() {
      this.value = "valid";
    }
    dirty() {
      this.value === "valid" && (this.value = "dirty");
    }
    abort() {
      this.value !== "aborted" && (this.value = "aborted");
    }
    static mergeArray(e, r) {
      let n = [];
      for (let s of r) {
        if (s.status === "aborted")
          return I.INVALID;
        s.status === "dirty" && e.dirty(), n.push(s.value);
      }
      return { status: e.value, value: n };
    }
    static async mergeObjectAsync(e, r) {
      let n = [];
      for (let s of r) {
        let o = await s.key, a = await s.value;
        n.push({
          key: o,
          value: a
        });
      }
      return t.mergeObjectSync(e, n);
    }
    static mergeObjectSync(e, r) {
      let n = {};
      for (let s of r) {
        let { key: o, value: a } = s;
        if (o.status === "aborted" || a.status === "aborted")
          return I.INVALID;
        o.status === "dirty" && e.dirty(), a.status === "dirty" && e.dirty(), o.value !== "__proto__" && (typeof a.value < "u" || s.alwaysSet) &&
        (n[o.value] = a.value);
      }
      return { status: e.value, value: n };
    }
  };
  I.ParseStatus = mr;
  I.INVALID = Object.freeze({
    status: "aborted"
  });
  var Aa = /* @__PURE__ */ i((t) => ({ status: "dirty", value: t }), "DIRTY");
  I.DIRTY = Aa;
  var Oa = /* @__PURE__ */ i((t) => ({ status: "valid", value: t }), "OK");
  I.OK = Oa;
  var ja = /* @__PURE__ */ i((t) => t.status === "aborted", "isAborted");
  I.isAborted = ja;
  var Ra = /* @__PURE__ */ i((t) => t.status === "dirty", "isDirty");
  I.isDirty = Ra;
  var Na = /* @__PURE__ */ i((t) => t.status === "valid", "isValid");
  I.isValid = Na;
  var Za = /* @__PURE__ */ i((t) => typeof Promise < "u" && t instanceof Promise, "isAsync");
  I.isAsync = Za;
});

// ../node_modules/zod/lib/helpers/typeAliases.js
var $n = S((Un) => {
  "use strict";
  Object.defineProperty(Un, "__esModule", { value: !0 });
});

// ../node_modules/zod/lib/helpers/errorUtil.js
var Fn = S((yt) => {
  "use strict";
  Object.defineProperty(yt, "__esModule", { value: !0 });
  yt.errorUtil = void 0;
  var Vn;
  (function(t) {
    t.errToObj = (e) => typeof e == "string" ? { message: e } : e || {}, t.toString = (e) => typeof e == "string" ? e : e?.message;
  })(Vn || (yt.errorUtil = Vn = {}));
});

// ../node_modules/zod/lib/types.js
var ts = S((d) => {
  "use strict";
  var bt = d && d.__classPrivateFieldGet || function(t, e, r, n) {
    if (r === "a" && !n) throw new TypeError("Private accessor was defined without a getter");
    if (typeof e == "function" ? t !== e || !n : !e.has(t)) throw new TypeError("Cannot read private member from an object whose class did n\
ot declare it");
    return r === "m" ? n : r === "a" ? n.call(t) : n ? n.value : e.get(t);
  }, qn = d && d.__classPrivateFieldSet || function(t, e, r, n, s) {
    if (n === "m") throw new TypeError("Private method is not writable");
    if (n === "a" && !s) throw new TypeError("Private accessor was defined without a setter");
    if (typeof e == "function" ? t !== e || !s : !e.has(t)) throw new TypeError("Cannot write private member to an object whose class did no\
t declare it");
    return n === "a" ? s.call(t, r) : s ? s.value = r : e.set(t, r), r;
  }, We, qe;
  Object.defineProperty(d, "__esModule", { value: !0 });
  d.boolean = d.bigint = d.array = d.any = d.coerce = d.ZodFirstPartyTypeKind = d.late = d.ZodSchema = d.Schema = d.custom = d.ZodReadonly =
  d.ZodPipeline = d.ZodBranded = d.BRAND = d.ZodNaN = d.ZodCatch = d.ZodDefault = d.ZodNullable = d.ZodOptional = d.ZodTransformer = d.ZodEffects =
  d.ZodPromise = d.ZodNativeEnum = d.ZodEnum = d.ZodLiteral = d.ZodLazy = d.ZodFunction = d.ZodSet = d.ZodMap = d.ZodRecord = d.ZodTuple = d.
  ZodIntersection = d.ZodDiscriminatedUnion = d.ZodUnion = d.ZodObject = d.ZodArray = d.ZodVoid = d.ZodNever = d.ZodUnknown = d.ZodAny = d.ZodNull =
  d.ZodUndefined = d.ZodSymbol = d.ZodDate = d.ZodBoolean = d.ZodBigInt = d.ZodNumber = d.ZodString = d.datetimeRegex = d.ZodType = void 0;
  d.NEVER = d.void = d.unknown = d.union = d.undefined = d.tuple = d.transformer = d.symbol = d.string = d.strictObject = d.set = d.record =
  d.promise = d.preprocess = d.pipeline = d.ostring = d.optional = d.onumber = d.oboolean = d.object = d.number = d.nullable = d.null = d.never =
  d.nativeEnum = d.nan = d.map = d.literal = d.lazy = d.intersection = d.instanceof = d.function = d.enum = d.effect = d.discriminatedUnion =
  d.date = void 0;
  var gt = ht(), y = Fn(), u = hr(), h = Fe(), m = mt(), L = class {
    static {
      i(this, "ParseInputLazyPath");
    }
    constructor(e, r, n, s) {
      this._cachedPath = [], this.parent = e, this.data = r, this._path = n, this._key = s;
    }
    get path() {
      return this._cachedPath.length || (this._key instanceof Array ? this._cachedPath.push(...this._path, ...this._key) : this._cachedPath.
      push(...this._path, this._key)), this._cachedPath;
    }
  }, Bn = /* @__PURE__ */ i((t, e) => {
    if ((0, u.isValid)(e))
      return { success: !0, data: e.value };
    if (!t.common.issues.length)
      throw new Error("Validation failed but no issues detected.");
    return {
      success: !1,
      get error() {
        if (this._error)
          return this._error;
        let r = new m.ZodError(t.common.issues);
        return this._error = r, this._error;
      }
    };
  }, "handleResult");
  function x(t) {
    if (!t)
      return {};
    let { errorMap: e, invalid_type_error: r, required_error: n, description: s } = t;
    if (e && (r || n))
      throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
    return e ? { errorMap: e, description: s } : { errorMap: /* @__PURE__ */ i((a, c) => {
      var l, p;
      let { message: f } = t;
      return a.code === "invalid_enum_value" ? { message: f ?? c.defaultError } : typeof c.data > "u" ? { message: (l = f ?? n) !== null && l !==
      void 0 ? l : c.defaultError } : a.code !== "invalid_type" ? { message: c.defaultError } : { message: (p = f ?? r) !== null && p !== void 0 ?
      p : c.defaultError };
    }, "customMap"), description: s };
  }
  i(x, "processCreateParams");
  var v = class {
    static {
      i(this, "ZodType");
    }
    get description() {
      return this._def.description;
    }
    _getType(e) {
      return (0, h.getParsedType)(e.data);
    }
    _getOrReturnCtx(e, r) {
      return r || {
        common: e.parent.common,
        data: e.data,
        parsedType: (0, h.getParsedType)(e.data),
        schemaErrorMap: this._def.errorMap,
        path: e.path,
        parent: e.parent
      };
    }
    _processInputParams(e) {
      return {
        status: new u.ParseStatus(),
        ctx: {
          common: e.parent.common,
          data: e.data,
          parsedType: (0, h.getParsedType)(e.data),
          schemaErrorMap: this._def.errorMap,
          path: e.path,
          parent: e.parent
        }
      };
    }
    _parseSync(e) {
      let r = this._parse(e);
      if ((0, u.isAsync)(r))
        throw new Error("Synchronous parse encountered promise.");
      return r;
    }
    _parseAsync(e) {
      let r = this._parse(e);
      return Promise.resolve(r);
    }
    parse(e, r) {
      let n = this.safeParse(e, r);
      if (n.success)
        return n.data;
      throw n.error;
    }
    safeParse(e, r) {
      var n;
      let s = {
        common: {
          issues: [],
          async: (n = r?.async) !== null && n !== void 0 ? n : !1,
          contextualErrorMap: r?.errorMap
        },
        path: r?.path || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: e,
        parsedType: (0, h.getParsedType)(e)
      }, o = this._parseSync({ data: e, path: s.path, parent: s });
      return Bn(s, o);
    }
    "~validate"(e) {
      var r, n;
      let s = {
        common: {
          issues: [],
          async: !!this["~standard"].async
        },
        path: [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: e,
        parsedType: (0, h.getParsedType)(e)
      };
      if (!this["~standard"].async)
        try {
          let o = this._parseSync({ data: e, path: [], parent: s });
          return (0, u.isValid)(o) ? {
            value: o.value
          } : {
            issues: s.common.issues
          };
        } catch (o) {
          !((n = (r = o?.message) === null || r === void 0 ? void 0 : r.toLowerCase()) === null || n === void 0) && n.includes("encountered") &&
          (this["~standard"].async = !0), s.common = {
            issues: [],
            async: !0
          };
        }
      return this._parseAsync({ data: e, path: [], parent: s }).then((o) => (0, u.isValid)(o) ? {
        value: o.value
      } : {
        issues: s.common.issues
      });
    }
    async parseAsync(e, r) {
      let n = await this.safeParseAsync(e, r);
      if (n.success)
        return n.data;
      throw n.error;
    }
    async safeParseAsync(e, r) {
      let n = {
        common: {
          issues: [],
          contextualErrorMap: r?.errorMap,
          async: !0
        },
        path: r?.path || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: e,
        parsedType: (0, h.getParsedType)(e)
      }, s = this._parse({ data: e, path: n.path, parent: n }), o = await ((0, u.isAsync)(s) ? s : Promise.resolve(s));
      return Bn(n, o);
    }
    refine(e, r) {
      let n = /* @__PURE__ */ i((s) => typeof r == "string" || typeof r > "u" ? { message: r } : typeof r == "function" ? r(s) : r, "getIssu\
eProperties");
      return this._refinement((s, o) => {
        let a = e(s), c = /* @__PURE__ */ i(() => o.addIssue({
          code: m.ZodIssueCode.custom,
          ...n(s)
        }), "setError");
        return typeof Promise < "u" && a instanceof Promise ? a.then((l) => l ? !0 : (c(), !1)) : a ? !0 : (c(), !1);
      });
    }
    refinement(e, r) {
      return this._refinement((n, s) => e(n) ? !0 : (s.addIssue(typeof r == "function" ? r(n, s) : r), !1));
    }
    _refinement(e) {
      return new Z({
        schema: this,
        typeName: g.ZodEffects,
        effect: { type: "refinement", refinement: e }
      });
    }
    superRefine(e) {
      return this._refinement(e);
    }
    constructor(e) {
      this.spa = this.safeParseAsync, this._def = e, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync =
      this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.
      bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.
      bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.
      promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(
      this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe =
      this.describe.bind(this), this.pipe = this.pipe.bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.
      bind(this), this.isOptional = this.isOptional.bind(this), this["~standard"] = {
        version: 1,
        vendor: "zod",
        validate: /* @__PURE__ */ i((r) => this["~validate"](r), "validate")
      };
    }
    optional() {
      return D.create(this, this._def);
    }
    nullable() {
      return W.create(this, this._def);
    }
    nullish() {
      return this.nullable().optional();
    }
    array() {
      return K.create(this);
    }
    promise() {
      return ee.create(this, this._def);
    }
    or(e) {
      return ue.create([this, e], this._def);
    }
    and(e) {
      return le.create(this, e, this._def);
    }
    transform(e) {
      return new Z({
        ...x(this._def),
        schema: this,
        typeName: g.ZodEffects,
        effect: { type: "transform", transform: e }
      });
    }
    default(e) {
      let r = typeof e == "function" ? e : () => e;
      return new ye({
        ...x(this._def),
        innerType: this,
        defaultValue: r,
        typeName: g.ZodDefault
      });
    }
    brand() {
      return new Ge({
        typeName: g.ZodBranded,
        type: this,
        ...x(this._def)
      });
    }
    catch(e) {
      let r = typeof e == "function" ? e : () => e;
      return new ge({
        ...x(this._def),
        innerType: this,
        catchValue: r,
        typeName: g.ZodCatch
      });
    }
    describe(e) {
      let r = this.constructor;
      return new r({
        ...this._def,
        description: e
      });
    }
    pipe(e) {
      return ze.create(this, e);
    }
    readonly() {
      return be.create(this);
    }
    isOptional() {
      return this.safeParse(void 0).success;
    }
    isNullable() {
      return this.safeParse(null).success;
    }
  };
  d.ZodType = v;
  d.Schema = v;
  d.ZodSchema = v;
  var Ma = /^c[^\s-]{8,}$/i, Da = /^[0-9a-z]+$/, La = /^[0-9A-HJKMNP-TV-Z]{26}$/i, Ua = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,
  $a = /^[a-z0-9_-]{21}$/i, Va = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/, Fa = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,
  Ba = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i, Wa = "^(\\p{Extended_Pictographic}|\\p{Emoji_Comp\
onent})+$", yr, qa = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/, Ga = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
  za = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,
  Ka = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
  Ja = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/, Ha = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
  Gn = "((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469\
]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))", Ya = new RegExp(`^${Gn}$`);
  function zn(t) {
    let e = "([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";
    return t.precision ? e = `${e}\\.\\d{${t.precision}}` : t.precision == null && (e = `${e}(\\.\\d+)?`), e;
  }
  i(zn, "timeRegexSource");
  function Xa(t) {
    return new RegExp(`^${zn(t)}$`);
  }
  i(Xa, "timeRegex");
  function Kn(t) {
    let e = `${Gn}T${zn(t)}`, r = [];
    return r.push(t.local ? "Z?" : "Z"), t.offset && r.push("([+-]\\d{2}:?\\d{2})"), e = `${e}(${r.join("|")})`, new RegExp(`^${e}$`);
  }
  i(Kn, "datetimeRegex");
  d.datetimeRegex = Kn;
  function Qa(t, e) {
    return !!((e === "v4" || !e) && qa.test(t) || (e === "v6" || !e) && za.test(t));
  }
  i(Qa, "isValidIP");
  function ec(t, e) {
    if (!Va.test(t))
      return !1;
    try {
      let [r] = t.split("."), n = r.replace(/-/g, "+").replace(/_/g, "/").padEnd(r.length + (4 - r.length % 4) % 4, "="), s = JSON.parse(atob(
      n));
      return !(typeof s != "object" || s === null || !s.typ || !s.alg || e && s.alg !== e);
    } catch {
      return !1;
    }
  }
  i(ec, "isValidJWT");
  function tc(t, e) {
    return !!((e === "v4" || !e) && Ga.test(t) || (e === "v6" || !e) && Ka.test(t));
  }
  i(tc, "isValidCidr");
  var X = class t extends v {
    static {
      i(this, "ZodString");
    }
    _parse(e) {
      if (this._def.coerce && (e.data = String(e.data)), this._getType(e) !== h.ZodParsedType.string) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.string,
          received: o.parsedType
        }), u.INVALID;
      }
      let n = new u.ParseStatus(), s;
      for (let o of this._def.checks)
        if (o.kind === "min")
          e.data.length < o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_small,
            minimum: o.value,
            type: "string",
            inclusive: !0,
            exact: !1,
            message: o.message
          }), n.dirty());
        else if (o.kind === "max")
          e.data.length > o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_big,
            maximum: o.value,
            type: "string",
            inclusive: !0,
            exact: !1,
            message: o.message
          }), n.dirty());
        else if (o.kind === "length") {
          let a = e.data.length > o.value, c = e.data.length < o.value;
          (a || c) && (s = this._getOrReturnCtx(e, s), a ? (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_big,
            maximum: o.value,
            type: "string",
            inclusive: !0,
            exact: !0,
            message: o.message
          }) : c && (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_small,
            minimum: o.value,
            type: "string",
            inclusive: !0,
            exact: !0,
            message: o.message
          }), n.dirty());
        } else if (o.kind === "email")
          Ba.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "email",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "emoji")
          yr || (yr = new RegExp(Wa, "u")), yr.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "emoji",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "uuid")
          Ua.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "uuid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "nanoid")
          $a.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "nanoid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "cuid")
          Ma.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "cuid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "cuid2")
          Da.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "cuid2",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "ulid")
          La.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "ulid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "url")
          try {
            new URL(e.data);
          } catch {
            s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
              validation: "url",
              code: m.ZodIssueCode.invalid_string,
              message: o.message
            }), n.dirty();
          }
        else o.kind === "regex" ? (o.regex.lastIndex = 0, o.regex.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(
        s, {
          validation: "regex",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty())) : o.kind === "trim" ? e.data = e.data.trim() : o.kind === "includes" ? e.data.includes(o.value, o.position) || (s = this.
        _getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: { includes: o.value, position: o.position },
          message: o.message
        }), n.dirty()) : o.kind === "toLowerCase" ? e.data = e.data.toLowerCase() : o.kind === "toUpperCase" ? e.data = e.data.toUpperCase() :
        o.kind === "startsWith" ? e.data.startsWith(o.value) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: { startsWith: o.value },
          message: o.message
        }), n.dirty()) : o.kind === "endsWith" ? e.data.endsWith(o.value) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: { endsWith: o.value },
          message: o.message
        }), n.dirty()) : o.kind === "datetime" ? Kn(o).test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: "datetime",
          message: o.message
        }), n.dirty()) : o.kind === "date" ? Ya.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: "date",
          message: o.message
        }), n.dirty()) : o.kind === "time" ? Xa(o).test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: "time",
          message: o.message
        }), n.dirty()) : o.kind === "duration" ? Fa.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "duration",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "ip" ? Qa(e.data, o.version) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "ip",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "jwt" ? ec(e.data, o.alg) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "jwt",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "cidr" ? tc(e.data, o.version) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "cidr",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "base64" ? Ja.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "base64",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "base64url" ? Ha.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "base64url",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : h.util.assertNever(o);
      return { status: n.value, value: e.data };
    }
    _regex(e, r, n) {
      return this.refinement((s) => e.test(s), {
        validation: r,
        code: m.ZodIssueCode.invalid_string,
        ...y.errorUtil.errToObj(n)
      });
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    email(e) {
      return this._addCheck({ kind: "email", ...y.errorUtil.errToObj(e) });
    }
    url(e) {
      return this._addCheck({ kind: "url", ...y.errorUtil.errToObj(e) });
    }
    emoji(e) {
      return this._addCheck({ kind: "emoji", ...y.errorUtil.errToObj(e) });
    }
    uuid(e) {
      return this._addCheck({ kind: "uuid", ...y.errorUtil.errToObj(e) });
    }
    nanoid(e) {
      return this._addCheck({ kind: "nanoid", ...y.errorUtil.errToObj(e) });
    }
    cuid(e) {
      return this._addCheck({ kind: "cuid", ...y.errorUtil.errToObj(e) });
    }
    cuid2(e) {
      return this._addCheck({ kind: "cuid2", ...y.errorUtil.errToObj(e) });
    }
    ulid(e) {
      return this._addCheck({ kind: "ulid", ...y.errorUtil.errToObj(e) });
    }
    base64(e) {
      return this._addCheck({ kind: "base64", ...y.errorUtil.errToObj(e) });
    }
    base64url(e) {
      return this._addCheck({
        kind: "base64url",
        ...y.errorUtil.errToObj(e)
      });
    }
    jwt(e) {
      return this._addCheck({ kind: "jwt", ...y.errorUtil.errToObj(e) });
    }
    ip(e) {
      return this._addCheck({ kind: "ip", ...y.errorUtil.errToObj(e) });
    }
    cidr(e) {
      return this._addCheck({ kind: "cidr", ...y.errorUtil.errToObj(e) });
    }
    datetime(e) {
      var r, n;
      return typeof e == "string" ? this._addCheck({
        kind: "datetime",
        precision: null,
        offset: !1,
        local: !1,
        message: e
      }) : this._addCheck({
        kind: "datetime",
        precision: typeof e?.precision > "u" ? null : e?.precision,
        offset: (r = e?.offset) !== null && r !== void 0 ? r : !1,
        local: (n = e?.local) !== null && n !== void 0 ? n : !1,
        ...y.errorUtil.errToObj(e?.message)
      });
    }
    date(e) {
      return this._addCheck({ kind: "date", message: e });
    }
    time(e) {
      return typeof e == "string" ? this._addCheck({
        kind: "time",
        precision: null,
        message: e
      }) : this._addCheck({
        kind: "time",
        precision: typeof e?.precision > "u" ? null : e?.precision,
        ...y.errorUtil.errToObj(e?.message)
      });
    }
    duration(e) {
      return this._addCheck({ kind: "duration", ...y.errorUtil.errToObj(e) });
    }
    regex(e, r) {
      return this._addCheck({
        kind: "regex",
        regex: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    includes(e, r) {
      return this._addCheck({
        kind: "includes",
        value: e,
        position: r?.position,
        ...y.errorUtil.errToObj(r?.message)
      });
    }
    startsWith(e, r) {
      return this._addCheck({
        kind: "startsWith",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    endsWith(e, r) {
      return this._addCheck({
        kind: "endsWith",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    min(e, r) {
      return this._addCheck({
        kind: "min",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    max(e, r) {
      return this._addCheck({
        kind: "max",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    length(e, r) {
      return this._addCheck({
        kind: "length",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    /**
     * Equivalent to `.min(1)`
     */
    nonempty(e) {
      return this.min(1, y.errorUtil.errToObj(e));
    }
    trim() {
      return new t({
        ...this._def,
        checks: [...this._def.checks, { kind: "trim" }]
      });
    }
    toLowerCase() {
      return new t({
        ...this._def,
        checks: [...this._def.checks, { kind: "toLowerCase" }]
      });
    }
    toUpperCase() {
      return new t({
        ...this._def,
        checks: [...this._def.checks, { kind: "toUpperCase" }]
      });
    }
    get isDatetime() {
      return !!this._def.checks.find((e) => e.kind === "datetime");
    }
    get isDate() {
      return !!this._def.checks.find((e) => e.kind === "date");
    }
    get isTime() {
      return !!this._def.checks.find((e) => e.kind === "time");
    }
    get isDuration() {
      return !!this._def.checks.find((e) => e.kind === "duration");
    }
    get isEmail() {
      return !!this._def.checks.find((e) => e.kind === "email");
    }
    get isURL() {
      return !!this._def.checks.find((e) => e.kind === "url");
    }
    get isEmoji() {
      return !!this._def.checks.find((e) => e.kind === "emoji");
    }
    get isUUID() {
      return !!this._def.checks.find((e) => e.kind === "uuid");
    }
    get isNANOID() {
      return !!this._def.checks.find((e) => e.kind === "nanoid");
    }
    get isCUID() {
      return !!this._def.checks.find((e) => e.kind === "cuid");
    }
    get isCUID2() {
      return !!this._def.checks.find((e) => e.kind === "cuid2");
    }
    get isULID() {
      return !!this._def.checks.find((e) => e.kind === "ulid");
    }
    get isIP() {
      return !!this._def.checks.find((e) => e.kind === "ip");
    }
    get isCIDR() {
      return !!this._def.checks.find((e) => e.kind === "cidr");
    }
    get isBase64() {
      return !!this._def.checks.find((e) => e.kind === "base64");
    }
    get isBase64url() {
      return !!this._def.checks.find((e) => e.kind === "base64url");
    }
    get minLength() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e;
    }
    get maxLength() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e;
    }
  };
  d.ZodString = X;
  X.create = (t) => {
    var e;
    return new X({
      checks: [],
      typeName: g.ZodString,
      coerce: (e = t?.coerce) !== null && e !== void 0 ? e : !1,
      ...x(t)
    });
  };
  function rc(t, e) {
    let r = (t.toString().split(".")[1] || "").length, n = (e.toString().split(".")[1] || "").length, s = r > n ? r : n, o = parseInt(t.toFixed(
    s).replace(".", "")), a = parseInt(e.toFixed(s).replace(".", ""));
    return o % a / Math.pow(10, s);
  }
  i(rc, "floatSafeRemainder");
  var se = class t extends v {
    static {
      i(this, "ZodNumber");
    }
    constructor() {
      super(...arguments), this.min = this.gte, this.max = this.lte, this.step = this.multipleOf;
    }
    _parse(e) {
      if (this._def.coerce && (e.data = Number(e.data)), this._getType(e) !== h.ZodParsedType.number) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.number,
          received: o.parsedType
        }), u.INVALID;
      }
      let n, s = new u.ParseStatus();
      for (let o of this._def.checks)
        o.kind === "int" ? h.util.isInteger(e.data) || (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: "integer",
          received: "float",
          message: o.message
        }), s.dirty()) : o.kind === "min" ? (o.inclusive ? e.data < o.value : e.data <= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_small,
          minimum: o.value,
          type: "number",
          inclusive: o.inclusive,
          exact: !1,
          message: o.message
        }), s.dirty()) : o.kind === "max" ? (o.inclusive ? e.data > o.value : e.data >= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_big,
          maximum: o.value,
          type: "number",
          inclusive: o.inclusive,
          exact: !1,
          message: o.message
        }), s.dirty()) : o.kind === "multipleOf" ? rc(e.data, o.value) !== 0 && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(n,
        {
          code: m.ZodIssueCode.not_multiple_of,
          multipleOf: o.value,
          message: o.message
        }), s.dirty()) : o.kind === "finite" ? Number.isFinite(e.data) || (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.not_finite,
          message: o.message
        }), s.dirty()) : h.util.assertNever(o);
      return { status: s.value, value: e.data };
    }
    gte(e, r) {
      return this.setLimit("min", e, !0, y.errorUtil.toString(r));
    }
    gt(e, r) {
      return this.setLimit("min", e, !1, y.errorUtil.toString(r));
    }
    lte(e, r) {
      return this.setLimit("max", e, !0, y.errorUtil.toString(r));
    }
    lt(e, r) {
      return this.setLimit("max", e, !1, y.errorUtil.toString(r));
    }
    setLimit(e, r, n, s) {
      return new t({
        ...this._def,
        checks: [
          ...this._def.checks,
          {
            kind: e,
            value: r,
            inclusive: n,
            message: y.errorUtil.toString(s)
          }
        ]
      });
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    int(e) {
      return this._addCheck({
        kind: "int",
        message: y.errorUtil.toString(e)
      });
    }
    positive(e) {
      return this._addCheck({
        kind: "min",
        value: 0,
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    negative(e) {
      return this._addCheck({
        kind: "max",
        value: 0,
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    nonpositive(e) {
      return this._addCheck({
        kind: "max",
        value: 0,
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    nonnegative(e) {
      return this._addCheck({
        kind: "min",
        value: 0,
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    multipleOf(e, r) {
      return this._addCheck({
        kind: "multipleOf",
        value: e,
        message: y.errorUtil.toString(r)
      });
    }
    finite(e) {
      return this._addCheck({
        kind: "finite",
        message: y.errorUtil.toString(e)
      });
    }
    safe(e) {
      return this._addCheck({
        kind: "min",
        inclusive: !0,
        value: Number.MIN_SAFE_INTEGER,
        message: y.errorUtil.toString(e)
      })._addCheck({
        kind: "max",
        inclusive: !0,
        value: Number.MAX_SAFE_INTEGER,
        message: y.errorUtil.toString(e)
      });
    }
    get minValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e;
    }
    get maxValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e;
    }
    get isInt() {
      return !!this._def.checks.find((e) => e.kind === "int" || e.kind === "multipleOf" && h.util.isInteger(e.value));
    }
    get isFinite() {
      let e = null, r = null;
      for (let n of this._def.checks) {
        if (n.kind === "finite" || n.kind === "int" || n.kind === "multipleOf")
          return !0;
        n.kind === "min" ? (r === null || n.value > r) && (r = n.value) : n.kind === "max" && (e === null || n.value < e) && (e = n.value);
      }
      return Number.isFinite(r) && Number.isFinite(e);
    }
  };
  d.ZodNumber = se;
  se.create = (t) => new se({
    checks: [],
    typeName: g.ZodNumber,
    coerce: t?.coerce || !1,
    ...x(t)
  });
  var oe = class t extends v {
    static {
      i(this, "ZodBigInt");
    }
    constructor() {
      super(...arguments), this.min = this.gte, this.max = this.lte;
    }
    _parse(e) {
      if (this._def.coerce)
        try {
          e.data = BigInt(e.data);
        } catch {
          return this._getInvalidInput(e);
        }
      if (this._getType(e) !== h.ZodParsedType.bigint)
        return this._getInvalidInput(e);
      let n, s = new u.ParseStatus();
      for (let o of this._def.checks)
        o.kind === "min" ? (o.inclusive ? e.data < o.value : e.data <= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_small,
          type: "bigint",
          minimum: o.value,
          inclusive: o.inclusive,
          message: o.message
        }), s.dirty()) : o.kind === "max" ? (o.inclusive ? e.data > o.value : e.data >= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_big,
          type: "bigint",
          maximum: o.value,
          inclusive: o.inclusive,
          message: o.message
        }), s.dirty()) : o.kind === "multipleOf" ? e.data % o.value !== BigInt(0) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.not_multiple_of,
          multipleOf: o.value,
          message: o.message
        }), s.dirty()) : h.util.assertNever(o);
      return { status: s.value, value: e.data };
    }
    _getInvalidInput(e) {
      let r = this._getOrReturnCtx(e);
      return (0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.invalid_type,
        expected: h.ZodParsedType.bigint,
        received: r.parsedType
      }), u.INVALID;
    }
    gte(e, r) {
      return this.setLimit("min", e, !0, y.errorUtil.toString(r));
    }
    gt(e, r) {
      return this.setLimit("min", e, !1, y.errorUtil.toString(r));
    }
    lte(e, r) {
      return this.setLimit("max", e, !0, y.errorUtil.toString(r));
    }
    lt(e, r) {
      return this.setLimit("max", e, !1, y.errorUtil.toString(r));
    }
    setLimit(e, r, n, s) {
      return new t({
        ...this._def,
        checks: [
          ...this._def.checks,
          {
            kind: e,
            value: r,
            inclusive: n,
            message: y.errorUtil.toString(s)
          }
        ]
      });
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    positive(e) {
      return this._addCheck({
        kind: "min",
        value: BigInt(0),
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    negative(e) {
      return this._addCheck({
        kind: "max",
        value: BigInt(0),
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    nonpositive(e) {
      return this._addCheck({
        kind: "max",
        value: BigInt(0),
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    nonnegative(e) {
      return this._addCheck({
        kind: "min",
        value: BigInt(0),
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    multipleOf(e, r) {
      return this._addCheck({
        kind: "multipleOf",
        value: e,
        message: y.errorUtil.toString(r)
      });
    }
    get minValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e;
    }
    get maxValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e;
    }
  };
  d.ZodBigInt = oe;
  oe.create = (t) => {
    var e;
    return new oe({
      checks: [],
      typeName: g.ZodBigInt,
      coerce: (e = t?.coerce) !== null && e !== void 0 ? e : !1,
      ...x(t)
    });
  };
  var ie = class extends v {
    static {
      i(this, "ZodBoolean");
    }
    _parse(e) {
      if (this._def.coerce && (e.data = !!e.data), this._getType(e) !== h.ZodParsedType.boolean) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.boolean,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodBoolean = ie;
  ie.create = (t) => new ie({
    typeName: g.ZodBoolean,
    coerce: t?.coerce || !1,
    ...x(t)
  });
  var ae = class t extends v {
    static {
      i(this, "ZodDate");
    }
    _parse(e) {
      if (this._def.coerce && (e.data = new Date(e.data)), this._getType(e) !== h.ZodParsedType.date) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.date,
          received: o.parsedType
        }), u.INVALID;
      }
      if (isNaN(e.data.getTime())) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_date
        }), u.INVALID;
      }
      let n = new u.ParseStatus(), s;
      for (let o of this._def.checks)
        o.kind === "min" ? e.data.getTime() < o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.too_small,
          message: o.message,
          inclusive: !0,
          exact: !1,
          minimum: o.value,
          type: "date"
        }), n.dirty()) : o.kind === "max" ? e.data.getTime() > o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.too_big,
          message: o.message,
          inclusive: !0,
          exact: !1,
          maximum: o.value,
          type: "date"
        }), n.dirty()) : h.util.assertNever(o);
      return {
        status: n.value,
        value: new Date(e.data.getTime())
      };
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    min(e, r) {
      return this._addCheck({
        kind: "min",
        value: e.getTime(),
        message: y.errorUtil.toString(r)
      });
    }
    max(e, r) {
      return this._addCheck({
        kind: "max",
        value: e.getTime(),
        message: y.errorUtil.toString(r)
      });
    }
    get minDate() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e != null ? new Date(e) : null;
    }
    get maxDate() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e != null ? new Date(e) : null;
    }
  };
  d.ZodDate = ae;
  ae.create = (t) => new ae({
    checks: [],
    coerce: t?.coerce || !1,
    typeName: g.ZodDate,
    ...x(t)
  });
  var Te = class extends v {
    static {
      i(this, "ZodSymbol");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.symbol) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.symbol,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodSymbol = Te;
  Te.create = (t) => new Te({
    typeName: g.ZodSymbol,
    ...x(t)
  });
  var ce = class extends v {
    static {
      i(this, "ZodUndefined");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.undefined) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.undefined,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodUndefined = ce;
  ce.create = (t) => new ce({
    typeName: g.ZodUndefined,
    ...x(t)
  });
  var de = class extends v {
    static {
      i(this, "ZodNull");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.null) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.null,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodNull = de;
  de.create = (t) => new de({
    typeName: g.ZodNull,
    ...x(t)
  });
  var Q = class extends v {
    static {
      i(this, "ZodAny");
    }
    constructor() {
      super(...arguments), this._any = !0;
    }
    _parse(e) {
      return (0, u.OK)(e.data);
    }
  };
  d.ZodAny = Q;
  Q.create = (t) => new Q({
    typeName: g.ZodAny,
    ...x(t)
  });
  var z = class extends v {
    static {
      i(this, "ZodUnknown");
    }
    constructor() {
      super(...arguments), this._unknown = !0;
    }
    _parse(e) {
      return (0, u.OK)(e.data);
    }
  };
  d.ZodUnknown = z;
  z.create = (t) => new z({
    typeName: g.ZodUnknown,
    ...x(t)
  });
  var $ = class extends v {
    static {
      i(this, "ZodNever");
    }
    _parse(e) {
      let r = this._getOrReturnCtx(e);
      return (0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.invalid_type,
        expected: h.ZodParsedType.never,
        received: r.parsedType
      }), u.INVALID;
    }
  };
  d.ZodNever = $;
  $.create = (t) => new $({
    typeName: g.ZodNever,
    ...x(t)
  });
  var Ie = class extends v {
    static {
      i(this, "ZodVoid");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.undefined) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.void,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodVoid = Ie;
  Ie.create = (t) => new Ie({
    typeName: g.ZodVoid,
    ...x(t)
  });
  var K = class t extends v {
    static {
      i(this, "ZodArray");
    }
    _parse(e) {
      let { ctx: r, status: n } = this._processInputParams(e), s = this._def;
      if (r.parsedType !== h.ZodParsedType.array)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.array,
          received: r.parsedType
        }), u.INVALID;
      if (s.exactLength !== null) {
        let a = r.data.length > s.exactLength.value, c = r.data.length < s.exactLength.value;
        (a || c) && ((0, u.addIssueToContext)(r, {
          code: a ? m.ZodIssueCode.too_big : m.ZodIssueCode.too_small,
          minimum: c ? s.exactLength.value : void 0,
          maximum: a ? s.exactLength.value : void 0,
          type: "array",
          inclusive: !0,
          exact: !0,
          message: s.exactLength.message
        }), n.dirty());
      }
      if (s.minLength !== null && r.data.length < s.minLength.value && ((0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.too_small,
        minimum: s.minLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: s.minLength.message
      }), n.dirty()), s.maxLength !== null && r.data.length > s.maxLength.value && ((0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.too_big,
        maximum: s.maxLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: s.maxLength.message
      }), n.dirty()), r.common.async)
        return Promise.all([...r.data].map((a, c) => s.type._parseAsync(new L(r, a, r.path, c)))).then((a) => u.ParseStatus.mergeArray(n, a));
      let o = [...r.data].map((a, c) => s.type._parseSync(new L(r, a, r.path, c)));
      return u.ParseStatus.mergeArray(n, o);
    }
    get element() {
      return this._def.type;
    }
    min(e, r) {
      return new t({
        ...this._def,
        minLength: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    max(e, r) {
      return new t({
        ...this._def,
        maxLength: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    length(e, r) {
      return new t({
        ...this._def,
        exactLength: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    nonempty(e) {
      return this.min(1, e);
    }
  };
  d.ZodArray = K;
  K.create = (t, e) => new K({
    type: t,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: g.ZodArray,
    ...x(e)
  });
  function ke(t) {
    if (t instanceof R) {
      let e = {};
      for (let r in t.shape) {
        let n = t.shape[r];
        e[r] = D.create(ke(n));
      }
      return new R({
        ...t._def,
        shape: /* @__PURE__ */ i(() => e, "shape")
      });
    } else return t instanceof K ? new K({
      ...t._def,
      type: ke(t.element)
    }) : t instanceof D ? D.create(ke(t.unwrap())) : t instanceof W ? W.create(ke(t.unwrap())) : t instanceof B ? B.create(t.items.map((e) => ke(
    e))) : t;
  }
  i(ke, "deepPartialify");
  var R = class t extends v {
    static {
      i(this, "ZodObject");
    }
    constructor() {
      super(...arguments), this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend;
    }
    _getCached() {
      if (this._cached !== null)
        return this._cached;
      let e = this._def.shape(), r = h.util.objectKeys(e);
      return this._cached = { shape: e, keys: r };
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.object) {
        let p = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(p, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.object,
          received: p.parsedType
        }), u.INVALID;
      }
      let { status: n, ctx: s } = this._processInputParams(e), { shape: o, keys: a } = this._getCached(), c = [];
      if (!(this._def.catchall instanceof $ && this._def.unknownKeys === "strip"))
        for (let p in s.data)
          a.includes(p) || c.push(p);
      let l = [];
      for (let p of a) {
        let f = o[p], b = s.data[p];
        l.push({
          key: { status: "valid", value: p },
          value: f._parse(new L(s, b, s.path, p)),
          alwaysSet: p in s.data
        });
      }
      if (this._def.catchall instanceof $) {
        let p = this._def.unknownKeys;
        if (p === "passthrough")
          for (let f of c)
            l.push({
              key: { status: "valid", value: f },
              value: { status: "valid", value: s.data[f] }
            });
        else if (p === "strict")
          c.length > 0 && ((0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.unrecognized_keys,
            keys: c
          }), n.dirty());
        else if (p !== "strip")
          throw new Error("Internal ZodObject error: invalid unknownKeys value.");
      } else {
        let p = this._def.catchall;
        for (let f of c) {
          let b = s.data[f];
          l.push({
            key: { status: "valid", value: f },
            value: p._parse(
              new L(s, b, s.path, f)
              //, ctx.child(key), value, getParsedType(value)
            ),
            alwaysSet: f in s.data
          });
        }
      }
      return s.common.async ? Promise.resolve().then(async () => {
        let p = [];
        for (let f of l) {
          let b = await f.key, w = await f.value;
          p.push({
            key: b,
            value: w,
            alwaysSet: f.alwaysSet
          });
        }
        return p;
      }).then((p) => u.ParseStatus.mergeObjectSync(n, p)) : u.ParseStatus.mergeObjectSync(n, l);
    }
    get shape() {
      return this._def.shape();
    }
    strict(e) {
      return y.errorUtil.errToObj, new t({
        ...this._def,
        unknownKeys: "strict",
        ...e !== void 0 ? {
          errorMap: /* @__PURE__ */ i((r, n) => {
            var s, o, a, c;
            let l = (a = (o = (s = this._def).errorMap) === null || o === void 0 ? void 0 : o.call(s, r, n).message) !== null && a !== void 0 ?
            a : n.defaultError;
            return r.code === "unrecognized_keys" ? {
              message: (c = y.errorUtil.errToObj(e).message) !== null && c !== void 0 ? c : l
            } : {
              message: l
            };
          }, "errorMap")
        } : {}
      });
    }
    strip() {
      return new t({
        ...this._def,
        unknownKeys: "strip"
      });
    }
    passthrough() {
      return new t({
        ...this._def,
        unknownKeys: "passthrough"
      });
    }
    // const AugmentFactory =
    //   <Def extends ZodObjectDef>(def: Def) =>
    //   <Augmentation extends ZodRawShape>(
    //     augmentation: Augmentation
    //   ): ZodObject<
    //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
    //     Def["unknownKeys"],
    //     Def["catchall"]
    //   > => {
    //     return new ZodObject({
    //       ...def,
    //       shape: () => ({
    //         ...def.shape(),
    //         ...augmentation,
    //       }),
    //     }) as any;
    //   };
    extend(e) {
      return new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => ({
          ...this._def.shape(),
          ...e
        }), "shape")
      });
    }
    /**
     * Prior to zod@1.0.12 there was a bug in the
     * inferred type of merged objects. Please
     * upgrade if you are experiencing issues.
     */
    merge(e) {
      return new t({
        unknownKeys: e._def.unknownKeys,
        catchall: e._def.catchall,
        shape: /* @__PURE__ */ i(() => ({
          ...this._def.shape(),
          ...e._def.shape()
        }), "shape"),
        typeName: g.ZodObject
      });
    }
    // merge<
    //   Incoming extends AnyZodObject,
    //   Augmentation extends Incoming["shape"],
    //   NewOutput extends {
    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
    //       ? Augmentation[k]["_output"]
    //       : k extends keyof Output
    //       ? Output[k]
    //       : never;
    //   },
    //   NewInput extends {
    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
    //       ? Augmentation[k]["_input"]
    //       : k extends keyof Input
    //       ? Input[k]
    //       : never;
    //   }
    // >(
    //   merging: Incoming
    // ): ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"],
    //   NewOutput,
    //   NewInput
    // > {
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    setKey(e, r) {
      return this.augment({ [e]: r });
    }
    // merge<Incoming extends AnyZodObject>(
    //   merging: Incoming
    // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
    // ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"]
    // > {
    //   // const mergedShape = objectUtil.mergeShapes(
    //   //   this._def.shape(),
    //   //   merging._def.shape()
    //   // );
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    catchall(e) {
      return new t({
        ...this._def,
        catchall: e
      });
    }
    pick(e) {
      let r = {};
      return h.util.objectKeys(e).forEach((n) => {
        e[n] && this.shape[n] && (r[n] = this.shape[n]);
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    omit(e) {
      let r = {};
      return h.util.objectKeys(this.shape).forEach((n) => {
        e[n] || (r[n] = this.shape[n]);
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    /**
     * @deprecated
     */
    deepPartial() {
      return ke(this);
    }
    partial(e) {
      let r = {};
      return h.util.objectKeys(this.shape).forEach((n) => {
        let s = this.shape[n];
        e && !e[n] ? r[n] = s : r[n] = s.optional();
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    required(e) {
      let r = {};
      return h.util.objectKeys(this.shape).forEach((n) => {
        if (e && !e[n])
          r[n] = this.shape[n];
        else {
          let o = this.shape[n];
          for (; o instanceof D; )
            o = o._def.innerType;
          r[n] = o;
        }
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    keyof() {
      return Jn(h.util.objectKeys(this.shape));
    }
  };
  d.ZodObject = R;
  R.create = (t, e) => new R({
    shape: /* @__PURE__ */ i(() => t, "shape"),
    unknownKeys: "strip",
    catchall: $.create(),
    typeName: g.ZodObject,
    ...x(e)
  });
  R.strictCreate = (t, e) => new R({
    shape: /* @__PURE__ */ i(() => t, "shape"),
    unknownKeys: "strict",
    catchall: $.create(),
    typeName: g.ZodObject,
    ...x(e)
  });
  R.lazycreate = (t, e) => new R({
    shape: t,
    unknownKeys: "strip",
    catchall: $.create(),
    typeName: g.ZodObject,
    ...x(e)
  });
  var ue = class extends v {
    static {
      i(this, "ZodUnion");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = this._def.options;
      function s(o) {
        for (let c of o)
          if (c.result.status === "valid")
            return c.result;
        for (let c of o)
          if (c.result.status === "dirty")
            return r.common.issues.push(...c.ctx.common.issues), c.result;
        let a = o.map((c) => new m.ZodError(c.ctx.common.issues));
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_union,
          unionErrors: a
        }), u.INVALID;
      }
      if (i(s, "handleResults"), r.common.async)
        return Promise.all(n.map(async (o) => {
          let a = {
            ...r,
            common: {
              ...r.common,
              issues: []
            },
            parent: null
          };
          return {
            result: await o._parseAsync({
              data: r.data,
              path: r.path,
              parent: a
            }),
            ctx: a
          };
        })).then(s);
      {
        let o, a = [];
        for (let l of n) {
          let p = {
            ...r,
            common: {
              ...r.common,
              issues: []
            },
            parent: null
          }, f = l._parseSync({
            data: r.data,
            path: r.path,
            parent: p
          });
          if (f.status === "valid")
            return f;
          f.status === "dirty" && !o && (o = { result: f, ctx: p }), p.common.issues.length && a.push(p.common.issues);
        }
        if (o)
          return r.common.issues.push(...o.ctx.common.issues), o.result;
        let c = a.map((l) => new m.ZodError(l));
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_union,
          unionErrors: c
        }), u.INVALID;
      }
    }
    get options() {
      return this._def.options;
    }
  };
  d.ZodUnion = ue;
  ue.create = (t, e) => new ue({
    options: t,
    typeName: g.ZodUnion,
    ...x(e)
  });
  var G = /* @__PURE__ */ i((t) => t instanceof pe ? G(t.schema) : t instanceof Z ? G(t.innerType()) : t instanceof fe ? [t.value] : t instanceof
  me ? t.options : t instanceof he ? h.util.objectValues(t.enum) : t instanceof ye ? G(t._def.innerType) : t instanceof ce ? [void 0] : t instanceof
  de ? [null] : t instanceof D ? [void 0, ...G(t.unwrap())] : t instanceof W ? [null, ...G(t.unwrap())] : t instanceof Ge || t instanceof be ?
  G(t.unwrap()) : t instanceof ge ? G(t._def.innerType) : [], "getDiscriminator"), xt = class t extends v {
    static {
      i(this, "ZodDiscriminatedUnion");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      if (r.parsedType !== h.ZodParsedType.object)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.object,
          received: r.parsedType
        }), u.INVALID;
      let n = this.discriminator, s = r.data[n], o = this.optionsMap.get(s);
      return o ? r.common.async ? o._parseAsync({
        data: r.data,
        path: r.path,
        parent: r
      }) : o._parseSync({
        data: r.data,
        path: r.path,
        parent: r
      }) : ((0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.invalid_union_discriminator,
        options: Array.from(this.optionsMap.keys()),
        path: [n]
      }), u.INVALID);
    }
    get discriminator() {
      return this._def.discriminator;
    }
    get options() {
      return this._def.options;
    }
    get optionsMap() {
      return this._def.optionsMap;
    }
    /**
     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
     * have a different value for each object in the union.
     * @param discriminator the name of the discriminator property
     * @param types an array of object schemas
     * @param params
     */
    static create(e, r, n) {
      let s = /* @__PURE__ */ new Map();
      for (let o of r) {
        let a = G(o.shape[e]);
        if (!a.length)
          throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);
        for (let c of a) {
          if (s.has(c))
            throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(c)}`);
          s.set(c, o);
        }
      }
      return new t({
        typeName: g.ZodDiscriminatedUnion,
        discriminator: e,
        options: r,
        optionsMap: s,
        ...x(n)
      });
    }
  };
  d.ZodDiscriminatedUnion = xt;
  function gr(t, e) {
    let r = (0, h.getParsedType)(t), n = (0, h.getParsedType)(e);
    if (t === e)
      return { valid: !0, data: t };
    if (r === h.ZodParsedType.object && n === h.ZodParsedType.object) {
      let s = h.util.objectKeys(e), o = h.util.objectKeys(t).filter((c) => s.indexOf(c) !== -1), a = { ...t, ...e };
      for (let c of o) {
        let l = gr(t[c], e[c]);
        if (!l.valid)
          return { valid: !1 };
        a[c] = l.data;
      }
      return { valid: !0, data: a };
    } else if (r === h.ZodParsedType.array && n === h.ZodParsedType.array) {
      if (t.length !== e.length)
        return { valid: !1 };
      let s = [];
      for (let o = 0; o < t.length; o++) {
        let a = t[o], c = e[o], l = gr(a, c);
        if (!l.valid)
          return { valid: !1 };
        s.push(l.data);
      }
      return { valid: !0, data: s };
    } else return r === h.ZodParsedType.date && n === h.ZodParsedType.date && +t == +e ? { valid: !0, data: t } : { valid: !1 };
  }
  i(gr, "mergeValues");
  var le = class extends v {
    static {
      i(this, "ZodIntersection");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e), s = /* @__PURE__ */ i((o, a) => {
        if ((0, u.isAborted)(o) || (0, u.isAborted)(a))
          return u.INVALID;
        let c = gr(o.value, a.value);
        return c.valid ? (((0, u.isDirty)(o) || (0, u.isDirty)(a)) && r.dirty(), { status: r.value, value: c.data }) : ((0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.invalid_intersection_types
        }), u.INVALID);
      }, "handleParsed");
      return n.common.async ? Promise.all([
        this._def.left._parseAsync({
          data: n.data,
          path: n.path,
          parent: n
        }),
        this._def.right._parseAsync({
          data: n.data,
          path: n.path,
          parent: n
        })
      ]).then(([o, a]) => s(o, a)) : s(this._def.left._parseSync({
        data: n.data,
        path: n.path,
        parent: n
      }), this._def.right._parseSync({
        data: n.data,
        path: n.path,
        parent: n
      }));
    }
  };
  d.ZodIntersection = le;
  le.create = (t, e, r) => new le({
    left: t,
    right: e,
    typeName: g.ZodIntersection,
    ...x(r)
  });
  var B = class t extends v {
    static {
      i(this, "ZodTuple");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.array)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.array,
          received: n.parsedType
        }), u.INVALID;
      if (n.data.length < this._def.items.length)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.too_small,
          minimum: this._def.items.length,
          inclusive: !0,
          exact: !1,
          type: "array"
        }), u.INVALID;
      !this._def.rest && n.data.length > this._def.items.length && ((0, u.addIssueToContext)(n, {
        code: m.ZodIssueCode.too_big,
        maximum: this._def.items.length,
        inclusive: !0,
        exact: !1,
        type: "array"
      }), r.dirty());
      let o = [...n.data].map((a, c) => {
        let l = this._def.items[c] || this._def.rest;
        return l ? l._parse(new L(n, a, n.path, c)) : null;
      }).filter((a) => !!a);
      return n.common.async ? Promise.all(o).then((a) => u.ParseStatus.mergeArray(r, a)) : u.ParseStatus.mergeArray(r, o);
    }
    get items() {
      return this._def.items;
    }
    rest(e) {
      return new t({
        ...this._def,
        rest: e
      });
    }
  };
  d.ZodTuple = B;
  B.create = (t, e) => {
    if (!Array.isArray(t))
      throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
    return new B({
      items: t,
      typeName: g.ZodTuple,
      rest: null,
      ...x(e)
    });
  };
  var vt = class t extends v {
    static {
      i(this, "ZodRecord");
    }
    get keySchema() {
      return this._def.keyType;
    }
    get valueSchema() {
      return this._def.valueType;
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.object)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.object,
          received: n.parsedType
        }), u.INVALID;
      let s = [], o = this._def.keyType, a = this._def.valueType;
      for (let c in n.data)
        s.push({
          key: o._parse(new L(n, c, n.path, c)),
          value: a._parse(new L(n, n.data[c], n.path, c)),
          alwaysSet: c in n.data
        });
      return n.common.async ? u.ParseStatus.mergeObjectAsync(r, s) : u.ParseStatus.mergeObjectSync(r, s);
    }
    get element() {
      return this._def.valueType;
    }
    static create(e, r, n) {
      return r instanceof v ? new t({
        keyType: e,
        valueType: r,
        typeName: g.ZodRecord,
        ...x(n)
      }) : new t({
        keyType: X.create(),
        valueType: e,
        typeName: g.ZodRecord,
        ...x(r)
      });
    }
  };
  d.ZodRecord = vt;
  var Se = class extends v {
    static {
      i(this, "ZodMap");
    }
    get keySchema() {
      return this._def.keyType;
    }
    get valueSchema() {
      return this._def.valueType;
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.map)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.map,
          received: n.parsedType
        }), u.INVALID;
      let s = this._def.keyType, o = this._def.valueType, a = [...n.data.entries()].map(([c, l], p) => ({
        key: s._parse(new L(n, c, n.path, [p, "key"])),
        value: o._parse(new L(n, l, n.path, [p, "value"]))
      }));
      if (n.common.async) {
        let c = /* @__PURE__ */ new Map();
        return Promise.resolve().then(async () => {
          for (let l of a) {
            let p = await l.key, f = await l.value;
            if (p.status === "aborted" || f.status === "aborted")
              return u.INVALID;
            (p.status === "dirty" || f.status === "dirty") && r.dirty(), c.set(p.value, f.value);
          }
          return { status: r.value, value: c };
        });
      } else {
        let c = /* @__PURE__ */ new Map();
        for (let l of a) {
          let p = l.key, f = l.value;
          if (p.status === "aborted" || f.status === "aborted")
            return u.INVALID;
          (p.status === "dirty" || f.status === "dirty") && r.dirty(), c.set(p.value, f.value);
        }
        return { status: r.value, value: c };
      }
    }
  };
  d.ZodMap = Se;
  Se.create = (t, e, r) => new Se({
    valueType: e,
    keyType: t,
    typeName: g.ZodMap,
    ...x(r)
  });
  var Ee = class t extends v {
    static {
      i(this, "ZodSet");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.set)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.set,
          received: n.parsedType
        }), u.INVALID;
      let s = this._def;
      s.minSize !== null && n.data.size < s.minSize.value && ((0, u.addIssueToContext)(n, {
        code: m.ZodIssueCode.too_small,
        minimum: s.minSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: s.minSize.message
      }), r.dirty()), s.maxSize !== null && n.data.size > s.maxSize.value && ((0, u.addIssueToContext)(n, {
        code: m.ZodIssueCode.too_big,
        maximum: s.maxSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: s.maxSize.message
      }), r.dirty());
      let o = this._def.valueType;
      function a(l) {
        let p = /* @__PURE__ */ new Set();
        for (let f of l) {
          if (f.status === "aborted")
            return u.INVALID;
          f.status === "dirty" && r.dirty(), p.add(f.value);
        }
        return { status: r.value, value: p };
      }
      i(a, "finalizeSet");
      let c = [...n.data.values()].map((l, p) => o._parse(new L(n, l, n.path, p)));
      return n.common.async ? Promise.all(c).then((l) => a(l)) : a(c);
    }
    min(e, r) {
      return new t({
        ...this._def,
        minSize: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    max(e, r) {
      return new t({
        ...this._def,
        maxSize: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    size(e, r) {
      return this.min(e, r).max(e, r);
    }
    nonempty(e) {
      return this.min(1, e);
    }
  };
  d.ZodSet = Ee;
  Ee.create = (t, e) => new Ee({
    valueType: t,
    minSize: null,
    maxSize: null,
    typeName: g.ZodSet,
    ...x(e)
  });
  var _t = class t extends v {
    static {
      i(this, "ZodFunction");
    }
    constructor() {
      super(...arguments), this.validate = this.implement;
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      if (r.parsedType !== h.ZodParsedType.function)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.function,
          received: r.parsedType
        }), u.INVALID;
      function n(c, l) {
        return (0, u.makeIssue)({
          data: c,
          path: r.path,
          errorMaps: [
            r.common.contextualErrorMap,
            r.schemaErrorMap,
            (0, gt.getErrorMap)(),
            gt.defaultErrorMap
          ].filter((p) => !!p),
          issueData: {
            code: m.ZodIssueCode.invalid_arguments,
            argumentsError: l
          }
        });
      }
      i(n, "makeArgsIssue");
      function s(c, l) {
        return (0, u.makeIssue)({
          data: c,
          path: r.path,
          errorMaps: [
            r.common.contextualErrorMap,
            r.schemaErrorMap,
            (0, gt.getErrorMap)(),
            gt.defaultErrorMap
          ].filter((p) => !!p),
          issueData: {
            code: m.ZodIssueCode.invalid_return_type,
            returnTypeError: l
          }
        });
      }
      i(s, "makeReturnsIssue");
      let o = { errorMap: r.common.contextualErrorMap }, a = r.data;
      if (this._def.returns instanceof ee) {
        let c = this;
        return (0, u.OK)(async function(...l) {
          let p = new m.ZodError([]), f = await c._def.args.parseAsync(l, o).catch((_) => {
            throw p.addIssue(n(l, _)), p;
          }), b = await Reflect.apply(a, this, f);
          return await c._def.returns._def.type.parseAsync(b, o).catch((_) => {
            throw p.addIssue(s(b, _)), p;
          });
        });
      } else {
        let c = this;
        return (0, u.OK)(function(...l) {
          let p = c._def.args.safeParse(l, o);
          if (!p.success)
            throw new m.ZodError([n(l, p.error)]);
          let f = Reflect.apply(a, this, p.data), b = c._def.returns.safeParse(f, o);
          if (!b.success)
            throw new m.ZodError([s(f, b.error)]);
          return b.data;
        });
      }
    }
    parameters() {
      return this._def.args;
    }
    returnType() {
      return this._def.returns;
    }
    args(...e) {
      return new t({
        ...this._def,
        args: B.create(e).rest(z.create())
      });
    }
    returns(e) {
      return new t({
        ...this._def,
        returns: e
      });
    }
    implement(e) {
      return this.parse(e);
    }
    strictImplement(e) {
      return this.parse(e);
    }
    static create(e, r, n) {
      return new t({
        args: e || B.create([]).rest(z.create()),
        returns: r || z.create(),
        typeName: g.ZodFunction,
        ...x(n)
      });
    }
  };
  d.ZodFunction = _t;
  var pe = class extends v {
    static {
      i(this, "ZodLazy");
    }
    get schema() {
      return this._def.getter();
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      return this._def.getter()._parse({ data: r.data, path: r.path, parent: r });
    }
  };
  d.ZodLazy = pe;
  pe.create = (t, e) => new pe({
    getter: t,
    typeName: g.ZodLazy,
    ...x(e)
  });
  var fe = class extends v {
    static {
      i(this, "ZodLiteral");
    }
    _parse(e) {
      if (e.data !== this._def.value) {
        let r = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(r, {
          received: r.data,
          code: m.ZodIssueCode.invalid_literal,
          expected: this._def.value
        }), u.INVALID;
      }
      return { status: "valid", value: e.data };
    }
    get value() {
      return this._def.value;
    }
  };
  d.ZodLiteral = fe;
  fe.create = (t, e) => new fe({
    value: t,
    typeName: g.ZodLiteral,
    ...x(e)
  });
  function Jn(t, e) {
    return new me({
      values: t,
      typeName: g.ZodEnum,
      ...x(e)
    });
  }
  i(Jn, "createZodEnum");
  var me = class t extends v {
    static {
      i(this, "ZodEnum");
    }
    constructor() {
      super(...arguments), We.set(this, void 0);
    }
    _parse(e) {
      if (typeof e.data != "string") {
        let r = this._getOrReturnCtx(e), n = this._def.values;
        return (0, u.addIssueToContext)(r, {
          expected: h.util.joinValues(n),
          received: r.parsedType,
          code: m.ZodIssueCode.invalid_type
        }), u.INVALID;
      }
      if (bt(this, We, "f") || qn(this, We, new Set(this._def.values), "f"), !bt(this, We, "f").has(e.data)) {
        let r = this._getOrReturnCtx(e), n = this._def.values;
        return (0, u.addIssueToContext)(r, {
          received: r.data,
          code: m.ZodIssueCode.invalid_enum_value,
          options: n
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
    get options() {
      return this._def.values;
    }
    get enum() {
      let e = {};
      for (let r of this._def.values)
        e[r] = r;
      return e;
    }
    get Values() {
      let e = {};
      for (let r of this._def.values)
        e[r] = r;
      return e;
    }
    get Enum() {
      let e = {};
      for (let r of this._def.values)
        e[r] = r;
      return e;
    }
    extract(e, r = this._def) {
      return t.create(e, {
        ...this._def,
        ...r
      });
    }
    exclude(e, r = this._def) {
      return t.create(this.options.filter((n) => !e.includes(n)), {
        ...this._def,
        ...r
      });
    }
  };
  d.ZodEnum = me;
  We = /* @__PURE__ */ new WeakMap();
  me.create = Jn;
  var he = class extends v {
    static {
      i(this, "ZodNativeEnum");
    }
    constructor() {
      super(...arguments), qe.set(this, void 0);
    }
    _parse(e) {
      let r = h.util.getValidEnumValues(this._def.values), n = this._getOrReturnCtx(e);
      if (n.parsedType !== h.ZodParsedType.string && n.parsedType !== h.ZodParsedType.number) {
        let s = h.util.objectValues(r);
        return (0, u.addIssueToContext)(n, {
          expected: h.util.joinValues(s),
          received: n.parsedType,
          code: m.ZodIssueCode.invalid_type
        }), u.INVALID;
      }
      if (bt(this, qe, "f") || qn(this, qe, new Set(h.util.getValidEnumValues(this._def.values)), "f"), !bt(this, qe, "f").has(e.data)) {
        let s = h.util.objectValues(r);
        return (0, u.addIssueToContext)(n, {
          received: n.data,
          code: m.ZodIssueCode.invalid_enum_value,
          options: s
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
    get enum() {
      return this._def.values;
    }
  };
  d.ZodNativeEnum = he;
  qe = /* @__PURE__ */ new WeakMap();
  he.create = (t, e) => new he({
    values: t,
    typeName: g.ZodNativeEnum,
    ...x(e)
  });
  var ee = class extends v {
    static {
      i(this, "ZodPromise");
    }
    unwrap() {
      return this._def.type;
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      if (r.parsedType !== h.ZodParsedType.promise && r.common.async === !1)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.promise,
          received: r.parsedType
        }), u.INVALID;
      let n = r.parsedType === h.ZodParsedType.promise ? r.data : Promise.resolve(r.data);
      return (0, u.OK)(n.then((s) => this._def.type.parseAsync(s, {
        path: r.path,
        errorMap: r.common.contextualErrorMap
      })));
    }
  };
  d.ZodPromise = ee;
  ee.create = (t, e) => new ee({
    type: t,
    typeName: g.ZodPromise,
    ...x(e)
  });
  var Z = class extends v {
    static {
      i(this, "ZodEffects");
    }
    innerType() {
      return this._def.schema;
    }
    sourceType() {
      return this._def.schema._def.typeName === g.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e), s = this._def.effect || null, o = {
        addIssue: /* @__PURE__ */ i((a) => {
          (0, u.addIssueToContext)(n, a), a.fatal ? r.abort() : r.dirty();
        }, "addIssue"),
        get path() {
          return n.path;
        }
      };
      if (o.addIssue = o.addIssue.bind(o), s.type === "preprocess") {
        let a = s.transform(n.data, o);
        if (n.common.async)
          return Promise.resolve(a).then(async (c) => {
            if (r.value === "aborted")
              return u.INVALID;
            let l = await this._def.schema._parseAsync({
              data: c,
              path: n.path,
              parent: n
            });
            return l.status === "aborted" ? u.INVALID : l.status === "dirty" || r.value === "dirty" ? (0, u.DIRTY)(l.value) : l;
          });
        {
          if (r.value === "aborted")
            return u.INVALID;
          let c = this._def.schema._parseSync({
            data: a,
            path: n.path,
            parent: n
          });
          return c.status === "aborted" ? u.INVALID : c.status === "dirty" || r.value === "dirty" ? (0, u.DIRTY)(c.value) : c;
        }
      }
      if (s.type === "refinement") {
        let a = /* @__PURE__ */ i((c) => {
          let l = s.refinement(c, o);
          if (n.common.async)
            return Promise.resolve(l);
          if (l instanceof Promise)
            throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
          return c;
        }, "executeRefinement");
        if (n.common.async === !1) {
          let c = this._def.schema._parseSync({
            data: n.data,
            path: n.path,
            parent: n
          });
          return c.status === "aborted" ? u.INVALID : (c.status === "dirty" && r.dirty(), a(c.value), { status: r.value, value: c.value });
        } else
          return this._def.schema._parseAsync({ data: n.data, path: n.path, parent: n }).then((c) => c.status === "aborted" ? u.INVALID : (c.
          status === "dirty" && r.dirty(), a(c.value).then(() => ({ status: r.value, value: c.value }))));
      }
      if (s.type === "transform")
        if (n.common.async === !1) {
          let a = this._def.schema._parseSync({
            data: n.data,
            path: n.path,
            parent: n
          });
          if (!(0, u.isValid)(a))
            return a;
          let c = s.transform(a.value, o);
          if (c instanceof Promise)
            throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
          return { status: r.value, value: c };
        } else
          return this._def.schema._parseAsync({ data: n.data, path: n.path, parent: n }).then((a) => (0, u.isValid)(a) ? Promise.resolve(s.transform(
          a.value, o)).then((c) => ({ status: r.value, value: c })) : a);
      h.util.assertNever(s);
    }
  };
  d.ZodEffects = Z;
  d.ZodTransformer = Z;
  Z.create = (t, e, r) => new Z({
    schema: t,
    typeName: g.ZodEffects,
    effect: e,
    ...x(r)
  });
  Z.createWithPreprocess = (t, e, r) => new Z({
    schema: e,
    effect: { type: "preprocess", transform: t },
    typeName: g.ZodEffects,
    ...x(r)
  });
  var D = class extends v {
    static {
      i(this, "ZodOptional");
    }
    _parse(e) {
      return this._getType(e) === h.ZodParsedType.undefined ? (0, u.OK)(void 0) : this._def.innerType._parse(e);
    }
    unwrap() {
      return this._def.innerType;
    }
  };
  d.ZodOptional = D;
  D.create = (t, e) => new D({
    innerType: t,
    typeName: g.ZodOptional,
    ...x(e)
  });
  var W = class extends v {
    static {
      i(this, "ZodNullable");
    }
    _parse(e) {
      return this._getType(e) === h.ZodParsedType.null ? (0, u.OK)(null) : this._def.innerType._parse(e);
    }
    unwrap() {
      return this._def.innerType;
    }
  };
  d.ZodNullable = W;
  W.create = (t, e) => new W({
    innerType: t,
    typeName: g.ZodNullable,
    ...x(e)
  });
  var ye = class extends v {
    static {
      i(this, "ZodDefault");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = r.data;
      return r.parsedType === h.ZodParsedType.undefined && (n = this._def.defaultValue()), this._def.innerType._parse({
        data: n,
        path: r.path,
        parent: r
      });
    }
    removeDefault() {
      return this._def.innerType;
    }
  };
  d.ZodDefault = ye;
  ye.create = (t, e) => new ye({
    innerType: t,
    typeName: g.ZodDefault,
    defaultValue: typeof e.default == "function" ? e.default : () => e.default,
    ...x(e)
  });
  var ge = class extends v {
    static {
      i(this, "ZodCatch");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = {
        ...r,
        common: {
          ...r.common,
          issues: []
        }
      }, s = this._def.innerType._parse({
        data: n.data,
        path: n.path,
        parent: {
          ...n
        }
      });
      return (0, u.isAsync)(s) ? s.then((o) => ({
        status: "valid",
        value: o.status === "valid" ? o.value : this._def.catchValue({
          get error() {
            return new m.ZodError(n.common.issues);
          },
          input: n.data
        })
      })) : {
        status: "valid",
        value: s.status === "valid" ? s.value : this._def.catchValue({
          get error() {
            return new m.ZodError(n.common.issues);
          },
          input: n.data
        })
      };
    }
    removeCatch() {
      return this._def.innerType;
    }
  };
  d.ZodCatch = ge;
  ge.create = (t, e) => new ge({
    innerType: t,
    typeName: g.ZodCatch,
    catchValue: typeof e.catch == "function" ? e.catch : () => e.catch,
    ...x(e)
  });
  var Ce = class extends v {
    static {
      i(this, "ZodNaN");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.nan) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.nan,
          received: n.parsedType
        }), u.INVALID;
      }
      return { status: "valid", value: e.data };
    }
  };
  d.ZodNaN = Ce;
  Ce.create = (t) => new Ce({
    typeName: g.ZodNaN,
    ...x(t)
  });
  d.BRAND = Symbol("zod_brand");
  var Ge = class extends v {
    static {
      i(this, "ZodBranded");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = r.data;
      return this._def.type._parse({
        data: n,
        path: r.path,
        parent: r
      });
    }
    unwrap() {
      return this._def.type;
    }
  };
  d.ZodBranded = Ge;
  var ze = class t extends v {
    static {
      i(this, "ZodPipeline");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.common.async)
        return (/* @__PURE__ */ i(async () => {
          let o = await this._def.in._parseAsync({
            data: n.data,
            path: n.path,
            parent: n
          });
          return o.status === "aborted" ? u.INVALID : o.status === "dirty" ? (r.dirty(), (0, u.DIRTY)(o.value)) : this._def.out._parseAsync(
          {
            data: o.value,
            path: n.path,
            parent: n
          });
        }, "handleAsync"))();
      {
        let s = this._def.in._parseSync({
          data: n.data,
          path: n.path,
          parent: n
        });
        return s.status === "aborted" ? u.INVALID : s.status === "dirty" ? (r.dirty(), {
          status: "dirty",
          value: s.value
        }) : this._def.out._parseSync({
          data: s.value,
          path: n.path,
          parent: n
        });
      }
    }
    static create(e, r) {
      return new t({
        in: e,
        out: r,
        typeName: g.ZodPipeline
      });
    }
  };
  d.ZodPipeline = ze;
  var be = class extends v {
    static {
      i(this, "ZodReadonly");
    }
    _parse(e) {
      let r = this._def.innerType._parse(e), n = /* @__PURE__ */ i((s) => ((0, u.isValid)(s) && (s.value = Object.freeze(s.value)), s), "fre\
eze");
      return (0, u.isAsync)(r) ? r.then((s) => n(s)) : n(r);
    }
    unwrap() {
      return this._def.innerType;
    }
  };
  d.ZodReadonly = be;
  be.create = (t, e) => new be({
    innerType: t,
    typeName: g.ZodReadonly,
    ...x(e)
  });
  function Wn(t, e) {
    let r = typeof t == "function" ? t(e) : typeof t == "string" ? { message: t } : t;
    return typeof r == "string" ? { message: r } : r;
  }
  i(Wn, "cleanParams");
  function Hn(t, e = {}, r) {
    return t ? Q.create().superRefine((n, s) => {
      var o, a;
      let c = t(n);
      if (c instanceof Promise)
        return c.then((l) => {
          var p, f;
          if (!l) {
            let b = Wn(e, n), w = (f = (p = b.fatal) !== null && p !== void 0 ? p : r) !== null && f !== void 0 ? f : !0;
            s.addIssue({ code: "custom", ...b, fatal: w });
          }
        });
      if (!c) {
        let l = Wn(e, n), p = (a = (o = l.fatal) !== null && o !== void 0 ? o : r) !== null && a !== void 0 ? a : !0;
        s.addIssue({ code: "custom", ...l, fatal: p });
      }
    }) : Q.create();
  }
  i(Hn, "custom");
  d.custom = Hn;
  d.late = {
    object: R.lazycreate
  };
  var g;
  (function(t) {
    t.ZodString = "ZodString", t.ZodNumber = "ZodNumber", t.ZodNaN = "ZodNaN", t.ZodBigInt = "ZodBigInt", t.ZodBoolean = "ZodBoolean", t.ZodDate =
    "ZodDate", t.ZodSymbol = "ZodSymbol", t.ZodUndefined = "ZodUndefined", t.ZodNull = "ZodNull", t.ZodAny = "ZodAny", t.ZodUnknown = "ZodUn\
known", t.ZodNever = "ZodNever", t.ZodVoid = "ZodVoid", t.ZodArray = "ZodArray", t.ZodObject = "ZodObject", t.ZodUnion = "ZodUnion", t.ZodDiscriminatedUnion =
    "ZodDiscriminatedUnion", t.ZodIntersection = "ZodIntersection", t.ZodTuple = "ZodTuple", t.ZodRecord = "ZodRecord", t.ZodMap = "ZodMap",
    t.ZodSet = "ZodSet", t.ZodFunction = "ZodFunction", t.ZodLazy = "ZodLazy", t.ZodLiteral = "ZodLiteral", t.ZodEnum = "ZodEnum", t.ZodEffects =
    "ZodEffects", t.ZodNativeEnum = "ZodNativeEnum", t.ZodOptional = "ZodOptional", t.ZodNullable = "ZodNullable", t.ZodDefault = "ZodDefaul\
t", t.ZodCatch = "ZodCatch", t.ZodPromise = "ZodPromise", t.ZodBranded = "ZodBranded", t.ZodPipeline = "ZodPipeline", t.ZodReadonly = "ZodRe\
adonly";
  })(g || (d.ZodFirstPartyTypeKind = g = {}));
  var nc = /* @__PURE__ */ i((t, e = {
    message: `Input not instance of ${t.name}`
  }) => Hn((r) => r instanceof t, e), "instanceOfType");
  d.instanceof = nc;
  var Yn = X.create;
  d.string = Yn;
  var Xn = se.create;
  d.number = Xn;
  var sc = Ce.create;
  d.nan = sc;
  var oc = oe.create;
  d.bigint = oc;
  var Qn = ie.create;
  d.boolean = Qn;
  var ic = ae.create;
  d.date = ic;
  var ac = Te.create;
  d.symbol = ac;
  var cc = ce.create;
  d.undefined = cc;
  var dc = de.create;
  d.null = dc;
  var uc = Q.create;
  d.any = uc;
  var lc = z.create;
  d.unknown = lc;
  var pc = $.create;
  d.never = pc;
  var fc = Ie.create;
  d.void = fc;
  var mc = K.create;
  d.array = mc;
  var hc = R.create;
  d.object = hc;
  var yc = R.strictCreate;
  d.strictObject = yc;
  var gc = ue.create;
  d.union = gc;
  var bc = xt.create;
  d.discriminatedUnion = bc;
  var xc = le.create;
  d.intersection = xc;
  var vc = B.create;
  d.tuple = vc;
  var _c = vt.create;
  d.record = _c;
  var wc = Se.create;
  d.map = wc;
  var kc = Ee.create;
  d.set = kc;
  var Tc = _t.create;
  d.function = Tc;
  var Ic = pe.create;
  d.lazy = Ic;
  var Sc = fe.create;
  d.literal = Sc;
  var Ec = me.create;
  d.enum = Ec;
  var Cc = he.create;
  d.nativeEnum = Cc;
  var Pc = ee.create;
  d.promise = Pc;
  var es = Z.create;
  d.effect = es;
  d.transformer = es;
  var Ac = D.create;
  d.optional = Ac;
  var Oc = W.create;
  d.nullable = Oc;
  var jc = Z.createWithPreprocess;
  d.preprocess = jc;
  var Rc = ze.create;
  d.pipeline = Rc;
  var Nc = /* @__PURE__ */ i(() => Yn().optional(), "ostring");
  d.ostring = Nc;
  var Zc = /* @__PURE__ */ i(() => Xn().optional(), "onumber");
  d.onumber = Zc;
  var Mc = /* @__PURE__ */ i(() => Qn().optional(), "oboolean");
  d.oboolean = Mc;
  d.coerce = {
    string: /* @__PURE__ */ i((t) => X.create({ ...t, coerce: !0 }), "string"),
    number: /* @__PURE__ */ i((t) => se.create({ ...t, coerce: !0 }), "number"),
    boolean: /* @__PURE__ */ i((t) => ie.create({
      ...t,
      coerce: !0
    }), "boolean"),
    bigint: /* @__PURE__ */ i((t) => oe.create({ ...t, coerce: !0 }), "bigint"),
    date: /* @__PURE__ */ i((t) => ae.create({ ...t, coerce: !0 }), "date")
  };
  d.NEVER = u.INVALID;
});

// ../node_modules/zod/lib/external.js
var br = S((U) => {
  "use strict";
  var Dc = U && U.__createBinding || (Object.create ? function(t, e, r, n) {
    n === void 0 && (n = r);
    var s = Object.getOwnPropertyDescriptor(e, r);
    (!s || ("get" in s ? !e.__esModule : s.writable || s.configurable)) && (s = { enumerable: !0, get: /* @__PURE__ */ i(function() {
      return e[r];
    }, "get") }), Object.defineProperty(t, n, s);
  } : function(t, e, r, n) {
    n === void 0 && (n = r), t[n] = e[r];
  }), Pe = U && U.__exportStar || function(t, e) {
    for (var r in t) r !== "default" && !Object.prototype.hasOwnProperty.call(e, r) && Dc(e, t, r);
  };
  Object.defineProperty(U, "__esModule", { value: !0 });
  Pe(ht(), U);
  Pe(hr(), U);
  Pe($n(), U);
  Pe(Fe(), U);
  Pe(ts(), U);
  Pe(mt(), U);
});

// ../node_modules/zod/lib/index.js
var ss = S((N) => {
  "use strict";
  var rs = N && N.__createBinding || (Object.create ? function(t, e, r, n) {
    n === void 0 && (n = r);
    var s = Object.getOwnPropertyDescriptor(e, r);
    (!s || ("get" in s ? !e.__esModule : s.writable || s.configurable)) && (s = { enumerable: !0, get: /* @__PURE__ */ i(function() {
      return e[r];
    }, "get") }), Object.defineProperty(t, n, s);
  } : function(t, e, r, n) {
    n === void 0 && (n = r), t[n] = e[r];
  }), Lc = N && N.__setModuleDefault || (Object.create ? function(t, e) {
    Object.defineProperty(t, "default", { enumerable: !0, value: e });
  } : function(t, e) {
    t.default = e;
  }), Uc = N && N.__importStar || function(t) {
    if (t && t.__esModule) return t;
    var e = {};
    if (t != null) for (var r in t) r !== "default" && Object.prototype.hasOwnProperty.call(t, r) && rs(e, t, r);
    return Lc(e, t), e;
  }, $c = N && N.__exportStar || function(t, e) {
    for (var r in t) r !== "default" && !Object.prototype.hasOwnProperty.call(e, r) && rs(e, t, r);
  };
  Object.defineProperty(N, "__esModule", { value: !0 });
  N.z = void 0;
  var ns = Uc(br());
  N.z = ns;
  $c(br(), N);
  N.default = ns;
});

// ../node_modules/ts-dedent/dist/index.js
var is = S((Ke) => {
  "use strict";
  Object.defineProperty(Ke, "__esModule", { value: !0 });
  Ke.dedent = void 0;
  function os(t) {
    for (var e = [], r = 1; r < arguments.length; r++)
      e[r - 1] = arguments[r];
    var n = Array.from(typeof t == "string" ? [t] : t);
    n[n.length - 1] = n[n.length - 1].replace(/\r?\n([\t ]*)$/, "");
    var s = n.reduce(function(c, l) {
      var p = l.match(/\n([\t ]+|(?!\s).)/g);
      return p ? c.concat(p.map(function(f) {
        var b, w;
        return (w = (b = f.match(/[\t ]/g)) === null || b === void 0 ? void 0 : b.length) !== null && w !== void 0 ? w : 0;
      })) : c;
    }, []);
    if (s.length) {
      var o = new RegExp(`
[	 ]{` + Math.min.apply(Math, s) + "}", "g");
      n = n.map(function(c) {
        return c.replace(o, `
`);
      });
    }
    n[0] = n[0].replace(/^\r?\n/, "");
    var a = n[0];
    return e.forEach(function(c, l) {
      var p = a.match(/(?:^|\n)( *)$/), f = p ? p[1] : "", b = c;
      typeof c == "string" && c.includes(`
`) && (b = String(c).split(`
`).map(function(w, _) {
        return _ === 0 ? w : "" + f + w;
      }).join(`
`)), a += b + n[l + 1];
    }), a;
  }
  i(os, "dedent");
  Ke.dedent = os;
  Ke.default = os;
});

// ../node_modules/isexe/windows.js
var hs = S((Xl, ms) => {
  ms.exports = fs;
  fs.sync = qc;
  var ls = require("fs");
  function Wc(t, e) {
    var r = e.pathExt !== void 0 ? e.pathExt : process.env.PATHEXT;
    if (!r || (r = r.split(";"), r.indexOf("") !== -1))
      return !0;
    for (var n = 0; n < r.length; n++) {
      var s = r[n].toLowerCase();
      if (s && t.substr(-s.length).toLowerCase() === s)
        return !0;
    }
    return !1;
  }
  i(Wc, "checkPathExt");
  function ps(t, e, r) {
    return !t.isSymbolicLink() && !t.isFile() ? !1 : Wc(e, r);
  }
  i(ps, "checkStat");
  function fs(t, e, r) {
    ls.stat(t, function(n, s) {
      r(n, n ? !1 : ps(s, t, e));
    });
  }
  i(fs, "isexe");
  function qc(t, e) {
    return ps(ls.statSync(t), t, e);
  }
  i(qc, "sync");
});

// ../node_modules/isexe/mode.js
var vs = S((ep, xs) => {
  xs.exports = gs;
  gs.sync = Gc;
  var ys = require("fs");
  function gs(t, e, r) {
    ys.stat(t, function(n, s) {
      r(n, n ? !1 : bs(s, e));
    });
  }
  i(gs, "isexe");
  function Gc(t, e) {
    return bs(ys.statSync(t), e);
  }
  i(Gc, "sync");
  function bs(t, e) {
    return t.isFile() && zc(t, e);
  }
  i(bs, "checkStat");
  function zc(t, e) {
    var r = t.mode, n = t.uid, s = t.gid, o = e.uid !== void 0 ? e.uid : process.getuid && process.getuid(), a = e.gid !== void 0 ? e.gid : process.
    getgid && process.getgid(), c = parseInt("100", 8), l = parseInt("010", 8), p = parseInt("001", 8), f = c | l, b = r & p || r & l && s ===
    a || r & c && n === o || r & f && o === 0;
    return b;
  }
  i(zc, "checkMode");
});

// ../node_modules/isexe/index.js
var ws = S((np, _s) => {
  var rp = require("fs"), Et;
  process.platform === "win32" || global.TESTING_WINDOWS ? Et = hs() : Et = vs();
  _s.exports = xr;
  xr.sync = Kc;
  function xr(t, e, r) {
    if (typeof e == "function" && (r = e, e = {}), !r) {
      if (typeof Promise != "function")
        throw new TypeError("callback not provided");
      return new Promise(function(n, s) {
        xr(t, e || {}, function(o, a) {
          o ? s(o) : n(a);
        });
      });
    }
    Et(t, e || {}, function(n, s) {
      n && (n.code === "EACCES" || e && e.ignoreErrors) && (n = null, s = !1), r(n, s);
    });
  }
  i(xr, "isexe");
  function Kc(t, e) {
    try {
      return Et.sync(t, e || {});
    } catch (r) {
      if (e && e.ignoreErrors || r.code === "EACCES")
        return !1;
      throw r;
    }
  }
  i(Kc, "sync");
});

// ../node_modules/which/which.js
var Ps = S((op, Cs) => {
  var je = process.platform === "win32" || process.env.OSTYPE === "cygwin" || process.env.OSTYPE === "msys", ks = require("path"), Jc = je ?
  ";" : ":", Ts = ws(), Is = /* @__PURE__ */ i((t) => Object.assign(new Error(`not found: ${t}`), { code: "ENOENT" }), "getNotFoundError"), Ss = /* @__PURE__ */ i(
  (t, e) => {
    let r = e.colon || Jc, n = t.match(/\//) || je && t.match(/\\/) ? [""] : [
      // windows always checks the cwd first
      ...je ? [process.cwd()] : [],
      ...(e.path || process.env.PATH || /* istanbul ignore next: very unusual */
      "").split(r)
    ], s = je ? e.pathExt || process.env.PATHEXT || ".EXE;.CMD;.BAT;.COM" : "", o = je ? s.split(r) : [""];
    return je && t.indexOf(".") !== -1 && o[0] !== "" && o.unshift(""), {
      pathEnv: n,
      pathExt: o,
      pathExtExe: s
    };
  }, "getPathInfo"), Es = /* @__PURE__ */ i((t, e, r) => {
    typeof e == "function" && (r = e, e = {}), e || (e = {});
    let { pathEnv: n, pathExt: s, pathExtExe: o } = Ss(t, e), a = [], c = /* @__PURE__ */ i((p) => new Promise((f, b) => {
      if (p === n.length)
        return e.all && a.length ? f(a) : b(Is(t));
      let w = n[p], _ = /^".*"$/.test(w) ? w.slice(1, -1) : w, P = ks.join(_, t), E = !_ && /^\.[\\\/]/.test(t) ? t.slice(0, 2) + P : P;
      f(l(E, p, 0));
    }), "step"), l = /* @__PURE__ */ i((p, f, b) => new Promise((w, _) => {
      if (b === s.length)
        return w(c(f + 1));
      let P = s[b];
      Ts(p + P, { pathExt: o }, (E, A) => {
        if (!E && A)
          if (e.all)
            a.push(p + P);
          else
            return w(p + P);
        return w(l(p, f, b + 1));
      });
    }), "subStep");
    return r ? c(0).then((p) => r(null, p), r) : c(0);
  }, "which"), Hc = /* @__PURE__ */ i((t, e) => {
    e = e || {};
    let { pathEnv: r, pathExt: n, pathExtExe: s } = Ss(t, e), o = [];
    for (let a = 0; a < r.length; a++) {
      let c = r[a], l = /^".*"$/.test(c) ? c.slice(1, -1) : c, p = ks.join(l, t), f = !l && /^\.[\\\/]/.test(t) ? t.slice(0, 2) + p : p;
      for (let b = 0; b < n.length; b++) {
        let w = f + n[b];
        try {
          if (Ts.sync(w, { pathExt: s }))
            if (e.all)
              o.push(w);
            else
              return w;
        } catch {
        }
      }
    }
    if (e.all && o.length)
      return o;
    if (e.nothrow)
      return null;
    throw Is(t);
  }, "whichSync");
  Cs.exports = Es;
  Es.sync = Hc;
});

// ../node_modules/path-key/index.js
var Os = S((ap, vr) => {
  "use strict";
  var As = /* @__PURE__ */ i((t = {}) => {
    let e = t.env || process.env;
    return (t.platform || process.platform) !== "win32" ? "PATH" : Object.keys(e).reverse().find((n) => n.toUpperCase() === "PATH") || "Path";
  }, "pathKey");
  vr.exports = As;
  vr.exports.default = As;
});

// ../node_modules/cross-spawn/lib/util/resolveCommand.js
var Zs = S((dp, Ns) => {
  "use strict";
  var js = require("path"), Yc = Ps(), Xc = Os();
  function Rs(t, e) {
    let r = t.options.env || process.env, n = process.cwd(), s = t.options.cwd != null, o = s && process.chdir !== void 0 && !process.chdir.
    disabled;
    if (o)
      try {
        process.chdir(t.options.cwd);
      } catch {
      }
    let a;
    try {
      a = Yc.sync(t.command, {
        path: r[Xc({ env: r })],
        pathExt: e ? js.delimiter : void 0
      });
    } catch {
    } finally {
      o && process.chdir(n);
    }
    return a && (a = js.resolve(s ? t.options.cwd : "", a)), a;
  }
  i(Rs, "resolveCommandAttempt");
  function Qc(t) {
    return Rs(t) || Rs(t, !0);
  }
  i(Qc, "resolveCommand");
  Ns.exports = Qc;
});

// ../node_modules/cross-spawn/lib/util/escape.js
var Ms = S((lp, wr) => {
  "use strict";
  var _r = /([()\][%!^"`<>&|;, *?])/g;
  function ed(t) {
    return t = t.replace(_r, "^$1"), t;
  }
  i(ed, "escapeCommand");
  function td(t, e) {
    return t = `${t}`, t = t.replace(/(?=(\\+?)?)\1"/g, '$1$1\\"'), t = t.replace(/(?=(\\+?)?)\1$/, "$1$1"), t = `"${t}"`, t = t.replace(_r,
    "^$1"), e && (t = t.replace(_r, "^$1")), t;
  }
  i(td, "escapeArgument");
  wr.exports.command = ed;
  wr.exports.argument = td;
});

// ../node_modules/shebang-regex/index.js
var Ls = S((fp, Ds) => {
  "use strict";
  Ds.exports = /^#!(.*)/;
});

// ../node_modules/shebang-command/index.js
var $s = S((mp, Us) => {
  "use strict";
  var rd = Ls();
  Us.exports = (t = "") => {
    let e = t.match(rd);
    if (!e)
      return null;
    let [r, n] = e[0].replace(/#! ?/, "").split(" "), s = r.split("/").pop();
    return s === "env" ? n : n ? `${s} ${n}` : s;
  };
});

// ../node_modules/cross-spawn/lib/util/readShebang.js
var Fs = S((hp, Vs) => {
  "use strict";
  var kr = require("fs"), nd = $s();
  function sd(t) {
    let r = Buffer.alloc(150), n;
    try {
      n = kr.openSync(t, "r"), kr.readSync(n, r, 0, 150, 0), kr.closeSync(n);
    } catch {
    }
    return nd(r.toString());
  }
  i(sd, "readShebang");
  Vs.exports = sd;
});

// ../node_modules/cross-spawn/lib/parse.js
var Gs = S((gp, qs) => {
  "use strict";
  var od = require("path"), Bs = Zs(), Ws = Ms(), id = Fs(), ad = process.platform === "win32", cd = /\.(?:com|exe)$/i, dd = /node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;
  function ud(t) {
    t.file = Bs(t);
    let e = t.file && id(t.file);
    return e ? (t.args.unshift(t.file), t.command = e, Bs(t)) : t.file;
  }
  i(ud, "detectShebang");
  function ld(t) {
    if (!ad)
      return t;
    let e = ud(t), r = !cd.test(e);
    if (t.options.forceShell || r) {
      let n = dd.test(e);
      t.command = od.normalize(t.command), t.command = Ws.command(t.command), t.args = t.args.map((o) => Ws.argument(o, n));
      let s = [t.command].concat(t.args).join(" ");
      t.args = ["/d", "/s", "/c", `"${s}"`], t.command = process.env.comspec || "cmd.exe", t.options.windowsVerbatimArguments = !0;
    }
    return t;
  }
  i(ld, "parseNonShell");
  function pd(t, e, r) {
    e && !Array.isArray(e) && (r = e, e = null), e = e ? e.slice(0) : [], r = Object.assign({}, r);
    let n = {
      command: t,
      args: e,
      options: r,
      file: void 0,
      original: {
        command: t,
        args: e
      }
    };
    return r.shell ? n : ld(n);
  }
  i(pd, "parse");
  qs.exports = pd;
});

// ../node_modules/cross-spawn/lib/enoent.js
var Js = S((xp, Ks) => {
  "use strict";
  var Tr = process.platform === "win32";
  function Ir(t, e) {
    return Object.assign(new Error(`${e} ${t.command} ENOENT`), {
      code: "ENOENT",
      errno: "ENOENT",
      syscall: `${e} ${t.command}`,
      path: t.command,
      spawnargs: t.args
    });
  }
  i(Ir, "notFoundError");
  function fd(t, e) {
    if (!Tr)
      return;
    let r = t.emit;
    t.emit = function(n, s) {
      if (n === "exit") {
        let o = zs(s, e);
        if (o)
          return r.call(t, "error", o);
      }
      return r.apply(t, arguments);
    };
  }
  i(fd, "hookChildProcess");
  function zs(t, e) {
    return Tr && t === 1 && !e.file ? Ir(e.original, "spawn") : null;
  }
  i(zs, "verifyENOENT");
  function md(t, e) {
    return Tr && t === 1 && !e.file ? Ir(e.original, "spawnSync") : null;
  }
  i(md, "verifyENOENTSync");
  Ks.exports = {
    hookChildProcess: fd,
    verifyENOENT: zs,
    verifyENOENTSync: md,
    notFoundError: Ir
  };
});

// ../node_modules/cross-spawn/index.js
var Xs = S((_p, Re) => {
  "use strict";
  var Hs = require("child_process"), Sr = Gs(), Er = Js();
  function Ys(t, e, r) {
    let n = Sr(t, e, r), s = Hs.spawn(n.command, n.args, n.options);
    return Er.hookChildProcess(s, n), s;
  }
  i(Ys, "spawn");
  function hd(t, e, r) {
    let n = Sr(t, e, r), s = Hs.spawnSync(n.command, n.args, n.options);
    return s.error = s.error || Er.verifyENOENTSync(s.status, n), s;
  }
  i(hd, "spawnSync");
  Re.exports = Ys;
  Re.exports.spawn = Ys;
  Re.exports.sync = hd;
  Re.exports._parse = Sr;
  Re.exports._enoent = Er;
});

// ../node_modules/merge-stream/index.js
var Oo = S((Uf, Ao) => {
  "use strict";
  var { PassThrough: au } = require("stream");
  Ao.exports = function() {
    var t = [], e = new au({ objectMode: !0 });
    return e.setMaxListeners(0), e.add = r, e.isEmpty = n, e.on("unpipe", s), Array.prototype.slice.call(arguments).forEach(r), e;
    function r(o) {
      return Array.isArray(o) ? (o.forEach(r), this) : (t.push(o), o.once("end", s.bind(null, o)), o.once("error", e.emit.bind(e, "error")),
      o.pipe(e, { end: !1 }), this);
    }
    i(r, "add");
    function n() {
      return t.length == 0;
    }
    i(n, "isEmpty");
    function s(o) {
      t = t.filter(function(a) {
        return a !== o;
      }), !t.length && e.readable && e.end();
    }
    i(s, "remove");
  };
});

// ../node_modules/slash/index.js
function Qr(t) {
  return t.startsWith("\\\\?\\") ? t : t.replace(/\\/g, "/");
}
var oi = aa(() => {
  i(Qr, "slash");
});

// ../node_modules/common-path-prefix/index.js
var ci = S((xm, ai) => {
  "use strict";
  var { sep: _u } = require("path"), wu = /* @__PURE__ */ i((t) => {
    for (let e of t) {
      let r = /(\/|\\)/.exec(e);
      if (r !== null) return r[0];
    }
    return _u;
  }, "determineSeparator");
  ai.exports = /* @__PURE__ */ i(function(e, r = wu(e)) {
    let [n = "", ...s] = e;
    if (n === "" || s.length === 0) return "";
    let o = n.split(r), a = o.length;
    for (let l of s) {
      let p = l.split(r);
      for (let f = 0; f < a; f++)
        p[f] !== o[f] && (a = f);
      if (a === 0) return "";
    }
    let c = o.slice(0, a).join(r);
    return c.endsWith(r) ? c : c + r;
  }, "commonPathPrefix");
});

// ../node_modules/fetch-retry/index.js
var Vi = S((Ty, $i) => {
  "use strict";
  $i.exports = function(t, e) {
    if (e = e || {}, typeof t != "function")
      throw new te("fetch must be a function");
    if (typeof e != "object")
      throw new te("defaults must be an object");
    if (e.retries !== void 0 && !Ht(e.retries))
      throw new te("retries must be a positive integer");
    if (e.retryDelay !== void 0 && !Ht(e.retryDelay) && typeof e.retryDelay != "function")
      throw new te("retryDelay must be a positive integer or a function returning a positive integer");
    if (e.retryOn !== void 0 && !Array.isArray(e.retryOn) && typeof e.retryOn != "function")
      throw new te("retryOn property expects an array or function");
    var r = {
      retries: 3,
      retryDelay: 1e3,
      retryOn: []
    };
    return e = Object.assign(r, e), /* @__PURE__ */ i(function(s, o) {
      var a = e.retries, c = e.retryDelay, l = e.retryOn;
      if (o && o.retries !== void 0)
        if (Ht(o.retries))
          a = o.retries;
        else
          throw new te("retries must be a positive integer");
      if (o && o.retryDelay !== void 0)
        if (Ht(o.retryDelay) || typeof o.retryDelay == "function")
          c = o.retryDelay;
        else
          throw new te("retryDelay must be a positive integer or a function returning a positive integer");
      if (o && o.retryOn)
        if (Array.isArray(o.retryOn) || typeof o.retryOn == "function")
          l = o.retryOn;
        else
          throw new te("retryOn property expects an array or function");
      return new Promise(function(p, f) {
        var b = /* @__PURE__ */ i(function(_) {
          var P = typeof Request < "u" && s instanceof Request ? s.clone() : s;
          t(P, o).then(function(E) {
            if (Array.isArray(l) && l.indexOf(E.status) === -1)
              p(E);
            else if (typeof l == "function")
              try {
                return Promise.resolve(l(_, null, E)).then(function(A) {
                  A ? w(_, null, E) : p(E);
                }).catch(f);
              } catch (A) {
                f(A);
              }
            else
              _ < a ? w(_, null, E) : p(E);
          }).catch(function(E) {
            if (typeof l == "function")
              try {
                Promise.resolve(l(_, E, null)).then(function(A) {
                  A ? w(_, E, null) : f(E);
                }).catch(function(A) {
                  f(A);
                });
              } catch (A) {
                f(A);
              }
            else _ < a ? w(_, E, null) : f(E);
          });
        }, "wrappedFetch");
        function w(_, P, E) {
          var A = typeof c == "function" ? c(_, P, E) : c;
          setTimeout(function() {
            b(++_);
          }, A);
        }
        i(w, "retry"), b(0);
      });
    }, "fetchRetry");
  };
  function Ht(t) {
    return Number.isInteger(t) && t >= 0;
  }
  i(Ht, "isPositiveInteger");
  function te(t) {
    this.name = "ArgumentError", this.message = t;
  }
  i(te, "ArgumentError");
});

// src/telemetry/index.ts
var tl = {};
ca(tl, {
  addToGlobalContext: () => ea,
  cleanPaths: () => we,
  computeStorybookMetadata: () => Ui,
  getPrecedingUpgrade: () => Hi,
  getStorybookMetadata: () => un,
  isExampleStoryId: () => Qu,
  metaFrameworks: () => cn,
  oneWayHash: () => Yt,
  removeAnsiEscapeCodes: () => nr,
  sanitizeAddonName: () => dn,
  sanitizeError: () => Ve,
  telemetry: () => el
});
module.exports = da(tl);
var yn = require("storybook/internal/node-logger");

// src/telemetry/notify.ts
var rr = require("storybook/internal/common"), re = require("storybook/internal/node-logger"), wn = T(vn(), 1);
var _n = "telemetry-notification-date", kn = /* @__PURE__ */ i(async () => {
  await rr.cache.get(_n, null) || (rr.cache.set(_n, Date.now()), re.logger.log(
    `${re.CLI_COLORS.info("Attention:")} Storybook now collects completely anonymous telemetry regarding usage. This information is used to \
shape Storybook's roadmap and prioritize features.`
  ), re.logger.log(
    "You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:"
  ), re.logger.log(wn.default.cyan("https://storybook.js.org/telemetry")), re.logger.log(""));
}, "notify");

// src/telemetry/sanitize.ts
var sr = T(require("node:path"), 1);
function Tn(t) {
  return t.replace(/[-[/{}()*+?.\\^$|]/g, "\\$&");
}
i(Tn, "regexpEscape");
function nr(t = "") {
  return t.replace(/\u001B\[[0-9;]*m/g, "");
}
i(nr, "removeAnsiEscapeCodes");
function we(t, e = sr.default.sep) {
  if (!t)
    return t;
  let r = process.cwd().split(e);
  for (; r.length > 1; ) {
    let n = r.join(e), s = new RegExp(Tn(n), "gi");
    t = t.replace(s, "$SNIP");
    let o = r.join(e + e), a = new RegExp(Tn(o), "gi");
    t = t.replace(a, "$SNIP"), r.pop();
  }
  return t;
}
i(we, "cleanPaths");
function Ve(t, e = sr.default.sep) {
  try {
    t = {
      ...JSON.parse(JSON.stringify(t)),
      message: nr(t.message),
      stack: nr(t.stack),
      cause: t.cause,
      name: t.name
    };
    let r = we(JSON.stringify(t), e);
    return JSON.parse(r);
  } catch (r) {
    return `Sanitization error: ${r?.message}`;
  }
}
i(Ve, "sanitizeError");

// src/telemetry/storybook-metadata.ts
var Di = require("node:path"), q = require("storybook/internal/common"), Li = require("storybook/internal/csf-tools");

// ../node_modules/fd-package-json/dist/esm/main.js
var En = T(Sn(), 1), Cn = require("node:path"), pt = require("node:fs/promises"), Pn = require("node:fs");
async function ma(t) {
  try {
    return (await (0, pt.stat)(t)).isFile();
  } catch {
    return !1;
  }
}
i(ma, "fileExists");
async function or(t) {
  for (let e of (0, En.walkUp)(t)) {
    let r = (0, Cn.resolve)(e, "package.json");
    if (await ma(r))
      return r;
  }
  return null;
}
i(or, "findPackagePath");
async function An(t) {
  let e = await or(t);
  if (!e)
    return null;
  try {
    let r = await (0, pt.readFile)(e, { encoding: "utf8" });
    return JSON.parse(r);
  } catch {
    return null;
  }
}
i(An, "findPackage");

// ../node_modules/package-manager-detector/dist/constants.mjs
var On = [
  "npm",
  "yarn",
  "yarn@berry",
  "pnpm",
  "pnpm@6",
  "bun",
  "deno"
], ir = {
  "bun.lock": "bun",
  "bun.lockb": "bun",
  "deno.lock": "deno",
  "pnpm-lock.yaml": "pnpm",
  "pnpm-workspace.yaml": "pnpm",
  "yarn.lock": "yarn",
  "package-lock.json": "npm",
  "npm-shrinkwrap.json": "npm"
}, ar = {
  "node_modules/.deno/": "deno",
  "node_modules/.pnpm/": "pnpm",
  "node_modules/.yarn-state.yml": "yarn",
  // yarn v2+ (node-modules)
  "node_modules/.yarn_integrity": "yarn",
  // yarn v1
  "node_modules/.package-lock.json": "npm",
  ".pnp.cjs": "yarn",
  // yarn v3+ (pnp)
  ".pnp.js": "yarn",
  // yarn v2 (pnp)
  "bun.lock": "bun",
  "bun.lockb": "bun"
};

// ../node_modules/package-manager-detector/dist/detect.mjs
var dr = T(require("node:fs/promises"), 1), H = T(require("node:path"), 1), Rn = T(require("node:process"), 1);
async function cr(t, e) {
  try {
    let r = await dr.default.stat(t);
    return e === "file" ? r.isFile() : r.isDirectory();
  } catch {
    return !1;
  }
}
i(cr, "pathExists");
function* ha(t = Rn.default.cwd()) {
  let e = H.default.resolve(t), { root: r } = H.default.parse(e);
  for (; e && e !== r; )
    yield e, e = H.default.dirname(e);
}
i(ha, "lookup");
async function jn(t, e) {
  return !t || !cr(t, "file") ? null : await ga(t, e);
}
i(jn, "parsePackageJson");
async function ur(t = {}) {
  let { cwd: e, strategies: r = ["lockfile", "packageManager-field", "devEngines-field"], onUnknown: n } = t;
  for (let s of ha(e))
    for (let o of r)
      switch (o) {
        case "lockfile": {
          for (let a of Object.keys(ir))
            if (await cr(H.default.join(s, a), "file")) {
              let c = ir[a], l = await jn(H.default.join(s, "package.json"), n);
              return l || { name: c, agent: c };
            }
          break;
        }
        case "packageManager-field":
        case "devEngines-field": {
          let a = await jn(H.default.join(s, "package.json"), n);
          if (a)
            return a;
          break;
        }
        case "install-metadata": {
          for (let a of Object.keys(ar)) {
            let c = a.endsWith("/") ? "dir" : "file";
            if (await cr(H.default.join(s, a), c)) {
              let l = ar[a], p = l === "yarn" ? ba(a) ? "yarn" : "yarn@berry" : l;
              return { name: l, agent: p };
            }
          }
          break;
        }
      }
  return null;
}
i(ur, "detect");
function ya(t) {
  let e = /* @__PURE__ */ i((r) => r?.match(/\d+(\.\d+){0,2}/)?.[0] ?? r, "handelVer");
  if (typeof t.packageManager == "string") {
    let [r, n] = t.packageManager.replace(/^\^/, "").split("@");
    return { name: r, ver: e(n) };
  }
  if (typeof t.devEngines?.packageManager?.name == "string")
    return {
      name: t.devEngines.packageManager.name,
      ver: e(t.devEngines.packageManager.version)
    };
}
i(ya, "getNameAndVer");
async function ga(t, e) {
  try {
    let r = JSON.parse(await dr.default.readFile(t, "utf8")), n, s = ya(r);
    if (s) {
      let o = s.name, a = s.ver, c = a;
      return o === "yarn" && a && Number.parseInt(a) > 1 ? (n = "yarn@berry", c = "berry", { name: o, agent: n, version: c }) : o === "pnpm" &&
      a && Number.parseInt(a) < 7 ? (n = "pnpm@6", { name: o, agent: n, version: c }) : On.includes(o) ? (n = o, { name: o, agent: n, version: c }) :
      e?.(r.packageManager) ?? null;
    }
  } catch {
  }
  return null;
}
i(ga, "handlePackageManager");
function ba(t) {
  return t.endsWith(".yarn_integrity");
}
i(ba, "isMetadataYarnClassic");

// ../node_modules/package-manager-detector/dist/index.mjs
var bl = require("node:fs/promises"), xl = require("node:path"), vl = require("node:process");

// package.json
var ft = "9.0.12";

// src/cli/globalSettings.ts
var Tt = T(require("node:fs/promises"), 1), ds = require("node:os"), St = require("node:path"), Oe = T(ss(), 1);

// src/server-errors.ts
var cs = T(is(), 1);

// src/storybook-error.ts
function as({
  code: t,
  category: e
}) {
  let r = String(t).padStart(4, "0");
  return `SB_${e}_${r}`;
}
i(as, "parseErrorCode");
var wt = class t extends Error {
  constructor(r) {
    super(t.getFullMessage(r));
    /**
     * Data associated with the error. Used to provide additional information in the error message or
     * to be passed to telemetry.
     */
    this.data = {};
    /** Flag used to easily determine if the error originates from Storybook. */
    this.fromStorybook = !0;
    this.category = r.category, this.documentation = r.documentation ?? !1, this.code = r.code;
  }
  static {
    i(this, "StorybookError");
  }
  get fullErrorCode() {
    return as({ code: this.code, category: this.category });
  }
  /** Overrides the default `Error.name` property in the format: SB_<CATEGORY>_<CODE>. */
  get name() {
    let r = this.constructor.name;
    return `${this.fullErrorCode} (${r})`;
  }
  /** Generates the error message along with additional documentation link (if applicable). */
  static getFullMessage({
    documentation: r,
    code: n,
    category: s,
    message: o
  }) {
    let a;
    return r === !0 ? a = `https://storybook.js.org/error/${as({ code: n, category: s })}` : typeof r == "string" ? a = r : Array.isArray(r) &&
    (a = `
${r.map((c) => `	- ${c}`).join(`
`)}`), `${o}${a != null ? `

More info: ${a}
` : ""}`;
  }
};

// src/server-errors.ts
var kt = class extends wt {
  constructor(r) {
    super({
      category: "CORE-SERVER",
      code: 1,
      message: cs.dedent`
        Unable to save global settings file to ${r.filePath}
        ${r.error && `Reason: ${r.error}`}`
    });
    this.data = r;
  }
  static {
    i(this, "SavingGlobalSettingsFileError");
  }
};

// src/cli/globalSettings.ts
var Vc = (0, St.join)((0, ds.homedir)(), ".storybook", "settings.json"), Fc = 1, Bc = Oe.z.object({
  version: Oe.z.number(),
  // NOTE: every key (and subkey) below must be optional, for forwards compatibility reasons
  // (we can remove keys once they are deprecated)
  userSince: Oe.z.number().optional(),
  init: Oe.z.object({ skipOnboarding: Oe.z.boolean().optional() }).optional()
}), Ae;
async function us(t = Vc) {
  if (Ae)
    return Ae;
  try {
    let e = await Tt.default.readFile(t, "utf8"), r = Bc.parse(JSON.parse(e));
    Ae = new It(t, r);
  } catch {
    Ae = new It(t, { version: Fc, userSince: Date.now() }), await Ae.save();
  }
  return Ae;
}
i(us, "globalSettings");
var It = class {
  static {
    i(this, "Settings");
  }
  /**
   * Create a new Settings instance
   *
   * @param filePath Path to the JSON settings file
   * @param value Loaded value of settings
   */
  constructor(e, r) {
    this.filePath = e, this.value = r;
  }
  /** Save settings to the file */
  async save() {
    try {
      await Tt.default.mkdir((0, St.dirname)(this.filePath), { recursive: !0 }), await Tt.default.writeFile(this.filePath, JSON.stringify(this.
      value, null, 2));
    } catch (e) {
      throw new kt({
        filePath: this.filePath,
        error: e
      });
    }
  }
};

// src/telemetry/get-application-file-count.ts
var Ti = require("node:path");

// src/telemetry/exec-command-count-lines.ts
var si = require("node:readline");

// node_modules/execa/index.js
var Yo = require("node:buffer"), Xo = T(require("node:path"), 1), Vt = T(require("node:child_process"), 1), Xe = T(require("node:process"), 1),
Qo = T(Xs(), 1);

// ../node_modules/strip-final-newline/index.js
function Cr(t) {
  let e = typeof t == "string" ? `
` : 10, r = typeof t == "string" ? "\r" : 13;
  return t[t.length - 1] === e && (t = t.slice(0, -1)), t[t.length - 1] === r && (t = t.slice(0, -1)), t;
}
i(Cr, "stripFinalNewline");

// node_modules/npm-run-path/index.js
var Je = T(require("node:process"), 1), Ne = T(require("node:path"), 1), Pr = require("node:url");

// node_modules/path-key/index.js
function Ct(t = {}) {
  let {
    env: e = process.env,
    platform: r = process.platform
  } = t;
  return r !== "win32" ? "PATH" : Object.keys(e).reverse().find((n) => n.toUpperCase() === "PATH") || "Path";
}
i(Ct, "pathKey");

// node_modules/npm-run-path/index.js
var yd = /* @__PURE__ */ i(({
  cwd: t = Je.default.cwd(),
  path: e = Je.default.env[Ct()],
  preferLocal: r = !0,
  execPath: n = Je.default.execPath,
  addExecPath: s = !0
} = {}) => {
  let o = t instanceof URL ? (0, Pr.fileURLToPath)(t) : t, a = Ne.default.resolve(o), c = [];
  return r && gd(c, a), s && bd(c, n, a), [...c, e].join(Ne.default.delimiter);
}, "npmRunPath"), gd = /* @__PURE__ */ i((t, e) => {
  let r;
  for (; r !== e; )
    t.push(Ne.default.join(e, "node_modules/.bin")), r = e, e = Ne.default.resolve(e, "..");
}, "applyPreferLocal"), bd = /* @__PURE__ */ i((t, e, r) => {
  let n = e instanceof URL ? (0, Pr.fileURLToPath)(e) : e;
  t.push(Ne.default.resolve(r, n, ".."));
}, "applyExecPath"), Qs = /* @__PURE__ */ i(({ env: t = Je.default.env, ...e } = {}) => {
  t = { ...t };
  let r = Ct({ env: t });
  return e.path = t[r], t[r] = yd(e), t;
}, "npmRunPathEnv");

// node_modules/mimic-fn/index.js
var xd = /* @__PURE__ */ i((t, e, r, n) => {
  if (r === "length" || r === "prototype" || r === "arguments" || r === "caller")
    return;
  let s = Object.getOwnPropertyDescriptor(t, r), o = Object.getOwnPropertyDescriptor(e, r);
  !vd(s, o) && n || Object.defineProperty(t, r, o);
}, "copyProperty"), vd = /* @__PURE__ */ i(function(t, e) {
  return t === void 0 || t.configurable || t.writable === e.writable && t.enumerable === e.enumerable && t.configurable === e.configurable &&
  (t.writable || t.value === e.value);
}, "canCopyProperty"), _d = /* @__PURE__ */ i((t, e) => {
  let r = Object.getPrototypeOf(e);
  r !== Object.getPrototypeOf(t) && Object.setPrototypeOf(t, r);
}, "changePrototype"), wd = /* @__PURE__ */ i((t, e) => `/* Wrapped ${t}*/
${e}`, "wrappedToString"), kd = Object.getOwnPropertyDescriptor(Function.prototype, "toString"), Td = Object.getOwnPropertyDescriptor(Function.
prototype.toString, "name"), Id = /* @__PURE__ */ i((t, e, r) => {
  let n = r === "" ? "" : `with ${r.trim()}() `, s = wd.bind(null, n, e.toString());
  Object.defineProperty(s, "name", Td), Object.defineProperty(t, "toString", { ...kd, value: s });
}, "changeToString");
function Ar(t, e, { ignoreNonConfigurable: r = !1 } = {}) {
  let { name: n } = t;
  for (let s of Reflect.ownKeys(e))
    xd(t, e, s, r);
  return _d(t, e), Id(t, e, n), t;
}
i(Ar, "mimicFunction");

// node_modules/onetime/index.js
var Pt = /* @__PURE__ */ new WeakMap(), eo = /* @__PURE__ */ i((t, e = {}) => {
  if (typeof t != "function")
    throw new TypeError("Expected a function");
  let r, n = 0, s = t.displayName || t.name || "<anonymous>", o = /* @__PURE__ */ i(function(...a) {
    if (Pt.set(o, ++n), n === 1)
      r = t.apply(this, a), t = null;
    else if (e.throw === !0)
      throw new Error(`Function \`${s}\` can only be called once`);
    return r;
  }, "onetime");
  return Ar(o, t), Pt.set(o, n), o;
}, "onetime");
eo.callCount = (t) => {
  if (!Pt.has(t))
    throw new Error(`The given function \`${t.name}\` is not wrapped by the \`onetime\` package`);
  return Pt.get(t);
};
var to = eo;

// node_modules/execa/lib/error.js
var co = T(require("node:process"), 1);

// node_modules/human-signals/build/src/main.js
var io = require("node:os");

// node_modules/human-signals/build/src/realtime.js
var ro = /* @__PURE__ */ i(() => {
  let t = Or - no + 1;
  return Array.from({ length: t }, Sd);
}, "getRealtimeSignals"), Sd = /* @__PURE__ */ i((t, e) => ({
  name: `SIGRT${e + 1}`,
  number: no + e,
  action: "terminate",
  description: "Application-specific signal (realtime)",
  standard: "posix"
}), "getRealtimeSignal"), no = 34, Or = 64;

// node_modules/human-signals/build/src/signals.js
var oo = require("node:os");

// node_modules/human-signals/build/src/core.js
var so = [
  {
    name: "SIGHUP",
    number: 1,
    action: "terminate",
    description: "Terminal closed",
    standard: "posix"
  },
  {
    name: "SIGINT",
    number: 2,
    action: "terminate",
    description: "User interruption with CTRL-C",
    standard: "ansi"
  },
  {
    name: "SIGQUIT",
    number: 3,
    action: "core",
    description: "User interruption with CTRL-\\",
    standard: "posix"
  },
  {
    name: "SIGILL",
    number: 4,
    action: "core",
    description: "Invalid machine instruction",
    standard: "ansi"
  },
  {
    name: "SIGTRAP",
    number: 5,
    action: "core",
    description: "Debugger breakpoint",
    standard: "posix"
  },
  {
    name: "SIGABRT",
    number: 6,
    action: "core",
    description: "Aborted",
    standard: "ansi"
  },
  {
    name: "SIGIOT",
    number: 6,
    action: "core",
    description: "Aborted",
    standard: "bsd"
  },
  {
    name: "SIGBUS",
    number: 7,
    action: "core",
    description: "Bus error due to misaligned, non-existing address or paging error",
    standard: "bsd"
  },
  {
    name: "SIGEMT",
    number: 7,
    action: "terminate",
    description: "Command should be emulated but is not implemented",
    standard: "other"
  },
  {
    name: "SIGFPE",
    number: 8,
    action: "core",
    description: "Floating point arithmetic error",
    standard: "ansi"
  },
  {
    name: "SIGKILL",
    number: 9,
    action: "terminate",
    description: "Forced termination",
    standard: "posix",
    forced: !0
  },
  {
    name: "SIGUSR1",
    number: 10,
    action: "terminate",
    description: "Application-specific signal",
    standard: "posix"
  },
  {
    name: "SIGSEGV",
    number: 11,
    action: "core",
    description: "Segmentation fault",
    standard: "ansi"
  },
  {
    name: "SIGUSR2",
    number: 12,
    action: "terminate",
    description: "Application-specific signal",
    standard: "posix"
  },
  {
    name: "SIGPIPE",
    number: 13,
    action: "terminate",
    description: "Broken pipe or socket",
    standard: "posix"
  },
  {
    name: "SIGALRM",
    number: 14,
    action: "terminate",
    description: "Timeout or timer",
    standard: "posix"
  },
  {
    name: "SIGTERM",
    number: 15,
    action: "terminate",
    description: "Termination",
    standard: "ansi"
  },
  {
    name: "SIGSTKFLT",
    number: 16,
    action: "terminate",
    description: "Stack is empty or overflowed",
    standard: "other"
  },
  {
    name: "SIGCHLD",
    number: 17,
    action: "ignore",
    description: "Child process terminated, paused or unpaused",
    standard: "posix"
  },
  {
    name: "SIGCLD",
    number: 17,
    action: "ignore",
    description: "Child process terminated, paused or unpaused",
    standard: "other"
  },
  {
    name: "SIGCONT",
    number: 18,
    action: "unpause",
    description: "Unpaused",
    standard: "posix",
    forced: !0
  },
  {
    name: "SIGSTOP",
    number: 19,
    action: "pause",
    description: "Paused",
    standard: "posix",
    forced: !0
  },
  {
    name: "SIGTSTP",
    number: 20,
    action: "pause",
    description: 'Paused using CTRL-Z or "suspend"',
    standard: "posix"
  },
  {
    name: "SIGTTIN",
    number: 21,
    action: "pause",
    description: "Background process cannot read terminal input",
    standard: "posix"
  },
  {
    name: "SIGBREAK",
    number: 21,
    action: "terminate",
    description: "User interruption with CTRL-BREAK",
    standard: "other"
  },
  {
    name: "SIGTTOU",
    number: 22,
    action: "pause",
    description: "Background process cannot write to terminal output",
    standard: "posix"
  },
  {
    name: "SIGURG",
    number: 23,
    action: "ignore",
    description: "Socket received out-of-band data",
    standard: "bsd"
  },
  {
    name: "SIGXCPU",
    number: 24,
    action: "core",
    description: "Process timed out",
    standard: "bsd"
  },
  {
    name: "SIGXFSZ",
    number: 25,
    action: "core",
    description: "File too big",
    standard: "bsd"
  },
  {
    name: "SIGVTALRM",
    number: 26,
    action: "terminate",
    description: "Timeout or timer",
    standard: "bsd"
  },
  {
    name: "SIGPROF",
    number: 27,
    action: "terminate",
    description: "Timeout or timer",
    standard: "bsd"
  },
  {
    name: "SIGWINCH",
    number: 28,
    action: "ignore",
    description: "Terminal window size changed",
    standard: "bsd"
  },
  {
    name: "SIGIO",
    number: 29,
    action: "terminate",
    description: "I/O is available",
    standard: "other"
  },
  {
    name: "SIGPOLL",
    number: 29,
    action: "terminate",
    description: "Watched event",
    standard: "other"
  },
  {
    name: "SIGINFO",
    number: 29,
    action: "ignore",
    description: "Request for process information",
    standard: "other"
  },
  {
    name: "SIGPWR",
    number: 30,
    action: "terminate",
    description: "Device running out of power",
    standard: "systemv"
  },
  {
    name: "SIGSYS",
    number: 31,
    action: "core",
    description: "Invalid system call",
    standard: "other"
  },
  {
    name: "SIGUNUSED",
    number: 31,
    action: "terminate",
    description: "Invalid system call",
    standard: "other"
  }
];

// node_modules/human-signals/build/src/signals.js
var jr = /* @__PURE__ */ i(() => {
  let t = ro();
  return [...so, ...t].map(Ed);
}, "getSignals"), Ed = /* @__PURE__ */ i(({
  name: t,
  number: e,
  description: r,
  action: n,
  forced: s = !1,
  standard: o
}) => {
  let {
    signals: { [t]: a }
  } = oo.constants, c = a !== void 0;
  return { name: t, number: c ? a : e, description: r, supported: c, action: n, forced: s, standard: o };
}, "normalizeSignal");

// node_modules/human-signals/build/src/main.js
var Cd = /* @__PURE__ */ i(() => {
  let t = jr();
  return Object.fromEntries(t.map(Pd));
}, "getSignalsByName"), Pd = /* @__PURE__ */ i(({
  name: t,
  number: e,
  description: r,
  supported: n,
  action: s,
  forced: o,
  standard: a
}) => [t, { name: t, number: e, description: r, supported: n, action: s, forced: o, standard: a }], "getSignalByName"), ao = Cd(), Ad = /* @__PURE__ */ i(
() => {
  let t = jr(), e = Or + 1, r = Array.from(
    { length: e },
    (n, s) => Od(s, t)
  );
  return Object.assign({}, ...r);
}, "getSignalsByNumber"), Od = /* @__PURE__ */ i((t, e) => {
  let r = jd(t, e);
  if (r === void 0)
    return {};
  let { name: n, description: s, supported: o, action: a, forced: c, standard: l } = r;
  return {
    [t]: {
      name: n,
      number: t,
      description: s,
      supported: o,
      action: a,
      forced: c,
      standard: l
    }
  };
}, "getSignalByNumber"), jd = /* @__PURE__ */ i((t, e) => {
  let r = e.find(({ name: n }) => io.constants.signals[n] === t);
  return r !== void 0 ? r : e.find((n) => n.number === t);
}, "findSignalByNumber"), Wp = Ad();

// node_modules/execa/lib/error.js
var Rd = /* @__PURE__ */ i(({ timedOut: t, timeout: e, errorCode: r, signal: n, signalDescription: s, exitCode: o, isCanceled: a }) => t ? `\
timed out after ${e} milliseconds` : a ? "was canceled" : r !== void 0 ? `failed with ${r}` : n !== void 0 ? `was killed with ${n} (${s})` :
o !== void 0 ? `failed with exit code ${o}` : "failed", "getErrorPrefix"), He = /* @__PURE__ */ i(({
  stdout: t,
  stderr: e,
  all: r,
  error: n,
  signal: s,
  exitCode: o,
  command: a,
  escapedCommand: c,
  timedOut: l,
  isCanceled: p,
  killed: f,
  parsed: { options: { timeout: b, cwd: w = co.default.cwd() } }
}) => {
  o = o === null ? void 0 : o, s = s === null ? void 0 : s;
  let _ = s === void 0 ? void 0 : ao[s].description, P = n && n.code, A = `Command ${Rd({ timedOut: l, timeout: b, errorCode: P, signal: s, signalDescription: _,
  exitCode: o, isCanceled: p })}: ${a}`, J = Object.prototype.toString.call(n) === "[object Error]", Le = J ? `${A}
${n.message}` : A, _e = [Le, e, t].filter(Boolean).join(`
`);
  return J ? (n.originalMessage = n.message, n.message = _e) : n = new Error(_e), n.shortMessage = Le, n.command = a, n.escapedCommand = c, n.
  exitCode = o, n.signal = s, n.signalDescription = _, n.stdout = t, n.stderr = e, n.cwd = w, r !== void 0 && (n.all = r), "bufferedData" in
  n && delete n.bufferedData, n.failed = !0, n.timedOut = !!l, n.isCanceled = p, n.killed = f && !l, n;
}, "makeError");

// node_modules/execa/lib/stdio.js
var At = ["stdin", "stdout", "stderr"], Nd = /* @__PURE__ */ i((t) => At.some((e) => t[e] !== void 0), "hasAlias"), uo = /* @__PURE__ */ i((t) => {
  if (!t)
    return;
  let { stdio: e } = t;
  if (e === void 0)
    return At.map((n) => t[n]);
  if (Nd(t))
    throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${At.map((n) => `\`${n}\``).join(", ")}`);
  if (typeof e == "string")
    return e;
  if (!Array.isArray(e))
    throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);
  let r = Math.max(e.length, At.length);
  return Array.from({ length: r }, (n, s) => e[s]);
}, "normalizeStdio");

// node_modules/execa/lib/kill.js
var po = T(require("node:os"), 1);

// node_modules/signal-exit/dist/mjs/signals.js
var xe = [];
xe.push("SIGHUP", "SIGINT", "SIGTERM");
process.platform !== "win32" && xe.push(
  "SIGALRM",
  "SIGABRT",
  "SIGVTALRM",
  "SIGXCPU",
  "SIGXFSZ",
  "SIGUSR2",
  "SIGTRAP",
  "SIGSYS",
  "SIGQUIT",
  "SIGIOT"
  // should detect profiler and enable/disable accordingly.
  // see #21
  // 'SIGPROF'
);
process.platform === "linux" && xe.push("SIGIO", "SIGPOLL", "SIGPWR", "SIGSTKFLT");

// node_modules/signal-exit/dist/mjs/index.js
var Ot = /* @__PURE__ */ i((t) => !!t && typeof t == "object" && typeof t.removeListener == "function" && typeof t.emit == "function" && typeof t.
reallyExit == "function" && typeof t.listeners == "function" && typeof t.kill == "function" && typeof t.pid == "number" && typeof t.on == "f\
unction", "processOk"), Rr = Symbol.for("signal-exit emitter"), Nr = globalThis, Zd = Object.defineProperty.bind(Object), Zr = class {
  static {
    i(this, "Emitter");
  }
  emitted = {
    afterExit: !1,
    exit: !1
  };
  listeners = {
    afterExit: [],
    exit: []
  };
  count = 0;
  id = Math.random();
  constructor() {
    if (Nr[Rr])
      return Nr[Rr];
    Zd(Nr, Rr, {
      value: this,
      writable: !1,
      enumerable: !1,
      configurable: !1
    });
  }
  on(e, r) {
    this.listeners[e].push(r);
  }
  removeListener(e, r) {
    let n = this.listeners[e], s = n.indexOf(r);
    s !== -1 && (s === 0 && n.length === 1 ? n.length = 0 : n.splice(s, 1));
  }
  emit(e, r, n) {
    if (this.emitted[e])
      return !1;
    this.emitted[e] = !0;
    let s = !1;
    for (let o of this.listeners[e])
      s = o(r, n) === !0 || s;
    return e === "exit" && (s = this.emit("afterExit", r, n) || s), s;
  }
}, jt = class {
  static {
    i(this, "SignalExitBase");
  }
}, Md = /* @__PURE__ */ i((t) => ({
  onExit(e, r) {
    return t.onExit(e, r);
  },
  load() {
    return t.load();
  },
  unload() {
    return t.unload();
  }
}), "signalExitWrap"), Mr = class extends jt {
  static {
    i(this, "SignalExitFallback");
  }
  onExit() {
    return () => {
    };
  }
  load() {
  }
  unload() {
  }
}, Dr = class extends jt {
  static {
    i(this, "SignalExit");
  }
  // "SIGHUP" throws an `ENOSYS` error on Windows,
  // so use a supported signal instead
  /* c8 ignore start */
  #i = Lr.platform === "win32" ? "SIGINT" : "SIGHUP";
  /* c8 ignore stop */
  #t = new Zr();
  #e;
  #s;
  #o;
  #n = {};
  #r = !1;
  constructor(e) {
    super(), this.#e = e, this.#n = {};
    for (let r of xe)
      this.#n[r] = () => {
        let n = this.#e.listeners(r), { count: s } = this.#t, o = e;
        if (typeof o.__signal_exit_emitter__ == "object" && typeof o.__signal_exit_emitter__.count == "number" && (s += o.__signal_exit_emitter__.
        count), n.length === s) {
          this.unload();
          let a = this.#t.emit("exit", null, r), c = r === "SIGHUP" ? this.#i : r;
          a || e.kill(e.pid, c);
        }
      };
    this.#o = e.reallyExit, this.#s = e.emit;
  }
  onExit(e, r) {
    if (!Ot(this.#e))
      return () => {
      };
    this.#r === !1 && this.load();
    let n = r?.alwaysLast ? "afterExit" : "exit";
    return this.#t.on(n, e), () => {
      this.#t.removeListener(n, e), this.#t.listeners.exit.length === 0 && this.#t.listeners.afterExit.length === 0 && this.unload();
    };
  }
  load() {
    if (!this.#r) {
      this.#r = !0, this.#t.count += 1;
      for (let e of xe)
        try {
          let r = this.#n[e];
          r && this.#e.on(e, r);
        } catch {
        }
      this.#e.emit = (e, ...r) => this.#c(e, ...r), this.#e.reallyExit = (e) => this.#a(e);
    }
  }
  unload() {
    this.#r && (this.#r = !1, xe.forEach((e) => {
      let r = this.#n[e];
      if (!r)
        throw new Error("Listener not defined for signal: " + e);
      try {
        this.#e.removeListener(e, r);
      } catch {
      }
    }), this.#e.emit = this.#s, this.#e.reallyExit = this.#o, this.#t.count -= 1);
  }
  #a(e) {
    return Ot(this.#e) ? (this.#e.exitCode = e || 0, this.#t.emit("exit", this.#e.exitCode, null), this.#o.call(this.#e, this.#e.exitCode)) :
    0;
  }
  #c(e, ...r) {
    let n = this.#s;
    if (e === "exit" && Ot(this.#e)) {
      typeof r[0] == "number" && (this.#e.exitCode = r[0]);
      let s = n.call(this.#e, e, ...r);
      return this.#t.emit("exit", this.#e.exitCode, null), s;
    } else
      return n.call(this.#e, e, ...r);
  }
}, Lr = globalThis.process, {
  /**
   * Called when the process is exiting, whether via signal, explicit
   * exit, or running out of stuff to do.
   *
   * If the global process object is not suitable for instrumentation,
   * then this will be a no-op.
   *
   * Returns a function that may be used to unload signal-exit.
   */
  onExit: lo,
  /**
   * Load the listeners.  Likely you never need to call this, unless
   * doing a rather deep integration with signal-exit functionality.
   * Mostly exposed for the benefit of testing.
   *
   * @internal
   */
  load: ef,
  /**
   * Unload the listeners.  Likely you never need to call this, unless
   * doing a rather deep integration with signal-exit functionality.
   * Mostly exposed for the benefit of testing.
   *
   * @internal
   */
  unload: tf
} = Md(Ot(Lr) ? new Dr(Lr) : new Mr());

// node_modules/execa/lib/kill.js
var Dd = 1e3 * 5, fo = /* @__PURE__ */ i((t, e = "SIGTERM", r = {}) => {
  let n = t(e);
  return Ld(t, e, r, n), n;
}, "spawnedKill"), Ld = /* @__PURE__ */ i((t, e, r, n) => {
  if (!Ud(e, r, n))
    return;
  let s = Vd(r), o = setTimeout(() => {
    t("SIGKILL");
  }, s);
  o.unref && o.unref();
}, "setKillTimeout"), Ud = /* @__PURE__ */ i((t, { forceKillAfterTimeout: e }, r) => $d(t) && e !== !1 && r, "shouldForceKill"), $d = /* @__PURE__ */ i(
(t) => t === po.default.constants.signals.SIGTERM || typeof t == "string" && t.toUpperCase() === "SIGTERM", "isSigterm"), Vd = /* @__PURE__ */ i(
({ forceKillAfterTimeout: t = !0 }) => {
  if (t === !0)
    return Dd;
  if (!Number.isFinite(t) || t < 0)
    throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);
  return t;
}, "getForceKillAfterTimeout"), mo = /* @__PURE__ */ i((t, e) => {
  t.kill() && (e.isCanceled = !0);
}, "spawnedCancel"), Fd = /* @__PURE__ */ i((t, e, r) => {
  t.kill(e), r(Object.assign(new Error("Timed out"), { timedOut: !0, signal: e }));
}, "timeoutKill"), ho = /* @__PURE__ */ i((t, { timeout: e, killSignal: r = "SIGTERM" }, n) => {
  if (e === 0 || e === void 0)
    return n;
  let s, o = new Promise((c, l) => {
    s = setTimeout(() => {
      Fd(t, r, l);
    }, e);
  }), a = n.finally(() => {
    clearTimeout(s);
  });
  return Promise.race([o, a]);
}, "setupTimeout"), yo = /* @__PURE__ */ i(({ timeout: t }) => {
  if (t !== void 0 && (!Number.isFinite(t) || t < 0))
    throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);
}, "validateTimeout"), go = /* @__PURE__ */ i(async (t, { cleanup: e, detached: r }, n) => {
  if (!e || r)
    return n;
  let s = lo(() => {
    t.kill();
  });
  return n.finally(() => {
    s();
  });
}, "setExitHandler");

// node_modules/execa/lib/pipe.js
var bo = require("node:fs"), xo = require("node:child_process");

// node_modules/is-stream/index.js
function Rt(t) {
  return t !== null && typeof t == "object" && typeof t.pipe == "function";
}
i(Rt, "isStream");
function Ur(t) {
  return Rt(t) && t.writable !== !1 && typeof t._write == "function" && typeof t._writableState == "object";
}
i(Ur, "isWritableStream");

// node_modules/execa/lib/pipe.js
var Bd = /* @__PURE__ */ i((t) => t instanceof xo.ChildProcess && typeof t.then == "function", "isExecaChildProcess"), $r = /* @__PURE__ */ i(
(t, e, r) => {
  if (typeof r == "string")
    return t[e].pipe((0, bo.createWriteStream)(r)), t;
  if (Ur(r))
    return t[e].pipe(r), t;
  if (!Bd(r))
    throw new TypeError("The second argument must be a string, a stream or an Execa child process.");
  if (!Ur(r.stdin))
    throw new TypeError("The target child process's stdin must be available.");
  return t[e].pipe(r.stdin), r;
}, "pipeToTarget"), vo = /* @__PURE__ */ i((t) => {
  t.stdout !== null && (t.pipeStdout = $r.bind(void 0, t, "stdout")), t.stderr !== null && (t.pipeStderr = $r.bind(void 0, t, "stderr")), t.
  all !== void 0 && (t.pipeAll = $r.bind(void 0, t, "all"));
}, "addPipeMethods");

// node_modules/execa/lib/stream.js
var Ut = require("node:fs"), jo = require("node:timers/promises");

// node_modules/get-stream/source/contents.js
var Ye = /* @__PURE__ */ i(async (t, { init: e, convertChunk: r, getSize: n, truncateChunk: s, addChunk: o, getFinalChunk: a, finalize: c }, {
maxBuffer: l = Number.POSITIVE_INFINITY } = {}) => {
  if (!qd(t))
    throw new Error("The first argument must be a Readable, a ReadableStream, or an async iterable.");
  let p = e();
  p.length = 0;
  try {
    for await (let f of t) {
      let b = Gd(f), w = r[b](f, p);
      ko({ convertedChunk: w, state: p, getSize: n, truncateChunk: s, addChunk: o, maxBuffer: l });
    }
    return Wd({ state: p, convertChunk: r, getSize: n, truncateChunk: s, addChunk: o, getFinalChunk: a, maxBuffer: l }), c(p);
  } catch (f) {
    throw f.bufferedData = c(p), f;
  }
}, "getStreamContents"), Wd = /* @__PURE__ */ i(({ state: t, getSize: e, truncateChunk: r, addChunk: n, getFinalChunk: s, maxBuffer: o }) => {
  let a = s(t);
  a !== void 0 && ko({ convertedChunk: a, state: t, getSize: e, truncateChunk: r, addChunk: n, maxBuffer: o });
}, "appendFinalChunk"), ko = /* @__PURE__ */ i(({ convertedChunk: t, state: e, getSize: r, truncateChunk: n, addChunk: s, maxBuffer: o }) => {
  let a = r(t), c = e.length + a;
  if (c <= o) {
    _o(t, e, s, c);
    return;
  }
  let l = n(t, o - e.length);
  throw l !== void 0 && _o(l, e, s, o), new Nt();
}, "appendChunk"), _o = /* @__PURE__ */ i((t, e, r, n) => {
  e.contents = r(t, e, n), e.length = n;
}, "addNewChunk"), qd = /* @__PURE__ */ i((t) => typeof t == "object" && t !== null && typeof t[Symbol.asyncIterator] == "function", "isAsyn\
cIterable"), Gd = /* @__PURE__ */ i((t) => {
  let e = typeof t;
  if (e === "string")
    return "string";
  if (e !== "object" || t === null)
    return "others";
  if (globalThis.Buffer?.isBuffer(t))
    return "buffer";
  let r = wo.call(t);
  return r === "[object ArrayBuffer]" ? "arrayBuffer" : r === "[object DataView]" ? "dataView" : Number.isInteger(t.byteLength) && Number.isInteger(
  t.byteOffset) && wo.call(t.buffer) === "[object ArrayBuffer]" ? "typedArray" : "others";
}, "getChunkType"), { toString: wo } = Object.prototype, Nt = class extends Error {
  static {
    i(this, "MaxBufferError");
  }
  name = "MaxBufferError";
  constructor() {
    super("maxBuffer exceeded");
  }
};

// node_modules/get-stream/source/utils.js
var Vr = /* @__PURE__ */ i((t) => t, "identity"), Fr = /* @__PURE__ */ i(() => {
}, "noop"), Br = /* @__PURE__ */ i(({ contents: t }) => t, "getContentsProp"), Zt = /* @__PURE__ */ i((t) => {
  throw new Error(`Streams in object mode are not supported: ${String(t)}`);
}, "throwObjectStream"), Mt = /* @__PURE__ */ i((t) => t.length, "getLengthProp");

// node_modules/get-stream/source/array-buffer.js
async function Wr(t, e) {
  return Ye(t, tu, e);
}
i(Wr, "getStreamAsArrayBuffer");
var zd = /* @__PURE__ */ i(() => ({ contents: new ArrayBuffer(0) }), "initArrayBuffer"), Kd = /* @__PURE__ */ i((t) => Jd.encode(t), "useTex\
tEncoder"), Jd = new TextEncoder(), To = /* @__PURE__ */ i((t) => new Uint8Array(t), "useUint8Array"), Io = /* @__PURE__ */ i((t) => new Uint8Array(
t.buffer, t.byteOffset, t.byteLength), "useUint8ArrayWithOffset"), Hd = /* @__PURE__ */ i((t, e) => t.slice(0, e), "truncateArrayBufferChunk"),
Yd = /* @__PURE__ */ i((t, { contents: e, length: r }, n) => {
  let s = Co() ? Qd(e, n) : Xd(e, n);
  return new Uint8Array(s).set(t, r), s;
}, "addArrayBufferChunk"), Xd = /* @__PURE__ */ i((t, e) => {
  if (e <= t.byteLength)
    return t;
  let r = new ArrayBuffer(Eo(e));
  return new Uint8Array(r).set(new Uint8Array(t), 0), r;
}, "resizeArrayBufferSlow"), Qd = /* @__PURE__ */ i((t, e) => {
  if (e <= t.maxByteLength)
    return t.resize(e), t;
  let r = new ArrayBuffer(e, { maxByteLength: Eo(e) });
  return new Uint8Array(r).set(new Uint8Array(t), 0), r;
}, "resizeArrayBuffer"), Eo = /* @__PURE__ */ i((t) => So ** Math.ceil(Math.log(t) / Math.log(So)), "getNewContentsLength"), So = 2, eu = /* @__PURE__ */ i(
({ contents: t, length: e }) => Co() ? t : t.slice(0, e), "finalizeArrayBuffer"), Co = /* @__PURE__ */ i(() => "resize" in ArrayBuffer.prototype,
"hasArrayBufferResize"), tu = {
  init: zd,
  convertChunk: {
    string: Kd,
    buffer: To,
    arrayBuffer: To,
    dataView: Io,
    typedArray: Io,
    others: Zt
  },
  getSize: Mt,
  truncateChunk: Hd,
  addChunk: Yd,
  getFinalChunk: Fr,
  finalize: eu
};

// node_modules/get-stream/source/buffer.js
async function Dt(t, e) {
  if (!("Buffer" in globalThis))
    throw new Error("getStreamAsBuffer() is only supported in Node.js");
  try {
    return Po(await Wr(t, e));
  } catch (r) {
    throw r.bufferedData !== void 0 && (r.bufferedData = Po(r.bufferedData)), r;
  }
}
i(Dt, "getStreamAsBuffer");
var Po = /* @__PURE__ */ i((t) => globalThis.Buffer.from(t), "arrayBufferToNodeBuffer");

// node_modules/get-stream/source/string.js
async function qr(t, e) {
  return Ye(t, iu, e);
}
i(qr, "getStreamAsString");
var ru = /* @__PURE__ */ i(() => ({ contents: "", textDecoder: new TextDecoder() }), "initString"), Lt = /* @__PURE__ */ i((t, { textDecoder: e }) => e.
decode(t, { stream: !0 }), "useTextDecoder"), nu = /* @__PURE__ */ i((t, { contents: e }) => e + t, "addStringChunk"), su = /* @__PURE__ */ i(
(t, e) => t.slice(0, e), "truncateStringChunk"), ou = /* @__PURE__ */ i(({ textDecoder: t }) => {
  let e = t.decode();
  return e === "" ? void 0 : e;
}, "getFinalStringChunk"), iu = {
  init: ru,
  convertChunk: {
    string: Vr,
    buffer: Lt,
    arrayBuffer: Lt,
    dataView: Lt,
    typedArray: Lt,
    others: Zt
  },
  getSize: Mt,
  truncateChunk: su,
  addChunk: nu,
  getFinalChunk: ou,
  finalize: Br
};

// node_modules/execa/lib/stream.js
var Ro = T(Oo(), 1);
var No = /* @__PURE__ */ i((t) => {
  if (t !== void 0)
    throw new TypeError("The `input` and `inputFile` options cannot be both set.");
}, "validateInputOptions"), cu = /* @__PURE__ */ i(({ input: t, inputFile: e }) => typeof e != "string" ? t : (No(t), (0, Ut.readFileSync)(e)),
"getInputSync"), Zo = /* @__PURE__ */ i((t) => {
  let e = cu(t);
  if (Rt(e))
    throw new TypeError("The `input` option cannot be a stream in sync mode");
  return e;
}, "handleInputSync"), du = /* @__PURE__ */ i(({ input: t, inputFile: e }) => typeof e != "string" ? t : (No(t), (0, Ut.createReadStream)(e)),
"getInput"), Mo = /* @__PURE__ */ i((t, e) => {
  let r = du(e);
  r !== void 0 && (Rt(r) ? r.pipe(t.stdin) : t.stdin.end(r));
}, "handleInput"), Do = /* @__PURE__ */ i((t, { all: e }) => {
  if (!e || !t.stdout && !t.stderr)
    return;
  let r = (0, Ro.default)();
  return t.stdout && r.add(t.stdout), t.stderr && r.add(t.stderr), r;
}, "makeAllStream"), Gr = /* @__PURE__ */ i(async (t, e) => {
  if (!(!t || e === void 0)) {
    await (0, jo.setTimeout)(0), t.destroy();
    try {
      return await e;
    } catch (r) {
      return r.bufferedData;
    }
  }
}, "getBufferedData"), zr = /* @__PURE__ */ i((t, { encoding: e, buffer: r, maxBuffer: n }) => {
  if (!(!t || !r))
    return e === "utf8" || e === "utf-8" ? qr(t, { maxBuffer: n }) : e === null || e === "buffer" ? Dt(t, { maxBuffer: n }) : uu(t, n, e);
}, "getStreamPromise"), uu = /* @__PURE__ */ i(async (t, e, r) => (await Dt(t, { maxBuffer: e })).toString(r), "applyEncoding"), Lo = /* @__PURE__ */ i(
async ({ stdout: t, stderr: e, all: r }, { encoding: n, buffer: s, maxBuffer: o }, a) => {
  let c = zr(t, { encoding: n, buffer: s, maxBuffer: o }), l = zr(e, { encoding: n, buffer: s, maxBuffer: o }), p = zr(r, { encoding: n, buffer: s,
  maxBuffer: o * 2 });
  try {
    return await Promise.all([a, c, l, p]);
  } catch (f) {
    return Promise.all([
      { error: f, signal: f.signal, timedOut: f.timedOut },
      Gr(t, c),
      Gr(e, l),
      Gr(r, p)
    ]);
  }
}, "getSpawnedResult");

// node_modules/execa/lib/promise.js
var lu = (async () => {
})().constructor.prototype, pu = ["then", "catch", "finally"].map((t) => [
  t,
  Reflect.getOwnPropertyDescriptor(lu, t)
]), Kr = /* @__PURE__ */ i((t, e) => {
  for (let [r, n] of pu) {
    let s = typeof e == "function" ? (...o) => Reflect.apply(n.value, e(), o) : n.value.bind(e);
    Reflect.defineProperty(t, r, { ...n, value: s });
  }
}, "mergePromise"), Uo = /* @__PURE__ */ i((t) => new Promise((e, r) => {
  t.on("exit", (n, s) => {
    e({ exitCode: n, signal: s });
  }), t.on("error", (n) => {
    r(n);
  }), t.stdin && t.stdin.on("error", (n) => {
    r(n);
  });
}), "getSpawnedPromise");

// node_modules/execa/lib/command.js
var Fo = require("node:buffer"), Bo = require("node:child_process");
var Wo = /* @__PURE__ */ i((t, e = []) => Array.isArray(e) ? [t, ...e] : [t], "normalizeArgs"), fu = /^[\w.-]+$/, mu = /* @__PURE__ */ i((t) => typeof t !=
"string" || fu.test(t) ? t : `"${t.replaceAll('"', '\\"')}"`, "escapeArg"), Jr = /* @__PURE__ */ i((t, e) => Wo(t, e).join(" "), "joinComman\
d"), Hr = /* @__PURE__ */ i((t, e) => Wo(t, e).map((r) => mu(r)).join(" "), "getEscapedCommand"), qo = / +/g, Go = /* @__PURE__ */ i((t) => {
  let e = [];
  for (let r of t.trim().split(qo)) {
    let n = e.at(-1);
    n && n.endsWith("\\") ? e[e.length - 1] = `${n.slice(0, -1)} ${r}` : e.push(r);
  }
  return e;
}, "parseCommand"), $o = /* @__PURE__ */ i((t) => {
  let e = typeof t;
  if (e === "string")
    return t;
  if (e === "number")
    return String(t);
  if (e === "object" && t !== null && !(t instanceof Bo.ChildProcess) && "stdout" in t) {
    let r = typeof t.stdout;
    if (r === "string")
      return t.stdout;
    if (Fo.Buffer.isBuffer(t.stdout))
      return t.stdout.toString();
    throw new TypeError(`Unexpected "${r}" stdout in template expression`);
  }
  throw new TypeError(`Unexpected "${e}" in template expression`);
}, "parseExpression"), Vo = /* @__PURE__ */ i((t, e, r) => r || t.length === 0 || e.length === 0 ? [...t, ...e] : [
  ...t.slice(0, -1),
  `${t.at(-1)}${e[0]}`,
  ...e.slice(1)
], "concatTokens"), hu = /* @__PURE__ */ i(({ templates: t, expressions: e, tokens: r, index: n, template: s }) => {
  let o = s ?? t.raw[n], a = o.split(qo).filter(Boolean), c = Vo(
    r,
    a,
    o.startsWith(" ")
  );
  if (n === e.length)
    return c;
  let l = e[n], p = Array.isArray(l) ? l.map((f) => $o(f)) : [$o(l)];
  return Vo(
    c,
    p,
    o.endsWith(" ")
  );
}, "parseTemplate"), Yr = /* @__PURE__ */ i((t, e) => {
  let r = [];
  for (let [n, s] of t.entries())
    r = hu({ templates: t, expressions: e, tokens: r, index: n, template: s });
  return r;
}, "parseTemplates");

// node_modules/execa/lib/verbose.js
var zo = require("node:util"), Ko = T(require("node:process"), 1);
var Jo = (0, zo.debuglog)("execa").enabled, $t = /* @__PURE__ */ i((t, e) => String(t).padStart(e, "0"), "padField"), yu = /* @__PURE__ */ i(
() => {
  let t = /* @__PURE__ */ new Date();
  return `${$t(t.getHours(), 2)}:${$t(t.getMinutes(), 2)}:${$t(t.getSeconds(), 2)}.${$t(t.getMilliseconds(), 3)}`;
}, "getTimestamp"), Xr = /* @__PURE__ */ i((t, { verbose: e }) => {
  e && Ko.default.stderr.write(`[${yu()}] ${t}
`);
}, "logCommand");

// node_modules/execa/index.js
var gu = 1e3 * 1e3 * 100, bu = /* @__PURE__ */ i(({ env: t, extendEnv: e, preferLocal: r, localDir: n, execPath: s }) => {
  let o = e ? { ...Xe.default.env, ...t } : t;
  return r ? Qs({ env: o, cwd: n, execPath: s }) : o;
}, "getEnv"), ei = /* @__PURE__ */ i((t, e, r = {}) => {
  let n = Qo.default._parse(t, e, r);
  return t = n.command, e = n.args, r = n.options, r = {
    maxBuffer: gu,
    buffer: !0,
    stripFinalNewline: !0,
    extendEnv: !0,
    preferLocal: !1,
    localDir: r.cwd || Xe.default.cwd(),
    execPath: Xe.default.execPath,
    encoding: "utf8",
    reject: !0,
    cleanup: !0,
    all: !1,
    windowsHide: !0,
    verbose: Jo,
    ...r
  }, r.env = bu(r), r.stdio = uo(r), Xe.default.platform === "win32" && Xo.default.basename(t, ".exe") === "cmd" && e.unshift("/q"), { file: t,
  args: e, options: r, parsed: n };
}, "handleArguments"), Qe = /* @__PURE__ */ i((t, e, r) => typeof e != "string" && !Yo.Buffer.isBuffer(e) ? r === void 0 ? void 0 : "" : t.stripFinalNewline ?
Cr(e) : e, "handleOutput");
function ti(t, e, r) {
  let n = ei(t, e, r), s = Jr(t, e), o = Hr(t, e);
  Xr(o, n.options), yo(n.options);
  let a;
  try {
    a = Vt.default.spawn(n.file, n.args, n.options);
  } catch (_) {
    let P = new Vt.default.ChildProcess(), E = Promise.reject(He({
      error: _,
      stdout: "",
      stderr: "",
      all: "",
      command: s,
      escapedCommand: o,
      parsed: n,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    }));
    return Kr(P, E), P;
  }
  let c = Uo(a), l = ho(a, n.options, c), p = go(a, n.options, l), f = { isCanceled: !1 };
  a.kill = fo.bind(null, a.kill.bind(a)), a.cancel = mo.bind(null, a, f);
  let w = to(/* @__PURE__ */ i(async () => {
    let [{ error: _, exitCode: P, signal: E, timedOut: A }, J, Le, _e] = await Lo(a, n.options, p), Ue = Qe(n.options, J), it = Qe(n.options,
    Le), at = Qe(n.options, _e);
    if (_ || P !== 0 || E !== null) {
      let k = He({
        error: _,
        exitCode: P,
        signal: E,
        stdout: Ue,
        stderr: it,
        all: at,
        command: s,
        escapedCommand: o,
        parsed: n,
        timedOut: A,
        isCanceled: f.isCanceled || (n.options.signal ? n.options.signal.aborted : !1),
        killed: a.killed
      });
      if (!n.options.reject)
        return k;
      throw k;
    }
    return {
      command: s,
      escapedCommand: o,
      exitCode: 0,
      stdout: Ue,
      stderr: it,
      all: at,
      failed: !1,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    };
  }, "handlePromise"));
  return Mo(a, n.options), a.all = Do(a, n.options), vo(a), Kr(a, w), a;
}
i(ti, "execa");
function xu(t, e, r) {
  let n = ei(t, e, r), s = Jr(t, e), o = Hr(t, e);
  Xr(o, n.options);
  let a = Zo(n.options), c;
  try {
    c = Vt.default.spawnSync(n.file, n.args, { ...n.options, input: a });
  } catch (f) {
    throw He({
      error: f,
      stdout: "",
      stderr: "",
      all: "",
      command: s,
      escapedCommand: o,
      parsed: n,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    });
  }
  let l = Qe(n.options, c.stdout, c.error), p = Qe(n.options, c.stderr, c.error);
  if (c.error || c.status !== 0 || c.signal !== null) {
    let f = He({
      stdout: l,
      stderr: p,
      error: c.error,
      signal: c.signal,
      exitCode: c.status,
      command: s,
      escapedCommand: o,
      parsed: n,
      timedOut: c.error && c.error.code === "ETIMEDOUT",
      isCanceled: !1,
      killed: c.signal !== null
    });
    if (!n.options.reject)
      return f;
    throw f;
  }
  return {
    command: s,
    escapedCommand: o,
    exitCode: 0,
    stdout: l,
    stderr: p,
    failed: !1,
    timedOut: !1,
    isCanceled: !1,
    killed: !1
  };
}
i(xu, "execaSync");
var vu = /* @__PURE__ */ i(({ input: t, inputFile: e, stdio: r }) => t === void 0 && e === void 0 && r === void 0 ? { stdin: "inherit" } : {},
"normalizeScriptStdin"), Ho = /* @__PURE__ */ i((t = {}) => ({
  preferLocal: !0,
  ...vu(t),
  ...t
}), "normalizeScriptOptions");
function ri(t) {
  function e(r, ...n) {
    if (!Array.isArray(r))
      return ri({ ...t, ...r });
    let [s, ...o] = Yr(r, n);
    return ti(s, o, Ho(t));
  }
  return i(e, "$"), e.sync = (r, ...n) => {
    if (!Array.isArray(r))
      throw new TypeError("Please use $(options).sync`command` instead of $.sync(options)`command`.");
    let [s, ...o] = Yr(r, n);
    return xu(s, o, Ho(t));
  }, e;
}
i(ri, "create$");
var dm = ri();
function ni(t, e) {
  let [r, ...n] = Go(t);
  return ti(r, n, e);
}
i(ni, "execaCommand");

// src/telemetry/exec-command-count-lines.ts
async function Ft(t, e) {
  let r = ni(t, { shell: !0, buffer: !1, ...e });
  if (!r.stdout)
    throw new Error("Unexpected missing stdout");
  let n = 0, s = (0, si.createInterface)(r.stdout);
  return s.on("line", () => {
    n += 1;
  }), await r, s.close(), n;
}
i(Ft, "execCommandCountLines");

// src/common/utils/file-cache.ts
var et = require("node:crypto"), M = require("node:fs"), V = require("node:fs/promises"), ii = require("node:os"), Ze = require("node:path");
var Bt = class {
  static {
    i(this, "FileSystemCache");
  }
  constructor(e = {}) {
    this.prefix = (e.ns || e.prefix || "") + "-", this.hash_alg = e.hash_alg || "md5", this.cache_dir = e.basePath || (0, Ze.join)((0, ii.tmpdir)(),
    (0, et.randomBytes)(15).toString("base64").replace(/\//g, "-")), this.ttl = e.ttl || 0, (0, et.createHash)(this.hash_alg), (0, M.mkdirSync)(
    this.cache_dir, { recursive: !0 });
  }
  generateHash(e) {
    return (0, Ze.join)(this.cache_dir, this.prefix + (0, et.createHash)(this.hash_alg).update(e).digest("hex"));
  }
  isExpired(e, r) {
    return e.ttl != null && r > e.ttl;
  }
  parseCacheData(e, r) {
    let n = JSON.parse(e);
    return this.isExpired(n, Date.now()) ? r : n.content;
  }
  parseSetData(e, r, n = {}) {
    let s = n.ttl ?? this.ttl;
    return JSON.stringify({ key: e, content: r, ...s && { ttl: Date.now() + s * 1e3 } });
  }
  async get(e, r) {
    try {
      let n = await (0, V.readFile)(this.generateHash(e), "utf8");
      return this.parseCacheData(n, r);
    } catch {
      return r;
    }
  }
  getSync(e, r) {
    try {
      let n = (0, M.readFileSync)(this.generateHash(e), "utf8");
      return this.parseCacheData(n, r);
    } catch {
      return r;
    }
  }
  async set(e, r, n = {}) {
    let s = typeof n == "number" ? { ttl: n } : n;
    (0, M.mkdirSync)(this.cache_dir, { recursive: !0 }), await (0, V.writeFile)(this.generateHash(e), this.parseSetData(e, r, s), {
      encoding: s.encoding || "utf8"
    });
  }
  setSync(e, r, n = {}) {
    let s = typeof n == "number" ? { ttl: n } : n;
    (0, M.mkdirSync)(this.cache_dir, { recursive: !0 }), (0, M.writeFileSync)(this.generateHash(e), this.parseSetData(e, r, s), {
      encoding: s.encoding || "utf8"
    });
  }
  async setMany(e, r) {
    await Promise.all(e.map((n) => this.set(n.key, n.content ?? n.value, r)));
  }
  setManySync(e, r) {
    e.forEach((n) => this.setSync(n.key, n.content ?? n.value, r));
  }
  async remove(e) {
    await (0, V.rm)(this.generateHash(e), { force: !0 });
  }
  removeSync(e) {
    (0, M.rmSync)(this.generateHash(e), { force: !0 });
  }
  async clear() {
    let e = await (0, V.readdir)(this.cache_dir);
    await Promise.all(
      e.filter((r) => r.startsWith(this.prefix)).map((r) => (0, V.rm)((0, Ze.join)(this.cache_dir, r), { force: !0 }))
    );
  }
  clearSync() {
    (0, M.readdirSync)(this.cache_dir).filter((e) => e.startsWith(this.prefix)).forEach((e) => (0, M.rmSync)((0, Ze.join)(this.cache_dir, e),
    { force: !0 }));
  }
  async getAll() {
    let e = Date.now(), r = await (0, V.readdir)(this.cache_dir);
    return (await Promise.all(
      r.filter((s) => s.startsWith(this.prefix)).map((s) => (0, V.readFile)((0, Ze.join)(this.cache_dir, s), "utf8"))
    )).map((s) => JSON.parse(s)).filter((s) => s.content && !this.isExpired(s, e));
  }
  async load() {
    return {
      files: (await this.getAll()).map((r) => ({
        path: this.generateHash(r.key),
        value: r.content,
        key: r.key
      }))
    };
  }
};
function en(t) {
  return new Bt(t);
}
i(en, "createFileSystemCache");

// src/common/utils/resolve-path-in-sb-cache.ts
var sn = require("node:path");

// node_modules/find-cache-dir/index.js
var vi = T(require("node:process"), 1), Me = T(require("node:path"), 1), rt = T(require("node:fs"), 1), _i = T(ci(), 1);

// ../node_modules/pkg-dir/index.js
var yi = T(require("node:path"), 1);

// ../node_modules/pkg-dir/node_modules/find-up/index.js
var tt = T(require("node:path"), 1), mi = require("node:url");

// ../node_modules/locate-path/index.js
var di = T(require("node:process"), 1), ui = T(require("node:path"), 1), Wt = T(require("node:fs"), 1), li = require("node:url");
var pi = {
  directory: "isDirectory",
  file: "isFile"
};
function ku(t) {
  if (!Object.hasOwnProperty.call(pi, t))
    throw new Error(`Invalid type specified: ${t}`);
}
i(ku, "checkType");
var Tu = /* @__PURE__ */ i((t, e) => e[pi[t]](), "matchType"), Iu = /* @__PURE__ */ i((t) => t instanceof URL ? (0, li.fileURLToPath)(t) : t,
"toPath");
function tn(t, {
  cwd: e = di.default.cwd(),
  type: r = "file",
  allowSymlinks: n = !0
} = {}) {
  ku(r), e = Iu(e);
  let s = n ? Wt.default.statSync : Wt.default.lstatSync;
  for (let o of t)
    try {
      let a = s(ui.default.resolve(e, o), {
        throwIfNoEntry: !1
      });
      if (!a)
        continue;
      if (Tu(r, a))
        return o;
    } catch {
    }
}
i(tn, "locatePathSync");

// ../node_modules/pkg-dir/node_modules/path-exists/index.js
var fi = T(require("node:fs"), 1);

// ../node_modules/pkg-dir/node_modules/find-up/index.js
var Su = /* @__PURE__ */ i((t) => t instanceof URL ? (0, mi.fileURLToPath)(t) : t, "toPath"), Eu = Symbol("findUpStop");
function Cu(t, e = {}) {
  let r = tt.default.resolve(Su(e.cwd) || ""), { root: n } = tt.default.parse(r), s = e.stopAt || n, o = e.limit || Number.POSITIVE_INFINITY,
  a = [t].flat(), c = /* @__PURE__ */ i((p) => {
    if (typeof t != "function")
      return tn(a, p);
    let f = t(p.cwd);
    return typeof f == "string" ? tn([f], p) : f;
  }, "runMatcher"), l = [];
  for (; ; ) {
    let p = c({ ...e, cwd: r });
    if (p === Eu || (p && l.push(tt.default.resolve(r, p)), r === s || l.length >= o))
      break;
    r = tt.default.dirname(r);
  }
  return l;
}
i(Cu, "findUpMultipleSync");
function hi(t, e = {}) {
  return Cu(t, { ...e, limit: 1 })[0];
}
i(hi, "findUpSync");

// ../node_modules/pkg-dir/index.js
function gi({ cwd: t } = {}) {
  let e = hi("package.json", { cwd: t });
  return e && yi.default.dirname(e);
}
i(gi, "packageDirectorySync");

// node_modules/find-cache-dir/index.js
var { env: rn, cwd: Pu } = vi.default, bi = /* @__PURE__ */ i((t) => {
  try {
    return rt.default.accessSync(t, rt.default.constants.W_OK), !0;
  } catch {
    return !1;
  }
}, "isWritable");
function xi(t, e) {
  return e.create && rt.default.mkdirSync(t, { recursive: !0 }), t;
}
i(xi, "useDirectory");
function Au(t) {
  let e = Me.default.join(t, "node_modules");
  if (!(!bi(e) && (rt.default.existsSync(e) || !bi(Me.default.join(t)))))
    return e;
}
i(Au, "getNodeModuleDirectory");
function nn(t = {}) {
  if (rn.CACHE_DIR && !["true", "false", "1", "0"].includes(rn.CACHE_DIR))
    return xi(Me.default.join(rn.CACHE_DIR, t.name), t);
  let { cwd: e = Pu(), files: r } = t;
  if (r) {
    if (!Array.isArray(r))
      throw new TypeError(`Expected \`files\` option to be an array, got \`${typeof r}\`.`);
    e = (0, _i.default)(r.map((s) => Me.default.resolve(e, s)));
  }
  if (e = gi({ cwd: e }), !(!e || !Au(e)))
    return xi(Me.default.join(e, "node_modules", ".cache", t.name), t);
}
i(nn, "findCacheDirectory");

// src/common/utils/resolve-path-in-sb-cache.ts
function wi(t, e = "default") {
  let r = nn({ name: "storybook" });
  return r ||= (0, sn.join)(process.cwd(), "node_modules", ".cache", "storybook"), (0, sn.join)(r, e, t);
}
i(wi, "resolvePathInStorybookCache");

// src/telemetry/run-telemetry-operation.ts
var ki = en({
  basePath: wi("telemetry"),
  ns: "storybook",
  ttl: 24 * 60 * 60 * 1e3
  // 24h
}), qt = /* @__PURE__ */ i(async (t, e) => {
  let r = await ki.get(t);
  return r === void 0 && (r = await e(), r !== void 0 && await ki.set(t, r)), r;
}, "runTelemetryOperation");

// src/telemetry/get-application-file-count.ts
var Ou = ["page", "screen"], ju = ["js", "jsx", "ts", "tsx"], Ru = /* @__PURE__ */ i(async (t) => {
  let r = Ou.flatMap((n) => [
    n,
    [n[0].toUpperCase(), ...n.slice(1)].join("")
  ]).flatMap(
    (n) => ju.map((s) => `"${t}${Ti.sep}*${n}*.${s}"`)
  );
  try {
    let n = `git ls-files -- ${r.join(" ")}`;
    return await Ft(n);
  } catch {
    return;
  }
}, "getApplicationFilesCountUncached"), Ii = /* @__PURE__ */ i(async (t) => qt(
  "applicationFiles",
  async () => Ru(t)
), "getApplicationFileCount");

// src/telemetry/get-chromatic-version.ts
function Si(t) {
  let e = t.dependencies?.chromatic || t.devDependencies?.chromatic || t.peerDependencies?.chromatic;
  return e || (t.scripts && Object.values(t.scripts).find((r) => r?.match(/chromatic/)) ? "latest" : void 0);
}
i(Si, "getChromaticVersionSpecifier");

// src/telemetry/get-framework-info.ts
var Ai = require("node:path"), Oi = require("storybook/internal/common");

// src/telemetry/package-json.ts
var Ei = require("node:fs/promises"), Ci = require("node:path");
var on = /* @__PURE__ */ i(async (t) => {
  let e = Object.keys(t);
  return Promise.all(e.map(Gt));
}, "getActualPackageVersions"), Gt = /* @__PURE__ */ i(async (t) => {
  try {
    let e = await an(t);
    return {
      name: t,
      version: e.version
    };
  } catch {
    return { name: t, version: null };
  }
}, "getActualPackageVersion"), an = /* @__PURE__ */ i(async (t) => {
  try {
    let e = require.resolve((0, Ci.join)(t, "package.json"), {
      paths: [process.cwd()]
    });
    return JSON.parse(await (0, Ei.readFile)(e, { encoding: "utf8" }));
  } catch {
    return null;
  }
}, "getActualPackageJson");

// src/telemetry/get-framework-info.ts
var Nu = [
  "html",
  "react",
  "svelte",
  "vue3",
  "preact",
  "server",
  "vue",
  "web-components",
  "angular",
  "ember"
], Zu = ["builder-webpack5", "builder-vite"];
function Pi(t, e) {
  let { name: r = "", version: n, dependencies: s, devDependencies: o, peerDependencies: a } = t, c = {
    // We include the framework itself because it may be a renderer too (e.g. angular)
    [r]: n,
    ...s,
    ...o,
    ...a
  };
  return e.map((l) => `@storybook/${l}`).find((l) => c[l]);
}
i(Pi, "findMatchingPackage");
var Mu = /* @__PURE__ */ i((t) => {
  let e = (0, Ai.normalize)(t).replace(new RegExp(/\\/, "g"), "/");
  return Object.keys(Oi.frameworkPackages).find((n) => e.endsWith(n)) || we(t).replace(/.*node_modules[\\/]/, "");
}, "getFrameworkPackageName");
async function ji(t) {
  if (!t?.framework)
    return {};
  let e = typeof t.framework == "string" ? t.framework : t.framework?.name;
  if (!e)
    return {};
  let r = await an(e);
  if (!r)
    return {};
  let n = Pi(r, Zu), s = Pi(r, Nu), o = Mu(e), a = typeof t.framework == "object" ? t.framework.options : {};
  return {
    framework: {
      name: o,
      options: a
    },
    builder: n,
    renderer: s
  };
}
i(ji, "getFrameworkInfo");

// src/telemetry/get-has-router-package.ts
var Du = /* @__PURE__ */ new Set([
  "react-router",
  "react-router-dom",
  "remix",
  "@tanstack/react-router",
  "expo-router",
  "@reach/router",
  "react-easy-router",
  "@remix-run/router",
  "wouter",
  "wouter-preact",
  "preact-router",
  "vue-router",
  "unplugin-vue-router",
  "@angular/router",
  "@solidjs/router",
  // metaframeworks that imply routing
  "next",
  "react-scripts",
  "gatsby",
  "nuxt",
  "@sveltejs/kit"
]);
function Ri(t) {
  return Object.keys(t?.dependencies ?? {}).some(
    (e) => Du.has(e)
  );
}
i(Ri, "getHasRouterPackage");

// src/telemetry/get-monorepo-type.ts
var nt = require("node:fs"), zt = require("node:path"), Kt = require("storybook/internal/common");
var Ni = {
  Nx: "nx.json",
  Turborepo: "turbo.json",
  Lerna: "lerna.json",
  Rush: "rush.json",
  Lage: "lage.config.json"
}, Zi = /* @__PURE__ */ i(() => {
  let e = Object.keys(Ni).find((n) => {
    let s = (0, zt.join)((0, Kt.getProjectRoot)(), Ni[n]);
    return (0, nt.existsSync)(s);
  });
  if (e)
    return e;
  if (!(0, nt.existsSync)((0, zt.join)((0, Kt.getProjectRoot)(), "package.json")))
    return;
  if (JSON.parse(
    (0, nt.readFileSync)((0, zt.join)((0, Kt.getProjectRoot)(), "package.json"), { encoding: "utf8" })
  )?.workspaces)
    return "Workspaces";
}, "getMonorepoType");

// src/telemetry/get-portable-stories-usage.ts
var Lu = /* @__PURE__ */ i(async (t) => {
  try {
    let e = "git grep -l composeStor" + (t ? ` -- ${t}` : "");
    return await Ft(e);
  } catch (e) {
    return e.exitCode === 1 ? 0 : void 0;
  }
}, "getPortableStoriesFileCountUncached"), Mi = /* @__PURE__ */ i(async (t) => qt(
  "portableStories",
  async () => Lu(t)
), "getPortableStoriesFileCount");

// src/telemetry/storybook-metadata.ts
var cn = {
  next: "Next",
  "react-scripts": "CRA",
  gatsby: "Gatsby",
  "@nuxtjs/storybook": "nuxt",
  "@nrwl/storybook": "nx",
  "@vue/cli-service": "vue-cli",
  "@sveltejs/kit": "sveltekit"
}, dn = /* @__PURE__ */ i((t) => we(t).replace(/\/dist\/.*/, "").replace(/\.[mc]?[tj]?s[x]?$/, "").replace(/\/register$/, "").replace(/\/manager$/,
"").replace(/\/preset$/, ""), "sanitizeAddonName"), Ui = /* @__PURE__ */ i(async ({
  packageJsonPath: t,
  packageJson: e,
  mainConfig: r,
  configDir: n
}) => {
  let s = await us(), o = {
    generatedAt: (/* @__PURE__ */ new Date()).getTime(),
    userSince: s.value.userSince,
    hasCustomBabel: !1,
    hasCustomWebpack: !1,
    hasStaticDirs: !1,
    hasStorybookEslint: !1,
    refCount: 0
  }, a = {
    ...e?.dependencies,
    ...e?.devDependencies,
    ...e?.peerDependencies
  }, c = Object.keys(a).find((k) => !!cn[k]);
  if (c) {
    let { version: k } = await Gt(c);
    o.metaFramework = {
      name: cn[c],
      packageName: c,
      version: k
    };
  }
  let l = [
    "playwright",
    "vitest",
    "jest",
    "cypress",
    "nightwatch",
    "webdriver",
    "@web/test-runner",
    "puppeteer",
    "karma",
    "jasmine",
    "chai",
    "testing-library",
    "@ngneat/spectator",
    "wdio",
    "msw",
    "miragejs",
    "sinon",
    "chromatic"
  ], p = Object.keys(a).filter(
    (k) => l.find((j) => k.includes(j))
  );
  o.testPackages = Object.fromEntries(
    await Promise.all(
      p.map(async (k) => [k, (await Gt(k))?.version])
    )
  ), o.hasRouterPackage = Ri(e);
  let f = Zi();
  f && (o.monorepo = f);
  try {
    let k = await ur({ cwd: (0, q.getProjectRoot)() });
    k && (o.packageManager = {
      type: k.name,
      version: k.version,
      agent: k.agent
    });
  } catch {
  }
  let b = a.typescript ? "typescript" : "javascript";
  if (!r)
    return {
      ...o,
      storybookVersionSpecifier: q.versions.storybook,
      language: b
    };
  o.hasCustomBabel = !!r.babel, o.hasCustomWebpack = !!r.webpackFinal, o.hasStaticDirs = !!r.staticDirs, typeof r.typescript == "object" && (o.
  typescriptOptions = r.typescript);
  let w = await ji(r);
  typeof r.refs == "object" && (o.refCount = Object.keys(r.refs).length), typeof r.features == "object" && (o.features = r.features);
  let _ = {};
  r.addons && r.addons.forEach((k) => {
    let j, ct;
    typeof k == "string" ? j = dn(k) : (k.name.includes("addon-essentials") && (ct = k.options), j = dn(k.name)), _[j] = {
      options: ct,
      version: void 0
    };
  });
  let P = Si(e);
  P && (_.chromatic = {
    version: void 0,
    versionSpecifier: P,
    options: void 0
  }), (await on(_)).forEach(({ name: k, version: j }) => {
    _[k].version = j;
  });
  let A = Object.keys(_), J = Object.keys(a).filter((k) => k.includes("storybook") && !A.includes(k)).reduce((k, j) => ({
    ...k,
    [j]: { version: void 0 }
  }), {});
  (await on(J)).forEach(({ name: k, version: j }) => {
    J[k].version = j;
  });
  let _e = !!a["eslint-plugin-storybook"], Ue = (0, q.getStorybookInfo)(n);
  try {
    let { previewConfigPath: k } = Ue;
    if (k) {
      let j = await (0, Li.readConfig)(k), ct = !!(j.getFieldNode(["globals"]) || j.getFieldNode(["globalTypes"]));
      o.preview = { ...o.preview, usesGlobals: ct };
    }
  } catch {
  }
  let it = await Mi(), at = await Ii((0, Di.dirname)(t));
  return {
    ...o,
    ...w,
    portableStoriesFileCount: it,
    applicationFileCount: at,
    storybookVersion: ft,
    storybookVersionSpecifier: Ue.version,
    language: b,
    storybookPackages: J,
    addons: _,
    hasStorybookEslint: _e
  };
}, "computeStorybookMetadata");
async function Uu() {
  let t = await or(process.cwd());
  return t ? {
    packageJsonPath: t,
    packageJson: await An(t) || {}
  } : {
    packageJsonPath: process.cwd(),
    packageJson: {}
  };
}
i(Uu, "getPackageJsonDetails");
var Jt, un = /* @__PURE__ */ i(async (t) => {
  if (Jt)
    return Jt;
  let { packageJson: e, packageJsonPath: r } = await Uu(), n = (t || (0, q.getStorybookConfiguration)(
    String(e?.scripts?.storybook || ""),
    "-c",
    "--config-dir"
  )) ?? ".storybook", s = await (0, q.loadMainConfig)({ configDir: n }).catch(() => {
  });
  return Jt = await Ui({
    mainConfig: s,
    packageJson: e,
    packageJsonPath: r,
    configDir: n
  }), Jt;
}, "getStorybookMetadata");

// src/telemetry/telemetry.ts
var Xi = T(require("node:os"), 1), Qi = T(Vi(), 1);

// ../node_modules/nanoid/index.js
var ln = require("crypto");

// ../node_modules/nanoid/url-alphabet/index.js
var Fi = "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";

// ../node_modules/nanoid/index.js
var $u = 128, ve, De, Vu = /* @__PURE__ */ i((t) => {
  !ve || ve.length < t ? (ve = Buffer.allocUnsafe(t * $u), (0, ln.randomFillSync)(ve), De = 0) : De + t > ve.length && ((0, ln.randomFillSync)(
  ve), De = 0), De += t;
}, "fillPool");
var st = /* @__PURE__ */ i((t = 21) => {
  Vu(t -= 0);
  let e = "";
  for (let r = De - t; r < De; r++)
    e += Fi[ve[r] & 63];
  return e;
}, "nanoid");

// src/telemetry/anonymous-id.ts
var Wi = require("node:path"), qi = require("storybook/internal/common"), Gi = require("child_process");
oi();

// src/telemetry/one-way-hash.ts
var Bi = require("crypto");
var Yt = /* @__PURE__ */ i((t) => {
  let e = (0, Bi.createHash)("sha256");
  return e.update("storybook-telemetry-salt"), e.update(t), e.digest("hex");
}, "oneWayHash");

// src/telemetry/anonymous-id.ts
function Fu(t) {
  let n = t.trim().replace(/#.*$/, "").replace(/^.*@/, "").replace(/^.*\/\//, "");
  return (n.endsWith(".git") ? n : `${n}.git`).replace(":", "/");
}
i(Fu, "normalizeGitUrl");
function Bu(t, e) {
  return `${Fu(t)}${Qr(e)}`;
}
i(Bu, "unhashedProjectId");
var Xt, zi = /* @__PURE__ */ i(() => {
  if (Xt)
    return Xt;
  try {
    let t = (0, Wi.relative)((0, qi.getProjectRoot)(), process.cwd()), e = (0, Gi.execSync)("git config --local --get remote.origin.url", {
      timeout: 1e3,
      stdio: "pipe"
    });
    Xt = Yt(Bu(String(e), t));
  } catch {
  }
  return Xt;
}, "getAnonymousProjectId");

// src/telemetry/event-cache.ts
var Qt = require("storybook/internal/common");
var pn = Promise.resolve(), Wu = /* @__PURE__ */ i(async (t, e) => {
  let r = await Qt.cache.get("lastEvents") || {};
  r[t] = { body: e, timestamp: Date.now() }, await Qt.cache.set("lastEvents", r);
}, "setHelper"), Ji = /* @__PURE__ */ i(async (t, e) => (await pn, pn = Wu(t, e), pn), "set");
var qu = /* @__PURE__ */ i((t) => {
  let { body: e, timestamp: r } = t;
  return {
    timestamp: r,
    eventType: e?.eventType,
    eventId: e?.eventId,
    sessionId: e?.sessionId
  };
}, "upgradeFields"), Gu = ["init", "upgrade"], zu = ["build", "dev", "error"], Ki = /* @__PURE__ */ i((t, e) => {
  let r = e.map((n) => t?.[n]).filter(Boolean).sort((n, s) => s.timestamp - n.timestamp);
  return r.length > 0 ? r[0] : void 0;
}, "lastEvent"), Hi = /* @__PURE__ */ i(async (t = void 0) => {
  let e = t || await Qt.cache.get("lastEvents") || {}, r = Ki(e, Gu), n = Ki(e, zu);
  if (r)
    return !n?.timestamp || r.timestamp > n.timestamp ? qu(r) : void 0;
}, "getPrecedingUpgrade");

// src/telemetry/fetch.ts
var Yi = global.fetch;

// src/telemetry/session-id.ts
var fn = require("storybook/internal/common");
var Ku = 1e3 * 60 * 60 * 2, ot;
var mn = /* @__PURE__ */ i(async () => {
  let t = Date.now();
  if (!ot) {
    let e = await fn.cache.get("session");
    e && e.lastUsed >= t - Ku ? ot = e.id : ot = st();
  }
  return await fn.cache.set("session", { id: ot, lastUsed: t }), ot;
}, "getSessionId");

// src/telemetry/telemetry.ts
var Ju = (0, Qi.default)(Yi), Hu = process.env.STORYBOOK_TELEMETRY_URL || "https://storybook.js.org/event-log", er = [], ea = /* @__PURE__ */ i(
(t, e) => {
  hn[t] = e;
}, "addToGlobalContext"), Yu = /* @__PURE__ */ i(() => {
  try {
    let t = Xi.platform();
    return t === "win32" ? "Windows" : t === "darwin" ? "macOS" : t === "linux" ? "Linux" : `Other: ${t}`;
  } catch {
    return "Unknown";
  }
}, "getOperatingSystem"), hn = {
  inCI: !!process.env.CI,
  isTTY: process.stdout.isTTY,
  platform: Yu(),
  nodeVersion: process.versions.node,
  storybookVersion: ft
}, Xu = /* @__PURE__ */ i(async (t, e, r) => {
  let { eventType: n, payload: s, metadata: o, ...a } = t, c = await mn(), l = st(), p = { ...a, eventType: n, eventId: l, sessionId: c, metadata: o,
  payload: s, context: e };
  return Ju(Hu, {
    method: "post",
    body: JSON.stringify(p),
    headers: { "Content-Type": "application/json" },
    retries: 3,
    retryOn: [503, 504],
    retryDelay: /* @__PURE__ */ i((f) => 2 ** f * (typeof r?.retryDelay == "number" && !Number.isNaN(r?.retryDelay) ? r.retryDelay : 1e3), "\
retryDelay")
  });
}, "prepareRequest");
async function ta(t, e = { retryDelay: 1e3, immediate: !1 }) {
  let { eventType: r, payload: n, metadata: s, ...o } = t, a = e.stripMetadata ? hn : {
    ...hn,
    anonymousId: zi()
  }, c;
  try {
    c = Xu(t, a, e), er.push(c), e.immediate ? await Promise.all(er) : await c;
    let l = await mn(), p = st(), f = { ...o, eventType: r, eventId: p, sessionId: l, metadata: s, payload: n, context: a };
    await Ji(r, f);
  } catch {
  } finally {
    er = er.filter((l) => l !== c);
  }
}
i(ta, "sendTelemetry");

// src/telemetry/index.ts
var Qu = /* @__PURE__ */ i((t) => t.startsWith("example-button--") || t.startsWith("example-header--") || t.startsWith("example-page--"), "i\
sExampleStoryId"), el = /* @__PURE__ */ i(async (t, e = {}, r = {}) => {
  t !== "boot" && r.notify !== !1 && await kn();
  let n = {
    eventType: t,
    payload: e
  };
  try {
    r?.stripMetadata || (n.metadata = await un(r?.configDir));
  } catch (s) {
    n.payload.metadataErrorMessage = Ve(s).message, r?.enableCrashReports && (n.payload.metadataError = Ve(s));
  } finally {
    let { error: s } = n.payload;
    s && (n.payload.error = Ve(s)), (!n.payload.error || r?.enableCrashReports) && (process.env?.STORYBOOK_TELEMETRY_DEBUG && (yn.logger.info(
    `
[telemetry]`), yn.logger.info(JSON.stringify(n, null, 2))), await ta(n, r));
  }
}, "telemetry");
