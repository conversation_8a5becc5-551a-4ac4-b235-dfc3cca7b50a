import ESM_COMPAT_Module from "node:module";
import { fileURLToPath as ESM_COMPAT_fileURLToPath } from 'node:url';
import { dirname as ESM_COMPAT_dirname } from 'node:path';
const __filename = ESM_COMPAT_fileURLToPath(import.meta.url);
const __dirname = ESM_COMPAT_dirname(__filename);
const require = ESM_COMPAT_Module.createRequire(import.meta.url);
var hi = Object.create;
var $t = Object.defineProperty;
var yi = Object.getOwnPropertyDescriptor;
var gi = Object.getOwnPropertyNames;
var bi = Object.getPrototypeOf, xi = Object.prototype.hasOwnProperty;
var i = (t, e) => $t(t, "name", { value: e, configurable: !0 }), N = /* @__PURE__ */ ((t) => typeof require < "u" ? require : typeof Proxy <
"u" ? new Proxy(t, {
  get: (e, r) => (typeof require < "u" ? require : e)[r]
}) : t)(function(t) {
  if (typeof require < "u") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + t + '" is not supported');
});
var vi = (t, e) => () => (t && (e = t(t = 0)), e);
var I = (t, e) => () => (e || t((e = { exports: {} }).exports, e), e.exports);
var _i = (t, e, r, n) => {
  if (e && typeof e == "object" || typeof e == "function")
    for (let s of gi(e))
      !xi.call(t, s) && s !== r && $t(t, s, { get: () => e[s], enumerable: !(n = yi(e, s)) || n.enumerable });
  return t;
};
var z = (t, e, r) => (r = t != null ? hi(bi(t)) : {}, _i(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  e || !t || !t.__esModule ? $t(r, "default", { value: t, enumerable: !0 }) : r,
  t
));

// ../node_modules/picocolors/picocolors.js
var Qr = I((ml, Vt) => {
  var Qe = process || {}, Yr = Qe.argv || [], Xe = Qe.env || {}, wi = !(Xe.NO_COLOR || Yr.includes("--no-color")) && (!!Xe.FORCE_COLOR || Yr.
  includes("--color") || Qe.platform === "win32" || (Qe.stdout || {}).isTTY && Xe.TERM !== "dumb" || !!Xe.CI), ki = /* @__PURE__ */ i((t, e, r = t) => (n) => {
    let s = "" + n, o = s.indexOf(e, t.length);
    return ~o ? t + Ti(s, e, r, o) + e : t + s + e;
  }, "formatter"), Ti = /* @__PURE__ */ i((t, e, r, n) => {
    let s = "", o = 0;
    do
      s += t.substring(o, n) + r, o = n + e.length, n = t.indexOf(e, o);
    while (~n);
    return s + t.substring(o);
  }, "replaceClose"), Xr = /* @__PURE__ */ i((t = wi) => {
    let e = t ? ki : () => String;
    return {
      isColorSupported: t,
      reset: e("\x1B[0m", "\x1B[0m"),
      bold: e("\x1B[1m", "\x1B[22m", "\x1B[22m\x1B[1m"),
      dim: e("\x1B[2m", "\x1B[22m", "\x1B[22m\x1B[2m"),
      italic: e("\x1B[3m", "\x1B[23m"),
      underline: e("\x1B[4m", "\x1B[24m"),
      inverse: e("\x1B[7m", "\x1B[27m"),
      hidden: e("\x1B[8m", "\x1B[28m"),
      strikethrough: e("\x1B[9m", "\x1B[29m"),
      black: e("\x1B[30m", "\x1B[39m"),
      red: e("\x1B[31m", "\x1B[39m"),
      green: e("\x1B[32m", "\x1B[39m"),
      yellow: e("\x1B[33m", "\x1B[39m"),
      blue: e("\x1B[34m", "\x1B[39m"),
      magenta: e("\x1B[35m", "\x1B[39m"),
      cyan: e("\x1B[36m", "\x1B[39m"),
      white: e("\x1B[37m", "\x1B[39m"),
      gray: e("\x1B[90m", "\x1B[39m"),
      bgBlack: e("\x1B[40m", "\x1B[49m"),
      bgRed: e("\x1B[41m", "\x1B[49m"),
      bgGreen: e("\x1B[42m", "\x1B[49m"),
      bgYellow: e("\x1B[43m", "\x1B[49m"),
      bgBlue: e("\x1B[44m", "\x1B[49m"),
      bgMagenta: e("\x1B[45m", "\x1B[49m"),
      bgCyan: e("\x1B[46m", "\x1B[49m"),
      bgWhite: e("\x1B[47m", "\x1B[49m"),
      blackBright: e("\x1B[90m", "\x1B[39m"),
      redBright: e("\x1B[91m", "\x1B[39m"),
      greenBright: e("\x1B[92m", "\x1B[39m"),
      yellowBright: e("\x1B[93m", "\x1B[39m"),
      blueBright: e("\x1B[94m", "\x1B[39m"),
      magentaBright: e("\x1B[95m", "\x1B[39m"),
      cyanBright: e("\x1B[96m", "\x1B[39m"),
      whiteBright: e("\x1B[97m", "\x1B[39m"),
      bgBlackBright: e("\x1B[100m", "\x1B[49m"),
      bgRedBright: e("\x1B[101m", "\x1B[49m"),
      bgGreenBright: e("\x1B[102m", "\x1B[49m"),
      bgYellowBright: e("\x1B[103m", "\x1B[49m"),
      bgBlueBright: e("\x1B[104m", "\x1B[49m"),
      bgMagentaBright: e("\x1B[105m", "\x1B[49m"),
      bgCyanBright: e("\x1B[106m", "\x1B[49m"),
      bgWhiteBright: e("\x1B[107m", "\x1B[49m")
    };
  }, "createColors");
  Vt.exports = Xr();
  Vt.exports.createColors = Xr;
});

// ../node_modules/walk-up-path/dist/cjs/index.js
var dn = I((rt) => {
  "use strict";
  Object.defineProperty(rt, "__esModule", { value: !0 });
  rt.walkUp = void 0;
  var cn = N("path"), Si = /* @__PURE__ */ i(function* (t) {
    for (t = (0, cn.resolve)(t); t; ) {
      yield t;
      let e = (0, cn.dirname)(t);
      if (e === t)
        break;
      t = e;
    }
  }, "walkUp");
  rt.walkUp = Si;
});

// ../node_modules/zod/lib/helpers/util.js
var Ne = I((E) => {
  "use strict";
  Object.defineProperty(E, "__esModule", { value: !0 });
  E.getParsedType = E.ZodParsedType = E.objectUtil = E.util = void 0;
  var zt;
  (function(t) {
    t.assertEqual = (s) => s;
    function e(s) {
    }
    i(e, "assertIs"), t.assertIs = e;
    function r(s) {
      throw new Error();
    }
    i(r, "assertNever"), t.assertNever = r, t.arrayToEnum = (s) => {
      let o = {};
      for (let a of s)
        o[a] = a;
      return o;
    }, t.getValidEnumValues = (s) => {
      let o = t.objectKeys(s).filter((c) => typeof s[s[c]] != "number"), a = {};
      for (let c of o)
        a[c] = s[c];
      return t.objectValues(a);
    }, t.objectValues = (s) => t.objectKeys(s).map(function(o) {
      return s[o];
    }), t.objectKeys = typeof Object.keys == "function" ? (s) => Object.keys(s) : (s) => {
      let o = [];
      for (let a in s)
        Object.prototype.hasOwnProperty.call(s, a) && o.push(a);
      return o;
    }, t.find = (s, o) => {
      for (let a of s)
        if (o(a))
          return a;
    }, t.isInteger = typeof Number.isInteger == "function" ? (s) => Number.isInteger(s) : (s) => typeof s == "number" && isFinite(s) && Math.
    floor(s) === s;
    function n(s, o = " | ") {
      return s.map((a) => typeof a == "string" ? `'${a}'` : a).join(o);
    }
    i(n, "joinValues"), t.joinValues = n, t.jsonStringifyReplacer = (s, o) => typeof o == "bigint" ? o.toString() : o;
  })(zt || (E.util = zt = {}));
  var hn;
  (function(t) {
    t.mergeShapes = (e, r) => ({
      ...e,
      ...r
      // second overwrites first
    });
  })(hn || (E.objectUtil = hn = {}));
  E.ZodParsedType = zt.arrayToEnum([
    "string",
    "nan",
    "number",
    "integer",
    "float",
    "boolean",
    "date",
    "bigint",
    "symbol",
    "function",
    "undefined",
    "null",
    "array",
    "object",
    "unknown",
    "promise",
    "void",
    "never",
    "map",
    "set"
  ]);
  var Di = /* @__PURE__ */ i((t) => {
    switch (typeof t) {
      case "undefined":
        return E.ZodParsedType.undefined;
      case "string":
        return E.ZodParsedType.string;
      case "number":
        return isNaN(t) ? E.ZodParsedType.nan : E.ZodParsedType.number;
      case "boolean":
        return E.ZodParsedType.boolean;
      case "function":
        return E.ZodParsedType.function;
      case "bigint":
        return E.ZodParsedType.bigint;
      case "symbol":
        return E.ZodParsedType.symbol;
      case "object":
        return Array.isArray(t) ? E.ZodParsedType.array : t === null ? E.ZodParsedType.null : t.then && typeof t.then == "function" && t.catch &&
        typeof t.catch == "function" ? E.ZodParsedType.promise : typeof Map < "u" && t instanceof Map ? E.ZodParsedType.map : typeof Set < "\
u" && t instanceof Set ? E.ZodParsedType.set : typeof Date < "u" && t instanceof Date ? E.ZodParsedType.date : E.ZodParsedType.object;
      default:
        return E.ZodParsedType.unknown;
    }
  }, "getParsedType");
  E.getParsedType = Di;
});

// ../node_modules/zod/lib/ZodError.js
var st = I((K) => {
  "use strict";
  Object.defineProperty(K, "__esModule", { value: !0 });
  K.ZodError = K.quotelessJson = K.ZodIssueCode = void 0;
  var yn = Ne();
  K.ZodIssueCode = yn.util.arrayToEnum([
    "invalid_type",
    "invalid_literal",
    "custom",
    "invalid_union",
    "invalid_union_discriminator",
    "invalid_enum_value",
    "unrecognized_keys",
    "invalid_arguments",
    "invalid_return_type",
    "invalid_date",
    "invalid_string",
    "too_small",
    "too_big",
    "invalid_intersection_types",
    "not_multiple_of",
    "not_finite"
  ]);
  var Li = /* @__PURE__ */ i((t) => JSON.stringify(t, null, 2).replace(/"([^"]+)":/g, "$1:"), "quotelessJson");
  K.quotelessJson = Li;
  var Ze = class t extends Error {
    static {
      i(this, "ZodError");
    }
    get errors() {
      return this.issues;
    }
    constructor(e) {
      super(), this.issues = [], this.addIssue = (n) => {
        this.issues = [...this.issues, n];
      }, this.addIssues = (n = []) => {
        this.issues = [...this.issues, ...n];
      };
      let r = new.target.prototype;
      Object.setPrototypeOf ? Object.setPrototypeOf(this, r) : this.__proto__ = r, this.name = "ZodError", this.issues = e;
    }
    format(e) {
      let r = e || function(o) {
        return o.message;
      }, n = { _errors: [] }, s = /* @__PURE__ */ i((o) => {
        for (let a of o.issues)
          if (a.code === "invalid_union")
            a.unionErrors.map(s);
          else if (a.code === "invalid_return_type")
            s(a.returnTypeError);
          else if (a.code === "invalid_arguments")
            s(a.argumentsError);
          else if (a.path.length === 0)
            n._errors.push(r(a));
          else {
            let c = n, l = 0;
            for (; l < a.path.length; ) {
              let p = a.path[l];
              l === a.path.length - 1 ? (c[p] = c[p] || { _errors: [] }, c[p]._errors.push(r(a))) : c[p] = c[p] || { _errors: [] }, c = c[p],
              l++;
            }
          }
      }, "processError");
      return s(this), n;
    }
    static assert(e) {
      if (!(e instanceof t))
        throw new Error(`Not a ZodError: ${e}`);
    }
    toString() {
      return this.message;
    }
    get message() {
      return JSON.stringify(this.issues, yn.util.jsonStringifyReplacer, 2);
    }
    get isEmpty() {
      return this.issues.length === 0;
    }
    flatten(e = (r) => r.message) {
      let r = {}, n = [];
      for (let s of this.issues)
        s.path.length > 0 ? (r[s.path[0]] = r[s.path[0]] || [], r[s.path[0]].push(e(s))) : n.push(e(s));
      return { formErrors: n, fieldErrors: r };
    }
    get formErrors() {
      return this.flatten();
    }
  };
  K.ZodError = Ze;
  Ze.create = (t) => new Ze(t);
});

// ../node_modules/zod/lib/locales/en.js
var Jt = I((Kt) => {
  "use strict";
  Object.defineProperty(Kt, "__esModule", { value: !0 });
  var ee = Ne(), A = st(), Ui = /* @__PURE__ */ i((t, e) => {
    let r;
    switch (t.code) {
      case A.ZodIssueCode.invalid_type:
        t.received === ee.ZodParsedType.undefined ? r = "Required" : r = `Expected ${t.expected}, received ${t.received}`;
        break;
      case A.ZodIssueCode.invalid_literal:
        r = `Invalid literal value, expected ${JSON.stringify(t.expected, ee.util.jsonStringifyReplacer)}`;
        break;
      case A.ZodIssueCode.unrecognized_keys:
        r = `Unrecognized key(s) in object: ${ee.util.joinValues(t.keys, ", ")}`;
        break;
      case A.ZodIssueCode.invalid_union:
        r = "Invalid input";
        break;
      case A.ZodIssueCode.invalid_union_discriminator:
        r = `Invalid discriminator value. Expected ${ee.util.joinValues(t.options)}`;
        break;
      case A.ZodIssueCode.invalid_enum_value:
        r = `Invalid enum value. Expected ${ee.util.joinValues(t.options)}, received '${t.received}'`;
        break;
      case A.ZodIssueCode.invalid_arguments:
        r = "Invalid function arguments";
        break;
      case A.ZodIssueCode.invalid_return_type:
        r = "Invalid function return type";
        break;
      case A.ZodIssueCode.invalid_date:
        r = "Invalid date";
        break;
      case A.ZodIssueCode.invalid_string:
        typeof t.validation == "object" ? "includes" in t.validation ? (r = `Invalid input: must include "${t.validation.includes}"`, typeof t.
        validation.position == "number" && (r = `${r} at one or more positions greater than or equal to ${t.validation.position}`)) : "start\
sWith" in t.validation ? r = `Invalid input: must start with "${t.validation.startsWith}"` : "endsWith" in t.validation ? r = `Invalid input\
: must end with "${t.validation.endsWith}"` : ee.util.assertNever(t.validation) : t.validation !== "regex" ? r = `Invalid ${t.validation}` :
        r = "Invalid";
        break;
      case A.ZodIssueCode.too_small:
        t.type === "array" ? r = `Array must contain ${t.exact ? "exactly" : t.inclusive ? "at least" : "more than"} ${t.minimum} element(s)` :
        t.type === "string" ? r = `String must contain ${t.exact ? "exactly" : t.inclusive ? "at least" : "over"} ${t.minimum} character(s)` :
        t.type === "number" ? r = `Number must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "greater tha\
n "}${t.minimum}` : t.type === "date" ? r = `Date must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "gre\
ater than "}${new Date(Number(t.minimum))}` : r = "Invalid input";
        break;
      case A.ZodIssueCode.too_big:
        t.type === "array" ? r = `Array must contain ${t.exact ? "exactly" : t.inclusive ? "at most" : "less than"} ${t.maximum} element(s)` :
        t.type === "string" ? r = `String must contain ${t.exact ? "exactly" : t.inclusive ? "at most" : "under"} ${t.maximum} character(s)` :
        t.type === "number" ? r = `Number must be ${t.exact ? "exactly" : t.inclusive ? "less than or equal to" : "less than"} ${t.maximum}` :
        t.type === "bigint" ? r = `BigInt must be ${t.exact ? "exactly" : t.inclusive ? "less than or equal to" : "less than"} ${t.maximum}` :
        t.type === "date" ? r = `Date must be ${t.exact ? "exactly" : t.inclusive ? "smaller than or equal to" : "smaller than"} ${new Date(
        Number(t.maximum))}` : r = "Invalid input";
        break;
      case A.ZodIssueCode.custom:
        r = "Invalid input";
        break;
      case A.ZodIssueCode.invalid_intersection_types:
        r = "Intersection results could not be merged";
        break;
      case A.ZodIssueCode.not_multiple_of:
        r = `Number must be a multiple of ${t.multipleOf}`;
        break;
      case A.ZodIssueCode.not_finite:
        r = "Number must be finite";
        break;
      default:
        r = e.defaultError, ee.util.assertNever(t);
    }
    return { message: r };
  }, "errorMap");
  Kt.default = Ui;
});

// ../node_modules/zod/lib/errors.js
var ot = I(($) => {
  "use strict";
  var $i = $ && $.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty($, "__esModule", { value: !0 });
  $.getErrorMap = $.setErrorMap = $.defaultErrorMap = void 0;
  var gn = $i(Jt());
  $.defaultErrorMap = gn.default;
  var bn = gn.default;
  function Vi(t) {
    bn = t;
  }
  i(Vi, "setErrorMap");
  $.setErrorMap = Vi;
  function Fi() {
    return bn;
  }
  i(Fi, "getErrorMap");
  $.getErrorMap = Fi;
});

// ../node_modules/zod/lib/helpers/parseUtil.js
var Yt = I((T) => {
  "use strict";
  var Bi = T && T.__importDefault || function(t) {
    return t && t.__esModule ? t : { default: t };
  };
  Object.defineProperty(T, "__esModule", { value: !0 });
  T.isAsync = T.isValid = T.isDirty = T.isAborted = T.OK = T.DIRTY = T.INVALID = T.ParseStatus = T.addIssueToContext = T.EMPTY_PATH = T.makeIssue =
  void 0;
  var Wi = ot(), xn = Bi(Jt()), qi = /* @__PURE__ */ i((t) => {
    let { data: e, path: r, errorMaps: n, issueData: s } = t, o = [...r, ...s.path || []], a = {
      ...s,
      path: o
    };
    if (s.message !== void 0)
      return {
        ...s,
        path: o,
        message: s.message
      };
    let c = "", l = n.filter((p) => !!p).slice().reverse();
    for (let p of l)
      c = p(a, { data: e, defaultError: c }).message;
    return {
      ...s,
      path: o,
      message: c
    };
  }, "makeIssue");
  T.makeIssue = qi;
  T.EMPTY_PATH = [];
  function Gi(t, e) {
    let r = (0, Wi.getErrorMap)(), n = (0, T.makeIssue)({
      issueData: e,
      data: t.data,
      path: t.path,
      errorMaps: [
        t.common.contextualErrorMap,
        // contextual error map is first priority
        t.schemaErrorMap,
        // then schema-bound map if available
        r,
        // then global override map
        r === xn.default ? void 0 : xn.default
        // then global default map
      ].filter((s) => !!s)
    });
    t.common.issues.push(n);
  }
  i(Gi, "addIssueToContext");
  T.addIssueToContext = Gi;
  var Ht = class t {
    static {
      i(this, "ParseStatus");
    }
    constructor() {
      this.value = "valid";
    }
    dirty() {
      this.value === "valid" && (this.value = "dirty");
    }
    abort() {
      this.value !== "aborted" && (this.value = "aborted");
    }
    static mergeArray(e, r) {
      let n = [];
      for (let s of r) {
        if (s.status === "aborted")
          return T.INVALID;
        s.status === "dirty" && e.dirty(), n.push(s.value);
      }
      return { status: e.value, value: n };
    }
    static async mergeObjectAsync(e, r) {
      let n = [];
      for (let s of r) {
        let o = await s.key, a = await s.value;
        n.push({
          key: o,
          value: a
        });
      }
      return t.mergeObjectSync(e, n);
    }
    static mergeObjectSync(e, r) {
      let n = {};
      for (let s of r) {
        let { key: o, value: a } = s;
        if (o.status === "aborted" || a.status === "aborted")
          return T.INVALID;
        o.status === "dirty" && e.dirty(), a.status === "dirty" && e.dirty(), o.value !== "__proto__" && (typeof a.value < "u" || s.alwaysSet) &&
        (n[o.value] = a.value);
      }
      return { status: e.value, value: n };
    }
  };
  T.ParseStatus = Ht;
  T.INVALID = Object.freeze({
    status: "aborted"
  });
  var zi = /* @__PURE__ */ i((t) => ({ status: "dirty", value: t }), "DIRTY");
  T.DIRTY = zi;
  var Ki = /* @__PURE__ */ i((t) => ({ status: "valid", value: t }), "OK");
  T.OK = Ki;
  var Ji = /* @__PURE__ */ i((t) => t.status === "aborted", "isAborted");
  T.isAborted = Ji;
  var Hi = /* @__PURE__ */ i((t) => t.status === "dirty", "isDirty");
  T.isDirty = Hi;
  var Yi = /* @__PURE__ */ i((t) => t.status === "valid", "isValid");
  T.isValid = Yi;
  var Xi = /* @__PURE__ */ i((t) => typeof Promise < "u" && t instanceof Promise, "isAsync");
  T.isAsync = Xi;
});

// ../node_modules/zod/lib/helpers/typeAliases.js
var _n = I((vn) => {
  "use strict";
  Object.defineProperty(vn, "__esModule", { value: !0 });
});

// ../node_modules/zod/lib/helpers/errorUtil.js
var kn = I((it) => {
  "use strict";
  Object.defineProperty(it, "__esModule", { value: !0 });
  it.errorUtil = void 0;
  var wn;
  (function(t) {
    t.errToObj = (e) => typeof e == "string" ? { message: e } : e || {}, t.toString = (e) => typeof e == "string" ? e : e?.message;
  })(wn || (it.errorUtil = wn = {}));
});

// ../node_modules/zod/lib/types.js
var Mn = I((d) => {
  "use strict";
  var ct = d && d.__classPrivateFieldGet || function(t, e, r, n) {
    if (r === "a" && !n) throw new TypeError("Private accessor was defined without a getter");
    if (typeof e == "function" ? t !== e || !n : !e.has(t)) throw new TypeError("Cannot read private member from an object whose class did n\
ot declare it");
    return r === "m" ? n : r === "a" ? n.call(t) : n ? n.value : e.get(t);
  }, Sn = d && d.__classPrivateFieldSet || function(t, e, r, n, s) {
    if (n === "m") throw new TypeError("Private method is not writable");
    if (n === "a" && !s) throw new TypeError("Private accessor was defined without a setter");
    if (typeof e == "function" ? t !== e || !s : !e.has(t)) throw new TypeError("Cannot write private member to an object whose class did no\
t declare it");
    return n === "a" ? s.call(t, r) : s ? s.value = r : e.set(t, r), r;
  }, Me, De;
  Object.defineProperty(d, "__esModule", { value: !0 });
  d.boolean = d.bigint = d.array = d.any = d.coerce = d.ZodFirstPartyTypeKind = d.late = d.ZodSchema = d.Schema = d.custom = d.ZodReadonly =
  d.ZodPipeline = d.ZodBranded = d.BRAND = d.ZodNaN = d.ZodCatch = d.ZodDefault = d.ZodNullable = d.ZodOptional = d.ZodTransformer = d.ZodEffects =
  d.ZodPromise = d.ZodNativeEnum = d.ZodEnum = d.ZodLiteral = d.ZodLazy = d.ZodFunction = d.ZodSet = d.ZodMap = d.ZodRecord = d.ZodTuple = d.
  ZodIntersection = d.ZodDiscriminatedUnion = d.ZodUnion = d.ZodObject = d.ZodArray = d.ZodVoid = d.ZodNever = d.ZodUnknown = d.ZodAny = d.ZodNull =
  d.ZodUndefined = d.ZodSymbol = d.ZodDate = d.ZodBoolean = d.ZodBigInt = d.ZodNumber = d.ZodString = d.datetimeRegex = d.ZodType = void 0;
  d.NEVER = d.void = d.unknown = d.union = d.undefined = d.tuple = d.transformer = d.symbol = d.string = d.strictObject = d.set = d.record =
  d.promise = d.preprocess = d.pipeline = d.ostring = d.optional = d.onumber = d.oboolean = d.object = d.number = d.nullable = d.null = d.never =
  d.nativeEnum = d.nan = d.map = d.literal = d.lazy = d.intersection = d.instanceof = d.function = d.enum = d.effect = d.discriminatedUnion =
  d.date = void 0;
  var at = ot(), y = kn(), u = Yt(), h = Ne(), m = st(), D = class {
    static {
      i(this, "ParseInputLazyPath");
    }
    constructor(e, r, n, s) {
      this._cachedPath = [], this.parent = e, this.data = r, this._path = n, this._key = s;
    }
    get path() {
      return this._cachedPath.length || (this._key instanceof Array ? this._cachedPath.push(...this._path, ...this._key) : this._cachedPath.
      push(...this._path, this._key)), this._cachedPath;
    }
  }, Tn = /* @__PURE__ */ i((t, e) => {
    if ((0, u.isValid)(e))
      return { success: !0, data: e.value };
    if (!t.common.issues.length)
      throw new Error("Validation failed but no issues detected.");
    return {
      success: !1,
      get error() {
        if (this._error)
          return this._error;
        let r = new m.ZodError(t.common.issues);
        return this._error = r, this._error;
      }
    };
  }, "handleResult");
  function x(t) {
    if (!t)
      return {};
    let { errorMap: e, invalid_type_error: r, required_error: n, description: s } = t;
    if (e && (r || n))
      throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
    return e ? { errorMap: e, description: s } : { errorMap: /* @__PURE__ */ i((a, c) => {
      var l, p;
      let { message: f } = t;
      return a.code === "invalid_enum_value" ? { message: f ?? c.defaultError } : typeof c.data > "u" ? { message: (l = f ?? n) !== null && l !==
      void 0 ? l : c.defaultError } : a.code !== "invalid_type" ? { message: c.defaultError } : { message: (p = f ?? r) !== null && p !== void 0 ?
      p : c.defaultError };
    }, "customMap"), description: s };
  }
  i(x, "processCreateParams");
  var v = class {
    static {
      i(this, "ZodType");
    }
    get description() {
      return this._def.description;
    }
    _getType(e) {
      return (0, h.getParsedType)(e.data);
    }
    _getOrReturnCtx(e, r) {
      return r || {
        common: e.parent.common,
        data: e.data,
        parsedType: (0, h.getParsedType)(e.data),
        schemaErrorMap: this._def.errorMap,
        path: e.path,
        parent: e.parent
      };
    }
    _processInputParams(e) {
      return {
        status: new u.ParseStatus(),
        ctx: {
          common: e.parent.common,
          data: e.data,
          parsedType: (0, h.getParsedType)(e.data),
          schemaErrorMap: this._def.errorMap,
          path: e.path,
          parent: e.parent
        }
      };
    }
    _parseSync(e) {
      let r = this._parse(e);
      if ((0, u.isAsync)(r))
        throw new Error("Synchronous parse encountered promise.");
      return r;
    }
    _parseAsync(e) {
      let r = this._parse(e);
      return Promise.resolve(r);
    }
    parse(e, r) {
      let n = this.safeParse(e, r);
      if (n.success)
        return n.data;
      throw n.error;
    }
    safeParse(e, r) {
      var n;
      let s = {
        common: {
          issues: [],
          async: (n = r?.async) !== null && n !== void 0 ? n : !1,
          contextualErrorMap: r?.errorMap
        },
        path: r?.path || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: e,
        parsedType: (0, h.getParsedType)(e)
      }, o = this._parseSync({ data: e, path: s.path, parent: s });
      return Tn(s, o);
    }
    "~validate"(e) {
      var r, n;
      let s = {
        common: {
          issues: [],
          async: !!this["~standard"].async
        },
        path: [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: e,
        parsedType: (0, h.getParsedType)(e)
      };
      if (!this["~standard"].async)
        try {
          let o = this._parseSync({ data: e, path: [], parent: s });
          return (0, u.isValid)(o) ? {
            value: o.value
          } : {
            issues: s.common.issues
          };
        } catch (o) {
          !((n = (r = o?.message) === null || r === void 0 ? void 0 : r.toLowerCase()) === null || n === void 0) && n.includes("encountered") &&
          (this["~standard"].async = !0), s.common = {
            issues: [],
            async: !0
          };
        }
      return this._parseAsync({ data: e, path: [], parent: s }).then((o) => (0, u.isValid)(o) ? {
        value: o.value
      } : {
        issues: s.common.issues
      });
    }
    async parseAsync(e, r) {
      let n = await this.safeParseAsync(e, r);
      if (n.success)
        return n.data;
      throw n.error;
    }
    async safeParseAsync(e, r) {
      let n = {
        common: {
          issues: [],
          contextualErrorMap: r?.errorMap,
          async: !0
        },
        path: r?.path || [],
        schemaErrorMap: this._def.errorMap,
        parent: null,
        data: e,
        parsedType: (0, h.getParsedType)(e)
      }, s = this._parse({ data: e, path: n.path, parent: n }), o = await ((0, u.isAsync)(s) ? s : Promise.resolve(s));
      return Tn(n, o);
    }
    refine(e, r) {
      let n = /* @__PURE__ */ i((s) => typeof r == "string" || typeof r > "u" ? { message: r } : typeof r == "function" ? r(s) : r, "getIssu\
eProperties");
      return this._refinement((s, o) => {
        let a = e(s), c = /* @__PURE__ */ i(() => o.addIssue({
          code: m.ZodIssueCode.custom,
          ...n(s)
        }), "setError");
        return typeof Promise < "u" && a instanceof Promise ? a.then((l) => l ? !0 : (c(), !1)) : a ? !0 : (c(), !1);
      });
    }
    refinement(e, r) {
      return this._refinement((n, s) => e(n) ? !0 : (s.addIssue(typeof r == "function" ? r(n, s) : r), !1));
    }
    _refinement(e) {
      return new Z({
        schema: this,
        typeName: g.ZodEffects,
        effect: { type: "refinement", refinement: e }
      });
    }
    superRefine(e) {
      return this._refinement(e);
    }
    constructor(e) {
      this.spa = this.safeParseAsync, this._def = e, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync =
      this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.
      bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.
      bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.
      promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(
      this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe =
      this.describe.bind(this), this.pipe = this.pipe.bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.
      bind(this), this.isOptional = this.isOptional.bind(this), this["~standard"] = {
        version: 1,
        vendor: "zod",
        validate: /* @__PURE__ */ i((r) => this["~validate"](r), "validate")
      };
    }
    optional() {
      return M.create(this, this._def);
    }
    nullable() {
      return F.create(this, this._def);
    }
    nullish() {
      return this.nullable().optional();
    }
    array() {
      return q.create(this);
    }
    promise() {
      return Y.create(this, this._def);
    }
    or(e) {
      return ae.create([this, e], this._def);
    }
    and(e) {
      return ce.create(this, e, this._def);
    }
    transform(e) {
      return new Z({
        ...x(this._def),
        schema: this,
        typeName: g.ZodEffects,
        effect: { type: "transform", transform: e }
      });
    }
    default(e) {
      let r = typeof e == "function" ? e : () => e;
      return new fe({
        ...x(this._def),
        innerType: this,
        defaultValue: r,
        typeName: g.ZodDefault
      });
    }
    brand() {
      return new Le({
        typeName: g.ZodBranded,
        type: this,
        ...x(this._def)
      });
    }
    catch(e) {
      let r = typeof e == "function" ? e : () => e;
      return new me({
        ...x(this._def),
        innerType: this,
        catchValue: r,
        typeName: g.ZodCatch
      });
    }
    describe(e) {
      let r = this.constructor;
      return new r({
        ...this._def,
        description: e
      });
    }
    pipe(e) {
      return Ue.create(this, e);
    }
    readonly() {
      return he.create(this);
    }
    isOptional() {
      return this.safeParse(void 0).success;
    }
    isNullable() {
      return this.safeParse(null).success;
    }
  };
  d.ZodType = v;
  d.Schema = v;
  d.ZodSchema = v;
  var Qi = /^c[^\s-]{8,}$/i, ea = /^[0-9a-z]+$/, ta = /^[0-9A-HJKMNP-TV-Z]{26}$/i, ra = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,
  na = /^[a-z0-9_-]{21}$/i, sa = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/, oa = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,
  ia = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i, aa = "^(\\p{Extended_Pictographic}|\\p{Emoji_Comp\
onent})+$", Xt, ca = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/, da = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
  ua = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,
  la = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
  pa = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/, fa = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
  En = "((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469\
]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))", ma = new RegExp(`^${En}$`);
  function Cn(t) {
    let e = "([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";
    return t.precision ? e = `${e}\\.\\d{${t.precision}}` : t.precision == null && (e = `${e}(\\.\\d+)?`), e;
  }
  i(Cn, "timeRegexSource");
  function ha(t) {
    return new RegExp(`^${Cn(t)}$`);
  }
  i(ha, "timeRegex");
  function Pn(t) {
    let e = `${En}T${Cn(t)}`, r = [];
    return r.push(t.local ? "Z?" : "Z"), t.offset && r.push("([+-]\\d{2}:?\\d{2})"), e = `${e}(${r.join("|")})`, new RegExp(`^${e}$`);
  }
  i(Pn, "datetimeRegex");
  d.datetimeRegex = Pn;
  function ya(t, e) {
    return !!((e === "v4" || !e) && ca.test(t) || (e === "v6" || !e) && ua.test(t));
  }
  i(ya, "isValidIP");
  function ga(t, e) {
    if (!sa.test(t))
      return !1;
    try {
      let [r] = t.split("."), n = r.replace(/-/g, "+").replace(/_/g, "/").padEnd(r.length + (4 - r.length % 4) % 4, "="), s = JSON.parse(atob(
      n));
      return !(typeof s != "object" || s === null || !s.typ || !s.alg || e && s.alg !== e);
    } catch {
      return !1;
    }
  }
  i(ga, "isValidJWT");
  function ba(t, e) {
    return !!((e === "v4" || !e) && da.test(t) || (e === "v6" || !e) && la.test(t));
  }
  i(ba, "isValidCidr");
  var J = class t extends v {
    static {
      i(this, "ZodString");
    }
    _parse(e) {
      if (this._def.coerce && (e.data = String(e.data)), this._getType(e) !== h.ZodParsedType.string) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.string,
          received: o.parsedType
        }), u.INVALID;
      }
      let n = new u.ParseStatus(), s;
      for (let o of this._def.checks)
        if (o.kind === "min")
          e.data.length < o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_small,
            minimum: o.value,
            type: "string",
            inclusive: !0,
            exact: !1,
            message: o.message
          }), n.dirty());
        else if (o.kind === "max")
          e.data.length > o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_big,
            maximum: o.value,
            type: "string",
            inclusive: !0,
            exact: !1,
            message: o.message
          }), n.dirty());
        else if (o.kind === "length") {
          let a = e.data.length > o.value, c = e.data.length < o.value;
          (a || c) && (s = this._getOrReturnCtx(e, s), a ? (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_big,
            maximum: o.value,
            type: "string",
            inclusive: !0,
            exact: !0,
            message: o.message
          }) : c && (0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.too_small,
            minimum: o.value,
            type: "string",
            inclusive: !0,
            exact: !0,
            message: o.message
          }), n.dirty());
        } else if (o.kind === "email")
          ia.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "email",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "emoji")
          Xt || (Xt = new RegExp(aa, "u")), Xt.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "emoji",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "uuid")
          ra.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "uuid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "nanoid")
          na.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "nanoid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "cuid")
          Qi.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "cuid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "cuid2")
          ea.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "cuid2",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "ulid")
          ta.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
            validation: "ulid",
            code: m.ZodIssueCode.invalid_string,
            message: o.message
          }), n.dirty());
        else if (o.kind === "url")
          try {
            new URL(e.data);
          } catch {
            s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
              validation: "url",
              code: m.ZodIssueCode.invalid_string,
              message: o.message
            }), n.dirty();
          }
        else o.kind === "regex" ? (o.regex.lastIndex = 0, o.regex.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(
        s, {
          validation: "regex",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty())) : o.kind === "trim" ? e.data = e.data.trim() : o.kind === "includes" ? e.data.includes(o.value, o.position) || (s = this.
        _getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: { includes: o.value, position: o.position },
          message: o.message
        }), n.dirty()) : o.kind === "toLowerCase" ? e.data = e.data.toLowerCase() : o.kind === "toUpperCase" ? e.data = e.data.toUpperCase() :
        o.kind === "startsWith" ? e.data.startsWith(o.value) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: { startsWith: o.value },
          message: o.message
        }), n.dirty()) : o.kind === "endsWith" ? e.data.endsWith(o.value) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: { endsWith: o.value },
          message: o.message
        }), n.dirty()) : o.kind === "datetime" ? Pn(o).test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: "datetime",
          message: o.message
        }), n.dirty()) : o.kind === "date" ? ma.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: "date",
          message: o.message
        }), n.dirty()) : o.kind === "time" ? ha(o).test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.invalid_string,
          validation: "time",
          message: o.message
        }), n.dirty()) : o.kind === "duration" ? oa.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "duration",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "ip" ? ya(e.data, o.version) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "ip",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "jwt" ? ga(e.data, o.alg) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "jwt",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "cidr" ? ba(e.data, o.version) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "cidr",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "base64" ? pa.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "base64",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : o.kind === "base64url" ? fa.test(e.data) || (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          validation: "base64url",
          code: m.ZodIssueCode.invalid_string,
          message: o.message
        }), n.dirty()) : h.util.assertNever(o);
      return { status: n.value, value: e.data };
    }
    _regex(e, r, n) {
      return this.refinement((s) => e.test(s), {
        validation: r,
        code: m.ZodIssueCode.invalid_string,
        ...y.errorUtil.errToObj(n)
      });
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    email(e) {
      return this._addCheck({ kind: "email", ...y.errorUtil.errToObj(e) });
    }
    url(e) {
      return this._addCheck({ kind: "url", ...y.errorUtil.errToObj(e) });
    }
    emoji(e) {
      return this._addCheck({ kind: "emoji", ...y.errorUtil.errToObj(e) });
    }
    uuid(e) {
      return this._addCheck({ kind: "uuid", ...y.errorUtil.errToObj(e) });
    }
    nanoid(e) {
      return this._addCheck({ kind: "nanoid", ...y.errorUtil.errToObj(e) });
    }
    cuid(e) {
      return this._addCheck({ kind: "cuid", ...y.errorUtil.errToObj(e) });
    }
    cuid2(e) {
      return this._addCheck({ kind: "cuid2", ...y.errorUtil.errToObj(e) });
    }
    ulid(e) {
      return this._addCheck({ kind: "ulid", ...y.errorUtil.errToObj(e) });
    }
    base64(e) {
      return this._addCheck({ kind: "base64", ...y.errorUtil.errToObj(e) });
    }
    base64url(e) {
      return this._addCheck({
        kind: "base64url",
        ...y.errorUtil.errToObj(e)
      });
    }
    jwt(e) {
      return this._addCheck({ kind: "jwt", ...y.errorUtil.errToObj(e) });
    }
    ip(e) {
      return this._addCheck({ kind: "ip", ...y.errorUtil.errToObj(e) });
    }
    cidr(e) {
      return this._addCheck({ kind: "cidr", ...y.errorUtil.errToObj(e) });
    }
    datetime(e) {
      var r, n;
      return typeof e == "string" ? this._addCheck({
        kind: "datetime",
        precision: null,
        offset: !1,
        local: !1,
        message: e
      }) : this._addCheck({
        kind: "datetime",
        precision: typeof e?.precision > "u" ? null : e?.precision,
        offset: (r = e?.offset) !== null && r !== void 0 ? r : !1,
        local: (n = e?.local) !== null && n !== void 0 ? n : !1,
        ...y.errorUtil.errToObj(e?.message)
      });
    }
    date(e) {
      return this._addCheck({ kind: "date", message: e });
    }
    time(e) {
      return typeof e == "string" ? this._addCheck({
        kind: "time",
        precision: null,
        message: e
      }) : this._addCheck({
        kind: "time",
        precision: typeof e?.precision > "u" ? null : e?.precision,
        ...y.errorUtil.errToObj(e?.message)
      });
    }
    duration(e) {
      return this._addCheck({ kind: "duration", ...y.errorUtil.errToObj(e) });
    }
    regex(e, r) {
      return this._addCheck({
        kind: "regex",
        regex: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    includes(e, r) {
      return this._addCheck({
        kind: "includes",
        value: e,
        position: r?.position,
        ...y.errorUtil.errToObj(r?.message)
      });
    }
    startsWith(e, r) {
      return this._addCheck({
        kind: "startsWith",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    endsWith(e, r) {
      return this._addCheck({
        kind: "endsWith",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    min(e, r) {
      return this._addCheck({
        kind: "min",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    max(e, r) {
      return this._addCheck({
        kind: "max",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    length(e, r) {
      return this._addCheck({
        kind: "length",
        value: e,
        ...y.errorUtil.errToObj(r)
      });
    }
    /**
     * Equivalent to `.min(1)`
     */
    nonempty(e) {
      return this.min(1, y.errorUtil.errToObj(e));
    }
    trim() {
      return new t({
        ...this._def,
        checks: [...this._def.checks, { kind: "trim" }]
      });
    }
    toLowerCase() {
      return new t({
        ...this._def,
        checks: [...this._def.checks, { kind: "toLowerCase" }]
      });
    }
    toUpperCase() {
      return new t({
        ...this._def,
        checks: [...this._def.checks, { kind: "toUpperCase" }]
      });
    }
    get isDatetime() {
      return !!this._def.checks.find((e) => e.kind === "datetime");
    }
    get isDate() {
      return !!this._def.checks.find((e) => e.kind === "date");
    }
    get isTime() {
      return !!this._def.checks.find((e) => e.kind === "time");
    }
    get isDuration() {
      return !!this._def.checks.find((e) => e.kind === "duration");
    }
    get isEmail() {
      return !!this._def.checks.find((e) => e.kind === "email");
    }
    get isURL() {
      return !!this._def.checks.find((e) => e.kind === "url");
    }
    get isEmoji() {
      return !!this._def.checks.find((e) => e.kind === "emoji");
    }
    get isUUID() {
      return !!this._def.checks.find((e) => e.kind === "uuid");
    }
    get isNANOID() {
      return !!this._def.checks.find((e) => e.kind === "nanoid");
    }
    get isCUID() {
      return !!this._def.checks.find((e) => e.kind === "cuid");
    }
    get isCUID2() {
      return !!this._def.checks.find((e) => e.kind === "cuid2");
    }
    get isULID() {
      return !!this._def.checks.find((e) => e.kind === "ulid");
    }
    get isIP() {
      return !!this._def.checks.find((e) => e.kind === "ip");
    }
    get isCIDR() {
      return !!this._def.checks.find((e) => e.kind === "cidr");
    }
    get isBase64() {
      return !!this._def.checks.find((e) => e.kind === "base64");
    }
    get isBase64url() {
      return !!this._def.checks.find((e) => e.kind === "base64url");
    }
    get minLength() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e;
    }
    get maxLength() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e;
    }
  };
  d.ZodString = J;
  J.create = (t) => {
    var e;
    return new J({
      checks: [],
      typeName: g.ZodString,
      coerce: (e = t?.coerce) !== null && e !== void 0 ? e : !1,
      ...x(t)
    });
  };
  function xa(t, e) {
    let r = (t.toString().split(".")[1] || "").length, n = (e.toString().split(".")[1] || "").length, s = r > n ? r : n, o = parseInt(t.toFixed(
    s).replace(".", "")), a = parseInt(e.toFixed(s).replace(".", ""));
    return o % a / Math.pow(10, s);
  }
  i(xa, "floatSafeRemainder");
  var te = class t extends v {
    static {
      i(this, "ZodNumber");
    }
    constructor() {
      super(...arguments), this.min = this.gte, this.max = this.lte, this.step = this.multipleOf;
    }
    _parse(e) {
      if (this._def.coerce && (e.data = Number(e.data)), this._getType(e) !== h.ZodParsedType.number) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.number,
          received: o.parsedType
        }), u.INVALID;
      }
      let n, s = new u.ParseStatus();
      for (let o of this._def.checks)
        o.kind === "int" ? h.util.isInteger(e.data) || (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: "integer",
          received: "float",
          message: o.message
        }), s.dirty()) : o.kind === "min" ? (o.inclusive ? e.data < o.value : e.data <= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_small,
          minimum: o.value,
          type: "number",
          inclusive: o.inclusive,
          exact: !1,
          message: o.message
        }), s.dirty()) : o.kind === "max" ? (o.inclusive ? e.data > o.value : e.data >= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_big,
          maximum: o.value,
          type: "number",
          inclusive: o.inclusive,
          exact: !1,
          message: o.message
        }), s.dirty()) : o.kind === "multipleOf" ? xa(e.data, o.value) !== 0 && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(n,
        {
          code: m.ZodIssueCode.not_multiple_of,
          multipleOf: o.value,
          message: o.message
        }), s.dirty()) : o.kind === "finite" ? Number.isFinite(e.data) || (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.not_finite,
          message: o.message
        }), s.dirty()) : h.util.assertNever(o);
      return { status: s.value, value: e.data };
    }
    gte(e, r) {
      return this.setLimit("min", e, !0, y.errorUtil.toString(r));
    }
    gt(e, r) {
      return this.setLimit("min", e, !1, y.errorUtil.toString(r));
    }
    lte(e, r) {
      return this.setLimit("max", e, !0, y.errorUtil.toString(r));
    }
    lt(e, r) {
      return this.setLimit("max", e, !1, y.errorUtil.toString(r));
    }
    setLimit(e, r, n, s) {
      return new t({
        ...this._def,
        checks: [
          ...this._def.checks,
          {
            kind: e,
            value: r,
            inclusive: n,
            message: y.errorUtil.toString(s)
          }
        ]
      });
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    int(e) {
      return this._addCheck({
        kind: "int",
        message: y.errorUtil.toString(e)
      });
    }
    positive(e) {
      return this._addCheck({
        kind: "min",
        value: 0,
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    negative(e) {
      return this._addCheck({
        kind: "max",
        value: 0,
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    nonpositive(e) {
      return this._addCheck({
        kind: "max",
        value: 0,
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    nonnegative(e) {
      return this._addCheck({
        kind: "min",
        value: 0,
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    multipleOf(e, r) {
      return this._addCheck({
        kind: "multipleOf",
        value: e,
        message: y.errorUtil.toString(r)
      });
    }
    finite(e) {
      return this._addCheck({
        kind: "finite",
        message: y.errorUtil.toString(e)
      });
    }
    safe(e) {
      return this._addCheck({
        kind: "min",
        inclusive: !0,
        value: Number.MIN_SAFE_INTEGER,
        message: y.errorUtil.toString(e)
      })._addCheck({
        kind: "max",
        inclusive: !0,
        value: Number.MAX_SAFE_INTEGER,
        message: y.errorUtil.toString(e)
      });
    }
    get minValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e;
    }
    get maxValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e;
    }
    get isInt() {
      return !!this._def.checks.find((e) => e.kind === "int" || e.kind === "multipleOf" && h.util.isInteger(e.value));
    }
    get isFinite() {
      let e = null, r = null;
      for (let n of this._def.checks) {
        if (n.kind === "finite" || n.kind === "int" || n.kind === "multipleOf")
          return !0;
        n.kind === "min" ? (r === null || n.value > r) && (r = n.value) : n.kind === "max" && (e === null || n.value < e) && (e = n.value);
      }
      return Number.isFinite(r) && Number.isFinite(e);
    }
  };
  d.ZodNumber = te;
  te.create = (t) => new te({
    checks: [],
    typeName: g.ZodNumber,
    coerce: t?.coerce || !1,
    ...x(t)
  });
  var re = class t extends v {
    static {
      i(this, "ZodBigInt");
    }
    constructor() {
      super(...arguments), this.min = this.gte, this.max = this.lte;
    }
    _parse(e) {
      if (this._def.coerce)
        try {
          e.data = BigInt(e.data);
        } catch {
          return this._getInvalidInput(e);
        }
      if (this._getType(e) !== h.ZodParsedType.bigint)
        return this._getInvalidInput(e);
      let n, s = new u.ParseStatus();
      for (let o of this._def.checks)
        o.kind === "min" ? (o.inclusive ? e.data < o.value : e.data <= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_small,
          type: "bigint",
          minimum: o.value,
          inclusive: o.inclusive,
          message: o.message
        }), s.dirty()) : o.kind === "max" ? (o.inclusive ? e.data > o.value : e.data >= o.value) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.too_big,
          type: "bigint",
          maximum: o.value,
          inclusive: o.inclusive,
          message: o.message
        }), s.dirty()) : o.kind === "multipleOf" ? e.data % o.value !== BigInt(0) && (n = this._getOrReturnCtx(e, n), (0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.not_multiple_of,
          multipleOf: o.value,
          message: o.message
        }), s.dirty()) : h.util.assertNever(o);
      return { status: s.value, value: e.data };
    }
    _getInvalidInput(e) {
      let r = this._getOrReturnCtx(e);
      return (0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.invalid_type,
        expected: h.ZodParsedType.bigint,
        received: r.parsedType
      }), u.INVALID;
    }
    gte(e, r) {
      return this.setLimit("min", e, !0, y.errorUtil.toString(r));
    }
    gt(e, r) {
      return this.setLimit("min", e, !1, y.errorUtil.toString(r));
    }
    lte(e, r) {
      return this.setLimit("max", e, !0, y.errorUtil.toString(r));
    }
    lt(e, r) {
      return this.setLimit("max", e, !1, y.errorUtil.toString(r));
    }
    setLimit(e, r, n, s) {
      return new t({
        ...this._def,
        checks: [
          ...this._def.checks,
          {
            kind: e,
            value: r,
            inclusive: n,
            message: y.errorUtil.toString(s)
          }
        ]
      });
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    positive(e) {
      return this._addCheck({
        kind: "min",
        value: BigInt(0),
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    negative(e) {
      return this._addCheck({
        kind: "max",
        value: BigInt(0),
        inclusive: !1,
        message: y.errorUtil.toString(e)
      });
    }
    nonpositive(e) {
      return this._addCheck({
        kind: "max",
        value: BigInt(0),
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    nonnegative(e) {
      return this._addCheck({
        kind: "min",
        value: BigInt(0),
        inclusive: !0,
        message: y.errorUtil.toString(e)
      });
    }
    multipleOf(e, r) {
      return this._addCheck({
        kind: "multipleOf",
        value: e,
        message: y.errorUtil.toString(r)
      });
    }
    get minValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e;
    }
    get maxValue() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e;
    }
  };
  d.ZodBigInt = re;
  re.create = (t) => {
    var e;
    return new re({
      checks: [],
      typeName: g.ZodBigInt,
      coerce: (e = t?.coerce) !== null && e !== void 0 ? e : !1,
      ...x(t)
    });
  };
  var ne = class extends v {
    static {
      i(this, "ZodBoolean");
    }
    _parse(e) {
      if (this._def.coerce && (e.data = !!e.data), this._getType(e) !== h.ZodParsedType.boolean) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.boolean,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodBoolean = ne;
  ne.create = (t) => new ne({
    typeName: g.ZodBoolean,
    coerce: t?.coerce || !1,
    ...x(t)
  });
  var se = class t extends v {
    static {
      i(this, "ZodDate");
    }
    _parse(e) {
      if (this._def.coerce && (e.data = new Date(e.data)), this._getType(e) !== h.ZodParsedType.date) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.date,
          received: o.parsedType
        }), u.INVALID;
      }
      if (isNaN(e.data.getTime())) {
        let o = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(o, {
          code: m.ZodIssueCode.invalid_date
        }), u.INVALID;
      }
      let n = new u.ParseStatus(), s;
      for (let o of this._def.checks)
        o.kind === "min" ? e.data.getTime() < o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.too_small,
          message: o.message,
          inclusive: !0,
          exact: !1,
          minimum: o.value,
          type: "date"
        }), n.dirty()) : o.kind === "max" ? e.data.getTime() > o.value && (s = this._getOrReturnCtx(e, s), (0, u.addIssueToContext)(s, {
          code: m.ZodIssueCode.too_big,
          message: o.message,
          inclusive: !0,
          exact: !1,
          maximum: o.value,
          type: "date"
        }), n.dirty()) : h.util.assertNever(o);
      return {
        status: n.value,
        value: new Date(e.data.getTime())
      };
    }
    _addCheck(e) {
      return new t({
        ...this._def,
        checks: [...this._def.checks, e]
      });
    }
    min(e, r) {
      return this._addCheck({
        kind: "min",
        value: e.getTime(),
        message: y.errorUtil.toString(r)
      });
    }
    max(e, r) {
      return this._addCheck({
        kind: "max",
        value: e.getTime(),
        message: y.errorUtil.toString(r)
      });
    }
    get minDate() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "min" && (e === null || r.value > e) && (e = r.value);
      return e != null ? new Date(e) : null;
    }
    get maxDate() {
      let e = null;
      for (let r of this._def.checks)
        r.kind === "max" && (e === null || r.value < e) && (e = r.value);
      return e != null ? new Date(e) : null;
    }
  };
  d.ZodDate = se;
  se.create = (t) => new se({
    checks: [],
    coerce: t?.coerce || !1,
    typeName: g.ZodDate,
    ...x(t)
  });
  var ve = class extends v {
    static {
      i(this, "ZodSymbol");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.symbol) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.symbol,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodSymbol = ve;
  ve.create = (t) => new ve({
    typeName: g.ZodSymbol,
    ...x(t)
  });
  var oe = class extends v {
    static {
      i(this, "ZodUndefined");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.undefined) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.undefined,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodUndefined = oe;
  oe.create = (t) => new oe({
    typeName: g.ZodUndefined,
    ...x(t)
  });
  var ie = class extends v {
    static {
      i(this, "ZodNull");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.null) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.null,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodNull = ie;
  ie.create = (t) => new ie({
    typeName: g.ZodNull,
    ...x(t)
  });
  var H = class extends v {
    static {
      i(this, "ZodAny");
    }
    constructor() {
      super(...arguments), this._any = !0;
    }
    _parse(e) {
      return (0, u.OK)(e.data);
    }
  };
  d.ZodAny = H;
  H.create = (t) => new H({
    typeName: g.ZodAny,
    ...x(t)
  });
  var W = class extends v {
    static {
      i(this, "ZodUnknown");
    }
    constructor() {
      super(...arguments), this._unknown = !0;
    }
    _parse(e) {
      return (0, u.OK)(e.data);
    }
  };
  d.ZodUnknown = W;
  W.create = (t) => new W({
    typeName: g.ZodUnknown,
    ...x(t)
  });
  var U = class extends v {
    static {
      i(this, "ZodNever");
    }
    _parse(e) {
      let r = this._getOrReturnCtx(e);
      return (0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.invalid_type,
        expected: h.ZodParsedType.never,
        received: r.parsedType
      }), u.INVALID;
    }
  };
  d.ZodNever = U;
  U.create = (t) => new U({
    typeName: g.ZodNever,
    ...x(t)
  });
  var _e = class extends v {
    static {
      i(this, "ZodVoid");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.undefined) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.void,
          received: n.parsedType
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
  };
  d.ZodVoid = _e;
  _e.create = (t) => new _e({
    typeName: g.ZodVoid,
    ...x(t)
  });
  var q = class t extends v {
    static {
      i(this, "ZodArray");
    }
    _parse(e) {
      let { ctx: r, status: n } = this._processInputParams(e), s = this._def;
      if (r.parsedType !== h.ZodParsedType.array)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.array,
          received: r.parsedType
        }), u.INVALID;
      if (s.exactLength !== null) {
        let a = r.data.length > s.exactLength.value, c = r.data.length < s.exactLength.value;
        (a || c) && ((0, u.addIssueToContext)(r, {
          code: a ? m.ZodIssueCode.too_big : m.ZodIssueCode.too_small,
          minimum: c ? s.exactLength.value : void 0,
          maximum: a ? s.exactLength.value : void 0,
          type: "array",
          inclusive: !0,
          exact: !0,
          message: s.exactLength.message
        }), n.dirty());
      }
      if (s.minLength !== null && r.data.length < s.minLength.value && ((0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.too_small,
        minimum: s.minLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: s.minLength.message
      }), n.dirty()), s.maxLength !== null && r.data.length > s.maxLength.value && ((0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.too_big,
        maximum: s.maxLength.value,
        type: "array",
        inclusive: !0,
        exact: !1,
        message: s.maxLength.message
      }), n.dirty()), r.common.async)
        return Promise.all([...r.data].map((a, c) => s.type._parseAsync(new D(r, a, r.path, c)))).then((a) => u.ParseStatus.mergeArray(n, a));
      let o = [...r.data].map((a, c) => s.type._parseSync(new D(r, a, r.path, c)));
      return u.ParseStatus.mergeArray(n, o);
    }
    get element() {
      return this._def.type;
    }
    min(e, r) {
      return new t({
        ...this._def,
        minLength: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    max(e, r) {
      return new t({
        ...this._def,
        maxLength: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    length(e, r) {
      return new t({
        ...this._def,
        exactLength: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    nonempty(e) {
      return this.min(1, e);
    }
  };
  d.ZodArray = q;
  q.create = (t, e) => new q({
    type: t,
    minLength: null,
    maxLength: null,
    exactLength: null,
    typeName: g.ZodArray,
    ...x(e)
  });
  function xe(t) {
    if (t instanceof j) {
      let e = {};
      for (let r in t.shape) {
        let n = t.shape[r];
        e[r] = M.create(xe(n));
      }
      return new j({
        ...t._def,
        shape: /* @__PURE__ */ i(() => e, "shape")
      });
    } else return t instanceof q ? new q({
      ...t._def,
      type: xe(t.element)
    }) : t instanceof M ? M.create(xe(t.unwrap())) : t instanceof F ? F.create(xe(t.unwrap())) : t instanceof V ? V.create(t.items.map((e) => xe(
    e))) : t;
  }
  i(xe, "deepPartialify");
  var j = class t extends v {
    static {
      i(this, "ZodObject");
    }
    constructor() {
      super(...arguments), this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend;
    }
    _getCached() {
      if (this._cached !== null)
        return this._cached;
      let e = this._def.shape(), r = h.util.objectKeys(e);
      return this._cached = { shape: e, keys: r };
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.object) {
        let p = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(p, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.object,
          received: p.parsedType
        }), u.INVALID;
      }
      let { status: n, ctx: s } = this._processInputParams(e), { shape: o, keys: a } = this._getCached(), c = [];
      if (!(this._def.catchall instanceof U && this._def.unknownKeys === "strip"))
        for (let p in s.data)
          a.includes(p) || c.push(p);
      let l = [];
      for (let p of a) {
        let f = o[p], b = s.data[p];
        l.push({
          key: { status: "valid", value: p },
          value: f._parse(new D(s, b, s.path, p)),
          alwaysSet: p in s.data
        });
      }
      if (this._def.catchall instanceof U) {
        let p = this._def.unknownKeys;
        if (p === "passthrough")
          for (let f of c)
            l.push({
              key: { status: "valid", value: f },
              value: { status: "valid", value: s.data[f] }
            });
        else if (p === "strict")
          c.length > 0 && ((0, u.addIssueToContext)(s, {
            code: m.ZodIssueCode.unrecognized_keys,
            keys: c
          }), n.dirty());
        else if (p !== "strip")
          throw new Error("Internal ZodObject error: invalid unknownKeys value.");
      } else {
        let p = this._def.catchall;
        for (let f of c) {
          let b = s.data[f];
          l.push({
            key: { status: "valid", value: f },
            value: p._parse(
              new D(s, b, s.path, f)
              //, ctx.child(key), value, getParsedType(value)
            ),
            alwaysSet: f in s.data
          });
        }
      }
      return s.common.async ? Promise.resolve().then(async () => {
        let p = [];
        for (let f of l) {
          let b = await f.key, w = await f.value;
          p.push({
            key: b,
            value: w,
            alwaysSet: f.alwaysSet
          });
        }
        return p;
      }).then((p) => u.ParseStatus.mergeObjectSync(n, p)) : u.ParseStatus.mergeObjectSync(n, l);
    }
    get shape() {
      return this._def.shape();
    }
    strict(e) {
      return y.errorUtil.errToObj, new t({
        ...this._def,
        unknownKeys: "strict",
        ...e !== void 0 ? {
          errorMap: /* @__PURE__ */ i((r, n) => {
            var s, o, a, c;
            let l = (a = (o = (s = this._def).errorMap) === null || o === void 0 ? void 0 : o.call(s, r, n).message) !== null && a !== void 0 ?
            a : n.defaultError;
            return r.code === "unrecognized_keys" ? {
              message: (c = y.errorUtil.errToObj(e).message) !== null && c !== void 0 ? c : l
            } : {
              message: l
            };
          }, "errorMap")
        } : {}
      });
    }
    strip() {
      return new t({
        ...this._def,
        unknownKeys: "strip"
      });
    }
    passthrough() {
      return new t({
        ...this._def,
        unknownKeys: "passthrough"
      });
    }
    // const AugmentFactory =
    //   <Def extends ZodObjectDef>(def: Def) =>
    //   <Augmentation extends ZodRawShape>(
    //     augmentation: Augmentation
    //   ): ZodObject<
    //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
    //     Def["unknownKeys"],
    //     Def["catchall"]
    //   > => {
    //     return new ZodObject({
    //       ...def,
    //       shape: () => ({
    //         ...def.shape(),
    //         ...augmentation,
    //       }),
    //     }) as any;
    //   };
    extend(e) {
      return new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => ({
          ...this._def.shape(),
          ...e
        }), "shape")
      });
    }
    /**
     * Prior to zod@1.0.12 there was a bug in the
     * inferred type of merged objects. Please
     * upgrade if you are experiencing issues.
     */
    merge(e) {
      return new t({
        unknownKeys: e._def.unknownKeys,
        catchall: e._def.catchall,
        shape: /* @__PURE__ */ i(() => ({
          ...this._def.shape(),
          ...e._def.shape()
        }), "shape"),
        typeName: g.ZodObject
      });
    }
    // merge<
    //   Incoming extends AnyZodObject,
    //   Augmentation extends Incoming["shape"],
    //   NewOutput extends {
    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
    //       ? Augmentation[k]["_output"]
    //       : k extends keyof Output
    //       ? Output[k]
    //       : never;
    //   },
    //   NewInput extends {
    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
    //       ? Augmentation[k]["_input"]
    //       : k extends keyof Input
    //       ? Input[k]
    //       : never;
    //   }
    // >(
    //   merging: Incoming
    // ): ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"],
    //   NewOutput,
    //   NewInput
    // > {
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    setKey(e, r) {
      return this.augment({ [e]: r });
    }
    // merge<Incoming extends AnyZodObject>(
    //   merging: Incoming
    // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
    // ZodObject<
    //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
    //   Incoming["_def"]["unknownKeys"],
    //   Incoming["_def"]["catchall"]
    // > {
    //   // const mergedShape = objectUtil.mergeShapes(
    //   //   this._def.shape(),
    //   //   merging._def.shape()
    //   // );
    //   const merged: any = new ZodObject({
    //     unknownKeys: merging._def.unknownKeys,
    //     catchall: merging._def.catchall,
    //     shape: () =>
    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
    //     typeName: ZodFirstPartyTypeKind.ZodObject,
    //   }) as any;
    //   return merged;
    // }
    catchall(e) {
      return new t({
        ...this._def,
        catchall: e
      });
    }
    pick(e) {
      let r = {};
      return h.util.objectKeys(e).forEach((n) => {
        e[n] && this.shape[n] && (r[n] = this.shape[n]);
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    omit(e) {
      let r = {};
      return h.util.objectKeys(this.shape).forEach((n) => {
        e[n] || (r[n] = this.shape[n]);
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    /**
     * @deprecated
     */
    deepPartial() {
      return xe(this);
    }
    partial(e) {
      let r = {};
      return h.util.objectKeys(this.shape).forEach((n) => {
        let s = this.shape[n];
        e && !e[n] ? r[n] = s : r[n] = s.optional();
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    required(e) {
      let r = {};
      return h.util.objectKeys(this.shape).forEach((n) => {
        if (e && !e[n])
          r[n] = this.shape[n];
        else {
          let o = this.shape[n];
          for (; o instanceof M; )
            o = o._def.innerType;
          r[n] = o;
        }
      }), new t({
        ...this._def,
        shape: /* @__PURE__ */ i(() => r, "shape")
      });
    }
    keyof() {
      return An(h.util.objectKeys(this.shape));
    }
  };
  d.ZodObject = j;
  j.create = (t, e) => new j({
    shape: /* @__PURE__ */ i(() => t, "shape"),
    unknownKeys: "strip",
    catchall: U.create(),
    typeName: g.ZodObject,
    ...x(e)
  });
  j.strictCreate = (t, e) => new j({
    shape: /* @__PURE__ */ i(() => t, "shape"),
    unknownKeys: "strict",
    catchall: U.create(),
    typeName: g.ZodObject,
    ...x(e)
  });
  j.lazycreate = (t, e) => new j({
    shape: t,
    unknownKeys: "strip",
    catchall: U.create(),
    typeName: g.ZodObject,
    ...x(e)
  });
  var ae = class extends v {
    static {
      i(this, "ZodUnion");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = this._def.options;
      function s(o) {
        for (let c of o)
          if (c.result.status === "valid")
            return c.result;
        for (let c of o)
          if (c.result.status === "dirty")
            return r.common.issues.push(...c.ctx.common.issues), c.result;
        let a = o.map((c) => new m.ZodError(c.ctx.common.issues));
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_union,
          unionErrors: a
        }), u.INVALID;
      }
      if (i(s, "handleResults"), r.common.async)
        return Promise.all(n.map(async (o) => {
          let a = {
            ...r,
            common: {
              ...r.common,
              issues: []
            },
            parent: null
          };
          return {
            result: await o._parseAsync({
              data: r.data,
              path: r.path,
              parent: a
            }),
            ctx: a
          };
        })).then(s);
      {
        let o, a = [];
        for (let l of n) {
          let p = {
            ...r,
            common: {
              ...r.common,
              issues: []
            },
            parent: null
          }, f = l._parseSync({
            data: r.data,
            path: r.path,
            parent: p
          });
          if (f.status === "valid")
            return f;
          f.status === "dirty" && !o && (o = { result: f, ctx: p }), p.common.issues.length && a.push(p.common.issues);
        }
        if (o)
          return r.common.issues.push(...o.ctx.common.issues), o.result;
        let c = a.map((l) => new m.ZodError(l));
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_union,
          unionErrors: c
        }), u.INVALID;
      }
    }
    get options() {
      return this._def.options;
    }
  };
  d.ZodUnion = ae;
  ae.create = (t, e) => new ae({
    options: t,
    typeName: g.ZodUnion,
    ...x(e)
  });
  var B = /* @__PURE__ */ i((t) => t instanceof de ? B(t.schema) : t instanceof Z ? B(t.innerType()) : t instanceof ue ? [t.value] : t instanceof
  le ? t.options : t instanceof pe ? h.util.objectValues(t.enum) : t instanceof fe ? B(t._def.innerType) : t instanceof oe ? [void 0] : t instanceof
  ie ? [null] : t instanceof M ? [void 0, ...B(t.unwrap())] : t instanceof F ? [null, ...B(t.unwrap())] : t instanceof Le || t instanceof he ?
  B(t.unwrap()) : t instanceof me ? B(t._def.innerType) : [], "getDiscriminator"), dt = class t extends v {
    static {
      i(this, "ZodDiscriminatedUnion");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      if (r.parsedType !== h.ZodParsedType.object)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.object,
          received: r.parsedType
        }), u.INVALID;
      let n = this.discriminator, s = r.data[n], o = this.optionsMap.get(s);
      return o ? r.common.async ? o._parseAsync({
        data: r.data,
        path: r.path,
        parent: r
      }) : o._parseSync({
        data: r.data,
        path: r.path,
        parent: r
      }) : ((0, u.addIssueToContext)(r, {
        code: m.ZodIssueCode.invalid_union_discriminator,
        options: Array.from(this.optionsMap.keys()),
        path: [n]
      }), u.INVALID);
    }
    get discriminator() {
      return this._def.discriminator;
    }
    get options() {
      return this._def.options;
    }
    get optionsMap() {
      return this._def.optionsMap;
    }
    /**
     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.
     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must
     * have a different value for each object in the union.
     * @param discriminator the name of the discriminator property
     * @param types an array of object schemas
     * @param params
     */
    static create(e, r, n) {
      let s = /* @__PURE__ */ new Map();
      for (let o of r) {
        let a = B(o.shape[e]);
        if (!a.length)
          throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);
        for (let c of a) {
          if (s.has(c))
            throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(c)}`);
          s.set(c, o);
        }
      }
      return new t({
        typeName: g.ZodDiscriminatedUnion,
        discriminator: e,
        options: r,
        optionsMap: s,
        ...x(n)
      });
    }
  };
  d.ZodDiscriminatedUnion = dt;
  function Qt(t, e) {
    let r = (0, h.getParsedType)(t), n = (0, h.getParsedType)(e);
    if (t === e)
      return { valid: !0, data: t };
    if (r === h.ZodParsedType.object && n === h.ZodParsedType.object) {
      let s = h.util.objectKeys(e), o = h.util.objectKeys(t).filter((c) => s.indexOf(c) !== -1), a = { ...t, ...e };
      for (let c of o) {
        let l = Qt(t[c], e[c]);
        if (!l.valid)
          return { valid: !1 };
        a[c] = l.data;
      }
      return { valid: !0, data: a };
    } else if (r === h.ZodParsedType.array && n === h.ZodParsedType.array) {
      if (t.length !== e.length)
        return { valid: !1 };
      let s = [];
      for (let o = 0; o < t.length; o++) {
        let a = t[o], c = e[o], l = Qt(a, c);
        if (!l.valid)
          return { valid: !1 };
        s.push(l.data);
      }
      return { valid: !0, data: s };
    } else return r === h.ZodParsedType.date && n === h.ZodParsedType.date && +t == +e ? { valid: !0, data: t } : { valid: !1 };
  }
  i(Qt, "mergeValues");
  var ce = class extends v {
    static {
      i(this, "ZodIntersection");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e), s = /* @__PURE__ */ i((o, a) => {
        if ((0, u.isAborted)(o) || (0, u.isAborted)(a))
          return u.INVALID;
        let c = Qt(o.value, a.value);
        return c.valid ? (((0, u.isDirty)(o) || (0, u.isDirty)(a)) && r.dirty(), { status: r.value, value: c.data }) : ((0, u.addIssueToContext)(
        n, {
          code: m.ZodIssueCode.invalid_intersection_types
        }), u.INVALID);
      }, "handleParsed");
      return n.common.async ? Promise.all([
        this._def.left._parseAsync({
          data: n.data,
          path: n.path,
          parent: n
        }),
        this._def.right._parseAsync({
          data: n.data,
          path: n.path,
          parent: n
        })
      ]).then(([o, a]) => s(o, a)) : s(this._def.left._parseSync({
        data: n.data,
        path: n.path,
        parent: n
      }), this._def.right._parseSync({
        data: n.data,
        path: n.path,
        parent: n
      }));
    }
  };
  d.ZodIntersection = ce;
  ce.create = (t, e, r) => new ce({
    left: t,
    right: e,
    typeName: g.ZodIntersection,
    ...x(r)
  });
  var V = class t extends v {
    static {
      i(this, "ZodTuple");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.array)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.array,
          received: n.parsedType
        }), u.INVALID;
      if (n.data.length < this._def.items.length)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.too_small,
          minimum: this._def.items.length,
          inclusive: !0,
          exact: !1,
          type: "array"
        }), u.INVALID;
      !this._def.rest && n.data.length > this._def.items.length && ((0, u.addIssueToContext)(n, {
        code: m.ZodIssueCode.too_big,
        maximum: this._def.items.length,
        inclusive: !0,
        exact: !1,
        type: "array"
      }), r.dirty());
      let o = [...n.data].map((a, c) => {
        let l = this._def.items[c] || this._def.rest;
        return l ? l._parse(new D(n, a, n.path, c)) : null;
      }).filter((a) => !!a);
      return n.common.async ? Promise.all(o).then((a) => u.ParseStatus.mergeArray(r, a)) : u.ParseStatus.mergeArray(r, o);
    }
    get items() {
      return this._def.items;
    }
    rest(e) {
      return new t({
        ...this._def,
        rest: e
      });
    }
  };
  d.ZodTuple = V;
  V.create = (t, e) => {
    if (!Array.isArray(t))
      throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
    return new V({
      items: t,
      typeName: g.ZodTuple,
      rest: null,
      ...x(e)
    });
  };
  var ut = class t extends v {
    static {
      i(this, "ZodRecord");
    }
    get keySchema() {
      return this._def.keyType;
    }
    get valueSchema() {
      return this._def.valueType;
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.object)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.object,
          received: n.parsedType
        }), u.INVALID;
      let s = [], o = this._def.keyType, a = this._def.valueType;
      for (let c in n.data)
        s.push({
          key: o._parse(new D(n, c, n.path, c)),
          value: a._parse(new D(n, n.data[c], n.path, c)),
          alwaysSet: c in n.data
        });
      return n.common.async ? u.ParseStatus.mergeObjectAsync(r, s) : u.ParseStatus.mergeObjectSync(r, s);
    }
    get element() {
      return this._def.valueType;
    }
    static create(e, r, n) {
      return r instanceof v ? new t({
        keyType: e,
        valueType: r,
        typeName: g.ZodRecord,
        ...x(n)
      }) : new t({
        keyType: J.create(),
        valueType: e,
        typeName: g.ZodRecord,
        ...x(r)
      });
    }
  };
  d.ZodRecord = ut;
  var we = class extends v {
    static {
      i(this, "ZodMap");
    }
    get keySchema() {
      return this._def.keyType;
    }
    get valueSchema() {
      return this._def.valueType;
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.map)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.map,
          received: n.parsedType
        }), u.INVALID;
      let s = this._def.keyType, o = this._def.valueType, a = [...n.data.entries()].map(([c, l], p) => ({
        key: s._parse(new D(n, c, n.path, [p, "key"])),
        value: o._parse(new D(n, l, n.path, [p, "value"]))
      }));
      if (n.common.async) {
        let c = /* @__PURE__ */ new Map();
        return Promise.resolve().then(async () => {
          for (let l of a) {
            let p = await l.key, f = await l.value;
            if (p.status === "aborted" || f.status === "aborted")
              return u.INVALID;
            (p.status === "dirty" || f.status === "dirty") && r.dirty(), c.set(p.value, f.value);
          }
          return { status: r.value, value: c };
        });
      } else {
        let c = /* @__PURE__ */ new Map();
        for (let l of a) {
          let p = l.key, f = l.value;
          if (p.status === "aborted" || f.status === "aborted")
            return u.INVALID;
          (p.status === "dirty" || f.status === "dirty") && r.dirty(), c.set(p.value, f.value);
        }
        return { status: r.value, value: c };
      }
    }
  };
  d.ZodMap = we;
  we.create = (t, e, r) => new we({
    valueType: e,
    keyType: t,
    typeName: g.ZodMap,
    ...x(r)
  });
  var ke = class t extends v {
    static {
      i(this, "ZodSet");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.parsedType !== h.ZodParsedType.set)
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.set,
          received: n.parsedType
        }), u.INVALID;
      let s = this._def;
      s.minSize !== null && n.data.size < s.minSize.value && ((0, u.addIssueToContext)(n, {
        code: m.ZodIssueCode.too_small,
        minimum: s.minSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: s.minSize.message
      }), r.dirty()), s.maxSize !== null && n.data.size > s.maxSize.value && ((0, u.addIssueToContext)(n, {
        code: m.ZodIssueCode.too_big,
        maximum: s.maxSize.value,
        type: "set",
        inclusive: !0,
        exact: !1,
        message: s.maxSize.message
      }), r.dirty());
      let o = this._def.valueType;
      function a(l) {
        let p = /* @__PURE__ */ new Set();
        for (let f of l) {
          if (f.status === "aborted")
            return u.INVALID;
          f.status === "dirty" && r.dirty(), p.add(f.value);
        }
        return { status: r.value, value: p };
      }
      i(a, "finalizeSet");
      let c = [...n.data.values()].map((l, p) => o._parse(new D(n, l, n.path, p)));
      return n.common.async ? Promise.all(c).then((l) => a(l)) : a(c);
    }
    min(e, r) {
      return new t({
        ...this._def,
        minSize: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    max(e, r) {
      return new t({
        ...this._def,
        maxSize: { value: e, message: y.errorUtil.toString(r) }
      });
    }
    size(e, r) {
      return this.min(e, r).max(e, r);
    }
    nonempty(e) {
      return this.min(1, e);
    }
  };
  d.ZodSet = ke;
  ke.create = (t, e) => new ke({
    valueType: t,
    minSize: null,
    maxSize: null,
    typeName: g.ZodSet,
    ...x(e)
  });
  var lt = class t extends v {
    static {
      i(this, "ZodFunction");
    }
    constructor() {
      super(...arguments), this.validate = this.implement;
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      if (r.parsedType !== h.ZodParsedType.function)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.function,
          received: r.parsedType
        }), u.INVALID;
      function n(c, l) {
        return (0, u.makeIssue)({
          data: c,
          path: r.path,
          errorMaps: [
            r.common.contextualErrorMap,
            r.schemaErrorMap,
            (0, at.getErrorMap)(),
            at.defaultErrorMap
          ].filter((p) => !!p),
          issueData: {
            code: m.ZodIssueCode.invalid_arguments,
            argumentsError: l
          }
        });
      }
      i(n, "makeArgsIssue");
      function s(c, l) {
        return (0, u.makeIssue)({
          data: c,
          path: r.path,
          errorMaps: [
            r.common.contextualErrorMap,
            r.schemaErrorMap,
            (0, at.getErrorMap)(),
            at.defaultErrorMap
          ].filter((p) => !!p),
          issueData: {
            code: m.ZodIssueCode.invalid_return_type,
            returnTypeError: l
          }
        });
      }
      i(s, "makeReturnsIssue");
      let o = { errorMap: r.common.contextualErrorMap }, a = r.data;
      if (this._def.returns instanceof Y) {
        let c = this;
        return (0, u.OK)(async function(...l) {
          let p = new m.ZodError([]), f = await c._def.args.parseAsync(l, o).catch((_) => {
            throw p.addIssue(n(l, _)), p;
          }), b = await Reflect.apply(a, this, f);
          return await c._def.returns._def.type.parseAsync(b, o).catch((_) => {
            throw p.addIssue(s(b, _)), p;
          });
        });
      } else {
        let c = this;
        return (0, u.OK)(function(...l) {
          let p = c._def.args.safeParse(l, o);
          if (!p.success)
            throw new m.ZodError([n(l, p.error)]);
          let f = Reflect.apply(a, this, p.data), b = c._def.returns.safeParse(f, o);
          if (!b.success)
            throw new m.ZodError([s(f, b.error)]);
          return b.data;
        });
      }
    }
    parameters() {
      return this._def.args;
    }
    returnType() {
      return this._def.returns;
    }
    args(...e) {
      return new t({
        ...this._def,
        args: V.create(e).rest(W.create())
      });
    }
    returns(e) {
      return new t({
        ...this._def,
        returns: e
      });
    }
    implement(e) {
      return this.parse(e);
    }
    strictImplement(e) {
      return this.parse(e);
    }
    static create(e, r, n) {
      return new t({
        args: e || V.create([]).rest(W.create()),
        returns: r || W.create(),
        typeName: g.ZodFunction,
        ...x(n)
      });
    }
  };
  d.ZodFunction = lt;
  var de = class extends v {
    static {
      i(this, "ZodLazy");
    }
    get schema() {
      return this._def.getter();
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      return this._def.getter()._parse({ data: r.data, path: r.path, parent: r });
    }
  };
  d.ZodLazy = de;
  de.create = (t, e) => new de({
    getter: t,
    typeName: g.ZodLazy,
    ...x(e)
  });
  var ue = class extends v {
    static {
      i(this, "ZodLiteral");
    }
    _parse(e) {
      if (e.data !== this._def.value) {
        let r = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(r, {
          received: r.data,
          code: m.ZodIssueCode.invalid_literal,
          expected: this._def.value
        }), u.INVALID;
      }
      return { status: "valid", value: e.data };
    }
    get value() {
      return this._def.value;
    }
  };
  d.ZodLiteral = ue;
  ue.create = (t, e) => new ue({
    value: t,
    typeName: g.ZodLiteral,
    ...x(e)
  });
  function An(t, e) {
    return new le({
      values: t,
      typeName: g.ZodEnum,
      ...x(e)
    });
  }
  i(An, "createZodEnum");
  var le = class t extends v {
    static {
      i(this, "ZodEnum");
    }
    constructor() {
      super(...arguments), Me.set(this, void 0);
    }
    _parse(e) {
      if (typeof e.data != "string") {
        let r = this._getOrReturnCtx(e), n = this._def.values;
        return (0, u.addIssueToContext)(r, {
          expected: h.util.joinValues(n),
          received: r.parsedType,
          code: m.ZodIssueCode.invalid_type
        }), u.INVALID;
      }
      if (ct(this, Me, "f") || Sn(this, Me, new Set(this._def.values), "f"), !ct(this, Me, "f").has(e.data)) {
        let r = this._getOrReturnCtx(e), n = this._def.values;
        return (0, u.addIssueToContext)(r, {
          received: r.data,
          code: m.ZodIssueCode.invalid_enum_value,
          options: n
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
    get options() {
      return this._def.values;
    }
    get enum() {
      let e = {};
      for (let r of this._def.values)
        e[r] = r;
      return e;
    }
    get Values() {
      let e = {};
      for (let r of this._def.values)
        e[r] = r;
      return e;
    }
    get Enum() {
      let e = {};
      for (let r of this._def.values)
        e[r] = r;
      return e;
    }
    extract(e, r = this._def) {
      return t.create(e, {
        ...this._def,
        ...r
      });
    }
    exclude(e, r = this._def) {
      return t.create(this.options.filter((n) => !e.includes(n)), {
        ...this._def,
        ...r
      });
    }
  };
  d.ZodEnum = le;
  Me = /* @__PURE__ */ new WeakMap();
  le.create = An;
  var pe = class extends v {
    static {
      i(this, "ZodNativeEnum");
    }
    constructor() {
      super(...arguments), De.set(this, void 0);
    }
    _parse(e) {
      let r = h.util.getValidEnumValues(this._def.values), n = this._getOrReturnCtx(e);
      if (n.parsedType !== h.ZodParsedType.string && n.parsedType !== h.ZodParsedType.number) {
        let s = h.util.objectValues(r);
        return (0, u.addIssueToContext)(n, {
          expected: h.util.joinValues(s),
          received: n.parsedType,
          code: m.ZodIssueCode.invalid_type
        }), u.INVALID;
      }
      if (ct(this, De, "f") || Sn(this, De, new Set(h.util.getValidEnumValues(this._def.values)), "f"), !ct(this, De, "f").has(e.data)) {
        let s = h.util.objectValues(r);
        return (0, u.addIssueToContext)(n, {
          received: n.data,
          code: m.ZodIssueCode.invalid_enum_value,
          options: s
        }), u.INVALID;
      }
      return (0, u.OK)(e.data);
    }
    get enum() {
      return this._def.values;
    }
  };
  d.ZodNativeEnum = pe;
  De = /* @__PURE__ */ new WeakMap();
  pe.create = (t, e) => new pe({
    values: t,
    typeName: g.ZodNativeEnum,
    ...x(e)
  });
  var Y = class extends v {
    static {
      i(this, "ZodPromise");
    }
    unwrap() {
      return this._def.type;
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e);
      if (r.parsedType !== h.ZodParsedType.promise && r.common.async === !1)
        return (0, u.addIssueToContext)(r, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.promise,
          received: r.parsedType
        }), u.INVALID;
      let n = r.parsedType === h.ZodParsedType.promise ? r.data : Promise.resolve(r.data);
      return (0, u.OK)(n.then((s) => this._def.type.parseAsync(s, {
        path: r.path,
        errorMap: r.common.contextualErrorMap
      })));
    }
  };
  d.ZodPromise = Y;
  Y.create = (t, e) => new Y({
    type: t,
    typeName: g.ZodPromise,
    ...x(e)
  });
  var Z = class extends v {
    static {
      i(this, "ZodEffects");
    }
    innerType() {
      return this._def.schema;
    }
    sourceType() {
      return this._def.schema._def.typeName === g.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e), s = this._def.effect || null, o = {
        addIssue: /* @__PURE__ */ i((a) => {
          (0, u.addIssueToContext)(n, a), a.fatal ? r.abort() : r.dirty();
        }, "addIssue"),
        get path() {
          return n.path;
        }
      };
      if (o.addIssue = o.addIssue.bind(o), s.type === "preprocess") {
        let a = s.transform(n.data, o);
        if (n.common.async)
          return Promise.resolve(a).then(async (c) => {
            if (r.value === "aborted")
              return u.INVALID;
            let l = await this._def.schema._parseAsync({
              data: c,
              path: n.path,
              parent: n
            });
            return l.status === "aborted" ? u.INVALID : l.status === "dirty" || r.value === "dirty" ? (0, u.DIRTY)(l.value) : l;
          });
        {
          if (r.value === "aborted")
            return u.INVALID;
          let c = this._def.schema._parseSync({
            data: a,
            path: n.path,
            parent: n
          });
          return c.status === "aborted" ? u.INVALID : c.status === "dirty" || r.value === "dirty" ? (0, u.DIRTY)(c.value) : c;
        }
      }
      if (s.type === "refinement") {
        let a = /* @__PURE__ */ i((c) => {
          let l = s.refinement(c, o);
          if (n.common.async)
            return Promise.resolve(l);
          if (l instanceof Promise)
            throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
          return c;
        }, "executeRefinement");
        if (n.common.async === !1) {
          let c = this._def.schema._parseSync({
            data: n.data,
            path: n.path,
            parent: n
          });
          return c.status === "aborted" ? u.INVALID : (c.status === "dirty" && r.dirty(), a(c.value), { status: r.value, value: c.value });
        } else
          return this._def.schema._parseAsync({ data: n.data, path: n.path, parent: n }).then((c) => c.status === "aborted" ? u.INVALID : (c.
          status === "dirty" && r.dirty(), a(c.value).then(() => ({ status: r.value, value: c.value }))));
      }
      if (s.type === "transform")
        if (n.common.async === !1) {
          let a = this._def.schema._parseSync({
            data: n.data,
            path: n.path,
            parent: n
          });
          if (!(0, u.isValid)(a))
            return a;
          let c = s.transform(a.value, o);
          if (c instanceof Promise)
            throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
          return { status: r.value, value: c };
        } else
          return this._def.schema._parseAsync({ data: n.data, path: n.path, parent: n }).then((a) => (0, u.isValid)(a) ? Promise.resolve(s.transform(
          a.value, o)).then((c) => ({ status: r.value, value: c })) : a);
      h.util.assertNever(s);
    }
  };
  d.ZodEffects = Z;
  d.ZodTransformer = Z;
  Z.create = (t, e, r) => new Z({
    schema: t,
    typeName: g.ZodEffects,
    effect: e,
    ...x(r)
  });
  Z.createWithPreprocess = (t, e, r) => new Z({
    schema: e,
    effect: { type: "preprocess", transform: t },
    typeName: g.ZodEffects,
    ...x(r)
  });
  var M = class extends v {
    static {
      i(this, "ZodOptional");
    }
    _parse(e) {
      return this._getType(e) === h.ZodParsedType.undefined ? (0, u.OK)(void 0) : this._def.innerType._parse(e);
    }
    unwrap() {
      return this._def.innerType;
    }
  };
  d.ZodOptional = M;
  M.create = (t, e) => new M({
    innerType: t,
    typeName: g.ZodOptional,
    ...x(e)
  });
  var F = class extends v {
    static {
      i(this, "ZodNullable");
    }
    _parse(e) {
      return this._getType(e) === h.ZodParsedType.null ? (0, u.OK)(null) : this._def.innerType._parse(e);
    }
    unwrap() {
      return this._def.innerType;
    }
  };
  d.ZodNullable = F;
  F.create = (t, e) => new F({
    innerType: t,
    typeName: g.ZodNullable,
    ...x(e)
  });
  var fe = class extends v {
    static {
      i(this, "ZodDefault");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = r.data;
      return r.parsedType === h.ZodParsedType.undefined && (n = this._def.defaultValue()), this._def.innerType._parse({
        data: n,
        path: r.path,
        parent: r
      });
    }
    removeDefault() {
      return this._def.innerType;
    }
  };
  d.ZodDefault = fe;
  fe.create = (t, e) => new fe({
    innerType: t,
    typeName: g.ZodDefault,
    defaultValue: typeof e.default == "function" ? e.default : () => e.default,
    ...x(e)
  });
  var me = class extends v {
    static {
      i(this, "ZodCatch");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = {
        ...r,
        common: {
          ...r.common,
          issues: []
        }
      }, s = this._def.innerType._parse({
        data: n.data,
        path: n.path,
        parent: {
          ...n
        }
      });
      return (0, u.isAsync)(s) ? s.then((o) => ({
        status: "valid",
        value: o.status === "valid" ? o.value : this._def.catchValue({
          get error() {
            return new m.ZodError(n.common.issues);
          },
          input: n.data
        })
      })) : {
        status: "valid",
        value: s.status === "valid" ? s.value : this._def.catchValue({
          get error() {
            return new m.ZodError(n.common.issues);
          },
          input: n.data
        })
      };
    }
    removeCatch() {
      return this._def.innerType;
    }
  };
  d.ZodCatch = me;
  me.create = (t, e) => new me({
    innerType: t,
    typeName: g.ZodCatch,
    catchValue: typeof e.catch == "function" ? e.catch : () => e.catch,
    ...x(e)
  });
  var Te = class extends v {
    static {
      i(this, "ZodNaN");
    }
    _parse(e) {
      if (this._getType(e) !== h.ZodParsedType.nan) {
        let n = this._getOrReturnCtx(e);
        return (0, u.addIssueToContext)(n, {
          code: m.ZodIssueCode.invalid_type,
          expected: h.ZodParsedType.nan,
          received: n.parsedType
        }), u.INVALID;
      }
      return { status: "valid", value: e.data };
    }
  };
  d.ZodNaN = Te;
  Te.create = (t) => new Te({
    typeName: g.ZodNaN,
    ...x(t)
  });
  d.BRAND = Symbol("zod_brand");
  var Le = class extends v {
    static {
      i(this, "ZodBranded");
    }
    _parse(e) {
      let { ctx: r } = this._processInputParams(e), n = r.data;
      return this._def.type._parse({
        data: n,
        path: r.path,
        parent: r
      });
    }
    unwrap() {
      return this._def.type;
    }
  };
  d.ZodBranded = Le;
  var Ue = class t extends v {
    static {
      i(this, "ZodPipeline");
    }
    _parse(e) {
      let { status: r, ctx: n } = this._processInputParams(e);
      if (n.common.async)
        return (/* @__PURE__ */ i(async () => {
          let o = await this._def.in._parseAsync({
            data: n.data,
            path: n.path,
            parent: n
          });
          return o.status === "aborted" ? u.INVALID : o.status === "dirty" ? (r.dirty(), (0, u.DIRTY)(o.value)) : this._def.out._parseAsync(
          {
            data: o.value,
            path: n.path,
            parent: n
          });
        }, "handleAsync"))();
      {
        let s = this._def.in._parseSync({
          data: n.data,
          path: n.path,
          parent: n
        });
        return s.status === "aborted" ? u.INVALID : s.status === "dirty" ? (r.dirty(), {
          status: "dirty",
          value: s.value
        }) : this._def.out._parseSync({
          data: s.value,
          path: n.path,
          parent: n
        });
      }
    }
    static create(e, r) {
      return new t({
        in: e,
        out: r,
        typeName: g.ZodPipeline
      });
    }
  };
  d.ZodPipeline = Ue;
  var he = class extends v {
    static {
      i(this, "ZodReadonly");
    }
    _parse(e) {
      let r = this._def.innerType._parse(e), n = /* @__PURE__ */ i((s) => ((0, u.isValid)(s) && (s.value = Object.freeze(s.value)), s), "fre\
eze");
      return (0, u.isAsync)(r) ? r.then((s) => n(s)) : n(r);
    }
    unwrap() {
      return this._def.innerType;
    }
  };
  d.ZodReadonly = he;
  he.create = (t, e) => new he({
    innerType: t,
    typeName: g.ZodReadonly,
    ...x(e)
  });
  function In(t, e) {
    let r = typeof t == "function" ? t(e) : typeof t == "string" ? { message: t } : t;
    return typeof r == "string" ? { message: r } : r;
  }
  i(In, "cleanParams");
  function On(t, e = {}, r) {
    return t ? H.create().superRefine((n, s) => {
      var o, a;
      let c = t(n);
      if (c instanceof Promise)
        return c.then((l) => {
          var p, f;
          if (!l) {
            let b = In(e, n), w = (f = (p = b.fatal) !== null && p !== void 0 ? p : r) !== null && f !== void 0 ? f : !0;
            s.addIssue({ code: "custom", ...b, fatal: w });
          }
        });
      if (!c) {
        let l = In(e, n), p = (a = (o = l.fatal) !== null && o !== void 0 ? o : r) !== null && a !== void 0 ? a : !0;
        s.addIssue({ code: "custom", ...l, fatal: p });
      }
    }) : H.create();
  }
  i(On, "custom");
  d.custom = On;
  d.late = {
    object: j.lazycreate
  };
  var g;
  (function(t) {
    t.ZodString = "ZodString", t.ZodNumber = "ZodNumber", t.ZodNaN = "ZodNaN", t.ZodBigInt = "ZodBigInt", t.ZodBoolean = "ZodBoolean", t.ZodDate =
    "ZodDate", t.ZodSymbol = "ZodSymbol", t.ZodUndefined = "ZodUndefined", t.ZodNull = "ZodNull", t.ZodAny = "ZodAny", t.ZodUnknown = "ZodUn\
known", t.ZodNever = "ZodNever", t.ZodVoid = "ZodVoid", t.ZodArray = "ZodArray", t.ZodObject = "ZodObject", t.ZodUnion = "ZodUnion", t.ZodDiscriminatedUnion =
    "ZodDiscriminatedUnion", t.ZodIntersection = "ZodIntersection", t.ZodTuple = "ZodTuple", t.ZodRecord = "ZodRecord", t.ZodMap = "ZodMap",
    t.ZodSet = "ZodSet", t.ZodFunction = "ZodFunction", t.ZodLazy = "ZodLazy", t.ZodLiteral = "ZodLiteral", t.ZodEnum = "ZodEnum", t.ZodEffects =
    "ZodEffects", t.ZodNativeEnum = "ZodNativeEnum", t.ZodOptional = "ZodOptional", t.ZodNullable = "ZodNullable", t.ZodDefault = "ZodDefaul\
t", t.ZodCatch = "ZodCatch", t.ZodPromise = "ZodPromise", t.ZodBranded = "ZodBranded", t.ZodPipeline = "ZodPipeline", t.ZodReadonly = "ZodRe\
adonly";
  })(g || (d.ZodFirstPartyTypeKind = g = {}));
  var va = /* @__PURE__ */ i((t, e = {
    message: `Input not instance of ${t.name}`
  }) => On((r) => r instanceof t, e), "instanceOfType");
  d.instanceof = va;
  var jn = J.create;
  d.string = jn;
  var Rn = te.create;
  d.number = Rn;
  var _a = Te.create;
  d.nan = _a;
  var wa = re.create;
  d.bigint = wa;
  var Nn = ne.create;
  d.boolean = Nn;
  var ka = se.create;
  d.date = ka;
  var Ta = ve.create;
  d.symbol = Ta;
  var Ia = oe.create;
  d.undefined = Ia;
  var Sa = ie.create;
  d.null = Sa;
  var Ea = H.create;
  d.any = Ea;
  var Ca = W.create;
  d.unknown = Ca;
  var Pa = U.create;
  d.never = Pa;
  var Aa = _e.create;
  d.void = Aa;
  var Oa = q.create;
  d.array = Oa;
  var ja = j.create;
  d.object = ja;
  var Ra = j.strictCreate;
  d.strictObject = Ra;
  var Na = ae.create;
  d.union = Na;
  var Za = dt.create;
  d.discriminatedUnion = Za;
  var Ma = ce.create;
  d.intersection = Ma;
  var Da = V.create;
  d.tuple = Da;
  var La = ut.create;
  d.record = La;
  var Ua = we.create;
  d.map = Ua;
  var $a = ke.create;
  d.set = $a;
  var Va = lt.create;
  d.function = Va;
  var Fa = de.create;
  d.lazy = Fa;
  var Ba = ue.create;
  d.literal = Ba;
  var Wa = le.create;
  d.enum = Wa;
  var qa = pe.create;
  d.nativeEnum = qa;
  var Ga = Y.create;
  d.promise = Ga;
  var Zn = Z.create;
  d.effect = Zn;
  d.transformer = Zn;
  var za = M.create;
  d.optional = za;
  var Ka = F.create;
  d.nullable = Ka;
  var Ja = Z.createWithPreprocess;
  d.preprocess = Ja;
  var Ha = Ue.create;
  d.pipeline = Ha;
  var Ya = /* @__PURE__ */ i(() => jn().optional(), "ostring");
  d.ostring = Ya;
  var Xa = /* @__PURE__ */ i(() => Rn().optional(), "onumber");
  d.onumber = Xa;
  var Qa = /* @__PURE__ */ i(() => Nn().optional(), "oboolean");
  d.oboolean = Qa;
  d.coerce = {
    string: /* @__PURE__ */ i((t) => J.create({ ...t, coerce: !0 }), "string"),
    number: /* @__PURE__ */ i((t) => te.create({ ...t, coerce: !0 }), "number"),
    boolean: /* @__PURE__ */ i((t) => ne.create({
      ...t,
      coerce: !0
    }), "boolean"),
    bigint: /* @__PURE__ */ i((t) => re.create({ ...t, coerce: !0 }), "bigint"),
    date: /* @__PURE__ */ i((t) => se.create({ ...t, coerce: !0 }), "date")
  };
  d.NEVER = u.INVALID;
});

// ../node_modules/zod/lib/external.js
var er = I((L) => {
  "use strict";
  var ec = L && L.__createBinding || (Object.create ? function(t, e, r, n) {
    n === void 0 && (n = r);
    var s = Object.getOwnPropertyDescriptor(e, r);
    (!s || ("get" in s ? !e.__esModule : s.writable || s.configurable)) && (s = { enumerable: !0, get: /* @__PURE__ */ i(function() {
      return e[r];
    }, "get") }), Object.defineProperty(t, n, s);
  } : function(t, e, r, n) {
    n === void 0 && (n = r), t[n] = e[r];
  }), Ie = L && L.__exportStar || function(t, e) {
    for (var r in t) r !== "default" && !Object.prototype.hasOwnProperty.call(e, r) && ec(e, t, r);
  };
  Object.defineProperty(L, "__esModule", { value: !0 });
  Ie(ot(), L);
  Ie(Yt(), L);
  Ie(_n(), L);
  Ie(Ne(), L);
  Ie(Mn(), L);
  Ie(st(), L);
});

// ../node_modules/zod/lib/index.js
var Un = I((R) => {
  "use strict";
  var Dn = R && R.__createBinding || (Object.create ? function(t, e, r, n) {
    n === void 0 && (n = r);
    var s = Object.getOwnPropertyDescriptor(e, r);
    (!s || ("get" in s ? !e.__esModule : s.writable || s.configurable)) && (s = { enumerable: !0, get: /* @__PURE__ */ i(function() {
      return e[r];
    }, "get") }), Object.defineProperty(t, n, s);
  } : function(t, e, r, n) {
    n === void 0 && (n = r), t[n] = e[r];
  }), tc = R && R.__setModuleDefault || (Object.create ? function(t, e) {
    Object.defineProperty(t, "default", { enumerable: !0, value: e });
  } : function(t, e) {
    t.default = e;
  }), rc = R && R.__importStar || function(t) {
    if (t && t.__esModule) return t;
    var e = {};
    if (t != null) for (var r in t) r !== "default" && Object.prototype.hasOwnProperty.call(t, r) && Dn(e, t, r);
    return tc(e, t), e;
  }, nc = R && R.__exportStar || function(t, e) {
    for (var r in t) r !== "default" && !Object.prototype.hasOwnProperty.call(e, r) && Dn(e, t, r);
  };
  Object.defineProperty(R, "__esModule", { value: !0 });
  R.z = void 0;
  var Ln = rc(er());
  R.z = Ln;
  nc(er(), R);
  R.default = Ln;
});

// ../node_modules/ts-dedent/dist/index.js
var Vn = I(($e) => {
  "use strict";
  Object.defineProperty($e, "__esModule", { value: !0 });
  $e.dedent = void 0;
  function $n(t) {
    for (var e = [], r = 1; r < arguments.length; r++)
      e[r - 1] = arguments[r];
    var n = Array.from(typeof t == "string" ? [t] : t);
    n[n.length - 1] = n[n.length - 1].replace(/\r?\n([\t ]*)$/, "");
    var s = n.reduce(function(c, l) {
      var p = l.match(/\n([\t ]+|(?!\s).)/g);
      return p ? c.concat(p.map(function(f) {
        var b, w;
        return (w = (b = f.match(/[\t ]/g)) === null || b === void 0 ? void 0 : b.length) !== null && w !== void 0 ? w : 0;
      })) : c;
    }, []);
    if (s.length) {
      var o = new RegExp(`
[	 ]{` + Math.min.apply(Math, s) + "}", "g");
      n = n.map(function(c) {
        return c.replace(o, `
`);
      });
    }
    n[0] = n[0].replace(/^\r?\n/, "");
    var a = n[0];
    return e.forEach(function(c, l) {
      var p = a.match(/(?:^|\n)( *)$/), f = p ? p[1] : "", b = c;
      typeof c == "string" && c.includes(`
`) && (b = String(c).split(`
`).map(function(w, _) {
        return _ === 0 ? w : "" + f + w;
      }).join(`
`)), a += b + n[l + 1];
    }), a;
  }
  i($n, "dedent");
  $e.dedent = $n;
  $e.default = $n;
});

// ../node_modules/isexe/windows.js
var Jn = I((kp, Kn) => {
  Kn.exports = zn;
  zn.sync = lc;
  var qn = N("fs");
  function uc(t, e) {
    var r = e.pathExt !== void 0 ? e.pathExt : process.env.PATHEXT;
    if (!r || (r = r.split(";"), r.indexOf("") !== -1))
      return !0;
    for (var n = 0; n < r.length; n++) {
      var s = r[n].toLowerCase();
      if (s && t.substr(-s.length).toLowerCase() === s)
        return !0;
    }
    return !1;
  }
  i(uc, "checkPathExt");
  function Gn(t, e, r) {
    return !t.isSymbolicLink() && !t.isFile() ? !1 : uc(e, r);
  }
  i(Gn, "checkStat");
  function zn(t, e, r) {
    qn.stat(t, function(n, s) {
      r(n, n ? !1 : Gn(s, t, e));
    });
  }
  i(zn, "isexe");
  function lc(t, e) {
    return Gn(qn.statSync(t), t, e);
  }
  i(lc, "sync");
});

// ../node_modules/isexe/mode.js
var es = I((Ip, Qn) => {
  Qn.exports = Yn;
  Yn.sync = pc;
  var Hn = N("fs");
  function Yn(t, e, r) {
    Hn.stat(t, function(n, s) {
      r(n, n ? !1 : Xn(s, e));
    });
  }
  i(Yn, "isexe");
  function pc(t, e) {
    return Xn(Hn.statSync(t), e);
  }
  i(pc, "sync");
  function Xn(t, e) {
    return t.isFile() && fc(t, e);
  }
  i(Xn, "checkStat");
  function fc(t, e) {
    var r = t.mode, n = t.uid, s = t.gid, o = e.uid !== void 0 ? e.uid : process.getuid && process.getuid(), a = e.gid !== void 0 ? e.gid : process.
    getgid && process.getgid(), c = parseInt("100", 8), l = parseInt("010", 8), p = parseInt("001", 8), f = c | l, b = r & p || r & l && s ===
    a || r & c && n === o || r & f && o === 0;
    return b;
  }
  i(fc, "checkMode");
});

// ../node_modules/isexe/index.js
var rs = I((Cp, ts) => {
  var Ep = N("fs"), ht;
  process.platform === "win32" || global.TESTING_WINDOWS ? ht = Jn() : ht = es();
  ts.exports = rr;
  rr.sync = mc;
  function rr(t, e, r) {
    if (typeof e == "function" && (r = e, e = {}), !r) {
      if (typeof Promise != "function")
        throw new TypeError("callback not provided");
      return new Promise(function(n, s) {
        rr(t, e || {}, function(o, a) {
          o ? s(o) : n(a);
        });
      });
    }
    ht(t, e || {}, function(n, s) {
      n && (n.code === "EACCES" || e && e.ignoreErrors) && (n = null, s = !1), r(n, s);
    });
  }
  i(rr, "isexe");
  function mc(t, e) {
    try {
      return ht.sync(t, e || {});
    } catch (r) {
      if (e && e.ignoreErrors || r.code === "EACCES")
        return !1;
      throw r;
    }
  }
  i(mc, "sync");
});

// ../node_modules/which/which.js
var ds = I((Ap, cs) => {
  var Ce = process.platform === "win32" || process.env.OSTYPE === "cygwin" || process.env.OSTYPE === "msys", ns = N("path"), hc = Ce ? ";" :
  ":", ss = rs(), os = /* @__PURE__ */ i((t) => Object.assign(new Error(`not found: ${t}`), { code: "ENOENT" }), "getNotFoundError"), is = /* @__PURE__ */ i(
  (t, e) => {
    let r = e.colon || hc, n = t.match(/\//) || Ce && t.match(/\\/) ? [""] : [
      // windows always checks the cwd first
      ...Ce ? [process.cwd()] : [],
      ...(e.path || process.env.PATH || /* istanbul ignore next: very unusual */
      "").split(r)
    ], s = Ce ? e.pathExt || process.env.PATHEXT || ".EXE;.CMD;.BAT;.COM" : "", o = Ce ? s.split(r) : [""];
    return Ce && t.indexOf(".") !== -1 && o[0] !== "" && o.unshift(""), {
      pathEnv: n,
      pathExt: o,
      pathExtExe: s
    };
  }, "getPathInfo"), as = /* @__PURE__ */ i((t, e, r) => {
    typeof e == "function" && (r = e, e = {}), e || (e = {});
    let { pathEnv: n, pathExt: s, pathExtExe: o } = is(t, e), a = [], c = /* @__PURE__ */ i((p) => new Promise((f, b) => {
      if (p === n.length)
        return e.all && a.length ? f(a) : b(os(t));
      let w = n[p], _ = /^".*"$/.test(w) ? w.slice(1, -1) : w, C = ns.join(_, t), S = !_ && /^\.[\\\/]/.test(t) ? t.slice(0, 2) + C : C;
      f(l(S, p, 0));
    }), "step"), l = /* @__PURE__ */ i((p, f, b) => new Promise((w, _) => {
      if (b === s.length)
        return w(c(f + 1));
      let C = s[b];
      ss(p + C, { pathExt: o }, (S, P) => {
        if (!S && P)
          if (e.all)
            a.push(p + C);
          else
            return w(p + C);
        return w(l(p, f, b + 1));
      });
    }), "subStep");
    return r ? c(0).then((p) => r(null, p), r) : c(0);
  }, "which"), yc = /* @__PURE__ */ i((t, e) => {
    e = e || {};
    let { pathEnv: r, pathExt: n, pathExtExe: s } = is(t, e), o = [];
    for (let a = 0; a < r.length; a++) {
      let c = r[a], l = /^".*"$/.test(c) ? c.slice(1, -1) : c, p = ns.join(l, t), f = !l && /^\.[\\\/]/.test(t) ? t.slice(0, 2) + p : p;
      for (let b = 0; b < n.length; b++) {
        let w = f + n[b];
        try {
          if (ss.sync(w, { pathExt: s }))
            if (e.all)
              o.push(w);
            else
              return w;
        } catch {
        }
      }
    }
    if (e.all && o.length)
      return o;
    if (e.nothrow)
      return null;
    throw os(t);
  }, "whichSync");
  cs.exports = as;
  as.sync = yc;
});

// ../node_modules/path-key/index.js
var ls = I((jp, nr) => {
  "use strict";
  var us = /* @__PURE__ */ i((t = {}) => {
    let e = t.env || process.env;
    return (t.platform || process.platform) !== "win32" ? "PATH" : Object.keys(e).reverse().find((n) => n.toUpperCase() === "PATH") || "Path";
  }, "pathKey");
  nr.exports = us;
  nr.exports.default = us;
});

// ../node_modules/cross-spawn/lib/util/resolveCommand.js
var hs = I((Np, ms) => {
  "use strict";
  var ps = N("path"), gc = ds(), bc = ls();
  function fs(t, e) {
    let r = t.options.env || process.env, n = process.cwd(), s = t.options.cwd != null, o = s && process.chdir !== void 0 && !process.chdir.
    disabled;
    if (o)
      try {
        process.chdir(t.options.cwd);
      } catch {
      }
    let a;
    try {
      a = gc.sync(t.command, {
        path: r[bc({ env: r })],
        pathExt: e ? ps.delimiter : void 0
      });
    } catch {
    } finally {
      o && process.chdir(n);
    }
    return a && (a = ps.resolve(s ? t.options.cwd : "", a)), a;
  }
  i(fs, "resolveCommandAttempt");
  function xc(t) {
    return fs(t) || fs(t, !0);
  }
  i(xc, "resolveCommand");
  ms.exports = xc;
});

// ../node_modules/cross-spawn/lib/util/escape.js
var ys = I((Mp, or) => {
  "use strict";
  var sr = /([()\][%!^"`<>&|;, *?])/g;
  function vc(t) {
    return t = t.replace(sr, "^$1"), t;
  }
  i(vc, "escapeCommand");
  function _c(t, e) {
    return t = `${t}`, t = t.replace(/(?=(\\+?)?)\1"/g, '$1$1\\"'), t = t.replace(/(?=(\\+?)?)\1$/, "$1$1"), t = `"${t}"`, t = t.replace(sr,
    "^$1"), e && (t = t.replace(sr, "^$1")), t;
  }
  i(_c, "escapeArgument");
  or.exports.command = vc;
  or.exports.argument = _c;
});

// ../node_modules/shebang-regex/index.js
var bs = I((Lp, gs) => {
  "use strict";
  gs.exports = /^#!(.*)/;
});

// ../node_modules/shebang-command/index.js
var vs = I((Up, xs) => {
  "use strict";
  var wc = bs();
  xs.exports = (t = "") => {
    let e = t.match(wc);
    if (!e)
      return null;
    let [r, n] = e[0].replace(/#! ?/, "").split(" "), s = r.split("/").pop();
    return s === "env" ? n : n ? `${s} ${n}` : s;
  };
});

// ../node_modules/cross-spawn/lib/util/readShebang.js
var ws = I(($p, _s) => {
  "use strict";
  var ir = N("fs"), kc = vs();
  function Tc(t) {
    let r = Buffer.alloc(150), n;
    try {
      n = ir.openSync(t, "r"), ir.readSync(n, r, 0, 150, 0), ir.closeSync(n);
    } catch {
    }
    return kc(r.toString());
  }
  i(Tc, "readShebang");
  _s.exports = Tc;
});

// ../node_modules/cross-spawn/lib/parse.js
var Ss = I((Fp, Is) => {
  "use strict";
  var Ic = N("path"), ks = hs(), Ts = ys(), Sc = ws(), Ec = process.platform === "win32", Cc = /\.(?:com|exe)$/i, Pc = /node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;
  function Ac(t) {
    t.file = ks(t);
    let e = t.file && Sc(t.file);
    return e ? (t.args.unshift(t.file), t.command = e, ks(t)) : t.file;
  }
  i(Ac, "detectShebang");
  function Oc(t) {
    if (!Ec)
      return t;
    let e = Ac(t), r = !Cc.test(e);
    if (t.options.forceShell || r) {
      let n = Pc.test(e);
      t.command = Ic.normalize(t.command), t.command = Ts.command(t.command), t.args = t.args.map((o) => Ts.argument(o, n));
      let s = [t.command].concat(t.args).join(" ");
      t.args = ["/d", "/s", "/c", `"${s}"`], t.command = process.env.comspec || "cmd.exe", t.options.windowsVerbatimArguments = !0;
    }
    return t;
  }
  i(Oc, "parseNonShell");
  function jc(t, e, r) {
    e && !Array.isArray(e) && (r = e, e = null), e = e ? e.slice(0) : [], r = Object.assign({}, r);
    let n = {
      command: t,
      args: e,
      options: r,
      file: void 0,
      original: {
        command: t,
        args: e
      }
    };
    return r.shell ? n : Oc(n);
  }
  i(jc, "parse");
  Is.exports = jc;
});

// ../node_modules/cross-spawn/lib/enoent.js
var Ps = I((Wp, Cs) => {
  "use strict";
  var ar = process.platform === "win32";
  function cr(t, e) {
    return Object.assign(new Error(`${e} ${t.command} ENOENT`), {
      code: "ENOENT",
      errno: "ENOENT",
      syscall: `${e} ${t.command}`,
      path: t.command,
      spawnargs: t.args
    });
  }
  i(cr, "notFoundError");
  function Rc(t, e) {
    if (!ar)
      return;
    let r = t.emit;
    t.emit = function(n, s) {
      if (n === "exit") {
        let o = Es(s, e);
        if (o)
          return r.call(t, "error", o);
      }
      return r.apply(t, arguments);
    };
  }
  i(Rc, "hookChildProcess");
  function Es(t, e) {
    return ar && t === 1 && !e.file ? cr(e.original, "spawn") : null;
  }
  i(Es, "verifyENOENT");
  function Nc(t, e) {
    return ar && t === 1 && !e.file ? cr(e.original, "spawnSync") : null;
  }
  i(Nc, "verifyENOENTSync");
  Cs.exports = {
    hookChildProcess: Rc,
    verifyENOENT: Es,
    verifyENOENTSync: Nc,
    notFoundError: cr
  };
});

// ../node_modules/cross-spawn/index.js
var js = I((Gp, Pe) => {
  "use strict";
  var As = N("child_process"), dr = Ss(), ur = Ps();
  function Os(t, e, r) {
    let n = dr(t, e, r), s = As.spawn(n.command, n.args, n.options);
    return ur.hookChildProcess(s, n), s;
  }
  i(Os, "spawn");
  function Zc(t, e, r) {
    let n = dr(t, e, r), s = As.spawnSync(n.command, n.args, n.options);
    return s.error = s.error || ur.verifyENOENTSync(s.status, n), s;
  }
  i(Zc, "spawnSync");
  Pe.exports = Os;
  Pe.exports.spawn = Os;
  Pe.exports.sync = Zc;
  Pe.exports._parse = dr;
  Pe.exports._enoent = ur;
});

// ../node_modules/merge-stream/index.js
var oo = I((xm, so) => {
  "use strict";
  var { PassThrough: Rd } = N("stream");
  so.exports = function() {
    var t = [], e = new Rd({ objectMode: !0 });
    return e.setMaxListeners(0), e.add = r, e.isEmpty = n, e.on("unpipe", s), Array.prototype.slice.call(arguments).forEach(r), e;
    function r(o) {
      return Array.isArray(o) ? (o.forEach(r), this) : (t.push(o), o.once("end", s.bind(null, o)), o.once("error", e.emit.bind(e, "error")),
      o.pipe(e, { end: !1 }), this);
    }
    i(r, "add");
    function n() {
      return t.length == 0;
    }
    i(n, "isEmpty");
    function s(o) {
      t = t.filter(function(a) {
        return a !== o;
      }), !t.length && e.readable && e.end();
    }
    i(s, "remove");
  };
});

// ../node_modules/slash/index.js
function Mr(t) {
  return t.startsWith("\\\\?\\") ? t : t.replace(/\\/g, "/");
}
var So = vi(() => {
  i(Mr, "slash");
});

// ../node_modules/common-path-prefix/index.js
var Ro = I((hh, jo) => {
  "use strict";
  var { sep: du } = N("path"), uu = /* @__PURE__ */ i((t) => {
    for (let e of t) {
      let r = /(\/|\\)/.exec(e);
      if (r !== null) return r[0];
    }
    return du;
  }, "determineSeparator");
  jo.exports = /* @__PURE__ */ i(function(e, r = uu(e)) {
    let [n = "", ...s] = e;
    if (n === "" || s.length === 0) return "";
    let o = n.split(r), a = o.length;
    for (let l of s) {
      let p = l.split(r);
      for (let f = 0; f < a; f++)
        p[f] !== o[f] && (a = f);
      if (a === 0) return "";
    }
    let c = o.slice(0, a).join(r);
    return c.endsWith(r) ? c : c + r;
  }, "commonPathPrefix");
});

// ../node_modules/fetch-retry/index.js
var ni = I((Gg, ri) => {
  "use strict";
  ri.exports = function(t, e) {
    if (e = e || {}, typeof t != "function")
      throw new X("fetch must be a function");
    if (typeof e != "object")
      throw new X("defaults must be an object");
    if (e.retries !== void 0 && !Dt(e.retries))
      throw new X("retries must be a positive integer");
    if (e.retryDelay !== void 0 && !Dt(e.retryDelay) && typeof e.retryDelay != "function")
      throw new X("retryDelay must be a positive integer or a function returning a positive integer");
    if (e.retryOn !== void 0 && !Array.isArray(e.retryOn) && typeof e.retryOn != "function")
      throw new X("retryOn property expects an array or function");
    var r = {
      retries: 3,
      retryDelay: 1e3,
      retryOn: []
    };
    return e = Object.assign(r, e), /* @__PURE__ */ i(function(s, o) {
      var a = e.retries, c = e.retryDelay, l = e.retryOn;
      if (o && o.retries !== void 0)
        if (Dt(o.retries))
          a = o.retries;
        else
          throw new X("retries must be a positive integer");
      if (o && o.retryDelay !== void 0)
        if (Dt(o.retryDelay) || typeof o.retryDelay == "function")
          c = o.retryDelay;
        else
          throw new X("retryDelay must be a positive integer or a function returning a positive integer");
      if (o && o.retryOn)
        if (Array.isArray(o.retryOn) || typeof o.retryOn == "function")
          l = o.retryOn;
        else
          throw new X("retryOn property expects an array or function");
      return new Promise(function(p, f) {
        var b = /* @__PURE__ */ i(function(_) {
          var C = typeof Request < "u" && s instanceof Request ? s.clone() : s;
          t(C, o).then(function(S) {
            if (Array.isArray(l) && l.indexOf(S.status) === -1)
              p(S);
            else if (typeof l == "function")
              try {
                return Promise.resolve(l(_, null, S)).then(function(P) {
                  P ? w(_, null, S) : p(S);
                }).catch(f);
              } catch (P) {
                f(P);
              }
            else
              _ < a ? w(_, null, S) : p(S);
          }).catch(function(S) {
            if (typeof l == "function")
              try {
                Promise.resolve(l(_, S, null)).then(function(P) {
                  P ? w(_, S, null) : f(S);
                }).catch(function(P) {
                  f(P);
                });
              } catch (P) {
                f(P);
              }
            else _ < a ? w(_, S, null) : f(S);
          });
        }, "wrappedFetch");
        function w(_, C, S) {
          var P = typeof c == "function" ? c(_, C, S) : c;
          setTimeout(function() {
            b(++_);
          }, P);
        }
        i(w, "retry"), b(0);
      });
    }, "fetchRetry");
  };
  function Dt(t) {
    return Number.isInteger(t) && t >= 0;
  }
  i(Dt, "isPositiveInteger");
  function X(t) {
    this.name = "ArgumentError", this.message = t;
  }
  i(X, "ArgumentError");
});

// src/telemetry/index.ts
import { logger as mi } from "storybook/internal/node-logger";

// src/telemetry/notify.ts
var rn = z(Qr(), 1);
import { cache as en } from "storybook/internal/common";
import { CLI_COLORS as Ii, logger as et } from "storybook/internal/node-logger";
var tn = "telemetry-notification-date", nn = /* @__PURE__ */ i(async () => {
  await en.get(tn, null) || (en.set(tn, Date.now()), et.log(
    `${Ii.info("Attention:")} Storybook now collects completely anonymous telemetry regarding usage. This information is used to shape Story\
book's roadmap and prioritize features.`
  ), et.log(
    "You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:"
  ), et.log(rn.default.cyan("https://storybook.js.org/telemetry")), et.log(""));
}, "notify");

// src/telemetry/sanitize.ts
import an from "node:path";
function sn(t) {
  return t.replace(/[-[/{}()*+?.\\^$|]/g, "\\$&");
}
i(sn, "regexpEscape");
function on(t = "") {
  return t.replace(/\u001B\[[0-9;]*m/g, "");
}
i(on, "removeAnsiEscapeCodes");
function Re(t, e = an.sep) {
  if (!t)
    return t;
  let r = process.cwd().split(e);
  for (; r.length > 1; ) {
    let n = r.join(e), s = new RegExp(sn(n), "gi");
    t = t.replace(s, "$SNIP");
    let o = r.join(e + e), a = new RegExp(sn(o), "gi");
    t = t.replace(a, "$SNIP"), r.pop();
  }
  return t;
}
i(Re, "cleanPaths");
function tt(t, e = an.sep) {
  try {
    t = {
      ...JSON.parse(JSON.stringify(t)),
      message: on(t.message),
      stack: on(t.stack),
      cause: t.cause,
      name: t.name
    };
    let r = Re(JSON.stringify(t), e);
    return JSON.parse(r);
  } catch (r) {
    return `Sanitization error: ${r?.message}`;
  }
}
i(tt, "sanitizeError");

// src/telemetry/storybook-metadata.ts
import { dirname as Uu } from "node:path";
import {
  getProjectRoot as $u,
  getStorybookConfiguration as Vu,
  getStorybookInfo as Fu,
  loadMainConfig as Bu,
  versions as Wu
} from "storybook/internal/common";
import { readConfig as qu } from "storybook/internal/csf-tools";

// ../node_modules/fd-package-json/dist/esm/main.js
var un = z(dn(), 1);
import { resolve as Ei } from "node:path";
import { stat as Ci, readFile as Pi } from "node:fs/promises";
import { statSync as Cl, readFileSync as Pl } from "node:fs";
async function Ai(t) {
  try {
    return (await Ci(t)).isFile();
  } catch {
    return !1;
  }
}
i(Ai, "fileExists");
async function Ft(t) {
  for (let e of (0, un.walkUp)(t)) {
    let r = Ei(e, "package.json");
    if (await Ai(r))
      return r;
  }
  return null;
}
i(Ft, "findPackagePath");
async function ln(t) {
  let e = await Ft(t);
  if (!e)
    return null;
  try {
    let r = await Pi(e, { encoding: "utf8" });
    return JSON.parse(r);
  } catch {
    return null;
  }
}
i(ln, "findPackage");

// ../node_modules/package-manager-detector/dist/constants.mjs
var pn = [
  "npm",
  "yarn",
  "yarn@berry",
  "pnpm",
  "pnpm@6",
  "bun",
  "deno"
], Bt = {
  "bun.lock": "bun",
  "bun.lockb": "bun",
  "deno.lock": "deno",
  "pnpm-lock.yaml": "pnpm",
  "pnpm-workspace.yaml": "pnpm",
  "yarn.lock": "yarn",
  "package-lock.json": "npm",
  "npm-shrinkwrap.json": "npm"
}, Wt = {
  "node_modules/.deno/": "deno",
  "node_modules/.pnpm/": "pnpm",
  "node_modules/.yarn-state.yml": "yarn",
  // yarn v2+ (node-modules)
  "node_modules/.yarn_integrity": "yarn",
  // yarn v1
  "node_modules/.package-lock.json": "npm",
  ".pnp.cjs": "yarn",
  // yarn v3+ (pnp)
  ".pnp.js": "yarn",
  // yarn v2 (pnp)
  "bun.lock": "bun",
  "bun.lockb": "bun"
};

// ../node_modules/package-manager-detector/dist/detect.mjs
import mn from "node:fs/promises";
import Q from "node:path";
import Oi from "node:process";
async function qt(t, e) {
  try {
    let r = await mn.stat(t);
    return e === "file" ? r.isFile() : r.isDirectory();
  } catch {
    return !1;
  }
}
i(qt, "pathExists");
function* ji(t = Oi.cwd()) {
  let e = Q.resolve(t), { root: r } = Q.parse(e);
  for (; e && e !== r; )
    yield e, e = Q.dirname(e);
}
i(ji, "lookup");
async function fn(t, e) {
  return !t || !qt(t, "file") ? null : await Ni(t, e);
}
i(fn, "parsePackageJson");
async function Gt(t = {}) {
  let { cwd: e, strategies: r = ["lockfile", "packageManager-field", "devEngines-field"], onUnknown: n } = t;
  for (let s of ji(e))
    for (let o of r)
      switch (o) {
        case "lockfile": {
          for (let a of Object.keys(Bt))
            if (await qt(Q.join(s, a), "file")) {
              let c = Bt[a], l = await fn(Q.join(s, "package.json"), n);
              return l || { name: c, agent: c };
            }
          break;
        }
        case "packageManager-field":
        case "devEngines-field": {
          let a = await fn(Q.join(s, "package.json"), n);
          if (a)
            return a;
          break;
        }
        case "install-metadata": {
          for (let a of Object.keys(Wt)) {
            let c = a.endsWith("/") ? "dir" : "file";
            if (await qt(Q.join(s, a), c)) {
              let l = Wt[a], p = l === "yarn" ? Zi(a) ? "yarn" : "yarn@berry" : l;
              return { name: l, agent: p };
            }
          }
          break;
        }
      }
  return null;
}
i(Gt, "detect");
function Ri(t) {
  let e = /* @__PURE__ */ i((r) => r?.match(/\d+(\.\d+){0,2}/)?.[0] ?? r, "handelVer");
  if (typeof t.packageManager == "string") {
    let [r, n] = t.packageManager.replace(/^\^/, "").split("@");
    return { name: r, ver: e(n) };
  }
  if (typeof t.devEngines?.packageManager?.name == "string")
    return {
      name: t.devEngines.packageManager.name,
      ver: e(t.devEngines.packageManager.version)
    };
}
i(Ri, "getNameAndVer");
async function Ni(t, e) {
  try {
    let r = JSON.parse(await mn.readFile(t, "utf8")), n, s = Ri(r);
    if (s) {
      let o = s.name, a = s.ver, c = a;
      return o === "yarn" && a && Number.parseInt(a) > 1 ? (n = "yarn@berry", c = "berry", { name: o, agent: n, version: c }) : o === "pnpm" &&
      a && Number.parseInt(a) < 7 ? (n = "pnpm@6", { name: o, agent: n, version: c }) : pn.includes(o) ? (n = o, { name: o, agent: n, version: c }) :
      e?.(r.packageManager) ?? null;
    }
  } catch {
  }
  return null;
}
i(Ni, "handlePackageManager");
function Zi(t) {
  return t.endsWith(".yarn_integrity");
}
i(Zi, "isMetadataYarnClassic");

// ../node_modules/package-manager-detector/dist/index.mjs
import "node:fs/promises";
import "node:path";
import "node:process";

// package.json
var nt = "9.0.12";

// src/cli/globalSettings.ts
var Ee = z(Un(), 1);
import tr from "node:fs/promises";
import { homedir as sc } from "node:os";
import { dirname as oc, join as ic } from "node:path";

// src/server-errors.ts
var Bn = z(Vn(), 1);

// src/storybook-error.ts
function Fn({
  code: t,
  category: e
}) {
  let r = String(t).padStart(4, "0");
  return `SB_${e}_${r}`;
}
i(Fn, "parseErrorCode");
var pt = class t extends Error {
  constructor(r) {
    super(t.getFullMessage(r));
    /**
     * Data associated with the error. Used to provide additional information in the error message or
     * to be passed to telemetry.
     */
    this.data = {};
    /** Flag used to easily determine if the error originates from Storybook. */
    this.fromStorybook = !0;
    this.category = r.category, this.documentation = r.documentation ?? !1, this.code = r.code;
  }
  static {
    i(this, "StorybookError");
  }
  get fullErrorCode() {
    return Fn({ code: this.code, category: this.category });
  }
  /** Overrides the default `Error.name` property in the format: SB_<CATEGORY>_<CODE>. */
  get name() {
    let r = this.constructor.name;
    return `${this.fullErrorCode} (${r})`;
  }
  /** Generates the error message along with additional documentation link (if applicable). */
  static getFullMessage({
    documentation: r,
    code: n,
    category: s,
    message: o
  }) {
    let a;
    return r === !0 ? a = `https://storybook.js.org/error/${Fn({ code: n, category: s })}` : typeof r == "string" ? a = r : Array.isArray(r) &&
    (a = `
${r.map((c) => `	- ${c}`).join(`
`)}`), `${o}${a != null ? `

More info: ${a}
` : ""}`;
  }
};

// src/server-errors.ts
var ft = class extends pt {
  constructor(r) {
    super({
      category: "CORE-SERVER",
      code: 1,
      message: Bn.dedent`
        Unable to save global settings file to ${r.filePath}
        ${r.error && `Reason: ${r.error}`}`
    });
    this.data = r;
  }
  static {
    i(this, "SavingGlobalSettingsFileError");
  }
};

// src/cli/globalSettings.ts
var ac = ic(sc(), ".storybook", "settings.json"), cc = 1, dc = Ee.z.object({
  version: Ee.z.number(),
  // NOTE: every key (and subkey) below must be optional, for forwards compatibility reasons
  // (we can remove keys once they are deprecated)
  userSince: Ee.z.number().optional(),
  init: Ee.z.object({ skipOnboarding: Ee.z.boolean().optional() }).optional()
}), Se;
async function Wn(t = ac) {
  if (Se)
    return Se;
  try {
    let e = await tr.readFile(t, "utf8"), r = dc.parse(JSON.parse(e));
    Se = new mt(t, r);
  } catch {
    Se = new mt(t, { version: cc, userSince: Date.now() }), await Se.save();
  }
  return Se;
}
i(Wn, "globalSettings");
var mt = class {
  static {
    i(this, "Settings");
  }
  /**
   * Create a new Settings instance
   *
   * @param filePath Path to the JSON settings file
   * @param value Loaded value of settings
   */
  constructor(e, r) {
    this.filePath = e, this.value = r;
  }
  /** Save settings to the file */
  async save() {
    try {
      await tr.mkdir(oc(this.filePath), { recursive: !0 }), await tr.writeFile(this.filePath, JSON.stringify(this.value, null, 2));
    } catch (e) {
      throw new ft({
        filePath: this.filePath,
        error: e
      });
    }
  }
};

// src/telemetry/get-application-file-count.ts
import { sep as Iu } from "node:path";

// src/telemetry/exec-command-count-lines.ts
import { createInterface as ru } from "node:readline";

// node_modules/execa/index.js
var _o = z(js(), 1);
import { Buffer as Hd } from "node:buffer";
import Yd from "node:path";
import Zr from "node:child_process";
import Pt from "node:process";

// ../node_modules/strip-final-newline/index.js
function lr(t) {
  let e = typeof t == "string" ? `
` : 10, r = typeof t == "string" ? "\r" : 13;
  return t[t.length - 1] === e && (t = t.slice(0, -1)), t[t.length - 1] === r && (t = t.slice(0, -1)), t;
}
i(lr, "stripFinalNewline");

// node_modules/npm-run-path/index.js
import gt from "node:process";
import Ve from "node:path";
import { fileURLToPath as Rs } from "node:url";

// node_modules/path-key/index.js
function yt(t = {}) {
  let {
    env: e = process.env,
    platform: r = process.platform
  } = t;
  return r !== "win32" ? "PATH" : Object.keys(e).reverse().find((n) => n.toUpperCase() === "PATH") || "Path";
}
i(yt, "pathKey");

// node_modules/npm-run-path/index.js
var Mc = /* @__PURE__ */ i(({
  cwd: t = gt.cwd(),
  path: e = gt.env[yt()],
  preferLocal: r = !0,
  execPath: n = gt.execPath,
  addExecPath: s = !0
} = {}) => {
  let o = t instanceof URL ? Rs(t) : t, a = Ve.resolve(o), c = [];
  return r && Dc(c, a), s && Lc(c, n, a), [...c, e].join(Ve.delimiter);
}, "npmRunPath"), Dc = /* @__PURE__ */ i((t, e) => {
  let r;
  for (; r !== e; )
    t.push(Ve.join(e, "node_modules/.bin")), r = e, e = Ve.resolve(e, "..");
}, "applyPreferLocal"), Lc = /* @__PURE__ */ i((t, e, r) => {
  let n = e instanceof URL ? Rs(e) : e;
  t.push(Ve.resolve(r, n, ".."));
}, "applyExecPath"), Ns = /* @__PURE__ */ i(({ env: t = gt.env, ...e } = {}) => {
  t = { ...t };
  let r = yt({ env: t });
  return e.path = t[r], t[r] = Mc(e), t;
}, "npmRunPathEnv");

// node_modules/mimic-fn/index.js
var Uc = /* @__PURE__ */ i((t, e, r, n) => {
  if (r === "length" || r === "prototype" || r === "arguments" || r === "caller")
    return;
  let s = Object.getOwnPropertyDescriptor(t, r), o = Object.getOwnPropertyDescriptor(e, r);
  !$c(s, o) && n || Object.defineProperty(t, r, o);
}, "copyProperty"), $c = /* @__PURE__ */ i(function(t, e) {
  return t === void 0 || t.configurable || t.writable === e.writable && t.enumerable === e.enumerable && t.configurable === e.configurable &&
  (t.writable || t.value === e.value);
}, "canCopyProperty"), Vc = /* @__PURE__ */ i((t, e) => {
  let r = Object.getPrototypeOf(e);
  r !== Object.getPrototypeOf(t) && Object.setPrototypeOf(t, r);
}, "changePrototype"), Fc = /* @__PURE__ */ i((t, e) => `/* Wrapped ${t}*/
${e}`, "wrappedToString"), Bc = Object.getOwnPropertyDescriptor(Function.prototype, "toString"), Wc = Object.getOwnPropertyDescriptor(Function.
prototype.toString, "name"), qc = /* @__PURE__ */ i((t, e, r) => {
  let n = r === "" ? "" : `with ${r.trim()}() `, s = Fc.bind(null, n, e.toString());
  Object.defineProperty(s, "name", Wc), Object.defineProperty(t, "toString", { ...Bc, value: s });
}, "changeToString");
function pr(t, e, { ignoreNonConfigurable: r = !1 } = {}) {
  let { name: n } = t;
  for (let s of Reflect.ownKeys(e))
    Uc(t, e, s, r);
  return Vc(t, e), qc(t, e, n), t;
}
i(pr, "mimicFunction");

// node_modules/onetime/index.js
var bt = /* @__PURE__ */ new WeakMap(), Zs = /* @__PURE__ */ i((t, e = {}) => {
  if (typeof t != "function")
    throw new TypeError("Expected a function");
  let r, n = 0, s = t.displayName || t.name || "<anonymous>", o = /* @__PURE__ */ i(function(...a) {
    if (bt.set(o, ++n), n === 1)
      r = t.apply(this, a), t = null;
    else if (e.throw === !0)
      throw new Error(`Function \`${s}\` can only be called once`);
    return r;
  }, "onetime");
  return pr(o, t), bt.set(o, n), o;
}, "onetime");
Zs.callCount = (t) => {
  if (!bt.has(t))
    throw new Error(`The given function \`${t.name}\` is not wrapped by the \`onetime\` package`);
  return bt.get(t);
};
var Ms = Zs;

// node_modules/execa/lib/error.js
import td from "node:process";

// node_modules/human-signals/build/src/main.js
import { constants as Jc } from "node:os";

// node_modules/human-signals/build/src/realtime.js
var Ds = /* @__PURE__ */ i(() => {
  let t = fr - Ls + 1;
  return Array.from({ length: t }, Gc);
}, "getRealtimeSignals"), Gc = /* @__PURE__ */ i((t, e) => ({
  name: `SIGRT${e + 1}`,
  number: Ls + e,
  action: "terminate",
  description: "Application-specific signal (realtime)",
  standard: "posix"
}), "getRealtimeSignal"), Ls = 34, fr = 64;

// node_modules/human-signals/build/src/signals.js
import { constants as zc } from "node:os";

// node_modules/human-signals/build/src/core.js
var Us = [
  {
    name: "SIGHUP",
    number: 1,
    action: "terminate",
    description: "Terminal closed",
    standard: "posix"
  },
  {
    name: "SIGINT",
    number: 2,
    action: "terminate",
    description: "User interruption with CTRL-C",
    standard: "ansi"
  },
  {
    name: "SIGQUIT",
    number: 3,
    action: "core",
    description: "User interruption with CTRL-\\",
    standard: "posix"
  },
  {
    name: "SIGILL",
    number: 4,
    action: "core",
    description: "Invalid machine instruction",
    standard: "ansi"
  },
  {
    name: "SIGTRAP",
    number: 5,
    action: "core",
    description: "Debugger breakpoint",
    standard: "posix"
  },
  {
    name: "SIGABRT",
    number: 6,
    action: "core",
    description: "Aborted",
    standard: "ansi"
  },
  {
    name: "SIGIOT",
    number: 6,
    action: "core",
    description: "Aborted",
    standard: "bsd"
  },
  {
    name: "SIGBUS",
    number: 7,
    action: "core",
    description: "Bus error due to misaligned, non-existing address or paging error",
    standard: "bsd"
  },
  {
    name: "SIGEMT",
    number: 7,
    action: "terminate",
    description: "Command should be emulated but is not implemented",
    standard: "other"
  },
  {
    name: "SIGFPE",
    number: 8,
    action: "core",
    description: "Floating point arithmetic error",
    standard: "ansi"
  },
  {
    name: "SIGKILL",
    number: 9,
    action: "terminate",
    description: "Forced termination",
    standard: "posix",
    forced: !0
  },
  {
    name: "SIGUSR1",
    number: 10,
    action: "terminate",
    description: "Application-specific signal",
    standard: "posix"
  },
  {
    name: "SIGSEGV",
    number: 11,
    action: "core",
    description: "Segmentation fault",
    standard: "ansi"
  },
  {
    name: "SIGUSR2",
    number: 12,
    action: "terminate",
    description: "Application-specific signal",
    standard: "posix"
  },
  {
    name: "SIGPIPE",
    number: 13,
    action: "terminate",
    description: "Broken pipe or socket",
    standard: "posix"
  },
  {
    name: "SIGALRM",
    number: 14,
    action: "terminate",
    description: "Timeout or timer",
    standard: "posix"
  },
  {
    name: "SIGTERM",
    number: 15,
    action: "terminate",
    description: "Termination",
    standard: "ansi"
  },
  {
    name: "SIGSTKFLT",
    number: 16,
    action: "terminate",
    description: "Stack is empty or overflowed",
    standard: "other"
  },
  {
    name: "SIGCHLD",
    number: 17,
    action: "ignore",
    description: "Child process terminated, paused or unpaused",
    standard: "posix"
  },
  {
    name: "SIGCLD",
    number: 17,
    action: "ignore",
    description: "Child process terminated, paused or unpaused",
    standard: "other"
  },
  {
    name: "SIGCONT",
    number: 18,
    action: "unpause",
    description: "Unpaused",
    standard: "posix",
    forced: !0
  },
  {
    name: "SIGSTOP",
    number: 19,
    action: "pause",
    description: "Paused",
    standard: "posix",
    forced: !0
  },
  {
    name: "SIGTSTP",
    number: 20,
    action: "pause",
    description: 'Paused using CTRL-Z or "suspend"',
    standard: "posix"
  },
  {
    name: "SIGTTIN",
    number: 21,
    action: "pause",
    description: "Background process cannot read terminal input",
    standard: "posix"
  },
  {
    name: "SIGBREAK",
    number: 21,
    action: "terminate",
    description: "User interruption with CTRL-BREAK",
    standard: "other"
  },
  {
    name: "SIGTTOU",
    number: 22,
    action: "pause",
    description: "Background process cannot write to terminal output",
    standard: "posix"
  },
  {
    name: "SIGURG",
    number: 23,
    action: "ignore",
    description: "Socket received out-of-band data",
    standard: "bsd"
  },
  {
    name: "SIGXCPU",
    number: 24,
    action: "core",
    description: "Process timed out",
    standard: "bsd"
  },
  {
    name: "SIGXFSZ",
    number: 25,
    action: "core",
    description: "File too big",
    standard: "bsd"
  },
  {
    name: "SIGVTALRM",
    number: 26,
    action: "terminate",
    description: "Timeout or timer",
    standard: "bsd"
  },
  {
    name: "SIGPROF",
    number: 27,
    action: "terminate",
    description: "Timeout or timer",
    standard: "bsd"
  },
  {
    name: "SIGWINCH",
    number: 28,
    action: "ignore",
    description: "Terminal window size changed",
    standard: "bsd"
  },
  {
    name: "SIGIO",
    number: 29,
    action: "terminate",
    description: "I/O is available",
    standard: "other"
  },
  {
    name: "SIGPOLL",
    number: 29,
    action: "terminate",
    description: "Watched event",
    standard: "other"
  },
  {
    name: "SIGINFO",
    number: 29,
    action: "ignore",
    description: "Request for process information",
    standard: "other"
  },
  {
    name: "SIGPWR",
    number: 30,
    action: "terminate",
    description: "Device running out of power",
    standard: "systemv"
  },
  {
    name: "SIGSYS",
    number: 31,
    action: "core",
    description: "Invalid system call",
    standard: "other"
  },
  {
    name: "SIGUNUSED",
    number: 31,
    action: "terminate",
    description: "Invalid system call",
    standard: "other"
  }
];

// node_modules/human-signals/build/src/signals.js
var mr = /* @__PURE__ */ i(() => {
  let t = Ds();
  return [...Us, ...t].map(Kc);
}, "getSignals"), Kc = /* @__PURE__ */ i(({
  name: t,
  number: e,
  description: r,
  action: n,
  forced: s = !1,
  standard: o
}) => {
  let {
    signals: { [t]: a }
  } = zc, c = a !== void 0;
  return { name: t, number: c ? a : e, description: r, supported: c, action: n, forced: s, standard: o };
}, "normalizeSignal");

// node_modules/human-signals/build/src/main.js
var Hc = /* @__PURE__ */ i(() => {
  let t = mr();
  return Object.fromEntries(t.map(Yc));
}, "getSignalsByName"), Yc = /* @__PURE__ */ i(({
  name: t,
  number: e,
  description: r,
  supported: n,
  action: s,
  forced: o,
  standard: a
}) => [t, { name: t, number: e, description: r, supported: n, action: s, forced: o, standard: a }], "getSignalByName"), $s = Hc(), Xc = /* @__PURE__ */ i(
() => {
  let t = mr(), e = fr + 1, r = Array.from(
    { length: e },
    (n, s) => Qc(s, t)
  );
  return Object.assign({}, ...r);
}, "getSignalsByNumber"), Qc = /* @__PURE__ */ i((t, e) => {
  let r = ed(t, e);
  if (r === void 0)
    return {};
  let { name: n, description: s, supported: o, action: a, forced: c, standard: l } = r;
  return {
    [t]: {
      name: n,
      number: t,
      description: s,
      supported: o,
      action: a,
      forced: c,
      standard: l
    }
  };
}, "getSignalByNumber"), ed = /* @__PURE__ */ i((t, e) => {
  let r = e.find(({ name: n }) => Jc.signals[n] === t);
  return r !== void 0 ? r : e.find((n) => n.number === t);
}, "findSignalByNumber"), _f = Xc();

// node_modules/execa/lib/error.js
var rd = /* @__PURE__ */ i(({ timedOut: t, timeout: e, errorCode: r, signal: n, signalDescription: s, exitCode: o, isCanceled: a }) => t ? `\
timed out after ${e} milliseconds` : a ? "was canceled" : r !== void 0 ? `failed with ${r}` : n !== void 0 ? `was killed with ${n} (${s})` :
o !== void 0 ? `failed with exit code ${o}` : "failed", "getErrorPrefix"), Fe = /* @__PURE__ */ i(({
  stdout: t,
  stderr: e,
  all: r,
  error: n,
  signal: s,
  exitCode: o,
  command: a,
  escapedCommand: c,
  timedOut: l,
  isCanceled: p,
  killed: f,
  parsed: { options: { timeout: b, cwd: w = td.cwd() } }
}) => {
  o = o === null ? void 0 : o, s = s === null ? void 0 : s;
  let _ = s === void 0 ? void 0 : $s[s].description, C = n && n.code, P = `Command ${rd({ timedOut: l, timeout: b, errorCode: C, signal: s, signalDescription: _,
  exitCode: o, isCanceled: p })}: ${a}`, G = Object.prototype.toString.call(n) === "[object Error]", Oe = G ? `${P}
${n.message}` : P, be = [Oe, e, t].filter(Boolean).join(`
`);
  return G ? (n.originalMessage = n.message, n.message = be) : n = new Error(be), n.shortMessage = Oe, n.command = a, n.escapedCommand = c, n.
  exitCode = o, n.signal = s, n.signalDescription = _, n.stdout = t, n.stderr = e, n.cwd = w, r !== void 0 && (n.all = r), "bufferedData" in
  n && delete n.bufferedData, n.failed = !0, n.timedOut = !!l, n.isCanceled = p, n.killed = f && !l, n;
}, "makeError");

// node_modules/execa/lib/stdio.js
var xt = ["stdin", "stdout", "stderr"], nd = /* @__PURE__ */ i((t) => xt.some((e) => t[e] !== void 0), "hasAlias"), Vs = /* @__PURE__ */ i((t) => {
  if (!t)
    return;
  let { stdio: e } = t;
  if (e === void 0)
    return xt.map((n) => t[n]);
  if (nd(t))
    throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${xt.map((n) => `\`${n}\``).join(", ")}`);
  if (typeof e == "string")
    return e;
  if (!Array.isArray(e))
    throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);
  let r = Math.max(e.length, xt.length);
  return Array.from({ length: r }, (n, s) => e[s]);
}, "normalizeStdio");

// node_modules/execa/lib/kill.js
import id from "node:os";

// node_modules/signal-exit/dist/mjs/signals.js
var ye = [];
ye.push("SIGHUP", "SIGINT", "SIGTERM");
process.platform !== "win32" && ye.push(
  "SIGALRM",
  "SIGABRT",
  "SIGVTALRM",
  "SIGXCPU",
  "SIGXFSZ",
  "SIGUSR2",
  "SIGTRAP",
  "SIGSYS",
  "SIGQUIT",
  "SIGIOT"
  // should detect profiler and enable/disable accordingly.
  // see #21
  // 'SIGPROF'
);
process.platform === "linux" && ye.push("SIGIO", "SIGPOLL", "SIGPWR", "SIGSTKFLT");

// node_modules/signal-exit/dist/mjs/index.js
var vt = /* @__PURE__ */ i((t) => !!t && typeof t == "object" && typeof t.removeListener == "function" && typeof t.emit == "function" && typeof t.
reallyExit == "function" && typeof t.listeners == "function" && typeof t.kill == "function" && typeof t.pid == "number" && typeof t.on == "f\
unction", "processOk"), hr = Symbol.for("signal-exit emitter"), yr = globalThis, sd = Object.defineProperty.bind(Object), gr = class {
  static {
    i(this, "Emitter");
  }
  emitted = {
    afterExit: !1,
    exit: !1
  };
  listeners = {
    afterExit: [],
    exit: []
  };
  count = 0;
  id = Math.random();
  constructor() {
    if (yr[hr])
      return yr[hr];
    sd(yr, hr, {
      value: this,
      writable: !1,
      enumerable: !1,
      configurable: !1
    });
  }
  on(e, r) {
    this.listeners[e].push(r);
  }
  removeListener(e, r) {
    let n = this.listeners[e], s = n.indexOf(r);
    s !== -1 && (s === 0 && n.length === 1 ? n.length = 0 : n.splice(s, 1));
  }
  emit(e, r, n) {
    if (this.emitted[e])
      return !1;
    this.emitted[e] = !0;
    let s = !1;
    for (let o of this.listeners[e])
      s = o(r, n) === !0 || s;
    return e === "exit" && (s = this.emit("afterExit", r, n) || s), s;
  }
}, _t = class {
  static {
    i(this, "SignalExitBase");
  }
}, od = /* @__PURE__ */ i((t) => ({
  onExit(e, r) {
    return t.onExit(e, r);
  },
  load() {
    return t.load();
  },
  unload() {
    return t.unload();
  }
}), "signalExitWrap"), br = class extends _t {
  static {
    i(this, "SignalExitFallback");
  }
  onExit() {
    return () => {
    };
  }
  load() {
  }
  unload() {
  }
}, xr = class extends _t {
  static {
    i(this, "SignalExit");
  }
  // "SIGHUP" throws an `ENOSYS` error on Windows,
  // so use a supported signal instead
  /* c8 ignore start */
  #i = vr.platform === "win32" ? "SIGINT" : "SIGHUP";
  /* c8 ignore stop */
  #t = new gr();
  #e;
  #s;
  #o;
  #n = {};
  #r = !1;
  constructor(e) {
    super(), this.#e = e, this.#n = {};
    for (let r of ye)
      this.#n[r] = () => {
        let n = this.#e.listeners(r), { count: s } = this.#t, o = e;
        if (typeof o.__signal_exit_emitter__ == "object" && typeof o.__signal_exit_emitter__.count == "number" && (s += o.__signal_exit_emitter__.
        count), n.length === s) {
          this.unload();
          let a = this.#t.emit("exit", null, r), c = r === "SIGHUP" ? this.#i : r;
          a || e.kill(e.pid, c);
        }
      };
    this.#o = e.reallyExit, this.#s = e.emit;
  }
  onExit(e, r) {
    if (!vt(this.#e))
      return () => {
      };
    this.#r === !1 && this.load();
    let n = r?.alwaysLast ? "afterExit" : "exit";
    return this.#t.on(n, e), () => {
      this.#t.removeListener(n, e), this.#t.listeners.exit.length === 0 && this.#t.listeners.afterExit.length === 0 && this.unload();
    };
  }
  load() {
    if (!this.#r) {
      this.#r = !0, this.#t.count += 1;
      for (let e of ye)
        try {
          let r = this.#n[e];
          r && this.#e.on(e, r);
        } catch {
        }
      this.#e.emit = (e, ...r) => this.#c(e, ...r), this.#e.reallyExit = (e) => this.#a(e);
    }
  }
  unload() {
    this.#r && (this.#r = !1, ye.forEach((e) => {
      let r = this.#n[e];
      if (!r)
        throw new Error("Listener not defined for signal: " + e);
      try {
        this.#e.removeListener(e, r);
      } catch {
      }
    }), this.#e.emit = this.#s, this.#e.reallyExit = this.#o, this.#t.count -= 1);
  }
  #a(e) {
    return vt(this.#e) ? (this.#e.exitCode = e || 0, this.#t.emit("exit", this.#e.exitCode, null), this.#o.call(this.#e, this.#e.exitCode)) :
    0;
  }
  #c(e, ...r) {
    let n = this.#s;
    if (e === "exit" && vt(this.#e)) {
      typeof r[0] == "number" && (this.#e.exitCode = r[0]);
      let s = n.call(this.#e, e, ...r);
      return this.#t.emit("exit", this.#e.exitCode, null), s;
    } else
      return n.call(this.#e, e, ...r);
  }
}, vr = globalThis.process, {
  /**
   * Called when the process is exiting, whether via signal, explicit
   * exit, or running out of stuff to do.
   *
   * If the global process object is not suitable for instrumentation,
   * then this will be a no-op.
   *
   * Returns a function that may be used to unload signal-exit.
   */
  onExit: Fs,
  /**
   * Load the listeners.  Likely you never need to call this, unless
   * doing a rather deep integration with signal-exit functionality.
   * Mostly exposed for the benefit of testing.
   *
   * @internal
   */
  load: jf,
  /**
   * Unload the listeners.  Likely you never need to call this, unless
   * doing a rather deep integration with signal-exit functionality.
   * Mostly exposed for the benefit of testing.
   *
   * @internal
   */
  unload: Rf
} = od(vt(vr) ? new xr(vr) : new br());

// node_modules/execa/lib/kill.js
var ad = 1e3 * 5, Bs = /* @__PURE__ */ i((t, e = "SIGTERM", r = {}) => {
  let n = t(e);
  return cd(t, e, r, n), n;
}, "spawnedKill"), cd = /* @__PURE__ */ i((t, e, r, n) => {
  if (!dd(e, r, n))
    return;
  let s = ld(r), o = setTimeout(() => {
    t("SIGKILL");
  }, s);
  o.unref && o.unref();
}, "setKillTimeout"), dd = /* @__PURE__ */ i((t, { forceKillAfterTimeout: e }, r) => ud(t) && e !== !1 && r, "shouldForceKill"), ud = /* @__PURE__ */ i(
(t) => t === id.constants.signals.SIGTERM || typeof t == "string" && t.toUpperCase() === "SIGTERM", "isSigterm"), ld = /* @__PURE__ */ i(({ forceKillAfterTimeout: t = !0 }) => {
  if (t === !0)
    return ad;
  if (!Number.isFinite(t) || t < 0)
    throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);
  return t;
}, "getForceKillAfterTimeout"), Ws = /* @__PURE__ */ i((t, e) => {
  t.kill() && (e.isCanceled = !0);
}, "spawnedCancel"), pd = /* @__PURE__ */ i((t, e, r) => {
  t.kill(e), r(Object.assign(new Error("Timed out"), { timedOut: !0, signal: e }));
}, "timeoutKill"), qs = /* @__PURE__ */ i((t, { timeout: e, killSignal: r = "SIGTERM" }, n) => {
  if (e === 0 || e === void 0)
    return n;
  let s, o = new Promise((c, l) => {
    s = setTimeout(() => {
      pd(t, r, l);
    }, e);
  }), a = n.finally(() => {
    clearTimeout(s);
  });
  return Promise.race([o, a]);
}, "setupTimeout"), Gs = /* @__PURE__ */ i(({ timeout: t }) => {
  if (t !== void 0 && (!Number.isFinite(t) || t < 0))
    throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);
}, "validateTimeout"), zs = /* @__PURE__ */ i(async (t, { cleanup: e, detached: r }, n) => {
  if (!e || r)
    return n;
  let s = Fs(() => {
    t.kill();
  });
  return n.finally(() => {
    s();
  });
}, "setExitHandler");

// node_modules/execa/lib/pipe.js
import { createWriteStream as fd } from "node:fs";
import { ChildProcess as md } from "node:child_process";

// node_modules/is-stream/index.js
function wt(t) {
  return t !== null && typeof t == "object" && typeof t.pipe == "function";
}
i(wt, "isStream");
function _r(t) {
  return wt(t) && t.writable !== !1 && typeof t._write == "function" && typeof t._writableState == "object";
}
i(_r, "isWritableStream");

// node_modules/execa/lib/pipe.js
var hd = /* @__PURE__ */ i((t) => t instanceof md && typeof t.then == "function", "isExecaChildProcess"), wr = /* @__PURE__ */ i((t, e, r) => {
  if (typeof r == "string")
    return t[e].pipe(fd(r)), t;
  if (_r(r))
    return t[e].pipe(r), t;
  if (!hd(r))
    throw new TypeError("The second argument must be a string, a stream or an Execa child process.");
  if (!_r(r.stdin))
    throw new TypeError("The target child process's stdin must be available.");
  return t[e].pipe(r.stdin), r;
}, "pipeToTarget"), Ks = /* @__PURE__ */ i((t) => {
  t.stdout !== null && (t.pipeStdout = wr.bind(void 0, t, "stdout")), t.stderr !== null && (t.pipeStderr = wr.bind(void 0, t, "stderr")), t.
  all !== void 0 && (t.pipeAll = wr.bind(void 0, t, "all"));
}, "addPipeMethods");

// node_modules/execa/lib/stream.js
import { createReadStream as Nd, readFileSync as Zd } from "node:fs";
import { setTimeout as Md } from "node:timers/promises";

// node_modules/get-stream/source/contents.js
var Be = /* @__PURE__ */ i(async (t, { init: e, convertChunk: r, getSize: n, truncateChunk: s, addChunk: o, getFinalChunk: a, finalize: c }, {
maxBuffer: l = Number.POSITIVE_INFINITY } = {}) => {
  if (!gd(t))
    throw new Error("The first argument must be a Readable, a ReadableStream, or an async iterable.");
  let p = e();
  p.length = 0;
  try {
    for await (let f of t) {
      let b = bd(f), w = r[b](f, p);
      Ys({ convertedChunk: w, state: p, getSize: n, truncateChunk: s, addChunk: o, maxBuffer: l });
    }
    return yd({ state: p, convertChunk: r, getSize: n, truncateChunk: s, addChunk: o, getFinalChunk: a, maxBuffer: l }), c(p);
  } catch (f) {
    throw f.bufferedData = c(p), f;
  }
}, "getStreamContents"), yd = /* @__PURE__ */ i(({ state: t, getSize: e, truncateChunk: r, addChunk: n, getFinalChunk: s, maxBuffer: o }) => {
  let a = s(t);
  a !== void 0 && Ys({ convertedChunk: a, state: t, getSize: e, truncateChunk: r, addChunk: n, maxBuffer: o });
}, "appendFinalChunk"), Ys = /* @__PURE__ */ i(({ convertedChunk: t, state: e, getSize: r, truncateChunk: n, addChunk: s, maxBuffer: o }) => {
  let a = r(t), c = e.length + a;
  if (c <= o) {
    Js(t, e, s, c);
    return;
  }
  let l = n(t, o - e.length);
  throw l !== void 0 && Js(l, e, s, o), new kt();
}, "appendChunk"), Js = /* @__PURE__ */ i((t, e, r, n) => {
  e.contents = r(t, e, n), e.length = n;
}, "addNewChunk"), gd = /* @__PURE__ */ i((t) => typeof t == "object" && t !== null && typeof t[Symbol.asyncIterator] == "function", "isAsyn\
cIterable"), bd = /* @__PURE__ */ i((t) => {
  let e = typeof t;
  if (e === "string")
    return "string";
  if (e !== "object" || t === null)
    return "others";
  if (globalThis.Buffer?.isBuffer(t))
    return "buffer";
  let r = Hs.call(t);
  return r === "[object ArrayBuffer]" ? "arrayBuffer" : r === "[object DataView]" ? "dataView" : Number.isInteger(t.byteLength) && Number.isInteger(
  t.byteOffset) && Hs.call(t.buffer) === "[object ArrayBuffer]" ? "typedArray" : "others";
}, "getChunkType"), { toString: Hs } = Object.prototype, kt = class extends Error {
  static {
    i(this, "MaxBufferError");
  }
  name = "MaxBufferError";
  constructor() {
    super("maxBuffer exceeded");
  }
};

// node_modules/get-stream/source/utils.js
var kr = /* @__PURE__ */ i((t) => t, "identity"), Tr = /* @__PURE__ */ i(() => {
}, "noop"), Ir = /* @__PURE__ */ i(({ contents: t }) => t, "getContentsProp"), Tt = /* @__PURE__ */ i((t) => {
  throw new Error(`Streams in object mode are not supported: ${String(t)}`);
}, "throwObjectStream"), It = /* @__PURE__ */ i((t) => t.length, "getLengthProp");

// node_modules/get-stream/source/array-buffer.js
async function Sr(t, e) {
  return Be(t, Ed, e);
}
i(Sr, "getStreamAsArrayBuffer");
var xd = /* @__PURE__ */ i(() => ({ contents: new ArrayBuffer(0) }), "initArrayBuffer"), vd = /* @__PURE__ */ i((t) => _d.encode(t), "useTex\
tEncoder"), _d = new TextEncoder(), Xs = /* @__PURE__ */ i((t) => new Uint8Array(t), "useUint8Array"), Qs = /* @__PURE__ */ i((t) => new Uint8Array(
t.buffer, t.byteOffset, t.byteLength), "useUint8ArrayWithOffset"), wd = /* @__PURE__ */ i((t, e) => t.slice(0, e), "truncateArrayBufferChunk"),
kd = /* @__PURE__ */ i((t, { contents: e, length: r }, n) => {
  let s = ro() ? Id(e, n) : Td(e, n);
  return new Uint8Array(s).set(t, r), s;
}, "addArrayBufferChunk"), Td = /* @__PURE__ */ i((t, e) => {
  if (e <= t.byteLength)
    return t;
  let r = new ArrayBuffer(to(e));
  return new Uint8Array(r).set(new Uint8Array(t), 0), r;
}, "resizeArrayBufferSlow"), Id = /* @__PURE__ */ i((t, e) => {
  if (e <= t.maxByteLength)
    return t.resize(e), t;
  let r = new ArrayBuffer(e, { maxByteLength: to(e) });
  return new Uint8Array(r).set(new Uint8Array(t), 0), r;
}, "resizeArrayBuffer"), to = /* @__PURE__ */ i((t) => eo ** Math.ceil(Math.log(t) / Math.log(eo)), "getNewContentsLength"), eo = 2, Sd = /* @__PURE__ */ i(
({ contents: t, length: e }) => ro() ? t : t.slice(0, e), "finalizeArrayBuffer"), ro = /* @__PURE__ */ i(() => "resize" in ArrayBuffer.prototype,
"hasArrayBufferResize"), Ed = {
  init: xd,
  convertChunk: {
    string: vd,
    buffer: Xs,
    arrayBuffer: Xs,
    dataView: Qs,
    typedArray: Qs,
    others: Tt
  },
  getSize: It,
  truncateChunk: wd,
  addChunk: kd,
  getFinalChunk: Tr,
  finalize: Sd
};

// node_modules/get-stream/source/buffer.js
async function St(t, e) {
  if (!("Buffer" in globalThis))
    throw new Error("getStreamAsBuffer() is only supported in Node.js");
  try {
    return no(await Sr(t, e));
  } catch (r) {
    throw r.bufferedData !== void 0 && (r.bufferedData = no(r.bufferedData)), r;
  }
}
i(St, "getStreamAsBuffer");
var no = /* @__PURE__ */ i((t) => globalThis.Buffer.from(t), "arrayBufferToNodeBuffer");

// node_modules/get-stream/source/string.js
async function Er(t, e) {
  return Be(t, jd, e);
}
i(Er, "getStreamAsString");
var Cd = /* @__PURE__ */ i(() => ({ contents: "", textDecoder: new TextDecoder() }), "initString"), Et = /* @__PURE__ */ i((t, { textDecoder: e }) => e.
decode(t, { stream: !0 }), "useTextDecoder"), Pd = /* @__PURE__ */ i((t, { contents: e }) => e + t, "addStringChunk"), Ad = /* @__PURE__ */ i(
(t, e) => t.slice(0, e), "truncateStringChunk"), Od = /* @__PURE__ */ i(({ textDecoder: t }) => {
  let e = t.decode();
  return e === "" ? void 0 : e;
}, "getFinalStringChunk"), jd = {
  init: Cd,
  convertChunk: {
    string: kr,
    buffer: Et,
    arrayBuffer: Et,
    dataView: Et,
    typedArray: Et,
    others: Tt
  },
  getSize: It,
  truncateChunk: Ad,
  addChunk: Pd,
  getFinalChunk: Od,
  finalize: Ir
};

// node_modules/execa/lib/stream.js
var io = z(oo(), 1);
var ao = /* @__PURE__ */ i((t) => {
  if (t !== void 0)
    throw new TypeError("The `input` and `inputFile` options cannot be both set.");
}, "validateInputOptions"), Dd = /* @__PURE__ */ i(({ input: t, inputFile: e }) => typeof e != "string" ? t : (ao(t), Zd(e)), "getInputSync"),
co = /* @__PURE__ */ i((t) => {
  let e = Dd(t);
  if (wt(e))
    throw new TypeError("The `input` option cannot be a stream in sync mode");
  return e;
}, "handleInputSync"), Ld = /* @__PURE__ */ i(({ input: t, inputFile: e }) => typeof e != "string" ? t : (ao(t), Nd(e)), "getInput"), uo = /* @__PURE__ */ i(
(t, e) => {
  let r = Ld(e);
  r !== void 0 && (wt(r) ? r.pipe(t.stdin) : t.stdin.end(r));
}, "handleInput"), lo = /* @__PURE__ */ i((t, { all: e }) => {
  if (!e || !t.stdout && !t.stderr)
    return;
  let r = (0, io.default)();
  return t.stdout && r.add(t.stdout), t.stderr && r.add(t.stderr), r;
}, "makeAllStream"), Cr = /* @__PURE__ */ i(async (t, e) => {
  if (!(!t || e === void 0)) {
    await Md(0), t.destroy();
    try {
      return await e;
    } catch (r) {
      return r.bufferedData;
    }
  }
}, "getBufferedData"), Pr = /* @__PURE__ */ i((t, { encoding: e, buffer: r, maxBuffer: n }) => {
  if (!(!t || !r))
    return e === "utf8" || e === "utf-8" ? Er(t, { maxBuffer: n }) : e === null || e === "buffer" ? St(t, { maxBuffer: n }) : Ud(t, n, e);
}, "getStreamPromise"), Ud = /* @__PURE__ */ i(async (t, e, r) => (await St(t, { maxBuffer: e })).toString(r), "applyEncoding"), po = /* @__PURE__ */ i(
async ({ stdout: t, stderr: e, all: r }, { encoding: n, buffer: s, maxBuffer: o }, a) => {
  let c = Pr(t, { encoding: n, buffer: s, maxBuffer: o }), l = Pr(e, { encoding: n, buffer: s, maxBuffer: o }), p = Pr(r, { encoding: n, buffer: s,
  maxBuffer: o * 2 });
  try {
    return await Promise.all([a, c, l, p]);
  } catch (f) {
    return Promise.all([
      { error: f, signal: f.signal, timedOut: f.timedOut },
      Cr(t, c),
      Cr(e, l),
      Cr(r, p)
    ]);
  }
}, "getSpawnedResult");

// node_modules/execa/lib/promise.js
var $d = (async () => {
})().constructor.prototype, Vd = ["then", "catch", "finally"].map((t) => [
  t,
  Reflect.getOwnPropertyDescriptor($d, t)
]), Ar = /* @__PURE__ */ i((t, e) => {
  for (let [r, n] of Vd) {
    let s = typeof e == "function" ? (...o) => Reflect.apply(n.value, e(), o) : n.value.bind(e);
    Reflect.defineProperty(t, r, { ...n, value: s });
  }
}, "mergePromise"), fo = /* @__PURE__ */ i((t) => new Promise((e, r) => {
  t.on("exit", (n, s) => {
    e({ exitCode: n, signal: s });
  }), t.on("error", (n) => {
    r(n);
  }), t.stdin && t.stdin.on("error", (n) => {
    r(n);
  });
}), "getSpawnedPromise");

// node_modules/execa/lib/command.js
import { Buffer as Fd } from "node:buffer";
import { ChildProcess as Bd } from "node:child_process";
var yo = /* @__PURE__ */ i((t, e = []) => Array.isArray(e) ? [t, ...e] : [t], "normalizeArgs"), Wd = /^[\w.-]+$/, qd = /* @__PURE__ */ i((t) => typeof t !=
"string" || Wd.test(t) ? t : `"${t.replaceAll('"', '\\"')}"`, "escapeArg"), Or = /* @__PURE__ */ i((t, e) => yo(t, e).join(" "), "joinComman\
d"), jr = /* @__PURE__ */ i((t, e) => yo(t, e).map((r) => qd(r)).join(" "), "getEscapedCommand"), go = / +/g, bo = /* @__PURE__ */ i((t) => {
  let e = [];
  for (let r of t.trim().split(go)) {
    let n = e.at(-1);
    n && n.endsWith("\\") ? e[e.length - 1] = `${n.slice(0, -1)} ${r}` : e.push(r);
  }
  return e;
}, "parseCommand"), mo = /* @__PURE__ */ i((t) => {
  let e = typeof t;
  if (e === "string")
    return t;
  if (e === "number")
    return String(t);
  if (e === "object" && t !== null && !(t instanceof Bd) && "stdout" in t) {
    let r = typeof t.stdout;
    if (r === "string")
      return t.stdout;
    if (Fd.isBuffer(t.stdout))
      return t.stdout.toString();
    throw new TypeError(`Unexpected "${r}" stdout in template expression`);
  }
  throw new TypeError(`Unexpected "${e}" in template expression`);
}, "parseExpression"), ho = /* @__PURE__ */ i((t, e, r) => r || t.length === 0 || e.length === 0 ? [...t, ...e] : [
  ...t.slice(0, -1),
  `${t.at(-1)}${e[0]}`,
  ...e.slice(1)
], "concatTokens"), Gd = /* @__PURE__ */ i(({ templates: t, expressions: e, tokens: r, index: n, template: s }) => {
  let o = s ?? t.raw[n], a = o.split(go).filter(Boolean), c = ho(
    r,
    a,
    o.startsWith(" ")
  );
  if (n === e.length)
    return c;
  let l = e[n], p = Array.isArray(l) ? l.map((f) => mo(f)) : [mo(l)];
  return ho(
    c,
    p,
    o.endsWith(" ")
  );
}, "parseTemplate"), Rr = /* @__PURE__ */ i((t, e) => {
  let r = [];
  for (let [n, s] of t.entries())
    r = Gd({ templates: t, expressions: e, tokens: r, index: n, template: s });
  return r;
}, "parseTemplates");

// node_modules/execa/lib/verbose.js
import { debuglog as zd } from "node:util";
import Kd from "node:process";
var xo = zd("execa").enabled, Ct = /* @__PURE__ */ i((t, e) => String(t).padStart(e, "0"), "padField"), Jd = /* @__PURE__ */ i(() => {
  let t = /* @__PURE__ */ new Date();
  return `${Ct(t.getHours(), 2)}:${Ct(t.getMinutes(), 2)}:${Ct(t.getSeconds(), 2)}.${Ct(t.getMilliseconds(), 3)}`;
}, "getTimestamp"), Nr = /* @__PURE__ */ i((t, { verbose: e }) => {
  e && Kd.stderr.write(`[${Jd()}] ${t}
`);
}, "logCommand");

// node_modules/execa/index.js
var Xd = 1e3 * 1e3 * 100, Qd = /* @__PURE__ */ i(({ env: t, extendEnv: e, preferLocal: r, localDir: n, execPath: s }) => {
  let o = e ? { ...Pt.env, ...t } : t;
  return r ? Ns({ env: o, cwd: n, execPath: s }) : o;
}, "getEnv"), wo = /* @__PURE__ */ i((t, e, r = {}) => {
  let n = _o.default._parse(t, e, r);
  return t = n.command, e = n.args, r = n.options, r = {
    maxBuffer: Xd,
    buffer: !0,
    stripFinalNewline: !0,
    extendEnv: !0,
    preferLocal: !1,
    localDir: r.cwd || Pt.cwd(),
    execPath: Pt.execPath,
    encoding: "utf8",
    reject: !0,
    cleanup: !0,
    all: !1,
    windowsHide: !0,
    verbose: xo,
    ...r
  }, r.env = Qd(r), r.stdio = Vs(r), Pt.platform === "win32" && Yd.basename(t, ".exe") === "cmd" && e.unshift("/q"), { file: t, args: e, options: r,
  parsed: n };
}, "handleArguments"), We = /* @__PURE__ */ i((t, e, r) => typeof e != "string" && !Hd.isBuffer(e) ? r === void 0 ? void 0 : "" : t.stripFinalNewline ?
lr(e) : e, "handleOutput");
function ko(t, e, r) {
  let n = wo(t, e, r), s = Or(t, e), o = jr(t, e);
  Nr(o, n.options), Gs(n.options);
  let a;
  try {
    a = Zr.spawn(n.file, n.args, n.options);
  } catch (_) {
    let C = new Zr.ChildProcess(), S = Promise.reject(Fe({
      error: _,
      stdout: "",
      stderr: "",
      all: "",
      command: s,
      escapedCommand: o,
      parsed: n,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    }));
    return Ar(C, S), C;
  }
  let c = fo(a), l = qs(a, n.options, c), p = zs(a, n.options, l), f = { isCanceled: !1 };
  a.kill = Bs.bind(null, a.kill.bind(a)), a.cancel = Ws.bind(null, a, f);
  let w = Ms(/* @__PURE__ */ i(async () => {
    let [{ error: _, exitCode: C, signal: S, timedOut: P }, G, Oe, be] = await po(a, n.options, p), je = We(n.options, G), Je = We(n.options,
    Oe), He = We(n.options, be);
    if (_ || C !== 0 || S !== null) {
      let k = Fe({
        error: _,
        exitCode: C,
        signal: S,
        stdout: je,
        stderr: Je,
        all: He,
        command: s,
        escapedCommand: o,
        parsed: n,
        timedOut: P,
        isCanceled: f.isCanceled || (n.options.signal ? n.options.signal.aborted : !1),
        killed: a.killed
      });
      if (!n.options.reject)
        return k;
      throw k;
    }
    return {
      command: s,
      escapedCommand: o,
      exitCode: 0,
      stdout: je,
      stderr: Je,
      all: He,
      failed: !1,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    };
  }, "handlePromise"));
  return uo(a, n.options), a.all = lo(a, n.options), Ks(a), Ar(a, w), a;
}
i(ko, "execa");
function eu(t, e, r) {
  let n = wo(t, e, r), s = Or(t, e), o = jr(t, e);
  Nr(o, n.options);
  let a = co(n.options), c;
  try {
    c = Zr.spawnSync(n.file, n.args, { ...n.options, input: a });
  } catch (f) {
    throw Fe({
      error: f,
      stdout: "",
      stderr: "",
      all: "",
      command: s,
      escapedCommand: o,
      parsed: n,
      timedOut: !1,
      isCanceled: !1,
      killed: !1
    });
  }
  let l = We(n.options, c.stdout, c.error), p = We(n.options, c.stderr, c.error);
  if (c.error || c.status !== 0 || c.signal !== null) {
    let f = Fe({
      stdout: l,
      stderr: p,
      error: c.error,
      signal: c.signal,
      exitCode: c.status,
      command: s,
      escapedCommand: o,
      parsed: n,
      timedOut: c.error && c.error.code === "ETIMEDOUT",
      isCanceled: !1,
      killed: c.signal !== null
    });
    if (!n.options.reject)
      return f;
    throw f;
  }
  return {
    command: s,
    escapedCommand: o,
    exitCode: 0,
    stdout: l,
    stderr: p,
    failed: !1,
    timedOut: !1,
    isCanceled: !1,
    killed: !1
  };
}
i(eu, "execaSync");
var tu = /* @__PURE__ */ i(({ input: t, inputFile: e, stdio: r }) => t === void 0 && e === void 0 && r === void 0 ? { stdin: "inherit" } : {},
"normalizeScriptStdin"), vo = /* @__PURE__ */ i((t = {}) => ({
  preferLocal: !0,
  ...tu(t),
  ...t
}), "normalizeScriptOptions");
function To(t) {
  function e(r, ...n) {
    if (!Array.isArray(r))
      return To({ ...t, ...r });
    let [s, ...o] = Rr(r, n);
    return ko(s, o, vo(t));
  }
  return i(e, "$"), e.sync = (r, ...n) => {
    if (!Array.isArray(r))
      throw new TypeError("Please use $(options).sync`command` instead of $.sync(options)`command`.");
    let [s, ...o] = Rr(r, n);
    return eu(s, o, vo(t));
  }, e;
}
i(To, "create$");
var Qm = To();
function Io(t, e) {
  let [r, ...n] = bo(t);
  return ko(r, n, e);
}
i(Io, "execaCommand");

// src/telemetry/exec-command-count-lines.ts
async function At(t, e) {
  let r = Io(t, { shell: !0, buffer: !1, ...e });
  if (!r.stdout)
    throw new Error("Unexpected missing stdout");
  let n = 0, s = ru(r.stdout);
  return s.on("line", () => {
    n += 1;
  }), await r, s.close(), n;
}
i(At, "execCommandCountLines");

// src/common/utils/file-cache.ts
import { createHash as Eo, randomBytes as nu } from "node:crypto";
import { mkdirSync as Dr, readFileSync as su, readdirSync as ou, rmSync as Co, writeFileSync as iu } from "node:fs";
import { readFile as Po, readdir as Ao, rm as Oo, writeFile as au } from "node:fs/promises";
import { tmpdir as cu } from "node:os";
import { join as qe } from "node:path";
var Ot = class {
  static {
    i(this, "FileSystemCache");
  }
  constructor(e = {}) {
    this.prefix = (e.ns || e.prefix || "") + "-", this.hash_alg = e.hash_alg || "md5", this.cache_dir = e.basePath || qe(cu(), nu(15).toString(
    "base64").replace(/\//g, "-")), this.ttl = e.ttl || 0, Eo(this.hash_alg), Dr(this.cache_dir, { recursive: !0 });
  }
  generateHash(e) {
    return qe(this.cache_dir, this.prefix + Eo(this.hash_alg).update(e).digest("hex"));
  }
  isExpired(e, r) {
    return e.ttl != null && r > e.ttl;
  }
  parseCacheData(e, r) {
    let n = JSON.parse(e);
    return this.isExpired(n, Date.now()) ? r : n.content;
  }
  parseSetData(e, r, n = {}) {
    let s = n.ttl ?? this.ttl;
    return JSON.stringify({ key: e, content: r, ...s && { ttl: Date.now() + s * 1e3 } });
  }
  async get(e, r) {
    try {
      let n = await Po(this.generateHash(e), "utf8");
      return this.parseCacheData(n, r);
    } catch {
      return r;
    }
  }
  getSync(e, r) {
    try {
      let n = su(this.generateHash(e), "utf8");
      return this.parseCacheData(n, r);
    } catch {
      return r;
    }
  }
  async set(e, r, n = {}) {
    let s = typeof n == "number" ? { ttl: n } : n;
    Dr(this.cache_dir, { recursive: !0 }), await au(this.generateHash(e), this.parseSetData(e, r, s), {
      encoding: s.encoding || "utf8"
    });
  }
  setSync(e, r, n = {}) {
    let s = typeof n == "number" ? { ttl: n } : n;
    Dr(this.cache_dir, { recursive: !0 }), iu(this.generateHash(e), this.parseSetData(e, r, s), {
      encoding: s.encoding || "utf8"
    });
  }
  async setMany(e, r) {
    await Promise.all(e.map((n) => this.set(n.key, n.content ?? n.value, r)));
  }
  setManySync(e, r) {
    e.forEach((n) => this.setSync(n.key, n.content ?? n.value, r));
  }
  async remove(e) {
    await Oo(this.generateHash(e), { force: !0 });
  }
  removeSync(e) {
    Co(this.generateHash(e), { force: !0 });
  }
  async clear() {
    let e = await Ao(this.cache_dir);
    await Promise.all(
      e.filter((r) => r.startsWith(this.prefix)).map((r) => Oo(qe(this.cache_dir, r), { force: !0 }))
    );
  }
  clearSync() {
    ou(this.cache_dir).filter((e) => e.startsWith(this.prefix)).forEach((e) => Co(qe(this.cache_dir, e), { force: !0 }));
  }
  async getAll() {
    let e = Date.now(), r = await Ao(this.cache_dir);
    return (await Promise.all(
      r.filter((s) => s.startsWith(this.prefix)).map((s) => Po(qe(this.cache_dir, s), "utf8"))
    )).map((s) => JSON.parse(s)).filter((s) => s.content && !this.isExpired(s, e));
  }
  async load() {
    return {
      files: (await this.getAll()).map((r) => ({
        path: this.generateHash(r.key),
        value: r.content,
        key: r.key
      }))
    };
  }
};
function Lr(t) {
  return new Ot(t);
}
i(Lr, "createFileSystemCache");

// src/common/utils/resolve-path-in-sb-cache.ts
import { join as Vo } from "node:path";

// node_modules/find-cache-dir/index.js
var $o = z(Ro(), 1);
import wu from "node:process";
import Ge from "node:path";
import Rt from "node:fs";

// ../node_modules/pkg-dir/index.js
import _u from "node:path";

// ../node_modules/pkg-dir/node_modules/find-up/index.js
import jt from "node:path";
import { fileURLToPath as gu } from "node:url";

// ../node_modules/locate-path/index.js
import lu from "node:process";
import pu from "node:path";
import No, { promises as Eh } from "node:fs";
import { fileURLToPath as fu } from "node:url";
var Zo = {
  directory: "isDirectory",
  file: "isFile"
};
function mu(t) {
  if (!Object.hasOwnProperty.call(Zo, t))
    throw new Error(`Invalid type specified: ${t}`);
}
i(mu, "checkType");
var hu = /* @__PURE__ */ i((t, e) => e[Zo[t]](), "matchType"), yu = /* @__PURE__ */ i((t) => t instanceof URL ? fu(t) : t, "toPath");
function Ur(t, {
  cwd: e = lu.cwd(),
  type: r = "file",
  allowSymlinks: n = !0
} = {}) {
  mu(r), e = yu(e);
  let s = n ? No.statSync : No.lstatSync;
  for (let o of t)
    try {
      let a = s(pu.resolve(e, o), {
        throwIfNoEntry: !1
      });
      if (!a)
        continue;
      if (hu(r, a))
        return o;
    } catch {
    }
}
i(Ur, "locatePathSync");

// ../node_modules/pkg-dir/node_modules/path-exists/index.js
import Rh, { promises as Nh } from "node:fs";

// ../node_modules/pkg-dir/node_modules/find-up/index.js
var bu = /* @__PURE__ */ i((t) => t instanceof URL ? gu(t) : t, "toPath"), xu = Symbol("findUpStop");
function vu(t, e = {}) {
  let r = jt.resolve(bu(e.cwd) || ""), { root: n } = jt.parse(r), s = e.stopAt || n, o = e.limit || Number.POSITIVE_INFINITY, a = [t].flat(),
  c = /* @__PURE__ */ i((p) => {
    if (typeof t != "function")
      return Ur(a, p);
    let f = t(p.cwd);
    return typeof f == "string" ? Ur([f], p) : f;
  }, "runMatcher"), l = [];
  for (; ; ) {
    let p = c({ ...e, cwd: r });
    if (p === xu || (p && l.push(jt.resolve(r, p)), r === s || l.length >= o))
      break;
    r = jt.dirname(r);
  }
  return l;
}
i(vu, "findUpMultipleSync");
function Mo(t, e = {}) {
  return vu(t, { ...e, limit: 1 })[0];
}
i(Mo, "findUpSync");

// ../node_modules/pkg-dir/index.js
function Do({ cwd: t } = {}) {
  let e = Mo("package.json", { cwd: t });
  return e && _u.dirname(e);
}
i(Do, "packageDirectorySync");

// node_modules/find-cache-dir/index.js
var { env: $r, cwd: ku } = wu, Lo = /* @__PURE__ */ i((t) => {
  try {
    return Rt.accessSync(t, Rt.constants.W_OK), !0;
  } catch {
    return !1;
  }
}, "isWritable");
function Uo(t, e) {
  return e.create && Rt.mkdirSync(t, { recursive: !0 }), t;
}
i(Uo, "useDirectory");
function Tu(t) {
  let e = Ge.join(t, "node_modules");
  if (!(!Lo(e) && (Rt.existsSync(e) || !Lo(Ge.join(t)))))
    return e;
}
i(Tu, "getNodeModuleDirectory");
function Vr(t = {}) {
  if ($r.CACHE_DIR && !["true", "false", "1", "0"].includes($r.CACHE_DIR))
    return Uo(Ge.join($r.CACHE_DIR, t.name), t);
  let { cwd: e = ku(), files: r } = t;
  if (r) {
    if (!Array.isArray(r))
      throw new TypeError(`Expected \`files\` option to be an array, got \`${typeof r}\`.`);
    e = (0, $o.default)(r.map((s) => Ge.resolve(e, s)));
  }
  if (e = Do({ cwd: e }), !(!e || !Tu(e)))
    return Uo(Ge.join(e, "node_modules", ".cache", t.name), t);
}
i(Vr, "findCacheDirectory");

// src/common/utils/resolve-path-in-sb-cache.ts
function Fo(t, e = "default") {
  let r = Vr({ name: "storybook" });
  return r ||= Vo(process.cwd(), "node_modules", ".cache", "storybook"), Vo(r, e, t);
}
i(Fo, "resolvePathInStorybookCache");

// src/telemetry/run-telemetry-operation.ts
var Bo = Lr({
  basePath: Fo("telemetry"),
  ns: "storybook",
  ttl: 24 * 60 * 60 * 1e3
  // 24h
}), Nt = /* @__PURE__ */ i(async (t, e) => {
  let r = await Bo.get(t);
  return r === void 0 && (r = await e(), r !== void 0 && await Bo.set(t, r)), r;
}, "runTelemetryOperation");

// src/telemetry/get-application-file-count.ts
var Su = ["page", "screen"], Eu = ["js", "jsx", "ts", "tsx"], Cu = /* @__PURE__ */ i(async (t) => {
  let r = Su.flatMap((n) => [
    n,
    [n[0].toUpperCase(), ...n.slice(1)].join("")
  ]).flatMap(
    (n) => Eu.map((s) => `"${t}${Iu}*${n}*.${s}"`)
  );
  try {
    let n = `git ls-files -- ${r.join(" ")}`;
    return await At(n);
  } catch {
    return;
  }
}, "getApplicationFilesCountUncached"), Wo = /* @__PURE__ */ i(async (t) => Nt(
  "applicationFiles",
  async () => Cu(t)
), "getApplicationFileCount");

// src/telemetry/get-chromatic-version.ts
function qo(t) {
  let e = t.dependencies?.chromatic || t.devDependencies?.chromatic || t.peerDependencies?.chromatic;
  return e || (t.scripts && Object.values(t.scripts).find((r) => r?.match(/chromatic/)) ? "latest" : void 0);
}
i(qo, "getChromaticVersionSpecifier");

// src/telemetry/get-framework-info.ts
import { normalize as Ou } from "node:path";
import { frameworkPackages as ju } from "storybook/internal/common";

// src/telemetry/package-json.ts
import { readFile as Pu } from "node:fs/promises";
import { join as Au } from "node:path";
var Fr = /* @__PURE__ */ i(async (t) => {
  let e = Object.keys(t);
  return Promise.all(e.map(Zt));
}, "getActualPackageVersions"), Zt = /* @__PURE__ */ i(async (t) => {
  try {
    let e = await Br(t);
    return {
      name: t,
      version: e.version
    };
  } catch {
    return { name: t, version: null };
  }
}, "getActualPackageVersion"), Br = /* @__PURE__ */ i(async (t) => {
  try {
    let e = N.resolve(Au(t, "package.json"), {
      paths: [process.cwd()]
    });
    return JSON.parse(await Pu(e, { encoding: "utf8" }));
  } catch {
    return null;
  }
}, "getActualPackageJson");

// src/telemetry/get-framework-info.ts
var Ru = [
  "html",
  "react",
  "svelte",
  "vue3",
  "preact",
  "server",
  "vue",
  "web-components",
  "angular",
  "ember"
], Nu = ["builder-webpack5", "builder-vite"];
function Go(t, e) {
  let { name: r = "", version: n, dependencies: s, devDependencies: o, peerDependencies: a } = t, c = {
    // We include the framework itself because it may be a renderer too (e.g. angular)
    [r]: n,
    ...s,
    ...o,
    ...a
  };
  return e.map((l) => `@storybook/${l}`).find((l) => c[l]);
}
i(Go, "findMatchingPackage");
var Zu = /* @__PURE__ */ i((t) => {
  let e = Ou(t).replace(new RegExp(/\\/, "g"), "/");
  return Object.keys(ju).find((n) => e.endsWith(n)) || Re(t).replace(/.*node_modules[\\/]/, "");
}, "getFrameworkPackageName");
async function zo(t) {
  if (!t?.framework)
    return {};
  let e = typeof t.framework == "string" ? t.framework : t.framework?.name;
  if (!e)
    return {};
  let r = await Br(e);
  if (!r)
    return {};
  let n = Go(r, Nu), s = Go(r, Ru), o = Zu(e), a = typeof t.framework == "object" ? t.framework.options : {};
  return {
    framework: {
      name: o,
      options: a
    },
    builder: n,
    renderer: s
  };
}
i(zo, "getFrameworkInfo");

// src/telemetry/get-has-router-package.ts
var Mu = /* @__PURE__ */ new Set([
  "react-router",
  "react-router-dom",
  "remix",
  "@tanstack/react-router",
  "expo-router",
  "@reach/router",
  "react-easy-router",
  "@remix-run/router",
  "wouter",
  "wouter-preact",
  "preact-router",
  "vue-router",
  "unplugin-vue-router",
  "@angular/router",
  "@solidjs/router",
  // metaframeworks that imply routing
  "next",
  "react-scripts",
  "gatsby",
  "nuxt",
  "@sveltejs/kit"
]);
function Ko(t) {
  return Object.keys(t?.dependencies ?? {}).some(
    (e) => Mu.has(e)
  );
}
i(Ko, "getHasRouterPackage");

// src/telemetry/get-monorepo-type.ts
import { existsSync as Jo, readFileSync as Du } from "node:fs";
import { join as Wr } from "node:path";
import { getProjectRoot as qr } from "storybook/internal/common";
var Ho = {
  Nx: "nx.json",
  Turborepo: "turbo.json",
  Lerna: "lerna.json",
  Rush: "rush.json",
  Lage: "lage.config.json"
}, Yo = /* @__PURE__ */ i(() => {
  let e = Object.keys(Ho).find((n) => {
    let s = Wr(qr(), Ho[n]);
    return Jo(s);
  });
  if (e)
    return e;
  if (!Jo(Wr(qr(), "package.json")))
    return;
  if (JSON.parse(
    Du(Wr(qr(), "package.json"), { encoding: "utf8" })
  )?.workspaces)
    return "Workspaces";
}, "getMonorepoType");

// src/telemetry/get-portable-stories-usage.ts
var Lu = /* @__PURE__ */ i(async (t) => {
  try {
    let e = "git grep -l composeStor" + (t ? ` -- ${t}` : "");
    return await At(e);
  } catch (e) {
    return e.exitCode === 1 ? 0 : void 0;
  }
}, "getPortableStoriesFileCountUncached"), Xo = /* @__PURE__ */ i(async (t) => Nt(
  "portableStories",
  async () => Lu(t)
), "getPortableStoriesFileCount");

// src/telemetry/storybook-metadata.ts
var Qo = {
  next: "Next",
  "react-scripts": "CRA",
  gatsby: "Gatsby",
  "@nuxtjs/storybook": "nuxt",
  "@nrwl/storybook": "nx",
  "@vue/cli-service": "vue-cli",
  "@sveltejs/kit": "sveltekit"
}, ei = /* @__PURE__ */ i((t) => Re(t).replace(/\/dist\/.*/, "").replace(/\.[mc]?[tj]?s[x]?$/, "").replace(/\/register$/, "").replace(/\/manager$/,
"").replace(/\/preset$/, ""), "sanitizeAddonName"), Gu = /* @__PURE__ */ i(async ({
  packageJsonPath: t,
  packageJson: e,
  mainConfig: r,
  configDir: n
}) => {
  let s = await Wn(), o = {
    generatedAt: (/* @__PURE__ */ new Date()).getTime(),
    userSince: s.value.userSince,
    hasCustomBabel: !1,
    hasCustomWebpack: !1,
    hasStaticDirs: !1,
    hasStorybookEslint: !1,
    refCount: 0
  }, a = {
    ...e?.dependencies,
    ...e?.devDependencies,
    ...e?.peerDependencies
  }, c = Object.keys(a).find((k) => !!Qo[k]);
  if (c) {
    let { version: k } = await Zt(c);
    o.metaFramework = {
      name: Qo[c],
      packageName: c,
      version: k
    };
  }
  let l = [
    "playwright",
    "vitest",
    "jest",
    "cypress",
    "nightwatch",
    "webdriver",
    "@web/test-runner",
    "puppeteer",
    "karma",
    "jasmine",
    "chai",
    "testing-library",
    "@ngneat/spectator",
    "wdio",
    "msw",
    "miragejs",
    "sinon",
    "chromatic"
  ], p = Object.keys(a).filter(
    (k) => l.find((O) => k.includes(O))
  );
  o.testPackages = Object.fromEntries(
    await Promise.all(
      p.map(async (k) => [k, (await Zt(k))?.version])
    )
  ), o.hasRouterPackage = Ko(e);
  let f = Yo();
  f && (o.monorepo = f);
  try {
    let k = await Gt({ cwd: $u() });
    k && (o.packageManager = {
      type: k.name,
      version: k.version,
      agent: k.agent
    });
  } catch {
  }
  let b = a.typescript ? "typescript" : "javascript";
  if (!r)
    return {
      ...o,
      storybookVersionSpecifier: Wu.storybook,
      language: b
    };
  o.hasCustomBabel = !!r.babel, o.hasCustomWebpack = !!r.webpackFinal, o.hasStaticDirs = !!r.staticDirs, typeof r.typescript == "object" && (o.
  typescriptOptions = r.typescript);
  let w = await zo(r);
  typeof r.refs == "object" && (o.refCount = Object.keys(r.refs).length), typeof r.features == "object" && (o.features = r.features);
  let _ = {};
  r.addons && r.addons.forEach((k) => {
    let O, Ye;
    typeof k == "string" ? O = ei(k) : (k.name.includes("addon-essentials") && (Ye = k.options), O = ei(k.name)), _[O] = {
      options: Ye,
      version: void 0
    };
  });
  let C = qo(e);
  C && (_.chromatic = {
    version: void 0,
    versionSpecifier: C,
    options: void 0
  }), (await Fr(_)).forEach(({ name: k, version: O }) => {
    _[k].version = O;
  });
  let P = Object.keys(_), G = Object.keys(a).filter((k) => k.includes("storybook") && !P.includes(k)).reduce((k, O) => ({
    ...k,
    [O]: { version: void 0 }
  }), {});
  (await Fr(G)).forEach(({ name: k, version: O }) => {
    G[k].version = O;
  });
  let be = !!a["eslint-plugin-storybook"], je = Fu(n);
  try {
    let { previewConfigPath: k } = je;
    if (k) {
      let O = await qu(k), Ye = !!(O.getFieldNode(["globals"]) || O.getFieldNode(["globalTypes"]));
      o.preview = { ...o.preview, usesGlobals: Ye };
    }
  } catch {
  }
  let Je = await Xo(), He = await Wo(Uu(t));
  return {
    ...o,
    ...w,
    portableStoriesFileCount: Je,
    applicationFileCount: He,
    storybookVersion: nt,
    storybookVersionSpecifier: je.version,
    language: b,
    storybookPackages: G,
    addons: _,
    hasStorybookEslint: be
  };
}, "computeStorybookMetadata");
async function zu() {
  let t = await Ft(process.cwd());
  return t ? {
    packageJsonPath: t,
    packageJson: await ln(t) || {}
  } : {
    packageJsonPath: process.cwd(),
    packageJson: {}
  };
}
i(zu, "getPackageJsonDetails");
var Mt, ti = /* @__PURE__ */ i(async (t) => {
  if (Mt)
    return Mt;
  let { packageJson: e, packageJsonPath: r } = await zu(), n = (t || Vu(
    String(e?.scripts?.storybook || ""),
    "-c",
    "--config-dir"
  )) ?? ".storybook", s = await Bu({ configDir: n }).catch(() => {
  });
  return Mt = await Gu({
    mainConfig: s,
    packageJson: e,
    packageJsonPath: r,
    configDir: n
  }), Mt;
}, "getStorybookMetadata");

// src/telemetry/telemetry.ts
var pi = z(ni(), 1);
import * as li from "node:os";

// ../node_modules/nanoid/index.js
import { randomFillSync as oi } from "crypto";

// ../node_modules/nanoid/url-alphabet/index.js
var si = "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";

// ../node_modules/nanoid/index.js
var Ku = 128, ge, Ae, Ju = /* @__PURE__ */ i((t) => {
  !ge || ge.length < t ? (ge = Buffer.allocUnsafe(t * Ku), oi(ge), Ae = 0) : Ae + t > ge.length && (oi(ge), Ae = 0), Ae += t;
}, "fillPool");
var ze = /* @__PURE__ */ i((t = 21) => {
  Ju(t -= 0);
  let e = "";
  for (let r = Ae - t; r < Ae; r++)
    e += si[ge[r] & 63];
  return e;
}, "nanoid");

// src/telemetry/anonymous-id.ts
So();
import { relative as Yu } from "node:path";
import { getProjectRoot as Xu } from "storybook/internal/common";
import { execSync as Qu } from "child_process";

// src/telemetry/one-way-hash.ts
import { createHash as Hu } from "crypto";
var Gr = /* @__PURE__ */ i((t) => {
  let e = Hu("sha256");
  return e.update("storybook-telemetry-salt"), e.update(t), e.digest("hex");
}, "oneWayHash");

// src/telemetry/anonymous-id.ts
function el(t) {
  let n = t.trim().replace(/#.*$/, "").replace(/^.*@/, "").replace(/^.*\/\//, "");
  return (n.endsWith(".git") ? n : `${n}.git`).replace(":", "/");
}
i(el, "normalizeGitUrl");
function tl(t, e) {
  return `${el(t)}${Mr(e)}`;
}
i(tl, "unhashedProjectId");
var Lt, ii = /* @__PURE__ */ i(() => {
  if (Lt)
    return Lt;
  try {
    let t = Yu(Xu(), process.cwd()), e = Qu("git config --local --get remote.origin.url", {
      timeout: 1e3,
      stdio: "pipe"
    });
    Lt = Gr(tl(String(e), t));
  } catch {
  }
  return Lt;
}, "getAnonymousProjectId");

// src/telemetry/event-cache.ts
import { cache as Kr } from "storybook/internal/common";
var zr = Promise.resolve(), rl = /* @__PURE__ */ i(async (t, e) => {
  let r = await Kr.get("lastEvents") || {};
  r[t] = { body: e, timestamp: Date.now() }, await Kr.set("lastEvents", r);
}, "setHelper"), ci = /* @__PURE__ */ i(async (t, e) => (await zr, zr = rl(t, e), zr), "set");
var nl = /* @__PURE__ */ i((t) => {
  let { body: e, timestamp: r } = t;
  return {
    timestamp: r,
    eventType: e?.eventType,
    eventId: e?.eventId,
    sessionId: e?.sessionId
  };
}, "upgradeFields"), sl = ["init", "upgrade"], ol = ["build", "dev", "error"], ai = /* @__PURE__ */ i((t, e) => {
  let r = e.map((n) => t?.[n]).filter(Boolean).sort((n, s) => s.timestamp - n.timestamp);
  return r.length > 0 ? r[0] : void 0;
}, "lastEvent"), il = /* @__PURE__ */ i(async (t = void 0) => {
  let e = t || await Kr.get("lastEvents") || {}, r = ai(e, sl), n = ai(e, ol);
  if (r)
    return !n?.timestamp || r.timestamp > n.timestamp ? nl(r) : void 0;
}, "getPrecedingUpgrade");

// src/telemetry/fetch.ts
var di = global.fetch;

// src/telemetry/session-id.ts
import { cache as ui } from "storybook/internal/common";
var al = 1e3 * 60 * 60 * 2, Ke;
var Jr = /* @__PURE__ */ i(async () => {
  let t = Date.now();
  if (!Ke) {
    let e = await ui.get("session");
    e && e.lastUsed >= t - al ? Ke = e.id : Ke = ze();
  }
  return await ui.set("session", { id: Ke, lastUsed: t }), Ke;
}, "getSessionId");

// src/telemetry/telemetry.ts
var cl = (0, pi.default)(di), dl = process.env.STORYBOOK_TELEMETRY_URL || "https://storybook.js.org/event-log", Ut = [], ul = /* @__PURE__ */ i(
(t, e) => {
  Hr[t] = e;
}, "addToGlobalContext"), ll = /* @__PURE__ */ i(() => {
  try {
    let t = li.platform();
    return t === "win32" ? "Windows" : t === "darwin" ? "macOS" : t === "linux" ? "Linux" : `Other: ${t}`;
  } catch {
    return "Unknown";
  }
}, "getOperatingSystem"), Hr = {
  inCI: !!process.env.CI,
  isTTY: process.stdout.isTTY,
  platform: ll(),
  nodeVersion: process.versions.node,
  storybookVersion: nt
}, pl = /* @__PURE__ */ i(async (t, e, r) => {
  let { eventType: n, payload: s, metadata: o, ...a } = t, c = await Jr(), l = ze(), p = { ...a, eventType: n, eventId: l, sessionId: c, metadata: o,
  payload: s, context: e };
  return cl(dl, {
    method: "post",
    body: JSON.stringify(p),
    headers: { "Content-Type": "application/json" },
    retries: 3,
    retryOn: [503, 504],
    retryDelay: /* @__PURE__ */ i((f) => 2 ** f * (typeof r?.retryDelay == "number" && !Number.isNaN(r?.retryDelay) ? r.retryDelay : 1e3), "\
retryDelay")
  });
}, "prepareRequest");
async function fi(t, e = { retryDelay: 1e3, immediate: !1 }) {
  let { eventType: r, payload: n, metadata: s, ...o } = t, a = e.stripMetadata ? Hr : {
    ...Hr,
    anonymousId: ii()
  }, c;
  try {
    c = pl(t, a, e), Ut.push(c), e.immediate ? await Promise.all(Ut) : await c;
    let l = await Jr(), p = ze(), f = { ...o, eventType: r, eventId: p, sessionId: l, metadata: s, payload: n, context: a };
    await ci(r, f);
  } catch {
  } finally {
    Ut = Ut.filter((l) => l !== c);
  }
}
i(fi, "sendTelemetry");

// src/telemetry/index.ts
var Ab = /* @__PURE__ */ i((t) => t.startsWith("example-button--") || t.startsWith("example-header--") || t.startsWith("example-page--"), "i\
sExampleStoryId"), Ob = /* @__PURE__ */ i(async (t, e = {}, r = {}) => {
  t !== "boot" && r.notify !== !1 && await nn();
  let n = {
    eventType: t,
    payload: e
  };
  try {
    r?.stripMetadata || (n.metadata = await ti(r?.configDir));
  } catch (s) {
    n.payload.metadataErrorMessage = tt(s).message, r?.enableCrashReports && (n.payload.metadataError = tt(s));
  } finally {
    let { error: s } = n.payload;
    s && (n.payload.error = tt(s)), (!n.payload.error || r?.enableCrashReports) && (process.env?.STORYBOOK_TELEMETRY_DEBUG && (mi.info(`
[telemetry]`), mi.info(JSON.stringify(n, null, 2))), await fi(n, r));
  }
}, "telemetry");
export {
  ul as addToGlobalContext,
  Re as cleanPaths,
  Gu as computeStorybookMetadata,
  il as getPrecedingUpgrade,
  ti as getStorybookMetadata,
  Ab as isExampleStoryId,
  Qo as metaFrameworks,
  Gr as oneWayHash,
  on as removeAnsiEscapeCodes,
  ei as sanitizeAddonName,
  tt as sanitizeError,
  Ob as telemetry
};
