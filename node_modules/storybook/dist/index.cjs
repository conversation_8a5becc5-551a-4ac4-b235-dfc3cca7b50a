"use strict";
var l = Object.defineProperty;
var t = Object.getOwnPropertyDescriptor;
var h = Object.getOwnPropertyNames;
var n = Object.prototype.hasOwnProperty;
var d = (o, e, s, i) => {
  if (e && typeof e == "object" || typeof e == "function")
    for (let u of h(e))
      !n.call(o, u) && u !== s && l(o, u, { get: () => e[u], enumerable: !(i = t(e, u)) || i.enumerable });
  return o;
};
var r = (o) => d(l({}, "__esModule", { value: !0 }), o);

// src/index.ts
var a = {};
module.exports = r(a);
console.log("This file should remain unused until we have figured out the best API");
