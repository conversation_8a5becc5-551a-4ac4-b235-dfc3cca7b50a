"use strict";
var Ne = Object.defineProperty;
var pr = Object.getOwnPropertyDescriptor;
var gr = Object.getOwnPropertyNames;
var hr = Object.prototype.hasOwnProperty;
var c = (e, t) => Ne(e, "name", { value: t, configurable: !0 });
var dr = (e, t) => {
  for (var n in t)
    Ne(e, n, { get: t[n], enumerable: !0 });
}, yr = (e, t, n, r) => {
  if (t && typeof t == "object" || typeof t == "function")
    for (let o of gr(t))
      !hr.call(e, o) && o !== n && Ne(e, o, { get: () => t[o], enumerable: !(r = pr(t, o)) || r.enumerable });
  return e;
};
var br = (e) => yr(Ne({}, "__esModule", { value: !0 }), e);

// src/instrumenter/index.ts
var ii = {};
dr(ii, {
  CallStates: () => cr,
  EVENTS: () => ee,
  instrument: () => fr
});
module.exports = br(ii);

// src/instrumenter/instrumenter.ts
var ar = require("storybook/internal/client-logger"), de = require("storybook/internal/core-events"), H = require("@storybook/global");

// ../node_modules/tinyrainbow/dist/chunk-BVHSVHOK.js
var Sr = {
  reset: [0, 0],
  bold: [1, 22, "\x1B[22m\x1B[1m"],
  dim: [2, 22, "\x1B[22m\x1B[2m"],
  italic: [3, 23],
  underline: [4, 24],
  inverse: [7, 27],
  hidden: [8, 28],
  strikethrough: [9, 29],
  black: [30, 39],
  red: [31, 39],
  green: [32, 39],
  yellow: [33, 39],
  blue: [34, 39],
  magenta: [35, 39],
  cyan: [36, 39],
  white: [37, 39],
  gray: [90, 39],
  bgBlack: [40, 49],
  bgRed: [41, 49],
  bgGreen: [42, 49],
  bgYellow: [43, 49],
  bgBlue: [44, 49],
  bgMagenta: [45, 49],
  bgCyan: [46, 49],
  bgWhite: [47, 49],
  blackBright: [90, 39],
  redBright: [91, 39],
  greenBright: [92, 39],
  yellowBright: [93, 39],
  blueBright: [94, 39],
  magentaBright: [95, 39],
  cyanBright: [96, 39],
  whiteBright: [97, 39],
  bgBlackBright: [100, 49],
  bgRedBright: [101, 49],
  bgGreenBright: [102, 49],
  bgYellowBright: [103, 49],
  bgBlueBright: [104, 49],
  bgMagentaBright: [105, 49],
  bgCyanBright: [106, 49],
  bgWhiteBright: [107, 49]
}, Er = Object.entries(Sr);
function Qe(e) {
  return String(e);
}
c(Qe, "a");
Qe.open = "";
Qe.close = "";
function xt(e = !1) {
  let t = typeof process < "u" ? process : void 0, n = t?.env || {}, r = t?.argv || [];
  return !("NO_COLOR" in n || r.includes("--no-color")) && ("FORCE_COLOR" in n || r.includes("--color") || t?.platform === "win32" || e && n.
  TERM !== "dumb" || "CI" in n) || typeof window < "u" && !!window.chrome;
}
c(xt, "C");
function Dt(e = !1) {
  let t = xt(e), n = /* @__PURE__ */ c((i, a, l, m) => {
    let p = "", f = 0;
    do
      p += i.substring(f, m) + l, f = m + a.length, m = i.indexOf(a, f);
    while (~m);
    return p + i.substring(f);
  }, "i"), r = /* @__PURE__ */ c((i, a, l = i) => {
    let m = /* @__PURE__ */ c((p) => {
      let f = String(p), b = f.indexOf(a, i.length);
      return ~b ? i + n(f, a, l, b) + a : i + f + a;
    }, "o");
    return m.open = i, m.close = a, m;
  }, "g"), o = {
    isColorSupported: t
  }, s = /* @__PURE__ */ c((i) => `\x1B[${i}m`, "d");
  for (let [i, a] of Er)
    o[i] = t ? r(
      s(a[0]),
      s(a[1]),
      a[2]
    ) : Qe;
  return o;
}
c(Dt, "p");

// ../node_modules/tinyrainbow/dist/node.js
var Ft = require("tty");
var _r = process.env.FORCE_TTY !== void 0 || (0, Ft.isatty)(1);
var ne = Dt(_r);

// ../node_modules/@vitest/pretty-format/dist/index.js
function Zt(e, t) {
  return t.forEach(function(n) {
    n && typeof n != "string" && !Array.isArray(n) && Object.keys(n).forEach(function(r) {
      if (r !== "default" && !(r in e)) {
        var o = Object.getOwnPropertyDescriptor(n, r);
        Object.defineProperty(e, r, o.get ? o : {
          enumerable: !0,
          get: /* @__PURE__ */ c(function() {
            return n[r];
          }, "get")
        });
      }
    });
  }), Object.freeze(e);
}
c(Zt, "_mergeNamespaces");
function Tr(e, t) {
  let n = Object.keys(e), r = t === null ? n : n.sort(t);
  if (Object.getOwnPropertySymbols)
    for (let o of Object.getOwnPropertySymbols(e))
      Object.getOwnPropertyDescriptor(e, o).enumerable && r.push(o);
  return r;
}
c(Tr, "getKeysOfEnumerableProperties");
function Oe(e, t, n, r, o, s, i = ": ") {
  let a = "", l = 0, m = e.next();
  if (!m.done) {
    a += t.spacingOuter;
    let p = n + t.indent;
    for (; !m.done; ) {
      if (a += p, l++ === t.maxWidth) {
        a += "\u2026";
        break;
      }
      let f = s(m.value[0], t, p, r, o), b = s(m.value[1], t, p, r, o);
      a += f + i + b, m = e.next(), m.done ? t.min || (a += ",") : a += `,${t.spacingInner}`;
    }
    a += t.spacingOuter + n;
  }
  return a;
}
c(Oe, "printIteratorEntries");
function nt(e, t, n, r, o, s) {
  let i = "", a = 0, l = e.next();
  if (!l.done) {
    i += t.spacingOuter;
    let m = n + t.indent;
    for (; !l.done; ) {
      if (i += m, a++ === t.maxWidth) {
        i += "\u2026";
        break;
      }
      i += s(l.value, t, m, r, o), l = e.next(), l.done ? t.min || (i += ",") : i += `,${t.spacingInner}`;
    }
    i += t.spacingOuter + n;
  }
  return i;
}
c(nt, "printIteratorValues");
function De(e, t, n, r, o, s) {
  let i = "";
  e = e instanceof ArrayBuffer ? new DataView(e) : e;
  let a = /* @__PURE__ */ c((m) => m instanceof DataView, "isDataView"), l = a(e) ? e.byteLength : e.length;
  if (l > 0) {
    i += t.spacingOuter;
    let m = n + t.indent;
    for (let p = 0; p < l; p++) {
      if (i += m, p === t.maxWidth) {
        i += "\u2026";
        break;
      }
      (a(e) || p in e) && (i += s(a(e) ? e.getInt8(p) : e[p], t, m, r, o)), p < l - 1 ? i += `,${t.spacingInner}` : t.min || (i += ",");
    }
    i += t.spacingOuter + n;
  }
  return i;
}
c(De, "printListItems");
function rt(e, t, n, r, o, s) {
  let i = "", a = Tr(e, t.compareKeys);
  if (a.length > 0) {
    i += t.spacingOuter;
    let l = n + t.indent;
    for (let m = 0; m < a.length; m++) {
      let p = a[m], f = s(p, t, l, r, o), b = s(e[p], t, l, r, o);
      i += `${l + f}: ${b}`, m < a.length - 1 ? i += `,${t.spacingInner}` : t.min || (i += ",");
    }
    i += t.spacingOuter + n;
  }
  return i;
}
c(rt, "printObjectProperties");
var Cr = typeof Symbol == "function" && Symbol.for ? Symbol.for("jest.asymmetricMatcher") : 1267621, Ie = " ", Or = /* @__PURE__ */ c((e, t, n, r, o, s) => {
  let i = e.toString();
  if (i === "ArrayContaining" || i === "ArrayNotContaining")
    return ++r > t.maxDepth ? `[${i}]` : `${i + Ie}[${De(e.sample, t, n, r, o, s)}]`;
  if (i === "ObjectContaining" || i === "ObjectNotContaining")
    return ++r > t.maxDepth ? `[${i}]` : `${i + Ie}{${rt(e.sample, t, n, r, o, s)}}`;
  if (i === "StringMatching" || i === "StringNotMatching" || i === "StringContaining" || i === "StringNotContaining")
    return i + Ie + s(e.sample, t, n, r, o);
  if (typeof e.toAsymmetricMatcher != "function")
    throw new TypeError(`Asymmetric matcher ${e.constructor.name} does not implement toAsymmetricMatcher()`);
  return e.toAsymmetricMatcher();
}, "serialize$5"), $r = /* @__PURE__ */ c((e) => e && e.$$typeof === Cr, "test$5"), wr = {
  serialize: Or,
  test: $r
}, Ar = " ", vt = /* @__PURE__ */ new Set(["DOMStringMap", "NamedNodeMap"]), Rr = /^(?:HTML\w*Collection|NodeList)$/;
function Pr(e) {
  return vt.has(e) || Rr.test(e);
}
c(Pr, "testName");
var Nr = /* @__PURE__ */ c((e) => e && e.constructor && !!e.constructor.name && Pr(e.constructor.name), "test$4");
function Ir(e) {
  return e.constructor.name === "NamedNodeMap";
}
c(Ir, "isNamedNodeMap");
var Mr = /* @__PURE__ */ c((e, t, n, r, o, s) => {
  let i = e.constructor.name;
  return ++r > t.maxDepth ? `[${i}]` : (t.min ? "" : i + Ar) + (vt.has(i) ? `{${rt(Ir(e) ? [...e].reduce((a, l) => (a[l.name] = l.value, a),
  {}) : { ...e }, t, n, r, o, s)}}` : `[${De([...e], t, n, r, o, s)}]`);
}, "serialize$4"), Lr = {
  serialize: Mr,
  test: Nr
};
function Qt(e) {
  return e.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
}
c(Qt, "escapeHTML");
function ot(e, t, n, r, o, s, i) {
  let a = r + n.indent, l = n.colors;
  return e.map((m) => {
    let p = t[m], f = i(p, n, a, o, s);
    return typeof p != "string" && (f.includes(`
`) && (f = n.spacingOuter + a + f + n.spacingOuter + r), f = `{${f}}`), `${n.spacingInner + r + l.prop.open + m + l.prop.close}=${l.value.open}${f}${l.
    value.close}`;
  }).join("");
}
c(ot, "printProps");
function st(e, t, n, r, o, s) {
  return e.map((i) => t.spacingOuter + n + (typeof i == "string" ? en(i, t) : s(i, t, n, r, o))).join("");
}
c(st, "printChildren");
function en(e, t) {
  let n = t.colors.content;
  return n.open + Qt(e) + n.close;
}
c(en, "printText");
function xr(e, t) {
  let n = t.colors.comment;
  return `${n.open}<!--${Qt(e)}-->${n.close}`;
}
c(xr, "printComment");
function it(e, t, n, r, o) {
  let s = r.colors.tag;
  return `${s.open}<${e}${t && s.close + t + r.spacingOuter + o + s.open}${n ? `>${s.close}${n}${r.spacingOuter}${o}${s.open}</${e}` : `${t &&
  !r.min ? "" : " "}/`}>${s.close}`;
}
c(it, "printElement");
function ct(e, t) {
  let n = t.colors.tag;
  return `${n.open}<${e}${n.close} \u2026${n.open} />${n.close}`;
}
c(ct, "printElementAsLeaf");
var Dr = 1, tn = 3, nn = 8, rn = 11, Fr = /^(?:(?:HTML|SVG)\w*)?Element$/;
function jr(e) {
  try {
    return typeof e.hasAttribute == "function" && e.hasAttribute("is");
  } catch {
    return !1;
  }
}
c(jr, "testHasAttribute");
function kr(e) {
  let t = e.constructor.name, { nodeType: n, tagName: r } = e, o = typeof r == "string" && r.includes("-") || jr(e);
  return n === Dr && (Fr.test(t) || o) || n === tn && t === "Text" || n === nn && t === "Comment" || n === rn && t === "DocumentFragment";
}
c(kr, "testNode");
var Br = /* @__PURE__ */ c((e) => {
  var t;
  return (e == null || (t = e.constructor) === null || t === void 0 ? void 0 : t.name) && kr(e);
}, "test$3");
function zr(e) {
  return e.nodeType === tn;
}
c(zr, "nodeIsText");
function Yr(e) {
  return e.nodeType === nn;
}
c(Yr, "nodeIsComment");
function et(e) {
  return e.nodeType === rn;
}
c(et, "nodeIsFragment");
var Ur = /* @__PURE__ */ c((e, t, n, r, o, s) => {
  if (zr(e))
    return en(e.data, t);
  if (Yr(e))
    return xr(e.data, t);
  let i = et(e) ? "DocumentFragment" : e.tagName.toLowerCase();
  return ++r > t.maxDepth ? ct(i, t) : it(i, ot(et(e) ? [] : Array.from(e.attributes, (a) => a.name).sort(), et(e) ? {} : [...e.attributes].
  reduce((a, l) => (a[l.name] = l.value, a), {}), t, n + t.indent, r, o, s), st(Array.prototype.slice.call(e.childNodes || e.children), t, n +
  t.indent, r, o, s), t, n);
}, "serialize$3"), Wr = {
  serialize: Ur,
  test: Br
}, Vr = "@@__IMMUTABLE_ITERABLE__@@", qr = "@@__IMMUTABLE_LIST__@@", Kr = "@@__IMMUTABLE_KEYED__@@", Gr = "@@__IMMUTABLE_MAP__@@", jt = "@@_\
_IMMUTABLE_ORDERED__@@", Hr = "@@__IMMUTABLE_RECORD__@@", Jr = "@@__IMMUTABLE_SEQ__@@", Xr = "@@__IMMUTABLE_SET__@@", Zr = "@@__IMMUTABLE_ST\
ACK__@@", be = /* @__PURE__ */ c((e) => `Immutable.${e}`, "getImmutableName"), je = /* @__PURE__ */ c((e) => `[${e}]`, "printAsLeaf"), Ce = "\
 ", kt = "\u2026";
function vr(e, t, n, r, o, s, i) {
  return ++r > t.maxDepth ? je(be(i)) : `${be(i) + Ce}{${Oe(e.entries(), t, n, r, o, s)}}`;
}
c(vr, "printImmutableEntries");
function Qr(e) {
  let t = 0;
  return { next() {
    if (t < e._keys.length) {
      let n = e._keys[t++];
      return {
        done: !1,
        value: [n, e.get(n)]
      };
    }
    return {
      done: !0,
      value: void 0
    };
  } };
}
c(Qr, "getRecordEntries");
function eo(e, t, n, r, o, s) {
  let i = be(e._name || "Record");
  return ++r > t.maxDepth ? je(i) : `${i + Ce}{${Oe(Qr(e), t, n, r, o, s)}}`;
}
c(eo, "printImmutableRecord");
function to(e, t, n, r, o, s) {
  let i = be("Seq");
  return ++r > t.maxDepth ? je(i) : e[Kr] ? `${i + Ce}{${e._iter || e._object ? Oe(e.entries(), t, n, r, o, s) : kt}}` : `${i + Ce}[${e._iter ||
  e._array || e._collection || e._iterable ? nt(e.values(), t, n, r, o, s) : kt}]`;
}
c(to, "printImmutableSeq");
function tt(e, t, n, r, o, s, i) {
  return ++r > t.maxDepth ? je(be(i)) : `${be(i) + Ce}[${nt(e.values(), t, n, r, o, s)}]`;
}
c(tt, "printImmutableValues");
var no = /* @__PURE__ */ c((e, t, n, r, o, s) => e[Gr] ? vr(e, t, n, r, o, s, e[jt] ? "OrderedMap" : "Map") : e[qr] ? tt(e, t, n, r, o, s, "\
List") : e[Xr] ? tt(e, t, n, r, o, s, e[jt] ? "OrderedSet" : "Set") : e[Zr] ? tt(e, t, n, r, o, s, "Stack") : e[Jr] ? to(e, t, n, r, o, s) :
eo(e, t, n, r, o, s), "serialize$2"), ro = /* @__PURE__ */ c((e) => e && (e[Vr] === !0 || e[Hr] === !0), "test$2"), oo = {
  serialize: no,
  test: ro
};
function on(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
c(on, "getDefaultExportFromCjs");
var Me = { exports: {} }, P = {};
var Bt;
function so() {
  if (Bt) return P;
  Bt = 1;
  var e = Symbol.for("react.transitional.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), r = Symbol.for("react.\
strict_mode"), o = Symbol.for("react.profiler"), s = Symbol.for("react.consumer"), i = Symbol.for("react.context"), a = Symbol.for("react.fo\
rward_ref"), l = Symbol.for("react.suspense"), m = Symbol.for("react.suspense_list"), p = Symbol.for("react.memo"), f = Symbol.for("react.la\
zy"), b = Symbol.for("react.view_transition"), d = Symbol.for("react.client.reference");
  function g(u) {
    if (typeof u == "object" && u !== null) {
      var h = u.$$typeof;
      switch (h) {
        case e:
          switch (u = u.type, u) {
            case n:
            case o:
            case r:
            case l:
            case m:
            case b:
              return u;
            default:
              switch (u = u && u.$$typeof, u) {
                case i:
                case a:
                case f:
                case p:
                  return u;
                case s:
                  return u;
                default:
                  return h;
              }
          }
        case t:
          return h;
      }
    }
  }
  return c(g, "typeOf"), P.ContextConsumer = s, P.ContextProvider = i, P.Element = e, P.ForwardRef = a, P.Fragment = n, P.Lazy = f, P.Memo =
  p, P.Portal = t, P.Profiler = o, P.StrictMode = r, P.Suspense = l, P.SuspenseList = m, P.isContextConsumer = function(u) {
    return g(u) === s;
  }, P.isContextProvider = function(u) {
    return g(u) === i;
  }, P.isElement = function(u) {
    return typeof u == "object" && u !== null && u.$$typeof === e;
  }, P.isForwardRef = function(u) {
    return g(u) === a;
  }, P.isFragment = function(u) {
    return g(u) === n;
  }, P.isLazy = function(u) {
    return g(u) === f;
  }, P.isMemo = function(u) {
    return g(u) === p;
  }, P.isPortal = function(u) {
    return g(u) === t;
  }, P.isProfiler = function(u) {
    return g(u) === o;
  }, P.isStrictMode = function(u) {
    return g(u) === r;
  }, P.isSuspense = function(u) {
    return g(u) === l;
  }, P.isSuspenseList = function(u) {
    return g(u) === m;
  }, P.isValidElementType = function(u) {
    return typeof u == "string" || typeof u == "function" || u === n || u === o || u === r || u === l || u === m || typeof u == "object" && u !==
    null && (u.$$typeof === f || u.$$typeof === p || u.$$typeof === i || u.$$typeof === s || u.$$typeof === a || u.$$typeof === d || u.getModuleId !==
    void 0);
  }, P.typeOf = g, P;
}
c(so, "requireReactIs_production");
var N = {};
var zt;
function io() {
  return zt || (zt = 1, process.env.NODE_ENV !== "production" && function() {
    function e(u) {
      if (typeof u == "object" && u !== null) {
        var h = u.$$typeof;
        switch (h) {
          case t:
            switch (u = u.type, u) {
              case r:
              case s:
              case o:
              case m:
              case p:
              case d:
                return u;
              default:
                switch (u = u && u.$$typeof, u) {
                  case a:
                  case l:
                  case b:
                  case f:
                    return u;
                  case i:
                    return u;
                  default:
                    return h;
                }
            }
          case n:
            return h;
        }
      }
    }
    c(e, "typeOf");
    var t = Symbol.for("react.transitional.element"), n = Symbol.for("react.portal"), r = Symbol.for("react.fragment"), o = Symbol.for("reac\
t.strict_mode"), s = Symbol.for("react.profiler"), i = Symbol.for("react.consumer"), a = Symbol.for("react.context"), l = Symbol.for("react.\
forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), f = Symbol.for("react.memo"), b = Symbol.for("react.\
lazy"), d = Symbol.for("react.view_transition"), g = Symbol.for("react.client.reference");
    N.ContextConsumer = i, N.ContextProvider = a, N.Element = t, N.ForwardRef = l, N.Fragment = r, N.Lazy = b, N.Memo = f, N.Portal = n, N.Profiler =
    s, N.StrictMode = o, N.Suspense = m, N.SuspenseList = p, N.isContextConsumer = function(u) {
      return e(u) === i;
    }, N.isContextProvider = function(u) {
      return e(u) === a;
    }, N.isElement = function(u) {
      return typeof u == "object" && u !== null && u.$$typeof === t;
    }, N.isForwardRef = function(u) {
      return e(u) === l;
    }, N.isFragment = function(u) {
      return e(u) === r;
    }, N.isLazy = function(u) {
      return e(u) === b;
    }, N.isMemo = function(u) {
      return e(u) === f;
    }, N.isPortal = function(u) {
      return e(u) === n;
    }, N.isProfiler = function(u) {
      return e(u) === s;
    }, N.isStrictMode = function(u) {
      return e(u) === o;
    }, N.isSuspense = function(u) {
      return e(u) === m;
    }, N.isSuspenseList = function(u) {
      return e(u) === p;
    }, N.isValidElementType = function(u) {
      return typeof u == "string" || typeof u == "function" || u === r || u === s || u === o || u === m || u === p || typeof u == "object" &&
      u !== null && (u.$$typeof === b || u.$$typeof === f || u.$$typeof === a || u.$$typeof === i || u.$$typeof === l || u.$$typeof === g ||
      u.getModuleId !== void 0);
    }, N.typeOf = e;
  }()), N;
}
c(io, "requireReactIs_development$1");
var Yt;
function co() {
  return Yt || (Yt = 1, process.env.NODE_ENV === "production" ? Me.exports = so() : Me.exports = io()), Me.exports;
}
c(co, "requireReactIs$1");
var sn = co(), uo = /* @__PURE__ */ on(sn), lo = /* @__PURE__ */ Zt({
  __proto__: null,
  default: uo
}, [sn]), Le = { exports: {} }, w = {};
var Ut;
function ao() {
  if (Ut) return w;
  Ut = 1;
  var e = Symbol.for("react.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), r = Symbol.for("react.strict_mode"),
  o = Symbol.for("react.profiler"), s = Symbol.for("react.provider"), i = Symbol.for("react.context"), a = Symbol.for("react.server_context"),
  l = Symbol.for("react.forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), f = Symbol.for("react.memo"),
  b = Symbol.for("react.lazy"), d = Symbol.for("react.offscreen"), g;
  g = Symbol.for("react.module.reference");
  function u(h) {
    if (typeof h == "object" && h !== null) {
      var S = h.$$typeof;
      switch (S) {
        case e:
          switch (h = h.type, h) {
            case n:
            case o:
            case r:
            case m:
            case p:
              return h;
            default:
              switch (h = h && h.$$typeof, h) {
                case a:
                case i:
                case l:
                case b:
                case f:
                case s:
                  return h;
                default:
                  return S;
              }
          }
        case t:
          return S;
      }
    }
  }
  return c(u, "v"), w.ContextConsumer = i, w.ContextProvider = s, w.Element = e, w.ForwardRef = l, w.Fragment = n, w.Lazy = b, w.Memo = f, w.
  Portal = t, w.Profiler = o, w.StrictMode = r, w.Suspense = m, w.SuspenseList = p, w.isAsyncMode = function() {
    return !1;
  }, w.isConcurrentMode = function() {
    return !1;
  }, w.isContextConsumer = function(h) {
    return u(h) === i;
  }, w.isContextProvider = function(h) {
    return u(h) === s;
  }, w.isElement = function(h) {
    return typeof h == "object" && h !== null && h.$$typeof === e;
  }, w.isForwardRef = function(h) {
    return u(h) === l;
  }, w.isFragment = function(h) {
    return u(h) === n;
  }, w.isLazy = function(h) {
    return u(h) === b;
  }, w.isMemo = function(h) {
    return u(h) === f;
  }, w.isPortal = function(h) {
    return u(h) === t;
  }, w.isProfiler = function(h) {
    return u(h) === o;
  }, w.isStrictMode = function(h) {
    return u(h) === r;
  }, w.isSuspense = function(h) {
    return u(h) === m;
  }, w.isSuspenseList = function(h) {
    return u(h) === p;
  }, w.isValidElementType = function(h) {
    return typeof h == "string" || typeof h == "function" || h === n || h === o || h === r || h === m || h === p || h === d || typeof h == "\
object" && h !== null && (h.$$typeof === b || h.$$typeof === f || h.$$typeof === s || h.$$typeof === i || h.$$typeof === l || h.$$typeof ===
    g || h.getModuleId !== void 0);
  }, w.typeOf = u, w;
}
c(ao, "requireReactIs_production_min");
var A = {};
var Wt;
function fo() {
  return Wt || (Wt = 1, process.env.NODE_ENV !== "production" && function() {
    var e = Symbol.for("react.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), r = Symbol.for("react.strict_mode"),
    o = Symbol.for("react.profiler"), s = Symbol.for("react.provider"), i = Symbol.for("react.context"), a = Symbol.for("react.server_contex\
t"), l = Symbol.for("react.forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), f = Symbol.for("react.mem\
o"), b = Symbol.for("react.lazy"), d = Symbol.for("react.offscreen"), g = !1, u = !1, h = !1, S = !1, _ = !1, O;
    O = Symbol.for("react.module.reference");
    function y(C) {
      return !!(typeof C == "string" || typeof C == "function" || C === n || C === o || _ || C === r || C === m || C === p || S || C === d ||
      g || u || h || typeof C == "object" && C !== null && (C.$$typeof === b || C.$$typeof === f || C.$$typeof === s || C.$$typeof === i || C.
      $$typeof === l || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      C.$$typeof === O || C.getModuleId !== void 0));
    }
    c(y, "isValidElementType");
    function E(C) {
      if (typeof C == "object" && C !== null) {
        var ve = C.$$typeof;
        switch (ve) {
          case e:
            var Pe = C.type;
            switch (Pe) {
              case n:
              case o:
              case r:
              case m:
              case p:
                return Pe;
              default:
                var Lt = Pe && Pe.$$typeof;
                switch (Lt) {
                  case a:
                  case i:
                  case l:
                  case b:
                  case f:
                  case s:
                    return Lt;
                  default:
                    return ve;
                }
            }
          case t:
            return ve;
        }
      }
    }
    c(E, "typeOf");
    var $ = i, T = s, R = e, J = l, te = n, L = b, z = f, X = t, W = o, M = r, D = m, F = p, Z = !1, k = !1;
    function q(C) {
      return Z || (Z = !0, console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")), !1;
    }
    c(q, "isAsyncMode");
    function se(C) {
      return k || (k = !0, console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")), !1;
    }
    c(se, "isConcurrentMode");
    function K(C) {
      return E(C) === i;
    }
    c(K, "isContextConsumer");
    function G(C) {
      return E(C) === s;
    }
    c(G, "isContextProvider");
    function ce(C) {
      return typeof C == "object" && C !== null && C.$$typeof === e;
    }
    c(ce, "isElement");
    function v(C) {
      return E(C) === l;
    }
    c(v, "isForwardRef");
    function V(C) {
      return E(C) === n;
    }
    c(V, "isFragment");
    function ie(C) {
      return E(C) === b;
    }
    c(ie, "isLazy");
    function ye(C) {
      return E(C) === f;
    }
    c(ye, "isMemo");
    function ae(C) {
      return E(C) === t;
    }
    c(ae, "isPortal");
    function Te(C) {
      return E(C) === o;
    }
    c(Te, "isProfiler");
    function Ae(C) {
      return E(C) === r;
    }
    c(Ae, "isStrictMode");
    function Re(C) {
      return E(C) === m;
    }
    c(Re, "isSuspense");
    function mr(C) {
      return E(C) === p;
    }
    c(mr, "isSuspenseList"), A.ContextConsumer = $, A.ContextProvider = T, A.Element = R, A.ForwardRef = J, A.Fragment = te, A.Lazy = L, A.Memo =
    z, A.Portal = X, A.Profiler = W, A.StrictMode = M, A.Suspense = D, A.SuspenseList = F, A.isAsyncMode = q, A.isConcurrentMode = se, A.isContextConsumer =
    K, A.isContextProvider = G, A.isElement = ce, A.isForwardRef = v, A.isFragment = V, A.isLazy = ie, A.isMemo = ye, A.isPortal = ae, A.isProfiler =
    Te, A.isStrictMode = Ae, A.isSuspense = Re, A.isSuspenseList = mr, A.isValidElementType = y, A.typeOf = E;
  }()), A;
}
c(fo, "requireReactIs_development");
var Vt;
function mo() {
  return Vt || (Vt = 1, process.env.NODE_ENV === "production" ? Le.exports = ao() : Le.exports = fo()), Le.exports;
}
c(mo, "requireReactIs");
var cn = mo(), po = /* @__PURE__ */ on(cn), go = /* @__PURE__ */ Zt({
  __proto__: null,
  default: po
}, [cn]), ho = [
  "isAsyncMode",
  "isConcurrentMode",
  "isContextConsumer",
  "isContextProvider",
  "isElement",
  "isForwardRef",
  "isFragment",
  "isLazy",
  "isMemo",
  "isPortal",
  "isProfiler",
  "isStrictMode",
  "isSuspense",
  "isSuspenseList",
  "isValidElementType"
], me = Object.fromEntries(ho.map((e) => [e, (t) => go[e](t) || lo[e](t)]));
function un(e, t = []) {
  if (Array.isArray(e))
    for (let n of e)
      un(n, t);
  else e != null && e !== !1 && e !== "" && t.push(e);
  return t;
}
c(un, "getChildren");
function qt(e) {
  let t = e.type;
  if (typeof t == "string")
    return t;
  if (typeof t == "function")
    return t.displayName || t.name || "Unknown";
  if (me.isFragment(e))
    return "React.Fragment";
  if (me.isSuspense(e))
    return "React.Suspense";
  if (typeof t == "object" && t !== null) {
    if (me.isContextProvider(e))
      return "Context.Provider";
    if (me.isContextConsumer(e))
      return "Context.Consumer";
    if (me.isForwardRef(e)) {
      if (t.displayName)
        return t.displayName;
      let n = t.render.displayName || t.render.name || "";
      return n === "" ? "ForwardRef" : `ForwardRef(${n})`;
    }
    if (me.isMemo(e)) {
      let n = t.displayName || t.type.displayName || t.type.name || "";
      return n === "" ? "Memo" : `Memo(${n})`;
    }
  }
  return "UNDEFINED";
}
c(qt, "getType");
function yo(e) {
  let { props: t } = e;
  return Object.keys(t).filter((n) => n !== "children" && t[n] !== void 0).sort();
}
c(yo, "getPropKeys$1");
var bo = /* @__PURE__ */ c((e, t, n, r, o, s) => ++r > t.maxDepth ? ct(qt(e), t) : it(qt(e), ot(yo(e), e.props, t, n + t.indent, r, o, s), st(
un(e.props.children), t, n + t.indent, r, o, s), t, n), "serialize$1"), So = /* @__PURE__ */ c((e) => e != null && me.isElement(e), "test$1"),
Eo = {
  serialize: bo,
  test: So
}, _o = typeof Symbol == "function" && Symbol.for ? Symbol.for("react.test.json") : 245830487;
function To(e) {
  let { props: t } = e;
  return t ? Object.keys(t).filter((n) => t[n] !== void 0).sort() : [];
}
c(To, "getPropKeys");
var Co = /* @__PURE__ */ c((e, t, n, r, o, s) => ++r > t.maxDepth ? ct(e.type, t) : it(e.type, e.props ? ot(To(e), e.props, t, n + t.indent,
r, o, s) : "", e.children ? st(e.children, t, n + t.indent, r, o, s) : "", t, n), "serialize"), Oo = /* @__PURE__ */ c((e) => e && e.$$typeof ===
_o, "test"), $o = {
  serialize: Co,
  test: Oo
}, ln = Object.prototype.toString, wo = Date.prototype.toISOString, Ao = Error.prototype.toString, Kt = RegExp.prototype.toString;
function xe(e) {
  return typeof e.constructor == "function" && e.constructor.name || "Object";
}
c(xe, "getConstructorName");
function Ro(e) {
  return typeof window < "u" && e === window;
}
c(Ro, "isWindow");
var Po = /^Symbol\((.*)\)(.*)$/, No = /\n/g, Fe = class extends Error {
  static {
    c(this, "PrettyFormatPluginError");
  }
  constructor(t, n) {
    super(t), this.stack = n, this.name = this.constructor.name;
  }
};
function Io(e) {
  return e === "[object Array]" || e === "[object ArrayBuffer]" || e === "[object DataView]" || e === "[object Float32Array]" || e === "[obj\
ect Float64Array]" || e === "[object Int8Array]" || e === "[object Int16Array]" || e === "[object Int32Array]" || e === "[object Uint8Array]" ||
  e === "[object Uint8ClampedArray]" || e === "[object Uint16Array]" || e === "[object Uint32Array]";
}
c(Io, "isToStringedArrayType");
function Mo(e) {
  return Object.is(e, -0) ? "-0" : String(e);
}
c(Mo, "printNumber");
function Lo(e) {
  return `${e}n`;
}
c(Lo, "printBigInt");
function Gt(e, t) {
  return t ? `[Function ${e.name || "anonymous"}]` : "[Function]";
}
c(Gt, "printFunction");
function Ht(e) {
  return String(e).replace(Po, "Symbol($1)");
}
c(Ht, "printSymbol");
function Jt(e) {
  return `[${Ao.call(e)}]`;
}
c(Jt, "printError");
function an(e, t, n, r) {
  if (e === !0 || e === !1)
    return `${e}`;
  if (e === void 0)
    return "undefined";
  if (e === null)
    return "null";
  let o = typeof e;
  if (o === "number")
    return Mo(e);
  if (o === "bigint")
    return Lo(e);
  if (o === "string")
    return r ? `"${e.replaceAll(/"|\\/g, "\\$&")}"` : `"${e}"`;
  if (o === "function")
    return Gt(e, t);
  if (o === "symbol")
    return Ht(e);
  let s = ln.call(e);
  return s === "[object WeakMap]" ? "WeakMap {}" : s === "[object WeakSet]" ? "WeakSet {}" : s === "[object Function]" || s === "[object Gen\
eratorFunction]" ? Gt(e, t) : s === "[object Symbol]" ? Ht(e) : s === "[object Date]" ? Number.isNaN(+e) ? "Date { NaN }" : wo.call(e) : s ===
  "[object Error]" ? Jt(e) : s === "[object RegExp]" ? n ? Kt.call(e).replaceAll(/[$()*+.?[\\\]^{|}]/g, "\\$&") : Kt.call(e) : e instanceof Error ?
  Jt(e) : null;
}
c(an, "printBasicValue");
function fn(e, t, n, r, o, s) {
  if (o.includes(e))
    return "[Circular]";
  o = [...o], o.push(e);
  let i = ++r > t.maxDepth, a = t.min;
  if (t.callToJSON && !i && e.toJSON && typeof e.toJSON == "function" && !s)
    return fe(e.toJSON(), t, n, r, o, !0);
  let l = ln.call(e);
  return l === "[object Arguments]" ? i ? "[Arguments]" : `${a ? "" : "Arguments "}[${De(e, t, n, r, o, fe)}]` : Io(l) ? i ? `[${e.constructor.
  name}]` : `${a || !t.printBasicPrototype && e.constructor.name === "Array" ? "" : `${e.constructor.name} `}[${De(e, t, n, r, o, fe)}]` : l ===
  "[object Map]" ? i ? "[Map]" : `Map {${Oe(e.entries(), t, n, r, o, fe, " => ")}}` : l === "[object Set]" ? i ? "[Set]" : `Set {${nt(e.values(),
  t, n, r, o, fe)}}` : i || Ro(e) ? `[${xe(e)}]` : `${a || !t.printBasicPrototype && xe(e) === "Object" ? "" : `${xe(e)} `}{${rt(e, t, n, r,
  o, fe)}}`;
}
c(fn, "printComplexValue");
var xo = {
  test: /* @__PURE__ */ c((e) => e && e instanceof Error, "test"),
  serialize(e, t, n, r, o, s) {
    if (o.includes(e))
      return "[Circular]";
    o = [...o, e];
    let i = ++r > t.maxDepth, { message: a, cause: l, ...m } = e, p = {
      message: a,
      ...typeof l < "u" ? { cause: l } : {},
      ...e instanceof AggregateError ? { errors: e.errors } : {},
      ...m
    }, f = e.name !== "Error" ? e.name : xe(e);
    return i ? `[${f}]` : `${f} {${Oe(Object.entries(p).values(), t, n, r, o, s)}}`;
  }
};
function Do(e) {
  return e.serialize != null;
}
c(Do, "isNewPlugin");
function mn(e, t, n, r, o, s) {
  let i;
  try {
    i = Do(e) ? e.serialize(t, n, r, o, s, fe) : e.print(t, (a) => fe(a, n, r, o, s), (a) => {
      let l = r + n.indent;
      return l + a.replaceAll(No, `
${l}`);
    }, {
      edgeSpacing: n.spacingOuter,
      min: n.min,
      spacing: n.spacingInner
    }, n.colors);
  } catch (a) {
    throw new Fe(a.message, a.stack);
  }
  if (typeof i != "string")
    throw new TypeError(`pretty-format: Plugin must return type "string" but instead returned "${typeof i}".`);
  return i;
}
c(mn, "printPlugin");
function pn(e, t) {
  for (let n of e)
    try {
      if (n.test(t))
        return n;
    } catch (r) {
      throw new Fe(r.message, r.stack);
    }
  return null;
}
c(pn, "findPlugin");
function fe(e, t, n, r, o, s) {
  let i = pn(t.plugins, e);
  if (i !== null)
    return mn(i, e, t, n, r, o);
  let a = an(e, t.printFunctionName, t.escapeRegex, t.escapeString);
  return a !== null ? a : fn(e, t, n, r, o, s);
}
c(fe, "printer");
var ut = {
  comment: "gray",
  content: "reset",
  prop: "yellow",
  tag: "cyan",
  value: "green"
}, gn = Object.keys(ut), re = {
  callToJSON: !0,
  compareKeys: void 0,
  escapeRegex: !1,
  escapeString: !0,
  highlight: !1,
  indent: 2,
  maxDepth: Number.POSITIVE_INFINITY,
  maxWidth: Number.POSITIVE_INFINITY,
  min: !1,
  plugins: [],
  printBasicPrototype: !0,
  printFunctionName: !0,
  theme: ut
};
function Fo(e) {
  for (let t of Object.keys(e))
    if (!Object.prototype.hasOwnProperty.call(re, t))
      throw new Error(`pretty-format: Unknown option "${t}".`);
  if (e.min && e.indent !== void 0 && e.indent !== 0)
    throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');
}
c(Fo, "validateOptions");
function jo() {
  return gn.reduce((e, t) => {
    let n = ut[t], r = n && ne[n];
    if (r && typeof r.close == "string" && typeof r.open == "string")
      e[t] = r;
    else
      throw new Error(`pretty-format: Option "theme" has a key "${t}" whose value "${n}" is undefined in ansi-styles.`);
    return e;
  }, /* @__PURE__ */ Object.create(null));
}
c(jo, "getColorsHighlight");
function ko() {
  return gn.reduce((e, t) => (e[t] = {
    close: "",
    open: ""
  }, e), /* @__PURE__ */ Object.create(null));
}
c(ko, "getColorsEmpty");
function hn(e) {
  return e?.printFunctionName ?? re.printFunctionName;
}
c(hn, "getPrintFunctionName");
function dn(e) {
  return e?.escapeRegex ?? re.escapeRegex;
}
c(dn, "getEscapeRegex");
function yn(e) {
  return e?.escapeString ?? re.escapeString;
}
c(yn, "getEscapeString");
function Xt(e) {
  return {
    callToJSON: e?.callToJSON ?? re.callToJSON,
    colors: e?.highlight ? jo() : ko(),
    compareKeys: typeof e?.compareKeys == "function" || e?.compareKeys === null ? e.compareKeys : re.compareKeys,
    escapeRegex: dn(e),
    escapeString: yn(e),
    indent: e?.min ? "" : Bo(e?.indent ?? re.indent),
    maxDepth: e?.maxDepth ?? re.maxDepth,
    maxWidth: e?.maxWidth ?? re.maxWidth,
    min: e?.min ?? re.min,
    plugins: e?.plugins ?? re.plugins,
    printBasicPrototype: e?.printBasicPrototype ?? !0,
    printFunctionName: hn(e),
    spacingInner: e?.min ? " " : `
`,
    spacingOuter: e?.min ? "" : `
`
  };
}
c(Xt, "getConfig");
function Bo(e) {
  return Array.from({ length: e + 1 }).join(" ");
}
c(Bo, "createIndent");
function Q(e, t) {
  if (t && (Fo(t), t.plugins)) {
    let r = pn(t.plugins, e);
    if (r !== null)
      return mn(r, e, Xt(t), "", 0, []);
  }
  let n = an(e, hn(t), dn(t), yn(t));
  return n !== null ? n : fn(e, Xt(t), "", 0, []);
}
c(Q, "format");
var $e = {
  AsymmetricMatcher: wr,
  DOMCollection: Lr,
  DOMElement: Wr,
  Immutable: oo,
  ReactElement: Eo,
  ReactTestComponent: $o,
  Error: xo
};

// ../node_modules/loupe/lib/helpers.js
var bn = {
  bold: ["1", "22"],
  dim: ["2", "22"],
  italic: ["3", "23"],
  underline: ["4", "24"],
  // 5 & 6 are blinking
  inverse: ["7", "27"],
  hidden: ["8", "28"],
  strike: ["9", "29"],
  // 10-20 are fonts
  // 21-29 are resets for 1-9
  black: ["30", "39"],
  red: ["31", "39"],
  green: ["32", "39"],
  yellow: ["33", "39"],
  blue: ["34", "39"],
  magenta: ["35", "39"],
  cyan: ["36", "39"],
  white: ["37", "39"],
  brightblack: ["30;1", "39"],
  brightred: ["31;1", "39"],
  brightgreen: ["32;1", "39"],
  brightyellow: ["33;1", "39"],
  brightblue: ["34;1", "39"],
  brightmagenta: ["35;1", "39"],
  brightcyan: ["36;1", "39"],
  brightwhite: ["37;1", "39"],
  grey: ["90", "39"]
}, zo = {
  special: "cyan",
  number: "yellow",
  bigint: "yellow",
  boolean: "yellow",
  undefined: "grey",
  null: "bold",
  string: "green",
  symbol: "green",
  date: "magenta",
  regexp: "red"
}, ue = "\u2026";
function Yo(e, t) {
  let n = bn[zo[t]] || bn[t] || "";
  return n ? `\x1B[${n[0]}m${String(e)}\x1B[${n[1]}m` : String(e);
}
c(Yo, "colorise");
function Sn({
  showHidden: e = !1,
  depth: t = 2,
  colors: n = !1,
  customInspect: r = !0,
  showProxy: o = !1,
  maxArrayLength: s = 1 / 0,
  breakLength: i = 1 / 0,
  seen: a = [],
  // eslint-disable-next-line no-shadow
  truncate: l = 1 / 0,
  stylize: m = String
} = {}, p) {
  let f = {
    showHidden: !!e,
    depth: Number(t),
    colors: !!n,
    customInspect: !!r,
    showProxy: !!o,
    maxArrayLength: Number(s),
    breakLength: Number(i),
    truncate: Number(l),
    seen: a,
    inspect: p,
    stylize: m
  };
  return f.colors && (f.stylize = Yo), f;
}
c(Sn, "normaliseOptions");
function Uo(e) {
  return e >= "\uD800" && e <= "\uDBFF";
}
c(Uo, "isHighSurrogate");
function Y(e, t, n = ue) {
  e = String(e);
  let r = n.length, o = e.length;
  if (r > t && o > r)
    return n;
  if (o > t && o > r) {
    let s = t - r;
    return s > 0 && Uo(e[s - 1]) && (s = s - 1), `${e.slice(0, s)}${n}`;
  }
  return e;
}
c(Y, "truncate");
function j(e, t, n, r = ", ") {
  n = n || t.inspect;
  let o = e.length;
  if (o === 0)
    return "";
  let s = t.truncate, i = "", a = "", l = "";
  for (let m = 0; m < o; m += 1) {
    let p = m + 1 === e.length, f = m + 2 === e.length;
    l = `${ue}(${e.length - m})`;
    let b = e[m];
    t.truncate = s - i.length - (p ? 0 : r.length);
    let d = a || n(b, t) + (p ? "" : r), g = i.length + d.length, u = g + l.length;
    if (p && g > s && i.length + l.length <= s || !p && !f && u > s || (a = p ? "" : n(e[m + 1], t) + (f ? "" : r), !p && f && u > s && g + a.
    length > s))
      break;
    if (i += d, !p && !f && g + a.length >= s) {
      l = `${ue}(${e.length - m - 1})`;
      break;
    }
    l = "";
  }
  return `${i}${l}`;
}
c(j, "inspectList");
function Wo(e) {
  return e.match(/^[a-zA-Z_][a-zA-Z_0-9]*$/) ? e : JSON.stringify(e).replace(/'/g, "\\'").replace(/\\"/g, '"').replace(/(^"|"$)/g, "'");
}
c(Wo, "quoteComplexKey");
function le([e, t], n) {
  return n.truncate -= 2, typeof e == "string" ? e = Wo(e) : typeof e != "number" && (e = `[${n.inspect(e, n)}]`), n.truncate -= e.length, t =
  n.inspect(t, n), `${e}: ${t}`;
}
c(le, "inspectProperty");

// ../node_modules/loupe/lib/array.js
function lt(e, t) {
  let n = Object.keys(e).slice(e.length);
  if (!e.length && !n.length)
    return "[]";
  t.truncate -= 4;
  let r = j(e, t);
  t.truncate -= r.length;
  let o = "";
  return n.length && (o = j(n.map((s) => [s, e[s]]), t, le)), `[ ${r}${o ? `, ${o}` : ""} ]`;
}
c(lt, "inspectArray");

// ../node_modules/loupe/lib/typedarray.js
var Vo = /* @__PURE__ */ c((e) => typeof Buffer == "function" && e instanceof Buffer ? "Buffer" : e[Symbol.toStringTag] ? e[Symbol.toStringTag] :
e.constructor.name, "getArrayName");
function oe(e, t) {
  let n = Vo(e);
  t.truncate -= n.length + 4;
  let r = Object.keys(e).slice(e.length);
  if (!e.length && !r.length)
    return `${n}[]`;
  let o = "";
  for (let i = 0; i < e.length; i++) {
    let a = `${t.stylize(Y(e[i], t.truncate), "number")}${i === e.length - 1 ? "" : ", "}`;
    if (t.truncate -= a.length, e[i] !== e.length && t.truncate <= 3) {
      o += `${ue}(${e.length - e[i] + 1})`;
      break;
    }
    o += a;
  }
  let s = "";
  return r.length && (s = j(r.map((i) => [i, e[i]]), t, le)), `${n}[ ${o}${s ? `, ${s}` : ""} ]`;
}
c(oe, "inspectTypedArray");

// ../node_modules/loupe/lib/date.js
function at(e, t) {
  let n = e.toJSON();
  if (n === null)
    return "Invalid Date";
  let r = n.split("T"), o = r[0];
  return t.stylize(`${o}T${Y(r[1], t.truncate - o.length - 1)}`, "date");
}
c(at, "inspectDate");

// ../node_modules/loupe/lib/function.js
function ke(e, t) {
  let n = e[Symbol.toStringTag] || "Function", r = e.name;
  return r ? t.stylize(`[${n} ${Y(r, t.truncate - 11)}]`, "special") : t.stylize(`[${n}]`, "special");
}
c(ke, "inspectFunction");

// ../node_modules/loupe/lib/map.js
function qo([e, t], n) {
  return n.truncate -= 4, e = n.inspect(e, n), n.truncate -= e.length, t = n.inspect(t, n), `${e} => ${t}`;
}
c(qo, "inspectMapEntry");
function Ko(e) {
  let t = [];
  return e.forEach((n, r) => {
    t.push([r, n]);
  }), t;
}
c(Ko, "mapToEntries");
function ft(e, t) {
  return e.size === 0 ? "Map{}" : (t.truncate -= 7, `Map{ ${j(Ko(e), t, qo)} }`);
}
c(ft, "inspectMap");

// ../node_modules/loupe/lib/number.js
var Go = Number.isNaN || ((e) => e !== e);
function Be(e, t) {
  return Go(e) ? t.stylize("NaN", "number") : e === 1 / 0 ? t.stylize("Infinity", "number") : e === -1 / 0 ? t.stylize("-Infinity", "number") :
  e === 0 ? t.stylize(1 / e === 1 / 0 ? "+0" : "-0", "number") : t.stylize(Y(String(e), t.truncate), "number");
}
c(Be, "inspectNumber");

// ../node_modules/loupe/lib/bigint.js
function ze(e, t) {
  let n = Y(e.toString(), t.truncate - 1);
  return n !== ue && (n += "n"), t.stylize(n, "bigint");
}
c(ze, "inspectBigInt");

// ../node_modules/loupe/lib/regexp.js
function mt(e, t) {
  let n = e.toString().split("/")[2], r = t.truncate - (2 + n.length), o = e.source;
  return t.stylize(`/${Y(o, r)}/${n}`, "regexp");
}
c(mt, "inspectRegExp");

// ../node_modules/loupe/lib/set.js
function Ho(e) {
  let t = [];
  return e.forEach((n) => {
    t.push(n);
  }), t;
}
c(Ho, "arrayFromSet");
function pt(e, t) {
  return e.size === 0 ? "Set{}" : (t.truncate -= 7, `Set{ ${j(Ho(e), t)} }`);
}
c(pt, "inspectSet");

// ../node_modules/loupe/lib/string.js
var En = new RegExp("['\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\u\
ffff]", "g"), Jo = {
  "\b": "\\b",
  "	": "\\t",
  "\n": "\\n",
  "\f": "\\f",
  "\r": "\\r",
  "'": "\\'",
  "\\": "\\\\"
}, Xo = 16, Zo = 4;
function vo(e) {
  return Jo[e] || `\\u${`0000${e.charCodeAt(0).toString(Xo)}`.slice(-Zo)}`;
}
c(vo, "escape");
function Ye(e, t) {
  return En.test(e) && (e = e.replace(En, vo)), t.stylize(`'${Y(e, t.truncate - 2)}'`, "string");
}
c(Ye, "inspectString");

// ../node_modules/loupe/lib/symbol.js
function Ue(e) {
  return "description" in Symbol.prototype ? e.description ? `Symbol(${e.description})` : "Symbol()" : e.toString();
}
c(Ue, "inspectSymbol");

// ../node_modules/loupe/lib/promise.js
var _n = /* @__PURE__ */ c(() => "Promise{\u2026}", "getPromiseValue");
try {
  let { getPromiseDetails: e, kPending: t, kRejected: n } = process.binding("util");
  Array.isArray(e(Promise.resolve())) && (_n = /* @__PURE__ */ c((r, o) => {
    let [s, i] = e(r);
    return s === t ? "Promise{<pending>}" : `Promise${s === n ? "!" : ""}{${o.inspect(i, o)}}`;
  }, "getPromiseValue"));
} catch {
}
var Tn = _n;

// ../node_modules/loupe/lib/object.js
function pe(e, t) {
  let n = Object.getOwnPropertyNames(e), r = Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(e) : [];
  if (n.length === 0 && r.length === 0)
    return "{}";
  if (t.truncate -= 4, t.seen = t.seen || [], t.seen.includes(e))
    return "[Circular]";
  t.seen.push(e);
  let o = j(n.map((a) => [a, e[a]]), t, le), s = j(r.map((a) => [a, e[a]]), t, le);
  t.seen.pop();
  let i = "";
  return o && s && (i = ", "), `{ ${o}${i}${s} }`;
}
c(pe, "inspectObject");

// ../node_modules/loupe/lib/class.js
var gt = typeof Symbol < "u" && Symbol.toStringTag ? Symbol.toStringTag : !1;
function ht(e, t) {
  let n = "";
  return gt && gt in e && (n = e[gt]), n = n || e.constructor.name, (!n || n === "_class") && (n = "<Anonymous Class>"), t.truncate -= n.length,
  `${n}${pe(e, t)}`;
}
c(ht, "inspectClass");

// ../node_modules/loupe/lib/arguments.js
function dt(e, t) {
  return e.length === 0 ? "Arguments[]" : (t.truncate -= 13, `Arguments[ ${j(e, t)} ]`);
}
c(dt, "inspectArguments");

// ../node_modules/loupe/lib/error.js
var Qo = [
  "stack",
  "line",
  "column",
  "name",
  "message",
  "fileName",
  "lineNumber",
  "columnNumber",
  "number",
  "description",
  "cause"
];
function yt(e, t) {
  let n = Object.getOwnPropertyNames(e).filter((i) => Qo.indexOf(i) === -1), r = e.name;
  t.truncate -= r.length;
  let o = "";
  if (typeof e.message == "string" ? o = Y(e.message, t.truncate) : n.unshift("message"), o = o ? `: ${o}` : "", t.truncate -= o.length + 5,
  t.seen = t.seen || [], t.seen.includes(e))
    return "[Circular]";
  t.seen.push(e);
  let s = j(n.map((i) => [i, e[i]]), t, le);
  return `${r}${o}${s ? ` { ${s} }` : ""}`;
}
c(yt, "inspectObject");

// ../node_modules/loupe/lib/html.js
function es([e, t], n) {
  return n.truncate -= 3, t ? `${n.stylize(String(e), "yellow")}=${n.stylize(`"${t}"`, "string")}` : `${n.stylize(String(e), "yellow")}`;
}
c(es, "inspectAttribute");
function We(e, t) {
  return j(e, t, Ve, `
`);
}
c(We, "inspectHTMLCollection");
function Ve(e, t) {
  let n = e.getAttributeNames(), r = e.tagName.toLowerCase(), o = t.stylize(`<${r}`, "special"), s = t.stylize(">", "special"), i = t.stylize(
  `</${r}>`, "special");
  t.truncate -= r.length * 2 + 5;
  let a = "";
  n.length > 0 && (a += " ", a += j(n.map((p) => [p, e.getAttribute(p)]), t, es, " ")), t.truncate -= a.length;
  let l = t.truncate, m = We(e.children, t);
  return m && m.length > l && (m = `${ue}(${e.children.length})`), `${o}${a}${s}${m}${i}`;
}
c(Ve, "inspectHTML");

// ../node_modules/loupe/lib/index.js
var ts = typeof Symbol == "function" && typeof Symbol.for == "function", bt = ts ? Symbol.for("chai/inspect") : "@@chai/inspect", Se = !1;
try {
  let e = require("util");
  Se = e.inspect ? e.inspect.custom : !1;
} catch {
  Se = !1;
}
var Cn = /* @__PURE__ */ new WeakMap(), On = {}, $n = {
  undefined: /* @__PURE__ */ c((e, t) => t.stylize("undefined", "undefined"), "undefined"),
  null: /* @__PURE__ */ c((e, t) => t.stylize("null", "null"), "null"),
  boolean: /* @__PURE__ */ c((e, t) => t.stylize(String(e), "boolean"), "boolean"),
  Boolean: /* @__PURE__ */ c((e, t) => t.stylize(String(e), "boolean"), "Boolean"),
  number: Be,
  Number: Be,
  bigint: ze,
  BigInt: ze,
  string: Ye,
  String: Ye,
  function: ke,
  Function: ke,
  symbol: Ue,
  // A Symbol polyfill will return `Symbol` not `symbol` from typedetect
  Symbol: Ue,
  Array: lt,
  Date: at,
  Map: ft,
  Set: pt,
  RegExp: mt,
  Promise: Tn,
  // WeakSet, WeakMap are totally opaque to us
  WeakSet: /* @__PURE__ */ c((e, t) => t.stylize("WeakSet{\u2026}", "special"), "WeakSet"),
  WeakMap: /* @__PURE__ */ c((e, t) => t.stylize("WeakMap{\u2026}", "special"), "WeakMap"),
  Arguments: dt,
  Int8Array: oe,
  Uint8Array: oe,
  Uint8ClampedArray: oe,
  Int16Array: oe,
  Uint16Array: oe,
  Int32Array: oe,
  Uint32Array: oe,
  Float32Array: oe,
  Float64Array: oe,
  Generator: /* @__PURE__ */ c(() => "", "Generator"),
  DataView: /* @__PURE__ */ c(() => "", "DataView"),
  ArrayBuffer: /* @__PURE__ */ c(() => "", "ArrayBuffer"),
  Error: yt,
  HTMLCollection: We,
  NodeList: We
}, ns = /* @__PURE__ */ c((e, t, n) => bt in e && typeof e[bt] == "function" ? e[bt](t) : Se && Se in e && typeof e[Se] == "function" ? e[Se](
t.depth, t) : "inspect" in e && typeof e.inspect == "function" ? e.inspect(t.depth, t) : "constructor" in e && Cn.has(e.constructor) ? Cn.get(
e.constructor)(e, t) : On[n] ? On[n](e, t) : "", "inspectCustom"), rs = Object.prototype.toString;
function qe(e, t = {}) {
  let n = Sn(t, qe), { customInspect: r } = n, o = e === null ? "null" : typeof e;
  if (o === "object" && (o = rs.call(e).slice(8, -1)), o in $n)
    return $n[o](e, n);
  if (r && e) {
    let i = ns(e, n, o);
    if (i)
      return typeof i == "string" ? i : qe(i, n);
  }
  let s = e ? Object.getPrototypeOf(e) : !1;
  return s === Object.prototype || s === null ? pe(e, n) : e && typeof HTMLElement == "function" && e instanceof HTMLElement ? Ve(e, n) : "c\
onstructor" in e ? e.constructor !== Object ? ht(e, n) : pe(e, n) : e === Object(e) ? pe(e, n) : n.stylize(String(e), o);
}
c(qe, "inspect");

// ../node_modules/@vitest/utils/dist/chunk-_commonjsHelpers.js
var { AsymmetricMatcher: ss, DOMCollection: is, DOMElement: cs, Immutable: us, ReactElement: ls, ReactTestComponent: as } = $e, wn = [
  as,
  ls,
  cs,
  is,
  us,
  ss
];
function ge(e, t = 10, { maxLength: n, ...r } = {}) {
  let o = n ?? 1e4, s;
  try {
    s = Q(e, {
      maxDepth: t,
      escapeString: !1,
      plugins: wn,
      ...r
    });
  } catch {
    s = Q(e, {
      callToJSON: !1,
      maxDepth: t,
      escapeString: !1,
      plugins: wn,
      ...r
    });
  }
  return s.length >= o && t > 1 ? ge(e, Math.floor(Math.min(t, Number.MAX_SAFE_INTEGER) / 2), {
    maxLength: n,
    ...r
  }) : s;
}
c(ge, "stringify");
var fs = /%[sdjifoOc%]/g;
function An(...e) {
  if (typeof e[0] != "string") {
    let s = [];
    for (let i = 0; i < e.length; i++)
      s.push(we(e[i], {
        depth: 0,
        colors: !1
      }));
    return s.join(" ");
  }
  let t = e.length, n = 1, r = e[0], o = String(r).replace(fs, (s) => {
    if (s === "%%")
      return "%";
    if (n >= t)
      return s;
    switch (s) {
      case "%s": {
        let i = e[n++];
        return typeof i == "bigint" ? `${i.toString()}n` : typeof i == "number" && i === 0 && 1 / i < 0 ? "-0" : typeof i == "object" && i !==
        null ? typeof i.toString == "function" && i.toString !== Object.prototype.toString ? i.toString() : we(i, {
          depth: 0,
          colors: !1
        }) : String(i);
      }
      case "%d": {
        let i = e[n++];
        return typeof i == "bigint" ? `${i.toString()}n` : Number(i).toString();
      }
      case "%i": {
        let i = e[n++];
        return typeof i == "bigint" ? `${i.toString()}n` : Number.parseInt(String(i)).toString();
      }
      case "%f":
        return Number.parseFloat(String(e[n++])).toString();
      case "%o":
        return we(e[n++], {
          showHidden: !0,
          showProxy: !0
        });
      case "%O":
        return we(e[n++]);
      case "%c":
        return n++, "";
      case "%j":
        try {
          return JSON.stringify(e[n++]);
        } catch (i) {
          let a = i.message;
          if (a.includes("circular structure") || a.includes("cyclic structures") || a.includes("cyclic object"))
            return "[Circular]";
          throw i;
        }
      default:
        return s;
    }
  });
  for (let s = e[n]; n < t; s = e[++n])
    s === null || typeof s != "object" ? o += ` ${s}` : o += ` ${we(s)}`;
  return o;
}
c(An, "format");
function we(e, t = {}) {
  return t.truncate === 0 && (t.truncate = Number.POSITIVE_INFINITY), qe(e, t);
}
c(we, "inspect");
function Rn(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
c(Rn, "getDefaultExportFromCjs");

// ../node_modules/@vitest/utils/dist/helpers.js
function ms(e) {
  return e === Object.prototype || e === Function.prototype || e === RegExp.prototype;
}
c(ms, "isFinalObj");
function Ke(e) {
  return Object.prototype.toString.apply(e).slice(8, -1);
}
c(Ke, "getType");
function ps(e, t) {
  let n = typeof t == "function" ? t : (r) => t.add(r);
  Object.getOwnPropertyNames(e).forEach(n), Object.getOwnPropertySymbols(e).forEach(n);
}
c(ps, "collectOwnProperties");
function Et(e) {
  let t = /* @__PURE__ */ new Set();
  return ms(e) ? [] : (ps(e, t), Array.from(t));
}
c(Et, "getOwnProperties");
var Pn = { forceWritable: !1 };
function _t(e, t = Pn) {
  return St(e, /* @__PURE__ */ new WeakMap(), t);
}
c(_t, "deepClone");
function St(e, t, n = Pn) {
  let r, o;
  if (t.has(e))
    return t.get(e);
  if (Array.isArray(e)) {
    for (o = Array.from({ length: r = e.length }), t.set(e, o); r--; )
      o[r] = St(e[r], t, n);
    return o;
  }
  if (Object.prototype.toString.call(e) === "[object Object]") {
    o = Object.create(Object.getPrototypeOf(e)), t.set(e, o);
    let s = Et(e);
    for (let i of s) {
      let a = Object.getOwnPropertyDescriptor(e, i);
      if (!a)
        continue;
      let l = St(e[i], t, n);
      n.forceWritable ? Object.defineProperty(o, i, {
        enumerable: a.enumerable,
        configurable: !0,
        writable: !0,
        value: l
      }) : "get" in a ? Object.defineProperty(o, i, {
        ...a,
        get() {
          return l;
        }
      }) : Object.defineProperty(o, i, {
        ...a,
        value: l
      });
    }
    return o;
  }
  return e;
}
c(St, "clone");

// ../node_modules/@vitest/utils/dist/diff.js
var U = -1, B = 1, x = 0, I = class {
  static {
    c(this, "Diff");
  }
  0;
  1;
  constructor(t, n) {
    this[0] = t, this[1] = n;
  }
};
function gs(e, t) {
  if (!e || !t || e.charAt(0) !== t.charAt(0))
    return 0;
  let n = 0, r = Math.min(e.length, t.length), o = r, s = 0;
  for (; n < o; )
    e.substring(s, o) === t.substring(s, o) ? (n = o, s = n) : r = o, o = Math.floor((r - n) / 2 + n);
  return o;
}
c(gs, "diff_commonPrefix");
function qn(e, t) {
  if (!e || !t || e.charAt(e.length - 1) !== t.charAt(t.length - 1))
    return 0;
  let n = 0, r = Math.min(e.length, t.length), o = r, s = 0;
  for (; n < o; )
    e.substring(e.length - o, e.length - s) === t.substring(t.length - o, t.length - s) ? (n = o, s = n) : r = o, o = Math.floor((r - n) / 2 +
    n);
  return o;
}
c(qn, "diff_commonSuffix");
function Nn(e, t) {
  let n = e.length, r = t.length;
  if (n === 0 || r === 0)
    return 0;
  n > r ? e = e.substring(n - r) : n < r && (t = t.substring(0, n));
  let o = Math.min(n, r);
  if (e === t)
    return o;
  let s = 0, i = 1;
  for (; ; ) {
    let a = e.substring(o - i), l = t.indexOf(a);
    if (l === -1)
      return s;
    i += l, (l === 0 || e.substring(o - i) === t.substring(0, i)) && (s = i, i++);
  }
}
c(Nn, "diff_commonOverlap_");
function hs(e) {
  let t = !1, n = [], r = 0, o = null, s = 0, i = 0, a = 0, l = 0, m = 0;
  for (; s < e.length; )
    e[s][0] === x ? (n[r++] = s, i = l, a = m, l = 0, m = 0, o = e[s][1]) : (e[s][0] === B ? l += e[s][1].length : m += e[s][1].length, o &&
    o.length <= Math.max(i, a) && o.length <= Math.max(l, m) && (e.splice(n[r - 1], 0, new I(U, o)), e[n[r - 1] + 1][0] = B, r--, r--, s = r >
    0 ? n[r - 1] : -1, i = 0, a = 0, l = 0, m = 0, o = null, t = !0)), s++;
  for (t && Kn(e), bs(e), s = 1; s < e.length; ) {
    if (e[s - 1][0] === U && e[s][0] === B) {
      let p = e[s - 1][1], f = e[s][1], b = Nn(p, f), d = Nn(f, p);
      b >= d ? (b >= p.length / 2 || b >= f.length / 2) && (e.splice(s, 0, new I(x, f.substring(0, b))), e[s - 1][1] = p.substring(0, p.length -
      b), e[s + 1][1] = f.substring(b), s++) : (d >= p.length / 2 || d >= f.length / 2) && (e.splice(s, 0, new I(x, p.substring(0, d))), e[s -
      1][0] = B, e[s - 1][1] = f.substring(0, f.length - d), e[s + 1][0] = U, e[s + 1][1] = p.substring(d), s++), s++;
    }
    s++;
  }
}
c(hs, "diff_cleanupSemantic");
var In = /[^a-z0-9]/i, Mn = /\s/, Ln = /[\r\n]/, ds = /\n\r?\n$/, ys = /^\r?\n\r?\n/;
function bs(e) {
  let t = 1;
  for (; t < e.length - 1; ) {
    if (e[t - 1][0] === x && e[t + 1][0] === x) {
      let n = e[t - 1][1], r = e[t][1], o = e[t + 1][1], s = qn(n, r);
      if (s) {
        let p = r.substring(r.length - s);
        n = n.substring(0, n.length - s), r = p + r.substring(0, r.length - s), o = p + o;
      }
      let i = n, a = r, l = o, m = Ge(n, r) + Ge(r, o);
      for (; r.charAt(0) === o.charAt(0); ) {
        n += r.charAt(0), r = r.substring(1) + o.charAt(0), o = o.substring(1);
        let p = Ge(n, r) + Ge(r, o);
        p >= m && (m = p, i = n, a = r, l = o);
      }
      e[t - 1][1] !== i && (i ? e[t - 1][1] = i : (e.splice(t - 1, 1), t--), e[t][1] = a, l ? e[t + 1][1] = l : (e.splice(t + 1, 1), t--));
    }
    t++;
  }
}
c(bs, "diff_cleanupSemanticLossless");
function Kn(e) {
  e.push(new I(x, ""));
  let t = 0, n = 0, r = 0, o = "", s = "", i;
  for (; t < e.length; )
    switch (e[t][0]) {
      case B:
        r++, s += e[t][1], t++;
        break;
      case U:
        n++, o += e[t][1], t++;
        break;
      case x:
        n + r > 1 ? (n !== 0 && r !== 0 && (i = gs(s, o), i !== 0 && (t - n - r > 0 && e[t - n - r - 1][0] === x ? e[t - n - r - 1][1] += s.
        substring(0, i) : (e.splice(0, 0, new I(x, s.substring(0, i))), t++), s = s.substring(i), o = o.substring(i)), i = qn(s, o), i !== 0 &&
        (e[t][1] = s.substring(s.length - i) + e[t][1], s = s.substring(0, s.length - i), o = o.substring(0, o.length - i))), t -= n + r, e.
        splice(t, n + r), o.length && (e.splice(t, 0, new I(U, o)), t++), s.length && (e.splice(t, 0, new I(B, s)), t++), t++) : t !== 0 && e[t -
        1][0] === x ? (e[t - 1][1] += e[t][1], e.splice(t, 1)) : t++, r = 0, n = 0, o = "", s = "";
        break;
    }
  e[e.length - 1][1] === "" && e.pop();
  let a = !1;
  for (t = 1; t < e.length - 1; )
    e[t - 1][0] === x && e[t + 1][0] === x && (e[t][1].substring(e[t][1].length - e[t - 1][1].length) === e[t - 1][1] ? (e[t][1] = e[t - 1][1] +
    e[t][1].substring(0, e[t][1].length - e[t - 1][1].length), e[t + 1][1] = e[t - 1][1] + e[t + 1][1], e.splice(t - 1, 1), a = !0) : e[t][1].
    substring(0, e[t + 1][1].length) === e[t + 1][1] && (e[t - 1][1] += e[t + 1][1], e[t][1] = e[t][1].substring(e[t + 1][1].length) + e[t +
    1][1], e.splice(t + 1, 1), a = !0)), t++;
  a && Kn(e);
}
c(Kn, "diff_cleanupMerge");
function Ge(e, t) {
  if (!e || !t)
    return 6;
  let n = e.charAt(e.length - 1), r = t.charAt(0), o = n.match(In), s = r.match(In), i = o && n.match(Mn), a = s && r.match(Mn), l = i && n.
  match(Ln), m = a && r.match(Ln), p = l && e.match(ds), f = m && t.match(ys);
  return p || f ? 5 : l || m ? 4 : o && !i && a ? 3 : i || a ? 2 : o || s ? 1 : 0;
}
c(Ge, "diff_cleanupSemanticScore_");
var Gn = "Compared values have no visual difference.", Ss = "Compared values serialize to the same structure.\nPrinting internal object struc\
ture without calling `toJSON` instead.", He = {}, xn;
function Es() {
  if (xn) return He;
  xn = 1, Object.defineProperty(He, "__esModule", {
    value: !0
  }), He.default = b;
  let e = "diff-sequences", t = 0, n = /* @__PURE__ */ c((d, g, u, h, S) => {
    let _ = 0;
    for (; d < g && u < h && S(d, u); )
      d += 1, u += 1, _ += 1;
    return _;
  }, "countCommonItemsF"), r = /* @__PURE__ */ c((d, g, u, h, S) => {
    let _ = 0;
    for (; d <= g && u <= h && S(g, h); )
      g -= 1, h -= 1, _ += 1;
    return _;
  }, "countCommonItemsR"), o = /* @__PURE__ */ c((d, g, u, h, S, _, O) => {
    let y = 0, E = -d, $ = _[y], T = $;
    _[y] += n(
      $ + 1,
      g,
      h + $ - E + 1,
      u,
      S
    );
    let R = d < O ? d : O;
    for (y += 1, E += 2; y <= R; y += 1, E += 2) {
      if (y !== d && T < _[y])
        $ = _[y];
      else if ($ = T + 1, g <= $)
        return y - 1;
      T = _[y], _[y] = $ + n($ + 1, g, h + $ - E + 1, u, S);
    }
    return O;
  }, "extendPathsF"), s = /* @__PURE__ */ c((d, g, u, h, S, _, O) => {
    let y = 0, E = d, $ = _[y], T = $;
    _[y] -= r(
      g,
      $ - 1,
      u,
      h + $ - E - 1,
      S
    );
    let R = d < O ? d : O;
    for (y += 1, E -= 2; y <= R; y += 1, E -= 2) {
      if (y !== d && _[y] < T)
        $ = _[y];
      else if ($ = T - 1, $ < g)
        return y - 1;
      T = _[y], _[y] = $ - r(
        g,
        $ - 1,
        u,
        h + $ - E - 1,
        S
      );
    }
    return O;
  }, "extendPathsR"), i = /* @__PURE__ */ c((d, g, u, h, S, _, O, y, E, $, T) => {
    let R = h - g, J = u - g, L = S - h - J, z = -L - (d - 1), X = -L + (d - 1), W = t, M = d < y ? d : y;
    for (let D = 0, F = -d; D <= M; D += 1, F += 2) {
      let Z = D === 0 || D !== d && W < O[D], k = Z ? O[D] : W, q = Z ? k : k + 1, se = R + q - F, K = n(
        q + 1,
        u,
        se + 1,
        S,
        _
      ), G = q + K;
      if (W = O[D], O[D] = G, z <= F && F <= X) {
        let ce = (d - 1 - (F + L)) / 2;
        if (ce <= $ && E[ce] - 1 <= G) {
          let v = R + k - (Z ? F + 1 : F - 1), V = r(
            g,
            k,
            h,
            v,
            _
          ), ie = k - V, ye = v - V, ae = ie + 1, Te = ye + 1;
          T.nChangePreceding = d - 1, d - 1 === ae + Te - g - h ? (T.aEndPreceding = g, T.bEndPreceding = h) : (T.aEndPreceding = ae, T.bEndPreceding =
          Te), T.nCommonPreceding = V, V !== 0 && (T.aCommonPreceding = ae, T.bCommonPreceding = Te), T.nCommonFollowing = K, K !== 0 && (T.
          aCommonFollowing = q + 1, T.bCommonFollowing = se + 1);
          let Ae = G + 1, Re = se + K + 1;
          return T.nChangeFollowing = d - 1, d - 1 === u + S - Ae - Re ? (T.aStartFollowing = u, T.bStartFollowing = S) : (T.aStartFollowing =
          Ae, T.bStartFollowing = Re), !0;
        }
      }
    }
    return !1;
  }, "extendOverlappablePathsF"), a = /* @__PURE__ */ c((d, g, u, h, S, _, O, y, E, $, T) => {
    let R = S - u, J = u - g, L = S - h - J, z = L - d, X = L + d, W = t, M = d < $ ? d : $;
    for (let D = 0, F = d; D <= M; D += 1, F -= 2) {
      let Z = D === 0 || D !== d && E[D] < W, k = Z ? E[D] : W, q = Z ? k : k - 1, se = R + q - F, K = r(
        g,
        q - 1,
        h,
        se - 1,
        _
      ), G = q - K;
      if (W = E[D], E[D] = G, z <= F && F <= X) {
        let ce = (d + (F - L)) / 2;
        if (ce <= y && G - 1 <= O[ce]) {
          let v = se - K;
          if (T.nChangePreceding = d, d === G + v - g - h ? (T.aEndPreceding = g, T.bEndPreceding = h) : (T.aEndPreceding = G, T.bEndPreceding =
          v), T.nCommonPreceding = K, K !== 0 && (T.aCommonPreceding = G, T.bCommonPreceding = v), T.nChangeFollowing = d - 1, d === 1)
            T.nCommonFollowing = 0, T.aStartFollowing = u, T.bStartFollowing = S;
          else {
            let V = R + k - (Z ? F - 1 : F + 1), ie = n(
              k,
              u,
              V,
              S,
              _
            );
            T.nCommonFollowing = ie, ie !== 0 && (T.aCommonFollowing = k, T.bCommonFollowing = V);
            let ye = k + ie, ae = V + ie;
            d - 1 === u + S - ye - ae ? (T.aStartFollowing = u, T.bStartFollowing = S) : (T.aStartFollowing = ye, T.bStartFollowing = ae);
          }
          return !0;
        }
      }
    }
    return !1;
  }, "extendOverlappablePathsR"), l = /* @__PURE__ */ c((d, g, u, h, S, _, O, y, E) => {
    let $ = h - g, T = S - u, R = u - g, J = S - h, te = J - R, L = R, z = R;
    if (O[0] = g - 1, y[0] = u, te % 2 === 0) {
      let X = (d || te) / 2, W = (R + J) / 2;
      for (let M = 1; M <= W; M += 1)
        if (L = o(M, u, S, $, _, O, L), M < X)
          z = s(M, g, h, T, _, y, z);
        else if (
          // If a reverse path overlaps a forward path in the same diagonal,
          // return a division of the index intervals at the middle change.
          a(
            M,
            g,
            u,
            h,
            S,
            _,
            O,
            L,
            y,
            z,
            E
          )
        )
          return;
    } else {
      let X = ((d || te) + 1) / 2, W = (R + J + 1) / 2, M = 1;
      for (L = o(M, u, S, $, _, O, L), M += 1; M <= W; M += 1)
        if (z = s(
          M - 1,
          g,
          h,
          T,
          _,
          y,
          z
        ), M < X)
          L = o(M, u, S, $, _, O, L);
        else if (
          // If a forward path overlaps a reverse path in the same diagonal,
          // return a division of the index intervals at the middle change.
          i(
            M,
            g,
            u,
            h,
            S,
            _,
            O,
            L,
            y,
            z,
            E
          )
        )
          return;
    }
    throw new Error(
      `${e}: no overlap aStart=${g} aEnd=${u} bStart=${h} bEnd=${S}`
    );
  }, "divide"), m = /* @__PURE__ */ c((d, g, u, h, S, _, O, y, E, $) => {
    if (S - h < u - g) {
      if (_ = !_, _ && O.length === 1) {
        let { foundSubsequence: G, isCommon: ce } = O[0];
        O[1] = {
          foundSubsequence: /* @__PURE__ */ c((v, V, ie) => {
            G(v, ie, V);
          }, "foundSubsequence"),
          isCommon: /* @__PURE__ */ c((v, V) => ce(V, v), "isCommon")
        };
      }
      let se = g, K = u;
      g = h, u = S, h = se, S = K;
    }
    let { foundSubsequence: T, isCommon: R } = O[_ ? 1 : 0];
    l(
      d,
      g,
      u,
      h,
      S,
      R,
      y,
      E,
      $
    );
    let {
      nChangePreceding: J,
      aEndPreceding: te,
      bEndPreceding: L,
      nCommonPreceding: z,
      aCommonPreceding: X,
      bCommonPreceding: W,
      nCommonFollowing: M,
      aCommonFollowing: D,
      bCommonFollowing: F,
      nChangeFollowing: Z,
      aStartFollowing: k,
      bStartFollowing: q
    } = $;
    g < te && h < L && m(
      J,
      g,
      te,
      h,
      L,
      _,
      O,
      y,
      E,
      $
    ), z !== 0 && T(z, X, W), M !== 0 && T(M, D, F), k < u && q < S && m(
      Z,
      k,
      u,
      q,
      S,
      _,
      O,
      y,
      E,
      $
    );
  }, "findSubsequences"), p = /* @__PURE__ */ c((d, g) => {
    if (typeof g != "number")
      throw new TypeError(`${e}: ${d} typeof ${typeof g} is not a number`);
    if (!Number.isSafeInteger(g))
      throw new RangeError(`${e}: ${d} value ${g} is not a safe integer`);
    if (g < 0)
      throw new RangeError(`${e}: ${d} value ${g} is a negative integer`);
  }, "validateLength"), f = /* @__PURE__ */ c((d, g) => {
    let u = typeof g;
    if (u !== "function")
      throw new TypeError(`${e}: ${d} typeof ${u} is not a function`);
  }, "validateCallback");
  function b(d, g, u, h) {
    p("aLength", d), p("bLength", g), f("isCommon", u), f("foundSubsequence", h);
    let S = n(0, d, 0, g, u);
    if (S !== 0 && h(S, 0, 0), d !== S || g !== S) {
      let _ = S, O = S, y = r(
        _,
        d - 1,
        O,
        g - 1,
        u
      ), E = d - y, $ = g - y, T = S + y;
      d !== T && g !== T && m(
        0,
        _,
        E,
        O,
        $,
        !1,
        [
          {
            foundSubsequence: h,
            isCommon: u
          }
        ],
        [t],
        [t],
        {
          aCommonFollowing: t,
          aCommonPreceding: t,
          aEndPreceding: t,
          aStartFollowing: t,
          bCommonFollowing: t,
          bCommonPreceding: t,
          bEndPreceding: t,
          bStartFollowing: t,
          nChangeFollowing: t,
          nChangePreceding: t,
          nCommonFollowing: t,
          nCommonPreceding: t
        }
      ), y !== 0 && h(y, E, $);
    }
  }
  return c(b, "diffSequence"), He;
}
c(Es, "requireBuild");
var _s = Es(), Hn = /* @__PURE__ */ Rn(_s);
function Ts(e, t) {
  return e.replace(/\s+$/, (n) => t(n));
}
c(Ts, "formatTrailingSpaces");
function Rt(e, t, n, r, o, s) {
  return e.length !== 0 ? n(`${r} ${Ts(e, o)}`) : r !== " " ? n(r) : t && s.length !== 0 ? n(`${r} ${s}`) : "";
}
c(Rt, "printDiffLine");
function Jn(e, t, { aColor: n, aIndicator: r, changeLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return Rt(e, t, n, r, o, s);
}
c(Jn, "printDeleteLine");
function Xn(e, t, { bColor: n, bIndicator: r, changeLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return Rt(e, t, n, r, o, s);
}
c(Xn, "printInsertLine");
function Zn(e, t, { commonColor: n, commonIndicator: r, commonLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return Rt(e, t, n, r, o, s);
}
c(Zn, "printCommonLine");
function Dn(e, t, n, r, { patchColor: o }) {
  return o(`@@ -${e + 1},${t - e} +${n + 1},${r - n} @@`);
}
c(Dn, "createPatchMark");
function Cs(e, t) {
  let n = e.length, r = t.contextLines, o = r + r, s = n, i = !1, a = 0, l = 0;
  for (; l !== n; ) {
    let y = l;
    for (; l !== n && e[l][0] === x; )
      l += 1;
    if (y !== l)
      if (y === 0)
        l > r && (s -= l - r, i = !0);
      else if (l === n) {
        let E = l - y;
        E > r && (s -= E - r, i = !0);
      } else {
        let E = l - y;
        E > o && (s -= E - o, a += 1);
      }
    for (; l !== n && e[l][0] !== x; )
      l += 1;
  }
  let m = a !== 0 || i;
  a !== 0 ? s += a + 1 : i && (s += 1);
  let p = s - 1, f = [], b = 0;
  m && f.push("");
  let d = 0, g = 0, u = 0, h = 0, S = /* @__PURE__ */ c((y) => {
    let E = f.length;
    f.push(Zn(y, E === 0 || E === p, t)), u += 1, h += 1;
  }, "pushCommonLine"), _ = /* @__PURE__ */ c((y) => {
    let E = f.length;
    f.push(Jn(y, E === 0 || E === p, t)), u += 1;
  }, "pushDeleteLine"), O = /* @__PURE__ */ c((y) => {
    let E = f.length;
    f.push(Xn(y, E === 0 || E === p, t)), h += 1;
  }, "pushInsertLine");
  for (l = 0; l !== n; ) {
    let y = l;
    for (; l !== n && e[l][0] === x; )
      l += 1;
    if (y !== l)
      if (y === 0) {
        l > r && (y = l - r, d = y, g = y, u = d, h = g);
        for (let E = y; E !== l; E += 1)
          S(e[E][1]);
      } else if (l === n) {
        let E = l - y > r ? y + r : l;
        for (let $ = y; $ !== E; $ += 1)
          S(e[$][1]);
      } else {
        let E = l - y;
        if (E > o) {
          let $ = y + r;
          for (let R = y; R !== $; R += 1)
            S(e[R][1]);
          f[b] = Dn(d, u, g, h, t), b = f.length, f.push("");
          let T = E - o;
          d = u + T, g = h + T, u = d, h = g;
          for (let R = l - r; R !== l; R += 1)
            S(e[R][1]);
        } else
          for (let $ = y; $ !== l; $ += 1)
            S(e[$][1]);
      }
    for (; l !== n && e[l][0] === U; )
      _(e[l][1]), l += 1;
    for (; l !== n && e[l][0] === B; )
      O(e[l][1]), l += 1;
  }
  return m && (f[b] = Dn(d, u, g, h, t)), f.join(`
`);
}
c(Cs, "joinAlignedDiffsNoExpand");
function Os(e, t) {
  return e.map((n, r, o) => {
    let s = n[1], i = r === 0 || r === o.length - 1;
    switch (n[0]) {
      case U:
        return Jn(s, i, t);
      case B:
        return Xn(s, i, t);
      default:
        return Zn(s, i, t);
    }
  }).join(`
`);
}
c(Os, "joinAlignedDiffsExpand");
var Tt = /* @__PURE__ */ c((e) => e, "noColor"), vn = 5, $s = 0;
function ws() {
  return {
    aAnnotation: "Expected",
    aColor: ne.green,
    aIndicator: "-",
    bAnnotation: "Received",
    bColor: ne.red,
    bIndicator: "+",
    changeColor: ne.inverse,
    changeLineTrailingSpaceColor: Tt,
    commonColor: ne.dim,
    commonIndicator: " ",
    commonLineTrailingSpaceColor: Tt,
    compareKeys: void 0,
    contextLines: vn,
    emptyFirstOrLastLinePlaceholder: "",
    expand: !1,
    includeChangeCounts: !1,
    omitAnnotationLines: !1,
    patchColor: ne.yellow,
    printBasicPrototype: !1,
    truncateThreshold: $s,
    truncateAnnotation: "... Diff result is truncated",
    truncateAnnotationColor: Tt
  };
}
c(ws, "getDefaultOptions");
function As(e) {
  return e && typeof e == "function" ? e : void 0;
}
c(As, "getCompareKeys");
function Rs(e) {
  return typeof e == "number" && Number.isSafeInteger(e) && e >= 0 ? e : vn;
}
c(Rs, "getContextLines");
function he(e = {}) {
  return {
    ...ws(),
    ...e,
    compareKeys: As(e.compareKeys),
    contextLines: Rs(e.contextLines)
  };
}
c(he, "normalizeDiffOptions");
function Ee(e) {
  return e.length === 1 && e[0].length === 0;
}
c(Ee, "isEmptyString");
function Ps(e) {
  let t = 0, n = 0;
  return e.forEach((r) => {
    switch (r[0]) {
      case U:
        t += 1;
        break;
      case B:
        n += 1;
        break;
    }
  }), {
    a: t,
    b: n
  };
}
c(Ps, "countChanges");
function Ns({ aAnnotation: e, aColor: t, aIndicator: n, bAnnotation: r, bColor: o, bIndicator: s, includeChangeCounts: i, omitAnnotationLines: a }, l) {
  if (a)
    return "";
  let m = "", p = "";
  if (i) {
    let d = String(l.a), g = String(l.b), u = r.length - e.length, h = " ".repeat(Math.max(0, u)), S = " ".repeat(Math.max(0, -u)), _ = g.length -
    d.length, O = " ".repeat(Math.max(0, _)), y = " ".repeat(Math.max(0, -_));
    m = `${h}  ${n} ${O}${d}`, p = `${S}  ${s} ${y}${g}`;
  }
  let f = `${n} ${e}${m}`, b = `${s} ${r}${p}`;
  return `${t(f)}
${o(b)}

`;
}
c(Ns, "printAnnotation");
function Pt(e, t, n) {
  return Ns(n, Ps(e)) + (n.expand ? Os(e, n) : Cs(e, n)) + (t ? n.truncateAnnotationColor(`
${n.truncateAnnotation}`) : "");
}
c(Pt, "printDiffLines");
function Xe(e, t, n) {
  let r = he(n), [o, s] = Qn(Ee(e) ? [] : e, Ee(t) ? [] : t, r);
  return Pt(o, s, r);
}
c(Xe, "diffLinesUnified");
function Is(e, t, n, r, o) {
  if (Ee(e) && Ee(n) && (e = [], n = []), Ee(t) && Ee(r) && (t = [], r = []), e.length !== n.length || t.length !== r.length)
    return Xe(e, t, o);
  let [s, i] = Qn(n, r, o), a = 0, l = 0;
  return s.forEach((m) => {
    switch (m[0]) {
      case U:
        m[1] = e[a], a += 1;
        break;
      case B:
        m[1] = t[l], l += 1;
        break;
      default:
        m[1] = t[l], a += 1, l += 1;
    }
  }), Pt(s, i, he(o));
}
c(Is, "diffLinesUnified2");
function Qn(e, t, n) {
  let r = n?.truncateThreshold ?? !1, o = Math.max(Math.floor(n?.truncateThreshold ?? 0), 0), s = r ? Math.min(e.length, o) : e.length, i = r ?
  Math.min(t.length, o) : t.length, a = s !== e.length || i !== t.length, l = /* @__PURE__ */ c((d, g) => e[d] === t[g], "isCommon"), m = [],
  p = 0, f = 0;
  for (Hn(s, i, l, /* @__PURE__ */ c((d, g, u) => {
    for (; p !== g; p += 1)
      m.push(new I(U, e[p]));
    for (; f !== u; f += 1)
      m.push(new I(B, t[f]));
    for (; d !== 0; d -= 1, p += 1, f += 1)
      m.push(new I(x, t[f]));
  }, "foundSubsequence")); p !== s; p += 1)
    m.push(new I(U, e[p]));
  for (; f !== i; f += 1)
    m.push(new I(B, t[f]));
  return [m, a];
}
c(Qn, "diffLinesRaw");
function Fn(e) {
  if (e === void 0)
    return "undefined";
  if (e === null)
    return "null";
  if (Array.isArray(e))
    return "array";
  if (typeof e == "boolean")
    return "boolean";
  if (typeof e == "function")
    return "function";
  if (typeof e == "number")
    return "number";
  if (typeof e == "string")
    return "string";
  if (typeof e == "bigint")
    return "bigint";
  if (typeof e == "object") {
    if (e != null) {
      if (e.constructor === RegExp)
        return "regexp";
      if (e.constructor === Map)
        return "map";
      if (e.constructor === Set)
        return "set";
      if (e.constructor === Date)
        return "date";
    }
    return "object";
  } else if (typeof e == "symbol")
    return "symbol";
  throw new Error(`value of unknown type: ${e}`);
}
c(Fn, "getType");
function jn(e) {
  return e.includes(`\r
`) ? `\r
` : `
`;
}
c(jn, "getNewLineSymbol");
function Ms(e, t, n) {
  let r = n?.truncateThreshold ?? !1, o = Math.max(Math.floor(n?.truncateThreshold ?? 0), 0), s = e.length, i = t.length;
  if (r) {
    let d = e.includes(`
`), g = t.includes(`
`), u = jn(e), h = jn(t), S = d ? `${e.split(u, o).join(u)}
` : e, _ = g ? `${t.split(h, o).join(h)}
` : t;
    s = S.length, i = _.length;
  }
  let a = s !== e.length || i !== t.length, l = /* @__PURE__ */ c((d, g) => e[d] === t[g], "isCommon"), m = 0, p = 0, f = [];
  return Hn(s, i, l, /* @__PURE__ */ c((d, g, u) => {
    m !== g && f.push(new I(U, e.slice(m, g))), p !== u && f.push(new I(B, t.slice(p, u))), m = g + d, p = u + d, f.push(new I(x, t.slice(u,
    p)));
  }, "foundSubsequence")), m !== s && f.push(new I(U, e.slice(m))), p !== i && f.push(new I(B, t.slice(p))), [f, a];
}
c(Ms, "diffStrings");
function Ls(e, t, n) {
  return t.reduce((r, o) => r + (o[0] === x ? o[1] : o[0] === e && o[1].length !== 0 ? n(o[1]) : ""), "");
}
c(Ls, "concatenateRelevantDiffs");
var Je = class {
  static {
    c(this, "ChangeBuffer");
  }
  op;
  line;
  lines;
  changeColor;
  constructor(t, n) {
    this.op = t, this.line = [], this.lines = [], this.changeColor = n;
  }
  pushSubstring(t) {
    this.pushDiff(new I(this.op, t));
  }
  pushLine() {
    this.lines.push(this.line.length !== 1 ? new I(this.op, Ls(this.op, this.line, this.changeColor)) : this.line[0][0] === this.op ? this.line[0] :
    new I(this.op, this.line[0][1])), this.line.length = 0;
  }
  isLineEmpty() {
    return this.line.length === 0;
  }
  pushDiff(t) {
    this.line.push(t);
  }
  align(t) {
    let n = t[1];
    if (n.includes(`
`)) {
      let r = n.split(`
`), o = r.length - 1;
      r.forEach((s, i) => {
        i < o ? (this.pushSubstring(s), this.pushLine()) : s.length !== 0 && this.pushSubstring(s);
      });
    } else
      this.pushDiff(t);
  }
  moveLinesTo(t) {
    this.isLineEmpty() || this.pushLine(), t.push(...this.lines), this.lines.length = 0;
  }
}, Ot = class {
  static {
    c(this, "CommonBuffer");
  }
  deleteBuffer;
  insertBuffer;
  lines;
  constructor(t, n) {
    this.deleteBuffer = t, this.insertBuffer = n, this.lines = [];
  }
  pushDiffCommonLine(t) {
    this.lines.push(t);
  }
  pushDiffChangeLines(t) {
    let n = t[1].length === 0;
    (!n || this.deleteBuffer.isLineEmpty()) && this.deleteBuffer.pushDiff(t), (!n || this.insertBuffer.isLineEmpty()) && this.insertBuffer.pushDiff(
    t);
  }
  flushChangeLines() {
    this.deleteBuffer.moveLinesTo(this.lines), this.insertBuffer.moveLinesTo(this.lines);
  }
  align(t) {
    let n = t[0], r = t[1];
    if (r.includes(`
`)) {
      let o = r.split(`
`), s = o.length - 1;
      o.forEach((i, a) => {
        if (a === 0) {
          let l = new I(n, i);
          this.deleteBuffer.isLineEmpty() && this.insertBuffer.isLineEmpty() ? (this.flushChangeLines(), this.pushDiffCommonLine(l)) : (this.
          pushDiffChangeLines(l), this.flushChangeLines());
        } else a < s ? this.pushDiffCommonLine(new I(n, i)) : i.length !== 0 && this.pushDiffChangeLines(new I(n, i));
      });
    } else
      this.pushDiffChangeLines(t);
  }
  getLines() {
    return this.flushChangeLines(), this.lines;
  }
};
function xs(e, t) {
  let n = new Je(U, t), r = new Je(B, t), o = new Ot(n, r);
  return e.forEach((s) => {
    switch (s[0]) {
      case U:
        n.align(s);
        break;
      case B:
        r.align(s);
        break;
      default:
        o.align(s);
    }
  }), o.getLines();
}
c(xs, "getAlignedDiffs");
function Ds(e, t) {
  if (t) {
    let n = e.length - 1;
    return e.some((r, o) => r[0] === x && (o !== n || r[1] !== `
`));
  }
  return e.some((n) => n[0] === x);
}
c(Ds, "hasCommonDiff");
function Fs(e, t, n) {
  if (e !== t && e.length !== 0 && t.length !== 0) {
    let r = e.includes(`
`) || t.includes(`
`), [o, s] = er(r ? `${e}
` : e, r ? `${t}
` : t, !0, n);
    if (Ds(o, r)) {
      let i = he(n), a = xs(o, i.changeColor);
      return Pt(a, s, i);
    }
  }
  return Xe(e.split(`
`), t.split(`
`), n);
}
c(Fs, "diffStringsUnified");
function er(e, t, n, r) {
  let [o, s] = Ms(e, t, r);
  return n && hs(o), [o, s];
}
c(er, "diffStringsRaw");
function $t(e, t) {
  let { commonColor: n } = he(t);
  return n(e);
}
c($t, "getCommonMessage");
var { AsymmetricMatcher: js, DOMCollection: ks, DOMElement: Bs, Immutable: zs, ReactElement: Ys, ReactTestComponent: Us } = $e, tr = [
  Us,
  Ys,
  Bs,
  ks,
  zs,
  js,
  $e.Error
], wt = {
  maxDepth: 20,
  plugins: tr
}, nr = {
  callToJSON: !1,
  maxDepth: 8,
  plugins: tr
};
function Ws(e, t, n) {
  if (Object.is(e, t))
    return "";
  let r = Fn(e), o = r, s = !1;
  if (r === "object" && typeof e.asymmetricMatch == "function") {
    if (e.$$typeof !== Symbol.for("jest.asymmetricMatcher") || typeof e.getExpectedType != "function")
      return;
    o = e.getExpectedType(), s = o === "string";
  }
  if (o !== Fn(t)) {
    let h = function(O) {
      return O.length <= u ? O : `${O.slice(0, u)}...`;
    };
    c(h, "truncate");
    let { aAnnotation: i, aColor: a, aIndicator: l, bAnnotation: m, bColor: p, bIndicator: f } = he(n), b = At(nr, n), d = Q(e, b), g = Q(t,
    b), u = 1e5;
    d = h(d), g = h(g);
    let S = `${a(`${l} ${i}:`)} 
${d}`, _ = `${p(`${f} ${m}:`)} 
${g}`;
    return `${S}

${_}`;
  }
  if (!s)
    switch (r) {
      case "string":
        return Xe(e.split(`
`), t.split(`
`), n);
      case "boolean":
      case "number":
        return Vs(e, t, n);
      case "map":
        return Ct(kn(e), kn(t), n);
      case "set":
        return Ct(Bn(e), Bn(t), n);
      default:
        return Ct(e, t, n);
    }
}
c(Ws, "diff");
function Vs(e, t, n) {
  let r = Q(e, wt), o = Q(t, wt);
  return r === o ? "" : Xe(r.split(`
`), o.split(`
`), n);
}
c(Vs, "comparePrimitive");
function kn(e) {
  return new Map(Array.from(e.entries()).sort());
}
c(kn, "sortMap");
function Bn(e) {
  return new Set(Array.from(e.values()).sort());
}
c(Bn, "sortSet");
function Ct(e, t, n) {
  let r, o = !1;
  try {
    let i = At(wt, n);
    r = zn(e, t, i, n);
  } catch {
    o = !0;
  }
  let s = $t(Gn, n);
  if (r === void 0 || r === s) {
    let i = At(nr, n);
    r = zn(e, t, i, n), r !== s && !o && (r = `${$t(Ss, n)}

${r}`);
  }
  return r;
}
c(Ct, "compareObjects");
function At(e, t) {
  let { compareKeys: n, printBasicPrototype: r, maxDepth: o } = he(t);
  return {
    ...e,
    compareKeys: n,
    printBasicPrototype: r,
    maxDepth: o ?? e.maxDepth
  };
}
c(At, "getFormatOptions");
function zn(e, t, n, r) {
  let o = {
    ...n,
    indent: 0
  }, s = Q(e, o), i = Q(t, o);
  if (s === i)
    return $t(Gn, r);
  {
    let a = Q(e, n), l = Q(t, n);
    return Is(a.split(`
`), l.split(`
`), s.split(`
`), i.split(`
`), r);
  }
}
c(zn, "getObjectsDifference");
var Yn = 2e4;
function Un(e) {
  return Ke(e) === "Object" && typeof e.asymmetricMatch == "function";
}
c(Un, "isAsymmetricMatcher");
function Wn(e, t) {
  let n = Ke(e), r = Ke(t);
  return n === r && (n === "Object" || n === "Array");
}
c(Wn, "isReplaceable");
function rr(e, t, n) {
  let { aAnnotation: r, bAnnotation: o } = he(n);
  if (typeof t == "string" && typeof e == "string" && t.length > 0 && e.length > 0 && t.length <= Yn && e.length <= Yn && t !== e) {
    if (t.includes(`
`) || e.includes(`
`))
      return Fs(t, e, n);
    let [p] = er(t, e, !0), f = p.some((u) => u[0] === x), b = qs(r, o), d = b(r) + Hs(Vn(p, U, f)), g = b(o) + Gs(Vn(p, B, f));
    return `${d}
${g}`;
  }
  let s = _t(t, { forceWritable: !0 }), i = _t(e, { forceWritable: !0 }), { replacedExpected: a, replacedActual: l } = or(i, s);
  return Ws(a, l, n);
}
c(rr, "printDiffOrStringify");
function or(e, t, n = /* @__PURE__ */ new WeakSet(), r = /* @__PURE__ */ new WeakSet()) {
  return e instanceof Error && t instanceof Error && typeof e.cause < "u" && typeof t.cause > "u" ? (delete e.cause, {
    replacedActual: e,
    replacedExpected: t
  }) : Wn(e, t) ? n.has(e) || r.has(t) ? {
    replacedActual: e,
    replacedExpected: t
  } : (n.add(e), r.add(t), Et(t).forEach((o) => {
    let s = t[o], i = e[o];
    if (Un(s))
      s.asymmetricMatch(i) && (e[o] = s);
    else if (Un(i))
      i.asymmetricMatch(s) && (t[o] = i);
    else if (Wn(i, s)) {
      let a = or(i, s, n, r);
      e[o] = a.replacedActual, t[o] = a.replacedExpected;
    }
  }), {
    replacedActual: e,
    replacedExpected: t
  }) : {
    replacedActual: e,
    replacedExpected: t
  };
}
c(or, "replaceAsymmetricMatcher");
function qs(...e) {
  let t = e.reduce((n, r) => r.length > n ? r.length : n, 0);
  return (n) => `${n}: ${" ".repeat(t - n.length)}`;
}
c(qs, "getLabelPrinter");
var Ks = "\xB7";
function sr(e) {
  return e.replace(/\s+$/gm, (t) => Ks.repeat(t.length));
}
c(sr, "replaceTrailingSpaces");
function Gs(e) {
  return ne.red(sr(ge(e)));
}
c(Gs, "printReceived");
function Hs(e) {
  return ne.green(sr(ge(e)));
}
c(Hs, "printExpected");
function Vn(e, t, n) {
  return e.reduce((r, o) => r + (o[0] === x ? o[1] : o[0] === t ? n ? ne.inverse(o[1]) : o[1] : ""), "");
}
c(Vn, "getCommonAndChangedSubstrings");

// ../node_modules/@vitest/utils/dist/error.js
var Js = "@@__IMMUTABLE_RECORD__@@", Xs = "@@__IMMUTABLE_ITERABLE__@@";
function Zs(e) {
  return e && (e[Xs] || e[Js]);
}
c(Zs, "isImmutable");
var vs = Object.getPrototypeOf({});
function ir(e) {
  return e instanceof Error ? `<unserializable>: ${e.message}` : typeof e == "string" ? `<unserializable>: ${e}` : "<unserializable>";
}
c(ir, "getUnserializableMessage");
function _e(e, t = /* @__PURE__ */ new WeakMap()) {
  if (!e || typeof e == "string")
    return e;
  if (typeof e == "function")
    return `Function<${e.name || "anonymous"}>`;
  if (typeof e == "symbol")
    return e.toString();
  if (typeof e != "object")
    return e;
  if (Zs(e))
    return _e(e.toJSON(), t);
  if (e instanceof Promise || e.constructor && e.constructor.prototype === "AsyncFunction")
    return "Promise";
  if (typeof Element < "u" && e instanceof Element)
    return e.tagName;
  if (typeof e.asymmetricMatch == "function")
    return `${e.toString()} ${An(e.sample)}`;
  if (typeof e.toJSON == "function")
    return _e(e.toJSON(), t);
  if (t.has(e))
    return t.get(e);
  if (Array.isArray(e)) {
    let n = new Array(e.length);
    return t.set(e, n), e.forEach((r, o) => {
      try {
        n[o] = _e(r, t);
      } catch (s) {
        n[o] = ir(s);
      }
    }), n;
  } else {
    let n = /* @__PURE__ */ Object.create(null);
    t.set(e, n);
    let r = e;
    for (; r && r !== vs; )
      Object.getOwnPropertyNames(r).forEach((o) => {
        if (!(o in n))
          try {
            n[o] = _e(e[o], t);
          } catch (s) {
            delete n[o], n[o] = ir(s);
          }
      }), r = Object.getPrototypeOf(r);
    return n;
  }
}
c(_e, "serializeValue");
function Qs(e) {
  return e.replace(/__(vite_ssr_import|vi_import)_\d+__\./g, "");
}
c(Qs, "normalizeErrorMessage");
function Nt(e, t, n = /* @__PURE__ */ new WeakSet()) {
  if (!e || typeof e != "object")
    return { message: String(e) };
  let r = e;
  r.stack && (r.stackStr = String(r.stack)), r.name && (r.nameStr = String(r.name)), (r.showDiff || r.showDiff === void 0 && r.expected !== void 0 &&
  r.actual !== void 0) && (r.diff = rr(r.actual, r.expected, {
    ...t,
    ...r.diffOptions
  })), typeof r.expected != "string" && (r.expected = ge(r.expected, 10)), typeof r.actual != "string" && (r.actual = ge(r.actual, 10));
  try {
    typeof r.message == "string" && (r.message = Qs(r.message));
  } catch {
  }
  try {
    !n.has(r) && typeof r.cause == "object" && (n.add(r), r.cause = Nt(r.cause, t, n));
  } catch {
  }
  try {
    return _e(r);
  } catch (o) {
    return _e(new Error(`Failed to fully serialize error: ${o?.message}
Inner error message: ${r?.message}`));
  }
}
c(Nt, "processError");

// src/instrumenter/EVENTS.ts
var ee = {
  CALL: "storybook/instrumenter/call",
  SYNC: "storybook/instrumenter/sync",
  START: "storybook/instrumenter/start",
  BACK: "storybook/instrumenter/back",
  GOTO: "storybook/instrumenter/goto",
  NEXT: "storybook/instrumenter/next",
  END: "storybook/instrumenter/end"
};

// src/instrumenter/preview-api.ts
var Ze = globalThis.__STORYBOOK_ADDONS_PREVIEW;

// src/instrumenter/types.ts
var cr = /* @__PURE__ */ ((o) => (o.DONE = "done", o.ERROR = "error", o.ACTIVE = "active", o.WAITING = "waiting", o))(cr || {});

// src/instrumenter/instrumenter.ts
var ei = new Error(
  "This function ran after the play function completed. Did you forget to `await` it?"
), ur = /* @__PURE__ */ c((e) => Object.prototype.toString.call(e) === "[object Object]", "isObject"), ti = /* @__PURE__ */ c((e) => Object.
prototype.toString.call(e) === "[object Module]", "isModule"), ni = /* @__PURE__ */ c((e) => {
  if (!ur(e) && !ti(e))
    return !1;
  if (e.constructor === void 0)
    return !0;
  let t = e.constructor.prototype;
  return !!ur(t);
}, "isInstrumentable"), ri = /* @__PURE__ */ c((e) => {
  try {
    return new e.constructor();
  } catch {
    return {};
  }
}, "construct"), It = /* @__PURE__ */ c(() => ({
  renderPhase: void 0,
  isDebugging: !1,
  isPlaying: !1,
  isLocked: !1,
  cursor: 0,
  calls: [],
  shadowCalls: [],
  callRefsByResult: /* @__PURE__ */ new Map(),
  chainedCallIds: /* @__PURE__ */ new Set(),
  ancestors: [],
  playUntil: void 0,
  resolvers: {},
  syncTimeout: void 0
}), "getInitialState"), lr = /* @__PURE__ */ c((e, t = !1) => {
  let n = (t ? e.shadowCalls : e.calls).filter((o) => o.retain);
  if (!n.length)
    return;
  let r = new Map(
    Array.from(e.callRefsByResult.entries()).filter(([, o]) => o.retain)
  );
  return { cursor: n.length, calls: n, callRefsByResult: r };
}, "getRetainedState"), Mt = class {
  constructor() {
    this.detached = !1;
    this.initialized = !1;
    // State is tracked per story to deal with multiple stories on the same canvas (i.e. docs mode)
    this.state = {};
    this.loadParentWindowState = /* @__PURE__ */ c(() => {
      try {
        this.state = H.global.window?.parent?.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER_STATE__ || {};
      } catch {
        this.detached = !0;
      }
    }, "loadParentWindowState");
    this.updateParentWindowState = /* @__PURE__ */ c(() => {
      try {
        H.global.window.parent.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER_STATE__ = this.state;
      } catch {
        this.detached = !0;
      }
    }, "updateParentWindowState");
    this.loadParentWindowState();
    let t = /* @__PURE__ */ c(({
      storyId: l,
      isPlaying: m = !0,
      isDebugging: p = !1
    }) => {
      let f = this.getState(l);
      this.setState(l, {
        ...It(),
        ...lr(f, p),
        shadowCalls: p ? f.shadowCalls : [],
        chainedCallIds: p ? f.chainedCallIds : /* @__PURE__ */ new Set(),
        playUntil: p ? f.playUntil : void 0,
        isPlaying: m,
        isDebugging: p
      }), this.sync(l);
    }, "resetState"), n = /* @__PURE__ */ c((l) => ({ storyId: m, playUntil: p }) => {
      this.getState(m).isDebugging || this.setState(m, ({ calls: b }) => ({
        calls: [],
        shadowCalls: b.map((d) => ({ ...d, status: "waiting" })),
        isDebugging: !0
      }));
      let f = this.getLog(m);
      this.setState(m, ({ shadowCalls: b }) => {
        if (p || !f.length)
          return { playUntil: p };
        let d = b.findIndex((g) => g.id === f[0].callId);
        return {
          playUntil: b.slice(0, d).filter((g) => g.interceptable && !g.ancestors?.length).slice(-1)[0]?.id
        };
      }), l.emit(de.FORCE_REMOUNT, { storyId: m, isDebugging: !0 });
    }, "start"), r = /* @__PURE__ */ c((l) => ({ storyId: m }) => {
      let p = this.getLog(m).filter((b) => !b.ancestors?.length), f = p.reduceRight((b, d, g) => b >= 0 || d.status === "waiting" ? b : g, -1);
      n(l)({ storyId: m, playUntil: p[f - 1]?.callId });
    }, "back"), o = /* @__PURE__ */ c((l) => ({ storyId: m, callId: p }) => {
      let { calls: f, shadowCalls: b, resolvers: d } = this.getState(m), g = f.find(({ id: h }) => h === p), u = b.find(({ id: h }) => h ===
      p);
      if (!g && u && Object.values(d).length > 0) {
        let h = this.getLog(m).find((S) => S.status === "waiting")?.callId;
        u.id !== h && this.setState(m, { playUntil: u.id }), Object.values(d).forEach((S) => S());
      } else
        n(l)({ storyId: m, playUntil: p });
    }, "goto"), s = /* @__PURE__ */ c((l) => ({ storyId: m }) => {
      let { resolvers: p } = this.getState(m);
      if (Object.values(p).length > 0)
        Object.values(p).forEach((f) => f());
      else {
        let f = this.getLog(m).find((b) => b.status === "waiting")?.callId;
        f ? n(l)({ storyId: m, playUntil: f }) : i({ storyId: m });
      }
    }, "next"), i = /* @__PURE__ */ c(({ storyId: l }) => {
      this.setState(l, { playUntil: void 0, isDebugging: !1 }), Object.values(this.getState(l).resolvers).forEach((m) => m());
    }, "end"), a = /* @__PURE__ */ c(({ storyId: l, newPhase: m }) => {
      let { isDebugging: p } = this.getState(l);
      this.setState(l, { renderPhase: m }), m === "preparing" && p && t({ storyId: l }), m === "playing" && t({ storyId: l, isDebugging: p }),
      m === "played" && this.setState(l, {
        isLocked: !1,
        isPlaying: !1,
        isDebugging: !1
      }), m === "errored" && this.setState(l, {
        isLocked: !1,
        isPlaying: !1
      });
    }, "renderPhaseChanged");
    Ze && Ze.ready().then(() => {
      this.channel = Ze.getChannel(), this.channel.on(de.FORCE_REMOUNT, t), this.channel.on(de.STORY_RENDER_PHASE_CHANGED, a), this.channel.
      on(de.SET_CURRENT_STORY, () => {
        this.initialized ? this.cleanup() : this.initialized = !0;
      }), this.channel.on(ee.START, n(this.channel)), this.channel.on(ee.BACK, r(this.channel)), this.channel.on(ee.GOTO, o(this.channel)), this.
      channel.on(ee.NEXT, s(this.channel)), this.channel.on(ee.END, i);
    });
  }
  static {
    c(this, "Instrumenter");
  }
  getState(t) {
    return this.state[t] || It();
  }
  setState(t, n) {
    let r = this.getState(t), o = typeof n == "function" ? n(r) : n;
    this.state = { ...this.state, [t]: { ...r, ...o } }, this.updateParentWindowState();
  }
  cleanup() {
    this.state = Object.entries(this.state).reduce(
      (r, [o, s]) => {
        let i = lr(s);
        return i && (r[o] = Object.assign(It(), i)), r;
      },
      {}
    );
    let n = { controlStates: {
      detached: this.detached,
      start: !1,
      back: !1,
      goto: !1,
      next: !1,
      end: !1
    }, logItems: [] };
    this.channel?.emit(ee.SYNC, n), this.updateParentWindowState();
  }
  getLog(t) {
    let { calls: n, shadowCalls: r } = this.getState(t), o = [...r];
    n.forEach((i, a) => {
      o[a] = i;
    });
    let s = /* @__PURE__ */ new Set();
    return o.reduceRight((i, a) => (a.args.forEach((l) => {
      l?.__callId__ && s.add(l.__callId__);
    }), a.path.forEach((l) => {
      l.__callId__ && s.add(l.__callId__);
    }), (a.interceptable || a.exception) && !s.has(a.id) && (i.unshift({ callId: a.id, status: a.status, ancestors: a.ancestors }), s.add(a.
    id)), i), []);
  }
  // Traverses the object structure to recursively patch all function properties.
  // Returns the original object, or a new object with the same constructor,
  // depending on whether it should mutate.
  instrument(t, n, r = 0) {
    if (!ni(t))
      return t;
    let { mutate: o = !1, path: s = [] } = n, i = n.getKeys ? n.getKeys(t, r) : Object.keys(t);
    return r += 1, i.reduce(
      (a, l) => {
        let m = oi(t, l);
        if (typeof m?.get == "function") {
          if (m.configurable) {
            let f = /* @__PURE__ */ c(() => m?.get?.bind(t)?.(), "getter");
            Object.defineProperty(a, l, {
              get: /* @__PURE__ */ c(() => this.instrument(f(), { ...n, path: s.concat(l) }, r), "get")
            });
          }
          return a;
        }
        let p = t[l];
        return typeof p != "function" ? (a[l] = this.instrument(p, { ...n, path: s.concat(l) }, r), a) : "__originalFn__" in p && typeof p.__originalFn__ ==
        "function" ? (a[l] = p, a) : (a[l] = (...f) => this.track(l, p, t, f, n), a[l].__originalFn__ = p, Object.defineProperty(a[l], "name",
        { value: l, writable: !1 }), Object.keys(p).length > 0 && Object.assign(
          a[l],
          this.instrument({ ...p }, { ...n, path: s.concat(l) }, r)
        ), a);
      },
      o ? t : ri(t)
    );
  }
  // Monkey patch an object method to record calls.
  // Returns a function that invokes the original function, records the invocation ("call") and
  // returns the original result.
  track(t, n, r, o, s) {
    let i = o?.[0]?.__storyId__ || H.global.__STORYBOOK_PREVIEW__?.selectionStore?.selection?.storyId, { cursor: a, ancestors: l } = this.getState(
    i);
    this.setState(i, { cursor: a + 1 });
    let m = `${l.slice(-1)[0] || i} [${a}] ${t}`, { path: p = [], intercept: f = !1, retain: b = !1 } = s, d = typeof f == "function" ? f(t,
    p) : f, g = { id: m, cursor: a, storyId: i, ancestors: l, path: p, method: t, args: o, interceptable: d, retain: b }, h = (d && !l.length ?
    this.intercept : this.invoke).call(this, n, r, g, s);
    return this.instrument(h, { ...s, mutate: !0, path: [{ __callId__: g.id }] });
  }
  intercept(t, n, r, o) {
    let { chainedCallIds: s, isDebugging: i, playUntil: a } = this.getState(r.storyId), l = s.has(r.id);
    return !i || l || a ? (a === r.id && this.setState(r.storyId, { playUntil: void 0 }), this.invoke(t, n, r, o)) : new Promise((m) => {
      this.setState(r.storyId, ({ resolvers: p }) => ({
        isLocked: !1,
        resolvers: { ...p, [r.id]: m }
      }));
    }).then(() => (this.setState(r.storyId, (m) => {
      let { [r.id]: p, ...f } = m.resolvers;
      return { isLocked: !0, resolvers: f };
    }), this.invoke(t, n, r, o)));
  }
  invoke(t, n, r, o) {
    let { callRefsByResult: s, renderPhase: i } = this.getState(r.storyId), a = 25, l = /* @__PURE__ */ c((f, b, d) => {
      if (d.includes(f))
        return "[Circular]";
      if (d = [...d, f], b > a)
        return "...";
      if (s.has(f))
        return s.get(f);
      if (f instanceof Array)
        return f.map((g) => l(g, ++b, d));
      if (f instanceof Date)
        return { __date__: { value: f.toISOString() } };
      if (f instanceof Error) {
        let { name: g, message: u, stack: h } = f;
        return { __error__: { name: g, message: u, stack: h } };
      }
      if (f instanceof RegExp) {
        let { flags: g, source: u } = f;
        return { __regexp__: { flags: g, source: u } };
      }
      if (f instanceof H.global.window?.HTMLElement) {
        let { prefix: g, localName: u, id: h, classList: S, innerText: _ } = f, O = Array.from(S);
        return { __element__: { prefix: g, localName: u, id: h, classNames: O, innerText: _ } };
      }
      return typeof f == "function" ? {
        __function__: { name: "getMockName" in f ? f.getMockName() : f.name }
      } : typeof f == "symbol" ? { __symbol__: { description: f.description } } : typeof f == "object" && f?.constructor?.name && f?.constructor?.
      name !== "Object" ? { __class__: { name: f.constructor.name } } : Object.prototype.toString.call(f) === "[object Object]" ? Object.fromEntries(
        Object.entries(f).map(([g, u]) => [g, l(u, ++b, d)])
      ) : f;
    }, "serializeValues"), m = {
      ...r,
      args: r.args.map((f) => l(f, 0, []))
    };
    r.path.forEach((f) => {
      f?.__callId__ && this.setState(r.storyId, ({ chainedCallIds: b }) => ({
        chainedCallIds: new Set(Array.from(b).concat(f.__callId__))
      }));
    });
    let p = /* @__PURE__ */ c((f) => {
      if (f instanceof Error) {
        let { name: b, message: d, stack: g, callId: u = r.id } = f, {
          showDiff: h = void 0,
          diff: S = void 0,
          actual: _ = void 0,
          expected: O = void 0
        } = f.name === "AssertionError" ? Nt(f) : f, y = { name: b, message: d, stack: g, callId: u, showDiff: h, diff: S, actual: _, expected: O };
        if (this.update({ ...m, status: "error", exception: y }), this.setState(r.storyId, (E) => ({
          callRefsByResult: new Map([
            ...Array.from(E.callRefsByResult.entries()),
            [f, { __callId__: r.id, retain: r.retain }]
          ])
        })), r.ancestors?.length)
          throw Object.prototype.hasOwnProperty.call(f, "callId") || Object.defineProperty(f, "callId", { value: r.id }), f;
      }
      throw f;
    }, "handleException");
    try {
      if (i === "played" && !r.retain)
        throw ei;
      let b = (o.getArgs ? o.getArgs(r, this.getState(r.storyId)) : r.args).map((g) => typeof g != "function" || si(g) || Object.keys(g).length ?
      g : (...u) => {
        let { cursor: h, ancestors: S } = this.getState(r.storyId);
        this.setState(r.storyId, { cursor: 0, ancestors: [...S, r.id] });
        let _ = /* @__PURE__ */ c(() => this.setState(r.storyId, { cursor: h, ancestors: S }), "restore"), O = !1;
        try {
          let y = g(...u);
          return y instanceof Promise ? (O = !0, y.finally(_)) : y;
        } finally {
          O || _();
        }
      }), d = t.apply(n, b);
      return d && ["object", "function", "symbol"].includes(typeof d) && this.setState(r.storyId, (g) => ({
        callRefsByResult: new Map([
          ...Array.from(g.callRefsByResult.entries()),
          [d, { __callId__: r.id, retain: r.retain }]
        ])
      })), this.update({
        ...m,
        status: d instanceof Promise ? "active" : "done"
      }), d instanceof Promise ? d.then((g) => (this.update({ ...m, status: "done" }), g), p) : d;
    } catch (f) {
      return p(f);
    }
  }
  // Sends the call info to the manager and synchronizes the log.
  update(t) {
    this.channel?.emit(ee.CALL, t), this.setState(t.storyId, ({ calls: n }) => {
      let r = n.concat(t).reduce((o, s) => Object.assign(o, { [s.id]: s }), {});
      return {
        // Calls are sorted to ensure parent calls always come before calls in their callback.
        calls: Object.values(r).sort(
          (o, s) => o.id.localeCompare(s.id, void 0, { numeric: !0 })
        )
      };
    }), this.sync(t.storyId);
  }
  // Builds a log of interceptable calls and control states and sends it to the manager.
  // Uses a 0ms debounce because this might get called many times in one tick.
  sync(t) {
    let n = /* @__PURE__ */ c(() => {
      let { isLocked: r, isPlaying: o } = this.getState(t), s = this.getLog(t), i = s.filter(({ ancestors: f }) => !f.length).find((f) => f.
      status === "waiting")?.callId, a = s.some((f) => f.status === "active");
      if (this.detached || r || a || s.length === 0) {
        let b = { controlStates: {
          detached: this.detached,
          start: !1,
          back: !1,
          goto: !1,
          next: !1,
          end: !1
        }, logItems: s };
        this.channel?.emit(ee.SYNC, b);
        return;
      }
      let l = s.some(
        (f) => f.status === "done" || f.status === "error"
      ), p = { controlStates: {
        detached: this.detached,
        start: l,
        back: l,
        goto: !0,
        next: o,
        end: o
      }, logItems: s, pausedAt: i };
      this.channel?.emit(ee.SYNC, p);
    }, "synchronize");
    this.setState(t, ({ syncTimeout: r }) => (clearTimeout(r), { syncTimeout: setTimeout(n, 0) }));
  }
};
function fr(e, t = {}) {
  try {
    let n = !1, r = !1;
    return H.global.window?.location?.search?.includes("instrument=true") ? n = !0 : H.global.window?.location?.search?.includes("instrument\
=false") && (r = !0), H.global.window?.parent === H.global.window && !n || r ? e : (H.global.window && !H.global.window.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__ &&
    (H.global.window.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__ = new Mt()), (H.global.window?.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__).
    instrument(e, t));
  } catch (n) {
    return ar.once.warn(n), e;
  }
}
c(fr, "instrument");
function oi(e, t) {
  let n = e;
  for (; n != null; ) {
    let r = Object.getOwnPropertyDescriptor(n, t);
    if (r)
      return r;
    n = Object.getPrototypeOf(n);
  }
}
c(oi, "getPropertyDescriptor");
function si(e) {
  if (typeof e != "function")
    return !1;
  let t = Object.getOwnPropertyDescriptor(e, "prototype");
  return t ? !t.writable : !1;
}
c(si, "isClass");
