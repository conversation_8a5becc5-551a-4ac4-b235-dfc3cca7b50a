var ar = Object.defineProperty;
var i = (e, t) => ar(e, "name", { value: t, configurable: !0 });
var fr = (e, t) => () => (t || e((t = { exports: {} }).exports, t), t.exports);

// (disabled):../node_modules/util/util.js
var _n = fr(() => {
});

// src/instrumenter/instrumenter.ts
import { once as qs } from "storybook/internal/client-logger";
import {
  FORCE_REMOUNT as ir,
  SET_CURRENT_STORY as Ks,
  STORY_RENDER_PHASE_CHANGED as Gs
} from "storybook/internal/core-events";
import { global as Z } from "@storybook/global";

// ../node_modules/tinyrainbow/dist/chunk-BVHSVHOK.js
var mr = {
  reset: [0, 0],
  bold: [1, 22, "\x1B[22m\x1B[1m"],
  dim: [2, 22, "\x1B[22m\x1B[2m"],
  italic: [3, 23],
  underline: [4, 24],
  inverse: [7, 27],
  hidden: [8, 28],
  strikethrough: [9, 29],
  black: [30, 39],
  red: [31, 39],
  green: [32, 39],
  yellow: [33, 39],
  blue: [34, 39],
  magenta: [35, 39],
  cyan: [36, 39],
  white: [37, 39],
  gray: [90, 39],
  bgBlack: [40, 49],
  bgRed: [41, 49],
  bgGreen: [42, 49],
  bgYellow: [43, 49],
  bgBlue: [44, 49],
  bgMagenta: [45, 49],
  bgCyan: [46, 49],
  bgWhite: [47, 49],
  blackBright: [90, 39],
  redBright: [91, 39],
  greenBright: [92, 39],
  yellowBright: [93, 39],
  blueBright: [94, 39],
  magentaBright: [95, 39],
  cyanBright: [96, 39],
  whiteBright: [97, 39],
  bgBlackBright: [100, 49],
  bgRedBright: [101, 49],
  bgGreenBright: [102, 49],
  bgYellowBright: [103, 49],
  bgBlueBright: [104, 49],
  bgMagentaBright: [105, 49],
  bgCyanBright: [106, 49],
  bgWhiteBright: [107, 49]
}, pr = Object.entries(mr);
function Ge(e) {
  return String(e);
}
i(Ge, "a");
Ge.open = "";
Ge.close = "";
function Dt(e = !1) {
  let t = typeof process < "u" ? process : void 0, n = t?.env || {}, r = t?.argv || [];
  return !("NO_COLOR" in n || r.includes("--no-color")) && ("FORCE_COLOR" in n || r.includes("--color") || t?.platform === "win32" || e && n.
  TERM !== "dumb" || "CI" in n) || typeof window < "u" && !!window.chrome;
}
i(Dt, "C");
function Ft(e = !1) {
  let t = Dt(e), n = /* @__PURE__ */ i((c, l, u, m) => {
    let p = "", a = 0;
    do
      p += c.substring(a, m) + u, a = m + l.length, m = c.indexOf(l, a);
    while (~m);
    return p + c.substring(a);
  }, "i"), r = /* @__PURE__ */ i((c, l, u = c) => {
    let m = /* @__PURE__ */ i((p) => {
      let a = String(p), S = a.indexOf(l, c.length);
      return ~S ? c + n(a, l, u, S) + l : c + a + l;
    }, "o");
    return m.open = c, m.close = l, m;
  }, "g"), o = {
    isColorSupported: t
  }, s = /* @__PURE__ */ i((c) => `\x1B[${c}m`, "d");
  for (let [c, l] of pr)
    o[c] = t ? r(
      s(l[0]),
      s(l[1]),
      l[2]
    ) : Ge;
  return o;
}
i(Ft, "p");

// ../node_modules/tinyrainbow/dist/browser.js
var v = Ft();

// ../node_modules/@vitest/pretty-format/dist/index.js
function Jt(e, t) {
  return t.forEach(function(n) {
    n && typeof n != "string" && !Array.isArray(n) && Object.keys(n).forEach(function(r) {
      if (r !== "default" && !(r in e)) {
        var o = Object.getOwnPropertyDescriptor(n, r);
        Object.defineProperty(e, r, o.get ? o : {
          enumerable: !0,
          get: /* @__PURE__ */ i(function() {
            return n[r];
          }, "get")
        });
      }
    });
  }), Object.freeze(e);
}
i(Jt, "_mergeNamespaces");
function gr(e, t) {
  let n = Object.keys(e), r = t === null ? n : n.sort(t);
  if (Object.getOwnPropertySymbols)
    for (let o of Object.getOwnPropertySymbols(e))
      Object.getOwnPropertyDescriptor(e, o).enumerable && r.push(o);
  return r;
}
i(gr, "getKeysOfEnumerableProperties");
function _e(e, t, n, r, o, s, c = ": ") {
  let l = "", u = 0, m = e.next();
  if (!m.done) {
    l += t.spacingOuter;
    let p = n + t.indent;
    for (; !m.done; ) {
      if (l += p, u++ === t.maxWidth) {
        l += "\u2026";
        break;
      }
      let a = s(m.value[0], t, p, r, o), S = s(m.value[1], t, p, r, o);
      l += a + c + S, m = e.next(), m.done ? t.min || (l += ",") : l += `,${t.spacingInner}`;
    }
    l += t.spacingOuter + n;
  }
  return l;
}
i(_e, "printIteratorEntries");
function Qe(e, t, n, r, o, s) {
  let c = "", l = 0, u = e.next();
  if (!u.done) {
    c += t.spacingOuter;
    let m = n + t.indent;
    for (; !u.done; ) {
      if (c += m, l++ === t.maxWidth) {
        c += "\u2026";
        break;
      }
      c += s(u.value, t, m, r, o), u = e.next(), u.done ? t.min || (c += ",") : c += `,${t.spacingInner}`;
    }
    c += t.spacingOuter + n;
  }
  return c;
}
i(Qe, "printIteratorValues");
function Pe(e, t, n, r, o, s) {
  let c = "";
  e = e instanceof ArrayBuffer ? new DataView(e) : e;
  let l = /* @__PURE__ */ i((m) => m instanceof DataView, "isDataView"), u = l(e) ? e.byteLength : e.length;
  if (u > 0) {
    c += t.spacingOuter;
    let m = n + t.indent;
    for (let p = 0; p < u; p++) {
      if (c += m, p === t.maxWidth) {
        c += "\u2026";
        break;
      }
      (l(e) || p in e) && (c += s(l(e) ? e.getInt8(p) : e[p], t, m, r, o)), p < u - 1 ? c += `,${t.spacingInner}` : t.min || (c += ",");
    }
    c += t.spacingOuter + n;
  }
  return c;
}
i(Pe, "printListItems");
function ve(e, t, n, r, o, s) {
  let c = "", l = gr(e, t.compareKeys);
  if (l.length > 0) {
    c += t.spacingOuter;
    let u = n + t.indent;
    for (let m = 0; m < l.length; m++) {
      let p = l[m], a = s(p, t, u, r, o), S = s(e[p], t, u, r, o);
      c += `${u + a}: ${S}`, m < l.length - 1 ? c += `,${t.spacingInner}` : t.min || (c += ",");
    }
    c += t.spacingOuter + n;
  }
  return c;
}
i(ve, "printObjectProperties");
var hr = typeof Symbol == "function" && Symbol.for ? Symbol.for("jest.asymmetricMatcher") : 1267621, Ae = " ", dr = /* @__PURE__ */ i((e, t, n, r, o, s) => {
  let c = e.toString();
  if (c === "ArrayContaining" || c === "ArrayNotContaining")
    return ++r > t.maxDepth ? `[${c}]` : `${c + Ae}[${Pe(e.sample, t, n, r, o, s)}]`;
  if (c === "ObjectContaining" || c === "ObjectNotContaining")
    return ++r > t.maxDepth ? `[${c}]` : `${c + Ae}{${ve(e.sample, t, n, r, o, s)}}`;
  if (c === "StringMatching" || c === "StringNotMatching" || c === "StringContaining" || c === "StringNotContaining")
    return c + Ae + s(e.sample, t, n, r, o);
  if (typeof e.toAsymmetricMatcher != "function")
    throw new TypeError(`Asymmetric matcher ${e.constructor.name} does not implement toAsymmetricMatcher()`);
  return e.toAsymmetricMatcher();
}, "serialize$5"), yr = /* @__PURE__ */ i((e) => e && e.$$typeof === hr, "test$5"), br = {
  serialize: dr,
  test: yr
}, Sr = " ", Xt = /* @__PURE__ */ new Set(["DOMStringMap", "NamedNodeMap"]), Er = /^(?:HTML\w*Collection|NodeList)$/;
function _r(e) {
  return Xt.has(e) || Er.test(e);
}
i(_r, "testName");
var Tr = /* @__PURE__ */ i((e) => e && e.constructor && !!e.constructor.name && _r(e.constructor.name), "test$4");
function Cr(e) {
  return e.constructor.name === "NamedNodeMap";
}
i(Cr, "isNamedNodeMap");
var Or = /* @__PURE__ */ i((e, t, n, r, o, s) => {
  let c = e.constructor.name;
  return ++r > t.maxDepth ? `[${c}]` : (t.min ? "" : c + Sr) + (Xt.has(c) ? `{${ve(Cr(e) ? [...e].reduce((l, u) => (l[u.name] = u.value, l),
  {}) : { ...e }, t, n, r, o, s)}}` : `[${Pe([...e], t, n, r, o, s)}]`);
}, "serialize$4"), $r = {
  serialize: Or,
  test: Tr
};
function Zt(e) {
  return e.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
}
i(Zt, "escapeHTML");
function et(e, t, n, r, o, s, c) {
  let l = r + n.indent, u = n.colors;
  return e.map((m) => {
    let p = t[m], a = c(p, n, l, o, s);
    return typeof p != "string" && (a.includes(`
`) && (a = n.spacingOuter + l + a + n.spacingOuter + r), a = `{${a}}`), `${n.spacingInner + r + u.prop.open + m + u.prop.close}=${u.value.open}${a}${u.
    value.close}`;
  }).join("");
}
i(et, "printProps");
function tt(e, t, n, r, o, s) {
  return e.map((c) => t.spacingOuter + n + (typeof c == "string" ? Qt(c, t) : s(c, t, n, r, o))).join("");
}
i(tt, "printChildren");
function Qt(e, t) {
  let n = t.colors.content;
  return n.open + Zt(e) + n.close;
}
i(Qt, "printText");
function wr(e, t) {
  let n = t.colors.comment;
  return `${n.open}<!--${Zt(e)}-->${n.close}`;
}
i(wr, "printComment");
function nt(e, t, n, r, o) {
  let s = r.colors.tag;
  return `${s.open}<${e}${t && s.close + t + r.spacingOuter + o + s.open}${n ? `>${s.close}${n}${r.spacingOuter}${o}${s.open}</${e}` : `${t &&
  !r.min ? "" : " "}/`}>${s.close}`;
}
i(nt, "printElement");
function rt(e, t) {
  let n = t.colors.tag;
  return `${n.open}<${e}${n.close} \u2026${n.open} />${n.close}`;
}
i(rt, "printElementAsLeaf");
var Ar = 1, vt = 3, en = 8, tn = 11, Rr = /^(?:(?:HTML|SVG)\w*)?Element$/;
function Pr(e) {
  try {
    return typeof e.hasAttribute == "function" && e.hasAttribute("is");
  } catch {
    return !1;
  }
}
i(Pr, "testHasAttribute");
function Nr(e) {
  let t = e.constructor.name, { nodeType: n, tagName: r } = e, o = typeof r == "string" && r.includes("-") || Pr(e);
  return n === Ar && (Rr.test(t) || o) || n === vt && t === "Text" || n === en && t === "Comment" || n === tn && t === "DocumentFragment";
}
i(Nr, "testNode");
var Ir = /* @__PURE__ */ i((e) => {
  var t;
  return (e == null || (t = e.constructor) === null || t === void 0 ? void 0 : t.name) && Nr(e);
}, "test$3");
function Mr(e) {
  return e.nodeType === vt;
}
i(Mr, "nodeIsText");
function Lr(e) {
  return e.nodeType === en;
}
i(Lr, "nodeIsComment");
function He(e) {
  return e.nodeType === tn;
}
i(He, "nodeIsFragment");
var xr = /* @__PURE__ */ i((e, t, n, r, o, s) => {
  if (Mr(e))
    return Qt(e.data, t);
  if (Lr(e))
    return wr(e.data, t);
  let c = He(e) ? "DocumentFragment" : e.tagName.toLowerCase();
  return ++r > t.maxDepth ? rt(c, t) : nt(c, et(He(e) ? [] : Array.from(e.attributes, (l) => l.name).sort(), He(e) ? {} : [...e.attributes].
  reduce((l, u) => (l[u.name] = u.value, l), {}), t, n + t.indent, r, o, s), tt(Array.prototype.slice.call(e.childNodes || e.children), t, n +
  t.indent, r, o, s), t, n);
}, "serialize$3"), Dr = {
  serialize: xr,
  test: Ir
}, Fr = "@@__IMMUTABLE_ITERABLE__@@", jr = "@@__IMMUTABLE_LIST__@@", kr = "@@__IMMUTABLE_KEYED__@@", Br = "@@__IMMUTABLE_MAP__@@", jt = "@@_\
_IMMUTABLE_ORDERED__@@", zr = "@@__IMMUTABLE_RECORD__@@", Yr = "@@__IMMUTABLE_SEQ__@@", Ur = "@@__IMMUTABLE_SET__@@", Wr = "@@__IMMUTABLE_ST\
ACK__@@", he = /* @__PURE__ */ i((e) => `Immutable.${e}`, "getImmutableName"), Ie = /* @__PURE__ */ i((e) => `[${e}]`, "printAsLeaf"), Ee = "\
 ", kt = "\u2026";
function Vr(e, t, n, r, o, s, c) {
  return ++r > t.maxDepth ? Ie(he(c)) : `${he(c) + Ee}{${_e(e.entries(), t, n, r, o, s)}}`;
}
i(Vr, "printImmutableEntries");
function qr(e) {
  let t = 0;
  return { next() {
    if (t < e._keys.length) {
      let n = e._keys[t++];
      return {
        done: !1,
        value: [n, e.get(n)]
      };
    }
    return {
      done: !0,
      value: void 0
    };
  } };
}
i(qr, "getRecordEntries");
function Kr(e, t, n, r, o, s) {
  let c = he(e._name || "Record");
  return ++r > t.maxDepth ? Ie(c) : `${c + Ee}{${_e(qr(e), t, n, r, o, s)}}`;
}
i(Kr, "printImmutableRecord");
function Gr(e, t, n, r, o, s) {
  let c = he("Seq");
  return ++r > t.maxDepth ? Ie(c) : e[kr] ? `${c + Ee}{${e._iter || e._object ? _e(e.entries(), t, n, r, o, s) : kt}}` : `${c + Ee}[${e._iter ||
  e._array || e._collection || e._iterable ? Qe(e.values(), t, n, r, o, s) : kt}]`;
}
i(Gr, "printImmutableSeq");
function Je(e, t, n, r, o, s, c) {
  return ++r > t.maxDepth ? Ie(he(c)) : `${he(c) + Ee}[${Qe(e.values(), t, n, r, o, s)}]`;
}
i(Je, "printImmutableValues");
var Hr = /* @__PURE__ */ i((e, t, n, r, o, s) => e[Br] ? Vr(e, t, n, r, o, s, e[jt] ? "OrderedMap" : "Map") : e[jr] ? Je(e, t, n, r, o, s, "\
List") : e[Ur] ? Je(e, t, n, r, o, s, e[jt] ? "OrderedSet" : "Set") : e[Wr] ? Je(e, t, n, r, o, s, "Stack") : e[Yr] ? Gr(e, t, n, r, o, s) :
Kr(e, t, n, r, o, s), "serialize$2"), Jr = /* @__PURE__ */ i((e) => e && (e[Fr] === !0 || e[zr] === !0), "test$2"), Xr = {
  serialize: Hr,
  test: Jr
};
function nn(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
i(nn, "getDefaultExportFromCjs");
var Xe = { exports: {} };
var R = {};
var Bt;
function Zr() {
  return Bt || (Bt = 1, function() {
    function e(f) {
      if (typeof f == "object" && f !== null) {
        var d = f.$$typeof;
        switch (d) {
          case t:
            switch (f = f.type, f) {
              case r:
              case s:
              case o:
              case m:
              case p:
              case g:
                return f;
              default:
                switch (f = f && f.$$typeof, f) {
                  case l:
                  case u:
                  case S:
                  case a:
                    return f;
                  case c:
                    return f;
                  default:
                    return d;
                }
            }
          case n:
            return d;
        }
      }
    }
    i(e, "typeOf");
    var t = Symbol.for("react.transitional.element"), n = Symbol.for("react.portal"), r = Symbol.for("react.fragment"), o = Symbol.for("reac\
t.strict_mode"), s = Symbol.for("react.profiler"), c = Symbol.for("react.consumer"), l = Symbol.for("react.context"), u = Symbol.for("react.\
forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), a = Symbol.for("react.memo"), S = Symbol.for("react.\
lazy"), g = Symbol.for("react.view_transition"), h = Symbol.for("react.client.reference");
    R.ContextConsumer = c, R.ContextProvider = l, R.Element = t, R.ForwardRef = u, R.Fragment = r, R.Lazy = S, R.Memo = a, R.Portal = n, R.Profiler =
    s, R.StrictMode = o, R.Suspense = m, R.SuspenseList = p, R.isContextConsumer = function(f) {
      return e(f) === c;
    }, R.isContextProvider = function(f) {
      return e(f) === l;
    }, R.isElement = function(f) {
      return typeof f == "object" && f !== null && f.$$typeof === t;
    }, R.isForwardRef = function(f) {
      return e(f) === u;
    }, R.isFragment = function(f) {
      return e(f) === r;
    }, R.isLazy = function(f) {
      return e(f) === S;
    }, R.isMemo = function(f) {
      return e(f) === a;
    }, R.isPortal = function(f) {
      return e(f) === n;
    }, R.isProfiler = function(f) {
      return e(f) === s;
    }, R.isStrictMode = function(f) {
      return e(f) === o;
    }, R.isSuspense = function(f) {
      return e(f) === m;
    }, R.isSuspenseList = function(f) {
      return e(f) === p;
    }, R.isValidElementType = function(f) {
      return typeof f == "string" || typeof f == "function" || f === r || f === s || f === o || f === m || f === p || typeof f == "object" &&
      f !== null && (f.$$typeof === S || f.$$typeof === a || f.$$typeof === l || f.$$typeof === c || f.$$typeof === u || f.$$typeof === h ||
      f.getModuleId !== void 0);
    }, R.typeOf = e;
  }()), R;
}
i(Zr, "requireReactIs_development$1");
var zt;
function Qr() {
  return zt || (zt = 1, Xe.exports = Zr()), Xe.exports;
}
i(Qr, "requireReactIs$1");
var rn = Qr(), vr = /* @__PURE__ */ nn(rn), eo = /* @__PURE__ */ Jt({
  __proto__: null,
  default: vr
}, [rn]), Ze = { exports: {} };
var w = {};
var Yt;
function to() {
  return Yt || (Yt = 1, function() {
    var e = Symbol.for("react.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), r = Symbol.for("react.strict_mode"),
    o = Symbol.for("react.profiler"), s = Symbol.for("react.provider"), c = Symbol.for("react.context"), l = Symbol.for("react.server_contex\
t"), u = Symbol.for("react.forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), a = Symbol.for("react.mem\
o"), S = Symbol.for("react.lazy"), g = Symbol.for("react.offscreen"), h = !1, f = !1, d = !1, b = !1, _ = !1, O;
    O = Symbol.for("react.module.reference");
    function y(C) {
      return !!(typeof C == "string" || typeof C == "function" || C === n || C === o || _ || C === r || C === m || C === p || b || C === g ||
      h || f || d || typeof C == "object" && C !== null && (C.$$typeof === S || C.$$typeof === a || C.$$typeof === s || C.$$typeof === c || C.
      $$typeof === u || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      C.$$typeof === O || C.getModuleId !== void 0));
    }
    i(y, "isValidElementType");
    function E(C) {
      if (typeof C == "object" && C !== null) {
        var Ke = C.$$typeof;
        switch (Ke) {
          case e:
            var we = C.type;
            switch (we) {
              case n:
              case o:
              case r:
              case m:
              case p:
                return we;
              default:
                var xt = we && we.$$typeof;
                switch (xt) {
                  case l:
                  case c:
                  case u:
                  case S:
                  case a:
                  case s:
                    return xt;
                  default:
                    return Ke;
                }
            }
          case t:
            return Ke;
        }
      }
    }
    i(E, "typeOf");
    var $ = c, T = s, A = e, K = u, Q = n, I = S, k = a, G = t, Y = o, N = r, L = m, x = p, H = !1, F = !1;
    function W(C) {
      return H || (H = !0, console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")), !1;
    }
    i(W, "isAsyncMode");
    function re(C) {
      return F || (F = !0, console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")), !1;
    }
    i(re, "isConcurrentMode");
    function V(C) {
      return E(C) === c;
    }
    i(V, "isContextConsumer");
    function q(C) {
      return E(C) === s;
    }
    i(q, "isContextProvider");
    function se(C) {
      return typeof C == "object" && C !== null && C.$$typeof === e;
    }
    i(se, "isElement");
    function J(C) {
      return E(C) === u;
    }
    i(J, "isForwardRef");
    function U(C) {
      return E(C) === n;
    }
    i(U, "isFragment");
    function oe(C) {
      return E(C) === S;
    }
    i(oe, "isLazy");
    function ge(C) {
      return E(C) === a;
    }
    i(ge, "isMemo");
    function ue(C) {
      return E(C) === t;
    }
    i(ue, "isPortal");
    function Se(C) {
      return E(C) === o;
    }
    i(Se, "isProfiler");
    function Oe(C) {
      return E(C) === r;
    }
    i(Oe, "isStrictMode");
    function $e(C) {
      return E(C) === m;
    }
    i($e, "isSuspense");
    function lr(C) {
      return E(C) === p;
    }
    i(lr, "isSuspenseList"), w.ContextConsumer = $, w.ContextProvider = T, w.Element = A, w.ForwardRef = K, w.Fragment = Q, w.Lazy = I, w.Memo =
    k, w.Portal = G, w.Profiler = Y, w.StrictMode = N, w.Suspense = L, w.SuspenseList = x, w.isAsyncMode = W, w.isConcurrentMode = re, w.isContextConsumer =
    V, w.isContextProvider = q, w.isElement = se, w.isForwardRef = J, w.isFragment = U, w.isLazy = oe, w.isMemo = ge, w.isPortal = ue, w.isProfiler =
    Se, w.isStrictMode = Oe, w.isSuspense = $e, w.isSuspenseList = lr, w.isValidElementType = y, w.typeOf = E;
  }()), w;
}
i(to, "requireReactIs_development");
var Ut;
function no() {
  return Ut || (Ut = 1, Ze.exports = to()), Ze.exports;
}
i(no, "requireReactIs");
var on = no(), ro = /* @__PURE__ */ nn(on), oo = /* @__PURE__ */ Jt({
  __proto__: null,
  default: ro
}, [on]), so = [
  "isAsyncMode",
  "isConcurrentMode",
  "isContextConsumer",
  "isContextProvider",
  "isElement",
  "isForwardRef",
  "isFragment",
  "isLazy",
  "isMemo",
  "isPortal",
  "isProfiler",
  "isStrictMode",
  "isSuspense",
  "isSuspenseList",
  "isValidElementType"
], ae = Object.fromEntries(so.map((e) => [e, (t) => oo[e](t) || eo[e](t)]));
function sn(e, t = []) {
  if (Array.isArray(e))
    for (let n of e)
      sn(n, t);
  else e != null && e !== !1 && e !== "" && t.push(e);
  return t;
}
i(sn, "getChildren");
function Wt(e) {
  let t = e.type;
  if (typeof t == "string")
    return t;
  if (typeof t == "function")
    return t.displayName || t.name || "Unknown";
  if (ae.isFragment(e))
    return "React.Fragment";
  if (ae.isSuspense(e))
    return "React.Suspense";
  if (typeof t == "object" && t !== null) {
    if (ae.isContextProvider(e))
      return "Context.Provider";
    if (ae.isContextConsumer(e))
      return "Context.Consumer";
    if (ae.isForwardRef(e)) {
      if (t.displayName)
        return t.displayName;
      let n = t.render.displayName || t.render.name || "";
      return n === "" ? "ForwardRef" : `ForwardRef(${n})`;
    }
    if (ae.isMemo(e)) {
      let n = t.displayName || t.type.displayName || t.type.name || "";
      return n === "" ? "Memo" : `Memo(${n})`;
    }
  }
  return "UNDEFINED";
}
i(Wt, "getType");
function io(e) {
  let { props: t } = e;
  return Object.keys(t).filter((n) => n !== "children" && t[n] !== void 0).sort();
}
i(io, "getPropKeys$1");
var co = /* @__PURE__ */ i((e, t, n, r, o, s) => ++r > t.maxDepth ? rt(Wt(e), t) : nt(Wt(e), et(io(e), e.props, t, n + t.indent, r, o, s), tt(
sn(e.props.children), t, n + t.indent, r, o, s), t, n), "serialize$1"), uo = /* @__PURE__ */ i((e) => e != null && ae.isElement(e), "test$1"),
lo = {
  serialize: co,
  test: uo
}, ao = typeof Symbol == "function" && Symbol.for ? Symbol.for("react.test.json") : 245830487;
function fo(e) {
  let { props: t } = e;
  return t ? Object.keys(t).filter((n) => t[n] !== void 0).sort() : [];
}
i(fo, "getPropKeys");
var mo = /* @__PURE__ */ i((e, t, n, r, o, s) => ++r > t.maxDepth ? rt(e.type, t) : nt(e.type, e.props ? et(fo(e), e.props, t, n + t.indent,
r, o, s) : "", e.children ? tt(e.children, t, n + t.indent, r, o, s) : "", t, n), "serialize"), po = /* @__PURE__ */ i((e) => e && e.$$typeof ===
ao, "test"), go = {
  serialize: mo,
  test: po
}, cn = Object.prototype.toString, ho = Date.prototype.toISOString, yo = Error.prototype.toString, Vt = RegExp.prototype.toString;
function Re(e) {
  return typeof e.constructor == "function" && e.constructor.name || "Object";
}
i(Re, "getConstructorName");
function bo(e) {
  return typeof window < "u" && e === window;
}
i(bo, "isWindow");
var So = /^Symbol\((.*)\)(.*)$/, Eo = /\n/g, st = class st extends Error {
  constructor(t, n) {
    super(t), this.stack = n, this.name = this.constructor.name;
  }
};
i(st, "PrettyFormatPluginError");
var Ne = st;
function _o(e) {
  return e === "[object Array]" || e === "[object ArrayBuffer]" || e === "[object DataView]" || e === "[object Float32Array]" || e === "[obj\
ect Float64Array]" || e === "[object Int8Array]" || e === "[object Int16Array]" || e === "[object Int32Array]" || e === "[object Uint8Array]" ||
  e === "[object Uint8ClampedArray]" || e === "[object Uint16Array]" || e === "[object Uint32Array]";
}
i(_o, "isToStringedArrayType");
function To(e) {
  return Object.is(e, -0) ? "-0" : String(e);
}
i(To, "printNumber");
function Co(e) {
  return `${e}n`;
}
i(Co, "printBigInt");
function qt(e, t) {
  return t ? `[Function ${e.name || "anonymous"}]` : "[Function]";
}
i(qt, "printFunction");
function Kt(e) {
  return String(e).replace(So, "Symbol($1)");
}
i(Kt, "printSymbol");
function Gt(e) {
  return `[${yo.call(e)}]`;
}
i(Gt, "printError");
function un(e, t, n, r) {
  if (e === !0 || e === !1)
    return `${e}`;
  if (e === void 0)
    return "undefined";
  if (e === null)
    return "null";
  let o = typeof e;
  if (o === "number")
    return To(e);
  if (o === "bigint")
    return Co(e);
  if (o === "string")
    return r ? `"${e.replaceAll(/"|\\/g, "\\$&")}"` : `"${e}"`;
  if (o === "function")
    return qt(e, t);
  if (o === "symbol")
    return Kt(e);
  let s = cn.call(e);
  return s === "[object WeakMap]" ? "WeakMap {}" : s === "[object WeakSet]" ? "WeakSet {}" : s === "[object Function]" || s === "[object Gen\
eratorFunction]" ? qt(e, t) : s === "[object Symbol]" ? Kt(e) : s === "[object Date]" ? Number.isNaN(+e) ? "Date { NaN }" : ho.call(e) : s ===
  "[object Error]" ? Gt(e) : s === "[object RegExp]" ? n ? Vt.call(e).replaceAll(/[$()*+.?[\\\]^{|}]/g, "\\$&") : Vt.call(e) : e instanceof Error ?
  Gt(e) : null;
}
i(un, "printBasicValue");
function ln(e, t, n, r, o, s) {
  if (o.includes(e))
    return "[Circular]";
  o = [...o], o.push(e);
  let c = ++r > t.maxDepth, l = t.min;
  if (t.callToJSON && !c && e.toJSON && typeof e.toJSON == "function" && !s)
    return le(e.toJSON(), t, n, r, o, !0);
  let u = cn.call(e);
  return u === "[object Arguments]" ? c ? "[Arguments]" : `${l ? "" : "Arguments "}[${Pe(e, t, n, r, o, le)}]` : _o(u) ? c ? `[${e.constructor.
  name}]` : `${l || !t.printBasicPrototype && e.constructor.name === "Array" ? "" : `${e.constructor.name} `}[${Pe(e, t, n, r, o, le)}]` : u ===
  "[object Map]" ? c ? "[Map]" : `Map {${_e(e.entries(), t, n, r, o, le, " => ")}}` : u === "[object Set]" ? c ? "[Set]" : `Set {${Qe(e.values(),
  t, n, r, o, le)}}` : c || bo(e) ? `[${Re(e)}]` : `${l || !t.printBasicPrototype && Re(e) === "Object" ? "" : `${Re(e)} `}{${ve(e, t, n, r,
  o, le)}}`;
}
i(ln, "printComplexValue");
var Oo = {
  test: /* @__PURE__ */ i((e) => e && e instanceof Error, "test"),
  serialize(e, t, n, r, o, s) {
    if (o.includes(e))
      return "[Circular]";
    o = [...o, e];
    let c = ++r > t.maxDepth, { message: l, cause: u, ...m } = e, p = {
      message: l,
      ...typeof u < "u" ? { cause: u } : {},
      ...e instanceof AggregateError ? { errors: e.errors } : {},
      ...m
    }, a = e.name !== "Error" ? e.name : Re(e);
    return c ? `[${a}]` : `${a} {${_e(Object.entries(p).values(), t, n, r, o, s)}}`;
  }
};
function $o(e) {
  return e.serialize != null;
}
i($o, "isNewPlugin");
function an(e, t, n, r, o, s) {
  let c;
  try {
    c = $o(e) ? e.serialize(t, n, r, o, s, le) : e.print(t, (l) => le(l, n, r, o, s), (l) => {
      let u = r + n.indent;
      return u + l.replaceAll(Eo, `
${u}`);
    }, {
      edgeSpacing: n.spacingOuter,
      min: n.min,
      spacing: n.spacingInner
    }, n.colors);
  } catch (l) {
    throw new Ne(l.message, l.stack);
  }
  if (typeof c != "string")
    throw new TypeError(`pretty-format: Plugin must return type "string" but instead returned "${typeof c}".`);
  return c;
}
i(an, "printPlugin");
function fn(e, t) {
  for (let n of e)
    try {
      if (n.test(t))
        return n;
    } catch (r) {
      throw new Ne(r.message, r.stack);
    }
  return null;
}
i(fn, "findPlugin");
function le(e, t, n, r, o, s) {
  let c = fn(t.plugins, e);
  if (c !== null)
    return an(c, e, t, n, r, o);
  let l = un(e, t.printFunctionName, t.escapeRegex, t.escapeString);
  return l !== null ? l : ln(e, t, n, r, o, s);
}
i(le, "printer");
var ot = {
  comment: "gray",
  content: "reset",
  prop: "yellow",
  tag: "cyan",
  value: "green"
}, mn = Object.keys(ot), ee = {
  callToJSON: !0,
  compareKeys: void 0,
  escapeRegex: !1,
  escapeString: !0,
  highlight: !1,
  indent: 2,
  maxDepth: Number.POSITIVE_INFINITY,
  maxWidth: Number.POSITIVE_INFINITY,
  min: !1,
  plugins: [],
  printBasicPrototype: !0,
  printFunctionName: !0,
  theme: ot
};
function wo(e) {
  for (let t of Object.keys(e))
    if (!Object.prototype.hasOwnProperty.call(ee, t))
      throw new Error(`pretty-format: Unknown option "${t}".`);
  if (e.min && e.indent !== void 0 && e.indent !== 0)
    throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');
}
i(wo, "validateOptions");
function Ao() {
  return mn.reduce((e, t) => {
    let n = ot[t], r = n && v[n];
    if (r && typeof r.close == "string" && typeof r.open == "string")
      e[t] = r;
    else
      throw new Error(`pretty-format: Option "theme" has a key "${t}" whose value "${n}" is undefined in ansi-styles.`);
    return e;
  }, /* @__PURE__ */ Object.create(null));
}
i(Ao, "getColorsHighlight");
function Ro() {
  return mn.reduce((e, t) => (e[t] = {
    close: "",
    open: ""
  }, e), /* @__PURE__ */ Object.create(null));
}
i(Ro, "getColorsEmpty");
function pn(e) {
  return e?.printFunctionName ?? ee.printFunctionName;
}
i(pn, "getPrintFunctionName");
function gn(e) {
  return e?.escapeRegex ?? ee.escapeRegex;
}
i(gn, "getEscapeRegex");
function hn(e) {
  return e?.escapeString ?? ee.escapeString;
}
i(hn, "getEscapeString");
function Ht(e) {
  return {
    callToJSON: e?.callToJSON ?? ee.callToJSON,
    colors: e?.highlight ? Ao() : Ro(),
    compareKeys: typeof e?.compareKeys == "function" || e?.compareKeys === null ? e.compareKeys : ee.compareKeys,
    escapeRegex: gn(e),
    escapeString: hn(e),
    indent: e?.min ? "" : Po(e?.indent ?? ee.indent),
    maxDepth: e?.maxDepth ?? ee.maxDepth,
    maxWidth: e?.maxWidth ?? ee.maxWidth,
    min: e?.min ?? ee.min,
    plugins: e?.plugins ?? ee.plugins,
    printBasicPrototype: e?.printBasicPrototype ?? !0,
    printFunctionName: pn(e),
    spacingInner: e?.min ? " " : `
`,
    spacingOuter: e?.min ? "" : `
`
  };
}
i(Ht, "getConfig");
function Po(e) {
  return Array.from({ length: e + 1 }).join(" ");
}
i(Po, "createIndent");
function X(e, t) {
  if (t && (wo(t), t.plugins)) {
    let r = fn(t.plugins, e);
    if (r !== null)
      return an(r, e, Ht(t), "", 0, []);
  }
  let n = un(e, pn(t), gn(t), hn(t));
  return n !== null ? n : ln(e, Ht(t), "", 0, []);
}
i(X, "format");
var Te = {
  AsymmetricMatcher: br,
  DOMCollection: $r,
  DOMElement: Dr,
  Immutable: Xr,
  ReactElement: lo,
  ReactTestComponent: go,
  Error: Oo
};

// ../node_modules/loupe/lib/helpers.js
var dn = {
  bold: ["1", "22"],
  dim: ["2", "22"],
  italic: ["3", "23"],
  underline: ["4", "24"],
  // 5 & 6 are blinking
  inverse: ["7", "27"],
  hidden: ["8", "28"],
  strike: ["9", "29"],
  // 10-20 are fonts
  // 21-29 are resets for 1-9
  black: ["30", "39"],
  red: ["31", "39"],
  green: ["32", "39"],
  yellow: ["33", "39"],
  blue: ["34", "39"],
  magenta: ["35", "39"],
  cyan: ["36", "39"],
  white: ["37", "39"],
  brightblack: ["30;1", "39"],
  brightred: ["31;1", "39"],
  brightgreen: ["32;1", "39"],
  brightyellow: ["33;1", "39"],
  brightblue: ["34;1", "39"],
  brightmagenta: ["35;1", "39"],
  brightcyan: ["36;1", "39"],
  brightwhite: ["37;1", "39"],
  grey: ["90", "39"]
}, No = {
  special: "cyan",
  number: "yellow",
  bigint: "yellow",
  boolean: "yellow",
  undefined: "grey",
  null: "bold",
  string: "green",
  symbol: "green",
  date: "magenta",
  regexp: "red"
}, ie = "\u2026";
function Io(e, t) {
  let n = dn[No[t]] || dn[t] || "";
  return n ? `\x1B[${n[0]}m${String(e)}\x1B[${n[1]}m` : String(e);
}
i(Io, "colorise");
function yn({
  showHidden: e = !1,
  depth: t = 2,
  colors: n = !1,
  customInspect: r = !0,
  showProxy: o = !1,
  maxArrayLength: s = 1 / 0,
  breakLength: c = 1 / 0,
  seen: l = [],
  // eslint-disable-next-line no-shadow
  truncate: u = 1 / 0,
  stylize: m = String
} = {}, p) {
  let a = {
    showHidden: !!e,
    depth: Number(t),
    colors: !!n,
    customInspect: !!r,
    showProxy: !!o,
    maxArrayLength: Number(s),
    breakLength: Number(c),
    truncate: Number(u),
    seen: l,
    inspect: p,
    stylize: m
  };
  return a.colors && (a.stylize = Io), a;
}
i(yn, "normaliseOptions");
function Mo(e) {
  return e >= "\uD800" && e <= "\uDBFF";
}
i(Mo, "isHighSurrogate");
function B(e, t, n = ie) {
  e = String(e);
  let r = n.length, o = e.length;
  if (r > t && o > r)
    return n;
  if (o > t && o > r) {
    let s = t - r;
    return s > 0 && Mo(e[s - 1]) && (s = s - 1), `${e.slice(0, s)}${n}`;
  }
  return e;
}
i(B, "truncate");
function D(e, t, n, r = ", ") {
  n = n || t.inspect;
  let o = e.length;
  if (o === 0)
    return "";
  let s = t.truncate, c = "", l = "", u = "";
  for (let m = 0; m < o; m += 1) {
    let p = m + 1 === e.length, a = m + 2 === e.length;
    u = `${ie}(${e.length - m})`;
    let S = e[m];
    t.truncate = s - c.length - (p ? 0 : r.length);
    let g = l || n(S, t) + (p ? "" : r), h = c.length + g.length, f = h + u.length;
    if (p && h > s && c.length + u.length <= s || !p && !a && f > s || (l = p ? "" : n(e[m + 1], t) + (a ? "" : r), !p && a && f > s && h + l.
    length > s))
      break;
    if (c += g, !p && !a && h + l.length >= s) {
      u = `${ie}(${e.length - m - 1})`;
      break;
    }
    u = "";
  }
  return `${c}${u}`;
}
i(D, "inspectList");
function Lo(e) {
  return e.match(/^[a-zA-Z_][a-zA-Z_0-9]*$/) ? e : JSON.stringify(e).replace(/'/g, "\\'").replace(/\\"/g, '"').replace(/(^"|"$)/g, "'");
}
i(Lo, "quoteComplexKey");
function ce([e, t], n) {
  return n.truncate -= 2, typeof e == "string" ? e = Lo(e) : typeof e != "number" && (e = `[${n.inspect(e, n)}]`), n.truncate -= e.length, t =
  n.inspect(t, n), `${e}: ${t}`;
}
i(ce, "inspectProperty");

// ../node_modules/loupe/lib/array.js
function it(e, t) {
  let n = Object.keys(e).slice(e.length);
  if (!e.length && !n.length)
    return "[]";
  t.truncate -= 4;
  let r = D(e, t);
  t.truncate -= r.length;
  let o = "";
  return n.length && (o = D(n.map((s) => [s, e[s]]), t, ce)), `[ ${r}${o ? `, ${o}` : ""} ]`;
}
i(it, "inspectArray");

// ../node_modules/loupe/lib/typedarray.js
var xo = /* @__PURE__ */ i((e) => typeof Buffer == "function" && e instanceof Buffer ? "Buffer" : e[Symbol.toStringTag] ? e[Symbol.toStringTag] :
e.constructor.name, "getArrayName");
function te(e, t) {
  let n = xo(e);
  t.truncate -= n.length + 4;
  let r = Object.keys(e).slice(e.length);
  if (!e.length && !r.length)
    return `${n}[]`;
  let o = "";
  for (let c = 0; c < e.length; c++) {
    let l = `${t.stylize(B(e[c], t.truncate), "number")}${c === e.length - 1 ? "" : ", "}`;
    if (t.truncate -= l.length, e[c] !== e.length && t.truncate <= 3) {
      o += `${ie}(${e.length - e[c] + 1})`;
      break;
    }
    o += l;
  }
  let s = "";
  return r.length && (s = D(r.map((c) => [c, e[c]]), t, ce)), `${n}[ ${o}${s ? `, ${s}` : ""} ]`;
}
i(te, "inspectTypedArray");

// ../node_modules/loupe/lib/date.js
function ct(e, t) {
  let n = e.toJSON();
  if (n === null)
    return "Invalid Date";
  let r = n.split("T"), o = r[0];
  return t.stylize(`${o}T${B(r[1], t.truncate - o.length - 1)}`, "date");
}
i(ct, "inspectDate");

// ../node_modules/loupe/lib/function.js
function Me(e, t) {
  let n = e[Symbol.toStringTag] || "Function", r = e.name;
  return r ? t.stylize(`[${n} ${B(r, t.truncate - 11)}]`, "special") : t.stylize(`[${n}]`, "special");
}
i(Me, "inspectFunction");

// ../node_modules/loupe/lib/map.js
function Do([e, t], n) {
  return n.truncate -= 4, e = n.inspect(e, n), n.truncate -= e.length, t = n.inspect(t, n), `${e} => ${t}`;
}
i(Do, "inspectMapEntry");
function Fo(e) {
  let t = [];
  return e.forEach((n, r) => {
    t.push([r, n]);
  }), t;
}
i(Fo, "mapToEntries");
function ut(e, t) {
  return e.size === 0 ? "Map{}" : (t.truncate -= 7, `Map{ ${D(Fo(e), t, Do)} }`);
}
i(ut, "inspectMap");

// ../node_modules/loupe/lib/number.js
var jo = Number.isNaN || ((e) => e !== e);
function Le(e, t) {
  return jo(e) ? t.stylize("NaN", "number") : e === 1 / 0 ? t.stylize("Infinity", "number") : e === -1 / 0 ? t.stylize("-Infinity", "number") :
  e === 0 ? t.stylize(1 / e === 1 / 0 ? "+0" : "-0", "number") : t.stylize(B(String(e), t.truncate), "number");
}
i(Le, "inspectNumber");

// ../node_modules/loupe/lib/bigint.js
function xe(e, t) {
  let n = B(e.toString(), t.truncate - 1);
  return n !== ie && (n += "n"), t.stylize(n, "bigint");
}
i(xe, "inspectBigInt");

// ../node_modules/loupe/lib/regexp.js
function lt(e, t) {
  let n = e.toString().split("/")[2], r = t.truncate - (2 + n.length), o = e.source;
  return t.stylize(`/${B(o, r)}/${n}`, "regexp");
}
i(lt, "inspectRegExp");

// ../node_modules/loupe/lib/set.js
function ko(e) {
  let t = [];
  return e.forEach((n) => {
    t.push(n);
  }), t;
}
i(ko, "arrayFromSet");
function at(e, t) {
  return e.size === 0 ? "Set{}" : (t.truncate -= 7, `Set{ ${D(ko(e), t)} }`);
}
i(at, "inspectSet");

// ../node_modules/loupe/lib/string.js
var bn = new RegExp("['\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\u\
ffff]", "g"), Bo = {
  "\b": "\\b",
  "	": "\\t",
  "\n": "\\n",
  "\f": "\\f",
  "\r": "\\r",
  "'": "\\'",
  "\\": "\\\\"
}, zo = 16, Yo = 4;
function Uo(e) {
  return Bo[e] || `\\u${`0000${e.charCodeAt(0).toString(zo)}`.slice(-Yo)}`;
}
i(Uo, "escape");
function De(e, t) {
  return bn.test(e) && (e = e.replace(bn, Uo)), t.stylize(`'${B(e, t.truncate - 2)}'`, "string");
}
i(De, "inspectString");

// ../node_modules/loupe/lib/symbol.js
function Fe(e) {
  return "description" in Symbol.prototype ? e.description ? `Symbol(${e.description})` : "Symbol()" : e.toString();
}
i(Fe, "inspectSymbol");

// ../node_modules/loupe/lib/promise.js
var Sn = /* @__PURE__ */ i(() => "Promise{\u2026}", "getPromiseValue");
try {
  let { getPromiseDetails: e, kPending: t, kRejected: n } = process.binding("util");
  Array.isArray(e(Promise.resolve())) && (Sn = /* @__PURE__ */ i((r, o) => {
    let [s, c] = e(r);
    return s === t ? "Promise{<pending>}" : `Promise${s === n ? "!" : ""}{${o.inspect(c, o)}}`;
  }, "getPromiseValue"));
} catch {
}
var En = Sn;

// ../node_modules/loupe/lib/object.js
function fe(e, t) {
  let n = Object.getOwnPropertyNames(e), r = Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(e) : [];
  if (n.length === 0 && r.length === 0)
    return "{}";
  if (t.truncate -= 4, t.seen = t.seen || [], t.seen.includes(e))
    return "[Circular]";
  t.seen.push(e);
  let o = D(n.map((l) => [l, e[l]]), t, ce), s = D(r.map((l) => [l, e[l]]), t, ce);
  t.seen.pop();
  let c = "";
  return o && s && (c = ", "), `{ ${o}${c}${s} }`;
}
i(fe, "inspectObject");

// ../node_modules/loupe/lib/class.js
var ft = typeof Symbol < "u" && Symbol.toStringTag ? Symbol.toStringTag : !1;
function mt(e, t) {
  let n = "";
  return ft && ft in e && (n = e[ft]), n = n || e.constructor.name, (!n || n === "_class") && (n = "<Anonymous Class>"), t.truncate -= n.length,
  `${n}${fe(e, t)}`;
}
i(mt, "inspectClass");

// ../node_modules/loupe/lib/arguments.js
function pt(e, t) {
  return e.length === 0 ? "Arguments[]" : (t.truncate -= 13, `Arguments[ ${D(e, t)} ]`);
}
i(pt, "inspectArguments");

// ../node_modules/loupe/lib/error.js
var Wo = [
  "stack",
  "line",
  "column",
  "name",
  "message",
  "fileName",
  "lineNumber",
  "columnNumber",
  "number",
  "description",
  "cause"
];
function gt(e, t) {
  let n = Object.getOwnPropertyNames(e).filter((c) => Wo.indexOf(c) === -1), r = e.name;
  t.truncate -= r.length;
  let o = "";
  if (typeof e.message == "string" ? o = B(e.message, t.truncate) : n.unshift("message"), o = o ? `: ${o}` : "", t.truncate -= o.length + 5,
  t.seen = t.seen || [], t.seen.includes(e))
    return "[Circular]";
  t.seen.push(e);
  let s = D(n.map((c) => [c, e[c]]), t, ce);
  return `${r}${o}${s ? ` { ${s} }` : ""}`;
}
i(gt, "inspectObject");

// ../node_modules/loupe/lib/html.js
function Vo([e, t], n) {
  return n.truncate -= 3, t ? `${n.stylize(String(e), "yellow")}=${n.stylize(`"${t}"`, "string")}` : `${n.stylize(String(e), "yellow")}`;
}
i(Vo, "inspectAttribute");
function je(e, t) {
  return D(e, t, ke, `
`);
}
i(je, "inspectHTMLCollection");
function ke(e, t) {
  let n = e.getAttributeNames(), r = e.tagName.toLowerCase(), o = t.stylize(`<${r}`, "special"), s = t.stylize(">", "special"), c = t.stylize(
  `</${r}>`, "special");
  t.truncate -= r.length * 2 + 5;
  let l = "";
  n.length > 0 && (l += " ", l += D(n.map((p) => [p, e.getAttribute(p)]), t, Vo, " ")), t.truncate -= l.length;
  let u = t.truncate, m = je(e.children, t);
  return m && m.length > u && (m = `${ie}(${e.children.length})`), `${o}${l}${s}${m}${c}`;
}
i(ke, "inspectHTML");

// ../node_modules/loupe/lib/index.js
var qo = typeof Symbol == "function" && typeof Symbol.for == "function", ht = qo ? Symbol.for("chai/inspect") : "@@chai/inspect", de = !1;
try {
  let e = _n();
  de = e.inspect ? e.inspect.custom : !1;
} catch {
  de = !1;
}
var Tn = /* @__PURE__ */ new WeakMap(), Cn = {}, On = {
  undefined: /* @__PURE__ */ i((e, t) => t.stylize("undefined", "undefined"), "undefined"),
  null: /* @__PURE__ */ i((e, t) => t.stylize("null", "null"), "null"),
  boolean: /* @__PURE__ */ i((e, t) => t.stylize(String(e), "boolean"), "boolean"),
  Boolean: /* @__PURE__ */ i((e, t) => t.stylize(String(e), "boolean"), "Boolean"),
  number: Le,
  Number: Le,
  bigint: xe,
  BigInt: xe,
  string: De,
  String: De,
  function: Me,
  Function: Me,
  symbol: Fe,
  // A Symbol polyfill will return `Symbol` not `symbol` from typedetect
  Symbol: Fe,
  Array: it,
  Date: ct,
  Map: ut,
  Set: at,
  RegExp: lt,
  Promise: En,
  // WeakSet, WeakMap are totally opaque to us
  WeakSet: /* @__PURE__ */ i((e, t) => t.stylize("WeakSet{\u2026}", "special"), "WeakSet"),
  WeakMap: /* @__PURE__ */ i((e, t) => t.stylize("WeakMap{\u2026}", "special"), "WeakMap"),
  Arguments: pt,
  Int8Array: te,
  Uint8Array: te,
  Uint8ClampedArray: te,
  Int16Array: te,
  Uint16Array: te,
  Int32Array: te,
  Uint32Array: te,
  Float32Array: te,
  Float64Array: te,
  Generator: /* @__PURE__ */ i(() => "", "Generator"),
  DataView: /* @__PURE__ */ i(() => "", "DataView"),
  ArrayBuffer: /* @__PURE__ */ i(() => "", "ArrayBuffer"),
  Error: gt,
  HTMLCollection: je,
  NodeList: je
}, Ko = /* @__PURE__ */ i((e, t, n) => ht in e && typeof e[ht] == "function" ? e[ht](t) : de && de in e && typeof e[de] == "function" ? e[de](
t.depth, t) : "inspect" in e && typeof e.inspect == "function" ? e.inspect(t.depth, t) : "constructor" in e && Tn.has(e.constructor) ? Tn.get(
e.constructor)(e, t) : Cn[n] ? Cn[n](e, t) : "", "inspectCustom"), Go = Object.prototype.toString;
function Be(e, t = {}) {
  let n = yn(t, Be), { customInspect: r } = n, o = e === null ? "null" : typeof e;
  if (o === "object" && (o = Go.call(e).slice(8, -1)), o in On)
    return On[o](e, n);
  if (r && e) {
    let c = Ko(e, n, o);
    if (c)
      return typeof c == "string" ? c : Be(c, n);
  }
  let s = e ? Object.getPrototypeOf(e) : !1;
  return s === Object.prototype || s === null ? fe(e, n) : e && typeof HTMLElement == "function" && e instanceof HTMLElement ? ke(e, n) : "c\
onstructor" in e ? e.constructor !== Object ? mt(e, n) : fe(e, n) : e === Object(e) ? fe(e, n) : n.stylize(String(e), o);
}
i(Be, "inspect");

// ../node_modules/@vitest/utils/dist/chunk-_commonjsHelpers.js
var { AsymmetricMatcher: Jo, DOMCollection: Xo, DOMElement: Zo, Immutable: Qo, ReactElement: vo, ReactTestComponent: es } = Te, $n = [
  es,
  vo,
  Zo,
  Xo,
  Qo,
  Jo
];
function me(e, t = 10, { maxLength: n, ...r } = {}) {
  let o = n ?? 1e4, s;
  try {
    s = X(e, {
      maxDepth: t,
      escapeString: !1,
      plugins: $n,
      ...r
    });
  } catch {
    s = X(e, {
      callToJSON: !1,
      maxDepth: t,
      escapeString: !1,
      plugins: $n,
      ...r
    });
  }
  return s.length >= o && t > 1 ? me(e, Math.floor(Math.min(t, Number.MAX_SAFE_INTEGER) / 2), {
    maxLength: n,
    ...r
  }) : s;
}
i(me, "stringify");
var ts = /%[sdjifoOc%]/g;
function wn(...e) {
  if (typeof e[0] != "string") {
    let s = [];
    for (let c = 0; c < e.length; c++)
      s.push(Ce(e[c], {
        depth: 0,
        colors: !1
      }));
    return s.join(" ");
  }
  let t = e.length, n = 1, r = e[0], o = String(r).replace(ts, (s) => {
    if (s === "%%")
      return "%";
    if (n >= t)
      return s;
    switch (s) {
      case "%s": {
        let c = e[n++];
        return typeof c == "bigint" ? `${c.toString()}n` : typeof c == "number" && c === 0 && 1 / c < 0 ? "-0" : typeof c == "object" && c !==
        null ? typeof c.toString == "function" && c.toString !== Object.prototype.toString ? c.toString() : Ce(c, {
          depth: 0,
          colors: !1
        }) : String(c);
      }
      case "%d": {
        let c = e[n++];
        return typeof c == "bigint" ? `${c.toString()}n` : Number(c).toString();
      }
      case "%i": {
        let c = e[n++];
        return typeof c == "bigint" ? `${c.toString()}n` : Number.parseInt(String(c)).toString();
      }
      case "%f":
        return Number.parseFloat(String(e[n++])).toString();
      case "%o":
        return Ce(e[n++], {
          showHidden: !0,
          showProxy: !0
        });
      case "%O":
        return Ce(e[n++]);
      case "%c":
        return n++, "";
      case "%j":
        try {
          return JSON.stringify(e[n++]);
        } catch (c) {
          let l = c.message;
          if (l.includes("circular structure") || l.includes("cyclic structures") || l.includes("cyclic object"))
            return "[Circular]";
          throw c;
        }
      default:
        return s;
    }
  });
  for (let s = e[n]; n < t; s = e[++n])
    s === null || typeof s != "object" ? o += ` ${s}` : o += ` ${Ce(s)}`;
  return o;
}
i(wn, "format");
function Ce(e, t = {}) {
  return t.truncate === 0 && (t.truncate = Number.POSITIVE_INFINITY), Be(e, t);
}
i(Ce, "inspect");
function An(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
i(An, "getDefaultExportFromCjs");

// ../node_modules/@vitest/utils/dist/helpers.js
function ns(e) {
  return e === Object.prototype || e === Function.prototype || e === RegExp.prototype;
}
i(ns, "isFinalObj");
function ze(e) {
  return Object.prototype.toString.apply(e).slice(8, -1);
}
i(ze, "getType");
function rs(e, t) {
  let n = typeof t == "function" ? t : (r) => t.add(r);
  Object.getOwnPropertyNames(e).forEach(n), Object.getOwnPropertySymbols(e).forEach(n);
}
i(rs, "collectOwnProperties");
function yt(e) {
  let t = /* @__PURE__ */ new Set();
  return ns(e) ? [] : (rs(e, t), Array.from(t));
}
i(yt, "getOwnProperties");
var Rn = { forceWritable: !1 };
function bt(e, t = Rn) {
  return dt(e, /* @__PURE__ */ new WeakMap(), t);
}
i(bt, "deepClone");
function dt(e, t, n = Rn) {
  let r, o;
  if (t.has(e))
    return t.get(e);
  if (Array.isArray(e)) {
    for (o = Array.from({ length: r = e.length }), t.set(e, o); r--; )
      o[r] = dt(e[r], t, n);
    return o;
  }
  if (Object.prototype.toString.call(e) === "[object Object]") {
    o = Object.create(Object.getPrototypeOf(e)), t.set(e, o);
    let s = yt(e);
    for (let c of s) {
      let l = Object.getOwnPropertyDescriptor(e, c);
      if (!l)
        continue;
      let u = dt(e[c], t, n);
      n.forceWritable ? Object.defineProperty(o, c, {
        enumerable: l.enumerable,
        configurable: !0,
        writable: !0,
        value: u
      }) : "get" in l ? Object.defineProperty(o, c, {
        ...l,
        get() {
          return u;
        }
      }) : Object.defineProperty(o, c, {
        ...l,
        value: u
      });
    }
    return o;
  }
  return e;
}
i(dt, "clone");

// ../node_modules/@vitest/utils/dist/diff.js
var z = -1, j = 1, M = 0, At = class At {
  0;
  1;
  constructor(t, n) {
    this[0] = t, this[1] = n;
  }
};
i(At, "Diff");
var P = At;
function os(e, t) {
  if (!e || !t || e.charAt(0) !== t.charAt(0))
    return 0;
  let n = 0, r = Math.min(e.length, t.length), o = r, s = 0;
  for (; n < o; )
    e.substring(s, o) === t.substring(s, o) ? (n = o, s = n) : r = o, o = Math.floor((r - n) / 2 + n);
  return o;
}
i(os, "diff_commonPrefix");
function Vn(e, t) {
  if (!e || !t || e.charAt(e.length - 1) !== t.charAt(t.length - 1))
    return 0;
  let n = 0, r = Math.min(e.length, t.length), o = r, s = 0;
  for (; n < o; )
    e.substring(e.length - o, e.length - s) === t.substring(t.length - o, t.length - s) ? (n = o, s = n) : r = o, o = Math.floor((r - n) / 2 +
    n);
  return o;
}
i(Vn, "diff_commonSuffix");
function Pn(e, t) {
  let n = e.length, r = t.length;
  if (n === 0 || r === 0)
    return 0;
  n > r ? e = e.substring(n - r) : n < r && (t = t.substring(0, n));
  let o = Math.min(n, r);
  if (e === t)
    return o;
  let s = 0, c = 1;
  for (; ; ) {
    let l = e.substring(o - c), u = t.indexOf(l);
    if (u === -1)
      return s;
    c += u, (u === 0 || e.substring(o - c) === t.substring(0, c)) && (s = c, c++);
  }
}
i(Pn, "diff_commonOverlap_");
function ss(e) {
  let t = !1, n = [], r = 0, o = null, s = 0, c = 0, l = 0, u = 0, m = 0;
  for (; s < e.length; )
    e[s][0] === M ? (n[r++] = s, c = u, l = m, u = 0, m = 0, o = e[s][1]) : (e[s][0] === j ? u += e[s][1].length : m += e[s][1].length, o &&
    o.length <= Math.max(c, l) && o.length <= Math.max(u, m) && (e.splice(n[r - 1], 0, new P(z, o)), e[n[r - 1] + 1][0] = j, r--, r--, s = r >
    0 ? n[r - 1] : -1, c = 0, l = 0, u = 0, m = 0, o = null, t = !0)), s++;
  for (t && qn(e), us(e), s = 1; s < e.length; ) {
    if (e[s - 1][0] === z && e[s][0] === j) {
      let p = e[s - 1][1], a = e[s][1], S = Pn(p, a), g = Pn(a, p);
      S >= g ? (S >= p.length / 2 || S >= a.length / 2) && (e.splice(s, 0, new P(M, a.substring(0, S))), e[s - 1][1] = p.substring(0, p.length -
      S), e[s + 1][1] = a.substring(S), s++) : (g >= p.length / 2 || g >= a.length / 2) && (e.splice(s, 0, new P(M, p.substring(0, g))), e[s -
      1][0] = j, e[s - 1][1] = a.substring(0, a.length - g), e[s + 1][0] = z, e[s + 1][1] = p.substring(g), s++), s++;
    }
    s++;
  }
}
i(ss, "diff_cleanupSemantic");
var Nn = /[^a-z0-9]/i, In = /\s/, Mn = /[\r\n]/, is = /\n\r?\n$/, cs = /^\r?\n\r?\n/;
function us(e) {
  let t = 1;
  for (; t < e.length - 1; ) {
    if (e[t - 1][0] === M && e[t + 1][0] === M) {
      let n = e[t - 1][1], r = e[t][1], o = e[t + 1][1], s = Vn(n, r);
      if (s) {
        let p = r.substring(r.length - s);
        n = n.substring(0, n.length - s), r = p + r.substring(0, r.length - s), o = p + o;
      }
      let c = n, l = r, u = o, m = Ye(n, r) + Ye(r, o);
      for (; r.charAt(0) === o.charAt(0); ) {
        n += r.charAt(0), r = r.substring(1) + o.charAt(0), o = o.substring(1);
        let p = Ye(n, r) + Ye(r, o);
        p >= m && (m = p, c = n, l = r, u = o);
      }
      e[t - 1][1] !== c && (c ? e[t - 1][1] = c : (e.splice(t - 1, 1), t--), e[t][1] = l, u ? e[t + 1][1] = u : (e.splice(t + 1, 1), t--));
    }
    t++;
  }
}
i(us, "diff_cleanupSemanticLossless");
function qn(e) {
  e.push(new P(M, ""));
  let t = 0, n = 0, r = 0, o = "", s = "", c;
  for (; t < e.length; )
    switch (e[t][0]) {
      case j:
        r++, s += e[t][1], t++;
        break;
      case z:
        n++, o += e[t][1], t++;
        break;
      case M:
        n + r > 1 ? (n !== 0 && r !== 0 && (c = os(s, o), c !== 0 && (t - n - r > 0 && e[t - n - r - 1][0] === M ? e[t - n - r - 1][1] += s.
        substring(0, c) : (e.splice(0, 0, new P(M, s.substring(0, c))), t++), s = s.substring(c), o = o.substring(c)), c = Vn(s, o), c !== 0 &&
        (e[t][1] = s.substring(s.length - c) + e[t][1], s = s.substring(0, s.length - c), o = o.substring(0, o.length - c))), t -= n + r, e.
        splice(t, n + r), o.length && (e.splice(t, 0, new P(z, o)), t++), s.length && (e.splice(t, 0, new P(j, s)), t++), t++) : t !== 0 && e[t -
        1][0] === M ? (e[t - 1][1] += e[t][1], e.splice(t, 1)) : t++, r = 0, n = 0, o = "", s = "";
        break;
    }
  e[e.length - 1][1] === "" && e.pop();
  let l = !1;
  for (t = 1; t < e.length - 1; )
    e[t - 1][0] === M && e[t + 1][0] === M && (e[t][1].substring(e[t][1].length - e[t - 1][1].length) === e[t - 1][1] ? (e[t][1] = e[t - 1][1] +
    e[t][1].substring(0, e[t][1].length - e[t - 1][1].length), e[t + 1][1] = e[t - 1][1] + e[t + 1][1], e.splice(t - 1, 1), l = !0) : e[t][1].
    substring(0, e[t + 1][1].length) === e[t + 1][1] && (e[t - 1][1] += e[t + 1][1], e[t][1] = e[t][1].substring(e[t + 1][1].length) + e[t +
    1][1], e.splice(t + 1, 1), l = !0)), t++;
  l && qn(e);
}
i(qn, "diff_cleanupMerge");
function Ye(e, t) {
  if (!e || !t)
    return 6;
  let n = e.charAt(e.length - 1), r = t.charAt(0), o = n.match(Nn), s = r.match(Nn), c = o && n.match(In), l = s && r.match(In), u = c && n.
  match(Mn), m = l && r.match(Mn), p = u && e.match(is), a = m && t.match(cs);
  return p || a ? 5 : u || m ? 4 : o && !c && l ? 3 : c || l ? 2 : o || s ? 1 : 0;
}
i(Ye, "diff_cleanupSemanticScore_");
var Kn = "Compared values have no visual difference.", ls = "Compared values serialize to the same structure.\nPrinting internal object struc\
ture without calling `toJSON` instead.", Ue = {}, Ln;
function as() {
  if (Ln) return Ue;
  Ln = 1, Object.defineProperty(Ue, "__esModule", {
    value: !0
  }), Ue.default = S;
  let e = "diff-sequences", t = 0, n = /* @__PURE__ */ i((g, h, f, d, b) => {
    let _ = 0;
    for (; g < h && f < d && b(g, f); )
      g += 1, f += 1, _ += 1;
    return _;
  }, "countCommonItemsF"), r = /* @__PURE__ */ i((g, h, f, d, b) => {
    let _ = 0;
    for (; g <= h && f <= d && b(h, d); )
      h -= 1, d -= 1, _ += 1;
    return _;
  }, "countCommonItemsR"), o = /* @__PURE__ */ i((g, h, f, d, b, _, O) => {
    let y = 0, E = -g, $ = _[y], T = $;
    _[y] += n(
      $ + 1,
      h,
      d + $ - E + 1,
      f,
      b
    );
    let A = g < O ? g : O;
    for (y += 1, E += 2; y <= A; y += 1, E += 2) {
      if (y !== g && T < _[y])
        $ = _[y];
      else if ($ = T + 1, h <= $)
        return y - 1;
      T = _[y], _[y] = $ + n($ + 1, h, d + $ - E + 1, f, b);
    }
    return O;
  }, "extendPathsF"), s = /* @__PURE__ */ i((g, h, f, d, b, _, O) => {
    let y = 0, E = g, $ = _[y], T = $;
    _[y] -= r(
      h,
      $ - 1,
      f,
      d + $ - E - 1,
      b
    );
    let A = g < O ? g : O;
    for (y += 1, E -= 2; y <= A; y += 1, E -= 2) {
      if (y !== g && _[y] < T)
        $ = _[y];
      else if ($ = T - 1, $ < h)
        return y - 1;
      T = _[y], _[y] = $ - r(
        h,
        $ - 1,
        f,
        d + $ - E - 1,
        b
      );
    }
    return O;
  }, "extendPathsR"), c = /* @__PURE__ */ i((g, h, f, d, b, _, O, y, E, $, T) => {
    let A = d - h, K = f - h, I = b - d - K, k = -I - (g - 1), G = -I + (g - 1), Y = t, N = g < y ? g : y;
    for (let L = 0, x = -g; L <= N; L += 1, x += 2) {
      let H = L === 0 || L !== g && Y < O[L], F = H ? O[L] : Y, W = H ? F : F + 1, re = A + W - x, V = n(
        W + 1,
        f,
        re + 1,
        b,
        _
      ), q = W + V;
      if (Y = O[L], O[L] = q, k <= x && x <= G) {
        let se = (g - 1 - (x + I)) / 2;
        if (se <= $ && E[se] - 1 <= q) {
          let J = A + F - (H ? x + 1 : x - 1), U = r(
            h,
            F,
            d,
            J,
            _
          ), oe = F - U, ge = J - U, ue = oe + 1, Se = ge + 1;
          T.nChangePreceding = g - 1, g - 1 === ue + Se - h - d ? (T.aEndPreceding = h, T.bEndPreceding = d) : (T.aEndPreceding = ue, T.bEndPreceding =
          Se), T.nCommonPreceding = U, U !== 0 && (T.aCommonPreceding = ue, T.bCommonPreceding = Se), T.nCommonFollowing = V, V !== 0 && (T.
          aCommonFollowing = W + 1, T.bCommonFollowing = re + 1);
          let Oe = q + 1, $e = re + V + 1;
          return T.nChangeFollowing = g - 1, g - 1 === f + b - Oe - $e ? (T.aStartFollowing = f, T.bStartFollowing = b) : (T.aStartFollowing =
          Oe, T.bStartFollowing = $e), !0;
        }
      }
    }
    return !1;
  }, "extendOverlappablePathsF"), l = /* @__PURE__ */ i((g, h, f, d, b, _, O, y, E, $, T) => {
    let A = b - f, K = f - h, I = b - d - K, k = I - g, G = I + g, Y = t, N = g < $ ? g : $;
    for (let L = 0, x = g; L <= N; L += 1, x -= 2) {
      let H = L === 0 || L !== g && E[L] < Y, F = H ? E[L] : Y, W = H ? F : F - 1, re = A + W - x, V = r(
        h,
        W - 1,
        d,
        re - 1,
        _
      ), q = W - V;
      if (Y = E[L], E[L] = q, k <= x && x <= G) {
        let se = (g + (x - I)) / 2;
        if (se <= y && q - 1 <= O[se]) {
          let J = re - V;
          if (T.nChangePreceding = g, g === q + J - h - d ? (T.aEndPreceding = h, T.bEndPreceding = d) : (T.aEndPreceding = q, T.bEndPreceding =
          J), T.nCommonPreceding = V, V !== 0 && (T.aCommonPreceding = q, T.bCommonPreceding = J), T.nChangeFollowing = g - 1, g === 1)
            T.nCommonFollowing = 0, T.aStartFollowing = f, T.bStartFollowing = b;
          else {
            let U = A + F - (H ? x - 1 : x + 1), oe = n(
              F,
              f,
              U,
              b,
              _
            );
            T.nCommonFollowing = oe, oe !== 0 && (T.aCommonFollowing = F, T.bCommonFollowing = U);
            let ge = F + oe, ue = U + oe;
            g - 1 === f + b - ge - ue ? (T.aStartFollowing = f, T.bStartFollowing = b) : (T.aStartFollowing = ge, T.bStartFollowing = ue);
          }
          return !0;
        }
      }
    }
    return !1;
  }, "extendOverlappablePathsR"), u = /* @__PURE__ */ i((g, h, f, d, b, _, O, y, E) => {
    let $ = d - h, T = b - f, A = f - h, K = b - d, Q = K - A, I = A, k = A;
    if (O[0] = h - 1, y[0] = f, Q % 2 === 0) {
      let G = (g || Q) / 2, Y = (A + K) / 2;
      for (let N = 1; N <= Y; N += 1)
        if (I = o(N, f, b, $, _, O, I), N < G)
          k = s(N, h, d, T, _, y, k);
        else if (
          // If a reverse path overlaps a forward path in the same diagonal,
          // return a division of the index intervals at the middle change.
          l(
            N,
            h,
            f,
            d,
            b,
            _,
            O,
            I,
            y,
            k,
            E
          )
        )
          return;
    } else {
      let G = ((g || Q) + 1) / 2, Y = (A + K + 1) / 2, N = 1;
      for (I = o(N, f, b, $, _, O, I), N += 1; N <= Y; N += 1)
        if (k = s(
          N - 1,
          h,
          d,
          T,
          _,
          y,
          k
        ), N < G)
          I = o(N, f, b, $, _, O, I);
        else if (
          // If a forward path overlaps a reverse path in the same diagonal,
          // return a division of the index intervals at the middle change.
          c(
            N,
            h,
            f,
            d,
            b,
            _,
            O,
            I,
            y,
            k,
            E
          )
        )
          return;
    }
    throw new Error(
      `${e}: no overlap aStart=${h} aEnd=${f} bStart=${d} bEnd=${b}`
    );
  }, "divide"), m = /* @__PURE__ */ i((g, h, f, d, b, _, O, y, E, $) => {
    if (b - d < f - h) {
      if (_ = !_, _ && O.length === 1) {
        let { foundSubsequence: q, isCommon: se } = O[0];
        O[1] = {
          foundSubsequence: /* @__PURE__ */ i((J, U, oe) => {
            q(J, oe, U);
          }, "foundSubsequence"),
          isCommon: /* @__PURE__ */ i((J, U) => se(U, J), "isCommon")
        };
      }
      let re = h, V = f;
      h = d, f = b, d = re, b = V;
    }
    let { foundSubsequence: T, isCommon: A } = O[_ ? 1 : 0];
    u(
      g,
      h,
      f,
      d,
      b,
      A,
      y,
      E,
      $
    );
    let {
      nChangePreceding: K,
      aEndPreceding: Q,
      bEndPreceding: I,
      nCommonPreceding: k,
      aCommonPreceding: G,
      bCommonPreceding: Y,
      nCommonFollowing: N,
      aCommonFollowing: L,
      bCommonFollowing: x,
      nChangeFollowing: H,
      aStartFollowing: F,
      bStartFollowing: W
    } = $;
    h < Q && d < I && m(
      K,
      h,
      Q,
      d,
      I,
      _,
      O,
      y,
      E,
      $
    ), k !== 0 && T(k, G, Y), N !== 0 && T(N, L, x), F < f && W < b && m(
      H,
      F,
      f,
      W,
      b,
      _,
      O,
      y,
      E,
      $
    );
  }, "findSubsequences"), p = /* @__PURE__ */ i((g, h) => {
    if (typeof h != "number")
      throw new TypeError(`${e}: ${g} typeof ${typeof h} is not a number`);
    if (!Number.isSafeInteger(h))
      throw new RangeError(`${e}: ${g} value ${h} is not a safe integer`);
    if (h < 0)
      throw new RangeError(`${e}: ${g} value ${h} is a negative integer`);
  }, "validateLength"), a = /* @__PURE__ */ i((g, h) => {
    let f = typeof h;
    if (f !== "function")
      throw new TypeError(`${e}: ${g} typeof ${f} is not a function`);
  }, "validateCallback");
  function S(g, h, f, d) {
    p("aLength", g), p("bLength", h), a("isCommon", f), a("foundSubsequence", d);
    let b = n(0, g, 0, h, f);
    if (b !== 0 && d(b, 0, 0), g !== b || h !== b) {
      let _ = b, O = b, y = r(
        _,
        g - 1,
        O,
        h - 1,
        f
      ), E = g - y, $ = h - y, T = b + y;
      g !== T && h !== T && m(
        0,
        _,
        E,
        O,
        $,
        !1,
        [
          {
            foundSubsequence: d,
            isCommon: f
          }
        ],
        [t],
        [t],
        {
          aCommonFollowing: t,
          aCommonPreceding: t,
          aEndPreceding: t,
          aStartFollowing: t,
          bCommonFollowing: t,
          bCommonPreceding: t,
          bEndPreceding: t,
          bStartFollowing: t,
          nChangeFollowing: t,
          nChangePreceding: t,
          nCommonFollowing: t,
          nCommonPreceding: t
        }
      ), y !== 0 && d(y, E, $);
    }
  }
  return i(S, "diffSequence"), Ue;
}
i(as, "requireBuild");
var fs = as(), Gn = /* @__PURE__ */ An(fs);
function ms(e, t) {
  return e.replace(/\s+$/, (n) => t(n));
}
i(ms, "formatTrailingSpaces");
function $t(e, t, n, r, o, s) {
  return e.length !== 0 ? n(`${r} ${ms(e, o)}`) : r !== " " ? n(r) : t && s.length !== 0 ? n(`${r} ${s}`) : "";
}
i($t, "printDiffLine");
function Hn(e, t, { aColor: n, aIndicator: r, changeLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return $t(e, t, n, r, o, s);
}
i(Hn, "printDeleteLine");
function Jn(e, t, { bColor: n, bIndicator: r, changeLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return $t(e, t, n, r, o, s);
}
i(Jn, "printInsertLine");
function Xn(e, t, { commonColor: n, commonIndicator: r, commonLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return $t(e, t, n, r, o, s);
}
i(Xn, "printCommonLine");
function xn(e, t, n, r, { patchColor: o }) {
  return o(`@@ -${e + 1},${t - e} +${n + 1},${r - n} @@`);
}
i(xn, "createPatchMark");
function ps(e, t) {
  let n = e.length, r = t.contextLines, o = r + r, s = n, c = !1, l = 0, u = 0;
  for (; u !== n; ) {
    let y = u;
    for (; u !== n && e[u][0] === M; )
      u += 1;
    if (y !== u)
      if (y === 0)
        u > r && (s -= u - r, c = !0);
      else if (u === n) {
        let E = u - y;
        E > r && (s -= E - r, c = !0);
      } else {
        let E = u - y;
        E > o && (s -= E - o, l += 1);
      }
    for (; u !== n && e[u][0] !== M; )
      u += 1;
  }
  let m = l !== 0 || c;
  l !== 0 ? s += l + 1 : c && (s += 1);
  let p = s - 1, a = [], S = 0;
  m && a.push("");
  let g = 0, h = 0, f = 0, d = 0, b = /* @__PURE__ */ i((y) => {
    let E = a.length;
    a.push(Xn(y, E === 0 || E === p, t)), f += 1, d += 1;
  }, "pushCommonLine"), _ = /* @__PURE__ */ i((y) => {
    let E = a.length;
    a.push(Hn(y, E === 0 || E === p, t)), f += 1;
  }, "pushDeleteLine"), O = /* @__PURE__ */ i((y) => {
    let E = a.length;
    a.push(Jn(y, E === 0 || E === p, t)), d += 1;
  }, "pushInsertLine");
  for (u = 0; u !== n; ) {
    let y = u;
    for (; u !== n && e[u][0] === M; )
      u += 1;
    if (y !== u)
      if (y === 0) {
        u > r && (y = u - r, g = y, h = y, f = g, d = h);
        for (let E = y; E !== u; E += 1)
          b(e[E][1]);
      } else if (u === n) {
        let E = u - y > r ? y + r : u;
        for (let $ = y; $ !== E; $ += 1)
          b(e[$][1]);
      } else {
        let E = u - y;
        if (E > o) {
          let $ = y + r;
          for (let A = y; A !== $; A += 1)
            b(e[A][1]);
          a[S] = xn(g, f, h, d, t), S = a.length, a.push("");
          let T = E - o;
          g = f + T, h = d + T, f = g, d = h;
          for (let A = u - r; A !== u; A += 1)
            b(e[A][1]);
        } else
          for (let $ = y; $ !== u; $ += 1)
            b(e[$][1]);
      }
    for (; u !== n && e[u][0] === z; )
      _(e[u][1]), u += 1;
    for (; u !== n && e[u][0] === j; )
      O(e[u][1]), u += 1;
  }
  return m && (a[S] = xn(g, f, h, d, t)), a.join(`
`);
}
i(ps, "joinAlignedDiffsNoExpand");
function gs(e, t) {
  return e.map((n, r, o) => {
    let s = n[1], c = r === 0 || r === o.length - 1;
    switch (n[0]) {
      case z:
        return Hn(s, c, t);
      case j:
        return Jn(s, c, t);
      default:
        return Xn(s, c, t);
    }
  }).join(`
`);
}
i(gs, "joinAlignedDiffsExpand");
var St = /* @__PURE__ */ i((e) => e, "noColor"), Zn = 5, hs = 0;
function ds() {
  return {
    aAnnotation: "Expected",
    aColor: v.green,
    aIndicator: "-",
    bAnnotation: "Received",
    bColor: v.red,
    bIndicator: "+",
    changeColor: v.inverse,
    changeLineTrailingSpaceColor: St,
    commonColor: v.dim,
    commonIndicator: " ",
    commonLineTrailingSpaceColor: St,
    compareKeys: void 0,
    contextLines: Zn,
    emptyFirstOrLastLinePlaceholder: "",
    expand: !1,
    includeChangeCounts: !1,
    omitAnnotationLines: !1,
    patchColor: v.yellow,
    printBasicPrototype: !1,
    truncateThreshold: hs,
    truncateAnnotation: "... Diff result is truncated",
    truncateAnnotationColor: St
  };
}
i(ds, "getDefaultOptions");
function ys(e) {
  return e && typeof e == "function" ? e : void 0;
}
i(ys, "getCompareKeys");
function bs(e) {
  return typeof e == "number" && Number.isSafeInteger(e) && e >= 0 ? e : Zn;
}
i(bs, "getContextLines");
function pe(e = {}) {
  return {
    ...ds(),
    ...e,
    compareKeys: ys(e.compareKeys),
    contextLines: bs(e.contextLines)
  };
}
i(pe, "normalizeDiffOptions");
function ye(e) {
  return e.length === 1 && e[0].length === 0;
}
i(ye, "isEmptyString");
function Ss(e) {
  let t = 0, n = 0;
  return e.forEach((r) => {
    switch (r[0]) {
      case z:
        t += 1;
        break;
      case j:
        n += 1;
        break;
    }
  }), {
    a: t,
    b: n
  };
}
i(Ss, "countChanges");
function Es({ aAnnotation: e, aColor: t, aIndicator: n, bAnnotation: r, bColor: o, bIndicator: s, includeChangeCounts: c, omitAnnotationLines: l }, u) {
  if (l)
    return "";
  let m = "", p = "";
  if (c) {
    let g = String(u.a), h = String(u.b), f = r.length - e.length, d = " ".repeat(Math.max(0, f)), b = " ".repeat(Math.max(0, -f)), _ = h.length -
    g.length, O = " ".repeat(Math.max(0, _)), y = " ".repeat(Math.max(0, -_));
    m = `${d}  ${n} ${O}${g}`, p = `${b}  ${s} ${y}${h}`;
  }
  let a = `${n} ${e}${m}`, S = `${s} ${r}${p}`;
  return `${t(a)}
${o(S)}

`;
}
i(Es, "printAnnotation");
function wt(e, t, n) {
  return Es(n, Ss(e)) + (n.expand ? gs(e, n) : ps(e, n)) + (t ? n.truncateAnnotationColor(`
${n.truncateAnnotation}`) : "");
}
i(wt, "printDiffLines");
function Ve(e, t, n) {
  let r = pe(n), [o, s] = Qn(ye(e) ? [] : e, ye(t) ? [] : t, r);
  return wt(o, s, r);
}
i(Ve, "diffLinesUnified");
function _s(e, t, n, r, o) {
  if (ye(e) && ye(n) && (e = [], n = []), ye(t) && ye(r) && (t = [], r = []), e.length !== n.length || t.length !== r.length)
    return Ve(e, t, o);
  let [s, c] = Qn(n, r, o), l = 0, u = 0;
  return s.forEach((m) => {
    switch (m[0]) {
      case z:
        m[1] = e[l], l += 1;
        break;
      case j:
        m[1] = t[u], u += 1;
        break;
      default:
        m[1] = t[u], l += 1, u += 1;
    }
  }), wt(s, c, pe(o));
}
i(_s, "diffLinesUnified2");
function Qn(e, t, n) {
  let r = n?.truncateThreshold ?? !1, o = Math.max(Math.floor(n?.truncateThreshold ?? 0), 0), s = r ? Math.min(e.length, o) : e.length, c = r ?
  Math.min(t.length, o) : t.length, l = s !== e.length || c !== t.length, u = /* @__PURE__ */ i((g, h) => e[g] === t[h], "isCommon"), m = [],
  p = 0, a = 0;
  for (Gn(s, c, u, /* @__PURE__ */ i((g, h, f) => {
    for (; p !== h; p += 1)
      m.push(new P(z, e[p]));
    for (; a !== f; a += 1)
      m.push(new P(j, t[a]));
    for (; g !== 0; g -= 1, p += 1, a += 1)
      m.push(new P(M, t[a]));
  }, "foundSubsequence")); p !== s; p += 1)
    m.push(new P(z, e[p]));
  for (; a !== c; a += 1)
    m.push(new P(j, t[a]));
  return [m, l];
}
i(Qn, "diffLinesRaw");
function Dn(e) {
  if (e === void 0)
    return "undefined";
  if (e === null)
    return "null";
  if (Array.isArray(e))
    return "array";
  if (typeof e == "boolean")
    return "boolean";
  if (typeof e == "function")
    return "function";
  if (typeof e == "number")
    return "number";
  if (typeof e == "string")
    return "string";
  if (typeof e == "bigint")
    return "bigint";
  if (typeof e == "object") {
    if (e != null) {
      if (e.constructor === RegExp)
        return "regexp";
      if (e.constructor === Map)
        return "map";
      if (e.constructor === Set)
        return "set";
      if (e.constructor === Date)
        return "date";
    }
    return "object";
  } else if (typeof e == "symbol")
    return "symbol";
  throw new Error(`value of unknown type: ${e}`);
}
i(Dn, "getType");
function Fn(e) {
  return e.includes(`\r
`) ? `\r
` : `
`;
}
i(Fn, "getNewLineSymbol");
function Ts(e, t, n) {
  let r = n?.truncateThreshold ?? !1, o = Math.max(Math.floor(n?.truncateThreshold ?? 0), 0), s = e.length, c = t.length;
  if (r) {
    let g = e.includes(`
`), h = t.includes(`
`), f = Fn(e), d = Fn(t), b = g ? `${e.split(f, o).join(f)}
` : e, _ = h ? `${t.split(d, o).join(d)}
` : t;
    s = b.length, c = _.length;
  }
  let l = s !== e.length || c !== t.length, u = /* @__PURE__ */ i((g, h) => e[g] === t[h], "isCommon"), m = 0, p = 0, a = [];
  return Gn(s, c, u, /* @__PURE__ */ i((g, h, f) => {
    m !== h && a.push(new P(z, e.slice(m, h))), p !== f && a.push(new P(j, t.slice(p, f))), m = h + g, p = f + g, a.push(new P(M, t.slice(f,
    p)));
  }, "foundSubsequence")), m !== s && a.push(new P(z, e.slice(m))), p !== c && a.push(new P(j, t.slice(p))), [a, l];
}
i(Ts, "diffStrings");
function Cs(e, t, n) {
  return t.reduce((r, o) => r + (o[0] === M ? o[1] : o[0] === e && o[1].length !== 0 ? n(o[1]) : ""), "");
}
i(Cs, "concatenateRelevantDiffs");
var Rt = class Rt {
  op;
  line;
  lines;
  changeColor;
  constructor(t, n) {
    this.op = t, this.line = [], this.lines = [], this.changeColor = n;
  }
  pushSubstring(t) {
    this.pushDiff(new P(this.op, t));
  }
  pushLine() {
    this.lines.push(this.line.length !== 1 ? new P(this.op, Cs(this.op, this.line, this.changeColor)) : this.line[0][0] === this.op ? this.line[0] :
    new P(this.op, this.line[0][1])), this.line.length = 0;
  }
  isLineEmpty() {
    return this.line.length === 0;
  }
  pushDiff(t) {
    this.line.push(t);
  }
  align(t) {
    let n = t[1];
    if (n.includes(`
`)) {
      let r = n.split(`
`), o = r.length - 1;
      r.forEach((s, c) => {
        c < o ? (this.pushSubstring(s), this.pushLine()) : s.length !== 0 && this.pushSubstring(s);
      });
    } else
      this.pushDiff(t);
  }
  moveLinesTo(t) {
    this.isLineEmpty() || this.pushLine(), t.push(...this.lines), this.lines.length = 0;
  }
};
i(Rt, "ChangeBuffer");
var We = Rt, Pt = class Pt {
  deleteBuffer;
  insertBuffer;
  lines;
  constructor(t, n) {
    this.deleteBuffer = t, this.insertBuffer = n, this.lines = [];
  }
  pushDiffCommonLine(t) {
    this.lines.push(t);
  }
  pushDiffChangeLines(t) {
    let n = t[1].length === 0;
    (!n || this.deleteBuffer.isLineEmpty()) && this.deleteBuffer.pushDiff(t), (!n || this.insertBuffer.isLineEmpty()) && this.insertBuffer.pushDiff(
    t);
  }
  flushChangeLines() {
    this.deleteBuffer.moveLinesTo(this.lines), this.insertBuffer.moveLinesTo(this.lines);
  }
  align(t) {
    let n = t[0], r = t[1];
    if (r.includes(`
`)) {
      let o = r.split(`
`), s = o.length - 1;
      o.forEach((c, l) => {
        if (l === 0) {
          let u = new P(n, c);
          this.deleteBuffer.isLineEmpty() && this.insertBuffer.isLineEmpty() ? (this.flushChangeLines(), this.pushDiffCommonLine(u)) : (this.
          pushDiffChangeLines(u), this.flushChangeLines());
        } else l < s ? this.pushDiffCommonLine(new P(n, c)) : c.length !== 0 && this.pushDiffChangeLines(new P(n, c));
      });
    } else
      this.pushDiffChangeLines(t);
  }
  getLines() {
    return this.flushChangeLines(), this.lines;
  }
};
i(Pt, "CommonBuffer");
var _t = Pt;
function Os(e, t) {
  let n = new We(z, t), r = new We(j, t), o = new _t(n, r);
  return e.forEach((s) => {
    switch (s[0]) {
      case z:
        n.align(s);
        break;
      case j:
        r.align(s);
        break;
      default:
        o.align(s);
    }
  }), o.getLines();
}
i(Os, "getAlignedDiffs");
function $s(e, t) {
  if (t) {
    let n = e.length - 1;
    return e.some((r, o) => r[0] === M && (o !== n || r[1] !== `
`));
  }
  return e.some((n) => n[0] === M);
}
i($s, "hasCommonDiff");
function ws(e, t, n) {
  if (e !== t && e.length !== 0 && t.length !== 0) {
    let r = e.includes(`
`) || t.includes(`
`), [o, s] = vn(r ? `${e}
` : e, r ? `${t}
` : t, !0, n);
    if ($s(o, r)) {
      let c = pe(n), l = Os(o, c.changeColor);
      return wt(l, s, c);
    }
  }
  return Ve(e.split(`
`), t.split(`
`), n);
}
i(ws, "diffStringsUnified");
function vn(e, t, n, r) {
  let [o, s] = Ts(e, t, r);
  return n && ss(o), [o, s];
}
i(vn, "diffStringsRaw");
function Tt(e, t) {
  let { commonColor: n } = pe(t);
  return n(e);
}
i(Tt, "getCommonMessage");
var { AsymmetricMatcher: As, DOMCollection: Rs, DOMElement: Ps, Immutable: Ns, ReactElement: Is, ReactTestComponent: Ms } = Te, er = [
  Ms,
  Is,
  Ps,
  Rs,
  Ns,
  As,
  Te.Error
], Ct = {
  maxDepth: 20,
  plugins: er
}, tr = {
  callToJSON: !1,
  maxDepth: 8,
  plugins: er
};
function Ls(e, t, n) {
  if (Object.is(e, t))
    return "";
  let r = Dn(e), o = r, s = !1;
  if (r === "object" && typeof e.asymmetricMatch == "function") {
    if (e.$$typeof !== Symbol.for("jest.asymmetricMatcher") || typeof e.getExpectedType != "function")
      return;
    o = e.getExpectedType(), s = o === "string";
  }
  if (o !== Dn(t)) {
    let d = function(O) {
      return O.length <= f ? O : `${O.slice(0, f)}...`;
    };
    i(d, "truncate");
    let { aAnnotation: c, aColor: l, aIndicator: u, bAnnotation: m, bColor: p, bIndicator: a } = pe(n), S = Ot(tr, n), g = X(e, S), h = X(t,
    S), f = 1e5;
    g = d(g), h = d(h);
    let b = `${l(`${u} ${c}:`)} 
${g}`, _ = `${p(`${a} ${m}:`)} 
${h}`;
    return `${b}

${_}`;
  }
  if (!s)
    switch (r) {
      case "string":
        return Ve(e.split(`
`), t.split(`
`), n);
      case "boolean":
      case "number":
        return xs(e, t, n);
      case "map":
        return Et(jn(e), jn(t), n);
      case "set":
        return Et(kn(e), kn(t), n);
      default:
        return Et(e, t, n);
    }
}
i(Ls, "diff");
function xs(e, t, n) {
  let r = X(e, Ct), o = X(t, Ct);
  return r === o ? "" : Ve(r.split(`
`), o.split(`
`), n);
}
i(xs, "comparePrimitive");
function jn(e) {
  return new Map(Array.from(e.entries()).sort());
}
i(jn, "sortMap");
function kn(e) {
  return new Set(Array.from(e.values()).sort());
}
i(kn, "sortSet");
function Et(e, t, n) {
  let r, o = !1;
  try {
    let c = Ot(Ct, n);
    r = Bn(e, t, c, n);
  } catch {
    o = !0;
  }
  let s = Tt(Kn, n);
  if (r === void 0 || r === s) {
    let c = Ot(tr, n);
    r = Bn(e, t, c, n), r !== s && !o && (r = `${Tt(ls, n)}

${r}`);
  }
  return r;
}
i(Et, "compareObjects");
function Ot(e, t) {
  let { compareKeys: n, printBasicPrototype: r, maxDepth: o } = pe(t);
  return {
    ...e,
    compareKeys: n,
    printBasicPrototype: r,
    maxDepth: o ?? e.maxDepth
  };
}
i(Ot, "getFormatOptions");
function Bn(e, t, n, r) {
  let o = {
    ...n,
    indent: 0
  }, s = X(e, o), c = X(t, o);
  if (s === c)
    return Tt(Kn, r);
  {
    let l = X(e, n), u = X(t, n);
    return _s(l.split(`
`), u.split(`
`), s.split(`
`), c.split(`
`), r);
  }
}
i(Bn, "getObjectsDifference");
var zn = 2e4;
function Yn(e) {
  return ze(e) === "Object" && typeof e.asymmetricMatch == "function";
}
i(Yn, "isAsymmetricMatcher");
function Un(e, t) {
  let n = ze(e), r = ze(t);
  return n === r && (n === "Object" || n === "Array");
}
i(Un, "isReplaceable");
function nr(e, t, n) {
  let { aAnnotation: r, bAnnotation: o } = pe(n);
  if (typeof t == "string" && typeof e == "string" && t.length > 0 && e.length > 0 && t.length <= zn && e.length <= zn && t !== e) {
    if (t.includes(`
`) || e.includes(`
`))
      return ws(t, e, n);
    let [p] = vn(t, e, !0), a = p.some((f) => f[0] === M), S = Ds(r, o), g = S(r) + ks(Wn(p, z, a)), h = S(o) + js(Wn(p, j, a));
    return `${g}
${h}`;
  }
  let s = bt(t, { forceWritable: !0 }), c = bt(e, { forceWritable: !0 }), { replacedExpected: l, replacedActual: u } = rr(c, s);
  return Ls(l, u, n);
}
i(nr, "printDiffOrStringify");
function rr(e, t, n = /* @__PURE__ */ new WeakSet(), r = /* @__PURE__ */ new WeakSet()) {
  return e instanceof Error && t instanceof Error && typeof e.cause < "u" && typeof t.cause > "u" ? (delete e.cause, {
    replacedActual: e,
    replacedExpected: t
  }) : Un(e, t) ? n.has(e) || r.has(t) ? {
    replacedActual: e,
    replacedExpected: t
  } : (n.add(e), r.add(t), yt(t).forEach((o) => {
    let s = t[o], c = e[o];
    if (Yn(s))
      s.asymmetricMatch(c) && (e[o] = s);
    else if (Yn(c))
      c.asymmetricMatch(s) && (t[o] = c);
    else if (Un(c, s)) {
      let l = rr(c, s, n, r);
      e[o] = l.replacedActual, t[o] = l.replacedExpected;
    }
  }), {
    replacedActual: e,
    replacedExpected: t
  }) : {
    replacedActual: e,
    replacedExpected: t
  };
}
i(rr, "replaceAsymmetricMatcher");
function Ds(...e) {
  let t = e.reduce((n, r) => r.length > n ? r.length : n, 0);
  return (n) => `${n}: ${" ".repeat(t - n.length)}`;
}
i(Ds, "getLabelPrinter");
var Fs = "\xB7";
function or(e) {
  return e.replace(/\s+$/gm, (t) => Fs.repeat(t.length));
}
i(or, "replaceTrailingSpaces");
function js(e) {
  return v.red(or(me(e)));
}
i(js, "printReceived");
function ks(e) {
  return v.green(or(me(e)));
}
i(ks, "printExpected");
function Wn(e, t, n) {
  return e.reduce((r, o) => r + (o[0] === M ? o[1] : o[0] === t ? n ? v.inverse(o[1]) : o[1] : ""), "");
}
i(Wn, "getCommonAndChangedSubstrings");

// ../node_modules/@vitest/utils/dist/error.js
var Bs = "@@__IMMUTABLE_RECORD__@@", zs = "@@__IMMUTABLE_ITERABLE__@@";
function Ys(e) {
  return e && (e[zs] || e[Bs]);
}
i(Ys, "isImmutable");
var Us = Object.getPrototypeOf({});
function sr(e) {
  return e instanceof Error ? `<unserializable>: ${e.message}` : typeof e == "string" ? `<unserializable>: ${e}` : "<unserializable>";
}
i(sr, "getUnserializableMessage");
function be(e, t = /* @__PURE__ */ new WeakMap()) {
  if (!e || typeof e == "string")
    return e;
  if (typeof e == "function")
    return `Function<${e.name || "anonymous"}>`;
  if (typeof e == "symbol")
    return e.toString();
  if (typeof e != "object")
    return e;
  if (Ys(e))
    return be(e.toJSON(), t);
  if (e instanceof Promise || e.constructor && e.constructor.prototype === "AsyncFunction")
    return "Promise";
  if (typeof Element < "u" && e instanceof Element)
    return e.tagName;
  if (typeof e.asymmetricMatch == "function")
    return `${e.toString()} ${wn(e.sample)}`;
  if (typeof e.toJSON == "function")
    return be(e.toJSON(), t);
  if (t.has(e))
    return t.get(e);
  if (Array.isArray(e)) {
    let n = new Array(e.length);
    return t.set(e, n), e.forEach((r, o) => {
      try {
        n[o] = be(r, t);
      } catch (s) {
        n[o] = sr(s);
      }
    }), n;
  } else {
    let n = /* @__PURE__ */ Object.create(null);
    t.set(e, n);
    let r = e;
    for (; r && r !== Us; )
      Object.getOwnPropertyNames(r).forEach((o) => {
        if (!(o in n))
          try {
            n[o] = be(e[o], t);
          } catch (s) {
            delete n[o], n[o] = sr(s);
          }
      }), r = Object.getPrototypeOf(r);
    return n;
  }
}
i(be, "serializeValue");
function Ws(e) {
  return e.replace(/__(vite_ssr_import|vi_import)_\d+__\./g, "");
}
i(Ws, "normalizeErrorMessage");
function Nt(e, t, n = /* @__PURE__ */ new WeakSet()) {
  if (!e || typeof e != "object")
    return { message: String(e) };
  let r = e;
  r.stack && (r.stackStr = String(r.stack)), r.name && (r.nameStr = String(r.name)), (r.showDiff || r.showDiff === void 0 && r.expected !== void 0 &&
  r.actual !== void 0) && (r.diff = nr(r.actual, r.expected, {
    ...t,
    ...r.diffOptions
  })), typeof r.expected != "string" && (r.expected = me(r.expected, 10)), typeof r.actual != "string" && (r.actual = me(r.actual, 10));
  try {
    typeof r.message == "string" && (r.message = Ws(r.message));
  } catch {
  }
  try {
    !n.has(r) && typeof r.cause == "object" && (n.add(r), r.cause = Nt(r.cause, t, n));
  } catch {
  }
  try {
    return be(r);
  } catch (o) {
    return be(new Error(`Failed to fully serialize error: ${o?.message}
Inner error message: ${r?.message}`));
  }
}
i(Nt, "processError");

// src/instrumenter/EVENTS.ts
var ne = {
  CALL: "storybook/instrumenter/call",
  SYNC: "storybook/instrumenter/sync",
  START: "storybook/instrumenter/start",
  BACK: "storybook/instrumenter/back",
  GOTO: "storybook/instrumenter/goto",
  NEXT: "storybook/instrumenter/next",
  END: "storybook/instrumenter/end"
};

// src/instrumenter/preview-api.ts
var qe = globalThis.__STORYBOOK_ADDONS_PREVIEW;

// src/instrumenter/types.ts
var Vs = /* @__PURE__ */ ((o) => (o.DONE = "done", o.ERROR = "error", o.ACTIVE = "active", o.WAITING = "waiting", o))(Vs || {});

// src/instrumenter/instrumenter.ts
var Hs = new Error(
  "This function ran after the play function completed. Did you forget to `await` it?"
), cr = /* @__PURE__ */ i((e) => Object.prototype.toString.call(e) === "[object Object]", "isObject"), Js = /* @__PURE__ */ i((e) => Object.
prototype.toString.call(e) === "[object Module]", "isModule"), Xs = /* @__PURE__ */ i((e) => {
  if (!cr(e) && !Js(e))
    return !1;
  if (e.constructor === void 0)
    return !0;
  let t = e.constructor.prototype;
  return !!cr(t);
}, "isInstrumentable"), Zs = /* @__PURE__ */ i((e) => {
  try {
    return new e.constructor();
  } catch {
    return {};
  }
}, "construct"), It = /* @__PURE__ */ i(() => ({
  renderPhase: void 0,
  isDebugging: !1,
  isPlaying: !1,
  isLocked: !1,
  cursor: 0,
  calls: [],
  shadowCalls: [],
  callRefsByResult: /* @__PURE__ */ new Map(),
  chainedCallIds: /* @__PURE__ */ new Set(),
  ancestors: [],
  playUntil: void 0,
  resolvers: {},
  syncTimeout: void 0
}), "getInitialState"), ur = /* @__PURE__ */ i((e, t = !1) => {
  let n = (t ? e.shadowCalls : e.calls).filter((o) => o.retain);
  if (!n.length)
    return;
  let r = new Map(
    Array.from(e.callRefsByResult.entries()).filter(([, o]) => o.retain)
  );
  return { cursor: n.length, calls: n, callRefsByResult: r };
}, "getRetainedState"), Lt = class Lt {
  constructor() {
    this.detached = !1;
    this.initialized = !1;
    // State is tracked per story to deal with multiple stories on the same canvas (i.e. docs mode)
    this.state = {};
    this.loadParentWindowState = /* @__PURE__ */ i(() => {
      try {
        this.state = Z.window?.parent?.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER_STATE__ || {};
      } catch {
        this.detached = !0;
      }
    }, "loadParentWindowState");
    this.updateParentWindowState = /* @__PURE__ */ i(() => {
      try {
        Z.window.parent.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER_STATE__ = this.state;
      } catch {
        this.detached = !0;
      }
    }, "updateParentWindowState");
    this.loadParentWindowState();
    let t = /* @__PURE__ */ i(({
      storyId: u,
      isPlaying: m = !0,
      isDebugging: p = !1
    }) => {
      let a = this.getState(u);
      this.setState(u, {
        ...It(),
        ...ur(a, p),
        shadowCalls: p ? a.shadowCalls : [],
        chainedCallIds: p ? a.chainedCallIds : /* @__PURE__ */ new Set(),
        playUntil: p ? a.playUntil : void 0,
        isPlaying: m,
        isDebugging: p
      }), this.sync(u);
    }, "resetState"), n = /* @__PURE__ */ i((u) => ({ storyId: m, playUntil: p }) => {
      this.getState(m).isDebugging || this.setState(m, ({ calls: S }) => ({
        calls: [],
        shadowCalls: S.map((g) => ({ ...g, status: "waiting" })),
        isDebugging: !0
      }));
      let a = this.getLog(m);
      this.setState(m, ({ shadowCalls: S }) => {
        if (p || !a.length)
          return { playUntil: p };
        let g = S.findIndex((h) => h.id === a[0].callId);
        return {
          playUntil: S.slice(0, g).filter((h) => h.interceptable && !h.ancestors?.length).slice(-1)[0]?.id
        };
      }), u.emit(ir, { storyId: m, isDebugging: !0 });
    }, "start"), r = /* @__PURE__ */ i((u) => ({ storyId: m }) => {
      let p = this.getLog(m).filter((S) => !S.ancestors?.length), a = p.reduceRight((S, g, h) => S >= 0 || g.status === "waiting" ? S : h, -1);
      n(u)({ storyId: m, playUntil: p[a - 1]?.callId });
    }, "back"), o = /* @__PURE__ */ i((u) => ({ storyId: m, callId: p }) => {
      let { calls: a, shadowCalls: S, resolvers: g } = this.getState(m), h = a.find(({ id: d }) => d === p), f = S.find(({ id: d }) => d ===
      p);
      if (!h && f && Object.values(g).length > 0) {
        let d = this.getLog(m).find((b) => b.status === "waiting")?.callId;
        f.id !== d && this.setState(m, { playUntil: f.id }), Object.values(g).forEach((b) => b());
      } else
        n(u)({ storyId: m, playUntil: p });
    }, "goto"), s = /* @__PURE__ */ i((u) => ({ storyId: m }) => {
      let { resolvers: p } = this.getState(m);
      if (Object.values(p).length > 0)
        Object.values(p).forEach((a) => a());
      else {
        let a = this.getLog(m).find((S) => S.status === "waiting")?.callId;
        a ? n(u)({ storyId: m, playUntil: a }) : c({ storyId: m });
      }
    }, "next"), c = /* @__PURE__ */ i(({ storyId: u }) => {
      this.setState(u, { playUntil: void 0, isDebugging: !1 }), Object.values(this.getState(u).resolvers).forEach((m) => m());
    }, "end"), l = /* @__PURE__ */ i(({ storyId: u, newPhase: m }) => {
      let { isDebugging: p } = this.getState(u);
      this.setState(u, { renderPhase: m }), m === "preparing" && p && t({ storyId: u }), m === "playing" && t({ storyId: u, isDebugging: p }),
      m === "played" && this.setState(u, {
        isLocked: !1,
        isPlaying: !1,
        isDebugging: !1
      }), m === "errored" && this.setState(u, {
        isLocked: !1,
        isPlaying: !1
      });
    }, "renderPhaseChanged");
    qe && qe.ready().then(() => {
      this.channel = qe.getChannel(), this.channel.on(ir, t), this.channel.on(Gs, l), this.channel.on(Ks, () => {
        this.initialized ? this.cleanup() : this.initialized = !0;
      }), this.channel.on(ne.START, n(this.channel)), this.channel.on(ne.BACK, r(this.channel)), this.channel.on(ne.GOTO, o(this.channel)), this.
      channel.on(ne.NEXT, s(this.channel)), this.channel.on(ne.END, c);
    });
  }
  getState(t) {
    return this.state[t] || It();
  }
  setState(t, n) {
    let r = this.getState(t), o = typeof n == "function" ? n(r) : n;
    this.state = { ...this.state, [t]: { ...r, ...o } }, this.updateParentWindowState();
  }
  cleanup() {
    this.state = Object.entries(this.state).reduce(
      (r, [o, s]) => {
        let c = ur(s);
        return c && (r[o] = Object.assign(It(), c)), r;
      },
      {}
    );
    let n = { controlStates: {
      detached: this.detached,
      start: !1,
      back: !1,
      goto: !1,
      next: !1,
      end: !1
    }, logItems: [] };
    this.channel?.emit(ne.SYNC, n), this.updateParentWindowState();
  }
  getLog(t) {
    let { calls: n, shadowCalls: r } = this.getState(t), o = [...r];
    n.forEach((c, l) => {
      o[l] = c;
    });
    let s = /* @__PURE__ */ new Set();
    return o.reduceRight((c, l) => (l.args.forEach((u) => {
      u?.__callId__ && s.add(u.__callId__);
    }), l.path.forEach((u) => {
      u.__callId__ && s.add(u.__callId__);
    }), (l.interceptable || l.exception) && !s.has(l.id) && (c.unshift({ callId: l.id, status: l.status, ancestors: l.ancestors }), s.add(l.
    id)), c), []);
  }
  // Traverses the object structure to recursively patch all function properties.
  // Returns the original object, or a new object with the same constructor,
  // depending on whether it should mutate.
  instrument(t, n, r = 0) {
    if (!Xs(t))
      return t;
    let { mutate: o = !1, path: s = [] } = n, c = n.getKeys ? n.getKeys(t, r) : Object.keys(t);
    return r += 1, c.reduce(
      (l, u) => {
        let m = vs(t, u);
        if (typeof m?.get == "function") {
          if (m.configurable) {
            let a = /* @__PURE__ */ i(() => m?.get?.bind(t)?.(), "getter");
            Object.defineProperty(l, u, {
              get: /* @__PURE__ */ i(() => this.instrument(a(), { ...n, path: s.concat(u) }, r), "get")
            });
          }
          return l;
        }
        let p = t[u];
        return typeof p != "function" ? (l[u] = this.instrument(p, { ...n, path: s.concat(u) }, r), l) : "__originalFn__" in p && typeof p.__originalFn__ ==
        "function" ? (l[u] = p, l) : (l[u] = (...a) => this.track(u, p, t, a, n), l[u].__originalFn__ = p, Object.defineProperty(l[u], "name",
        { value: u, writable: !1 }), Object.keys(p).length > 0 && Object.assign(
          l[u],
          this.instrument({ ...p }, { ...n, path: s.concat(u) }, r)
        ), l);
      },
      o ? t : Zs(t)
    );
  }
  // Monkey patch an object method to record calls.
  // Returns a function that invokes the original function, records the invocation ("call") and
  // returns the original result.
  track(t, n, r, o, s) {
    let c = o?.[0]?.__storyId__ || Z.__STORYBOOK_PREVIEW__?.selectionStore?.selection?.storyId, { cursor: l, ancestors: u } = this.getState(
    c);
    this.setState(c, { cursor: l + 1 });
    let m = `${u.slice(-1)[0] || c} [${l}] ${t}`, { path: p = [], intercept: a = !1, retain: S = !1 } = s, g = typeof a == "function" ? a(t,
    p) : a, h = { id: m, cursor: l, storyId: c, ancestors: u, path: p, method: t, args: o, interceptable: g, retain: S }, d = (g && !u.length ?
    this.intercept : this.invoke).call(this, n, r, h, s);
    return this.instrument(d, { ...s, mutate: !0, path: [{ __callId__: h.id }] });
  }
  intercept(t, n, r, o) {
    let { chainedCallIds: s, isDebugging: c, playUntil: l } = this.getState(r.storyId), u = s.has(r.id);
    return !c || u || l ? (l === r.id && this.setState(r.storyId, { playUntil: void 0 }), this.invoke(t, n, r, o)) : new Promise((m) => {
      this.setState(r.storyId, ({ resolvers: p }) => ({
        isLocked: !1,
        resolvers: { ...p, [r.id]: m }
      }));
    }).then(() => (this.setState(r.storyId, (m) => {
      let { [r.id]: p, ...a } = m.resolvers;
      return { isLocked: !0, resolvers: a };
    }), this.invoke(t, n, r, o)));
  }
  invoke(t, n, r, o) {
    let { callRefsByResult: s, renderPhase: c } = this.getState(r.storyId), l = 25, u = /* @__PURE__ */ i((a, S, g) => {
      if (g.includes(a))
        return "[Circular]";
      if (g = [...g, a], S > l)
        return "...";
      if (s.has(a))
        return s.get(a);
      if (a instanceof Array)
        return a.map((h) => u(h, ++S, g));
      if (a instanceof Date)
        return { __date__: { value: a.toISOString() } };
      if (a instanceof Error) {
        let { name: h, message: f, stack: d } = a;
        return { __error__: { name: h, message: f, stack: d } };
      }
      if (a instanceof RegExp) {
        let { flags: h, source: f } = a;
        return { __regexp__: { flags: h, source: f } };
      }
      if (a instanceof Z.window?.HTMLElement) {
        let { prefix: h, localName: f, id: d, classList: b, innerText: _ } = a, O = Array.from(b);
        return { __element__: { prefix: h, localName: f, id: d, classNames: O, innerText: _ } };
      }
      return typeof a == "function" ? {
        __function__: { name: "getMockName" in a ? a.getMockName() : a.name }
      } : typeof a == "symbol" ? { __symbol__: { description: a.description } } : typeof a == "object" && a?.constructor?.name && a?.constructor?.
      name !== "Object" ? { __class__: { name: a.constructor.name } } : Object.prototype.toString.call(a) === "[object Object]" ? Object.fromEntries(
        Object.entries(a).map(([h, f]) => [h, u(f, ++S, g)])
      ) : a;
    }, "serializeValues"), m = {
      ...r,
      args: r.args.map((a) => u(a, 0, []))
    };
    r.path.forEach((a) => {
      a?.__callId__ && this.setState(r.storyId, ({ chainedCallIds: S }) => ({
        chainedCallIds: new Set(Array.from(S).concat(a.__callId__))
      }));
    });
    let p = /* @__PURE__ */ i((a) => {
      if (a instanceof Error) {
        let { name: S, message: g, stack: h, callId: f = r.id } = a, {
          showDiff: d = void 0,
          diff: b = void 0,
          actual: _ = void 0,
          expected: O = void 0
        } = a.name === "AssertionError" ? Nt(a) : a, y = { name: S, message: g, stack: h, callId: f, showDiff: d, diff: b, actual: _, expected: O };
        if (this.update({ ...m, status: "error", exception: y }), this.setState(r.storyId, (E) => ({
          callRefsByResult: new Map([
            ...Array.from(E.callRefsByResult.entries()),
            [a, { __callId__: r.id, retain: r.retain }]
          ])
        })), r.ancestors?.length)
          throw Object.prototype.hasOwnProperty.call(a, "callId") || Object.defineProperty(a, "callId", { value: r.id }), a;
      }
      throw a;
    }, "handleException");
    try {
      if (c === "played" && !r.retain)
        throw Hs;
      let S = (o.getArgs ? o.getArgs(r, this.getState(r.storyId)) : r.args).map((h) => typeof h != "function" || ei(h) || Object.keys(h).length ?
      h : (...f) => {
        let { cursor: d, ancestors: b } = this.getState(r.storyId);
        this.setState(r.storyId, { cursor: 0, ancestors: [...b, r.id] });
        let _ = /* @__PURE__ */ i(() => this.setState(r.storyId, { cursor: d, ancestors: b }), "restore"), O = !1;
        try {
          let y = h(...f);
          return y instanceof Promise ? (O = !0, y.finally(_)) : y;
        } finally {
          O || _();
        }
      }), g = t.apply(n, S);
      return g && ["object", "function", "symbol"].includes(typeof g) && this.setState(r.storyId, (h) => ({
        callRefsByResult: new Map([
          ...Array.from(h.callRefsByResult.entries()),
          [g, { __callId__: r.id, retain: r.retain }]
        ])
      })), this.update({
        ...m,
        status: g instanceof Promise ? "active" : "done"
      }), g instanceof Promise ? g.then((h) => (this.update({ ...m, status: "done" }), h), p) : g;
    } catch (a) {
      return p(a);
    }
  }
  // Sends the call info to the manager and synchronizes the log.
  update(t) {
    this.channel?.emit(ne.CALL, t), this.setState(t.storyId, ({ calls: n }) => {
      let r = n.concat(t).reduce((o, s) => Object.assign(o, { [s.id]: s }), {});
      return {
        // Calls are sorted to ensure parent calls always come before calls in their callback.
        calls: Object.values(r).sort(
          (o, s) => o.id.localeCompare(s.id, void 0, { numeric: !0 })
        )
      };
    }), this.sync(t.storyId);
  }
  // Builds a log of interceptable calls and control states and sends it to the manager.
  // Uses a 0ms debounce because this might get called many times in one tick.
  sync(t) {
    let n = /* @__PURE__ */ i(() => {
      let { isLocked: r, isPlaying: o } = this.getState(t), s = this.getLog(t), c = s.filter(({ ancestors: a }) => !a.length).find((a) => a.
      status === "waiting")?.callId, l = s.some((a) => a.status === "active");
      if (this.detached || r || l || s.length === 0) {
        let S = { controlStates: {
          detached: this.detached,
          start: !1,
          back: !1,
          goto: !1,
          next: !1,
          end: !1
        }, logItems: s };
        this.channel?.emit(ne.SYNC, S);
        return;
      }
      let u = s.some(
        (a) => a.status === "done" || a.status === "error"
      ), p = { controlStates: {
        detached: this.detached,
        start: u,
        back: u,
        goto: !0,
        next: o,
        end: o
      }, logItems: s, pausedAt: c };
      this.channel?.emit(ne.SYNC, p);
    }, "synchronize");
    this.setState(t, ({ syncTimeout: r }) => (clearTimeout(r), { syncTimeout: setTimeout(n, 0) }));
  }
};
i(Lt, "Instrumenter");
var Mt = Lt;
function Qs(e, t = {}) {
  try {
    let n = !1, r = !1;
    return Z.window?.location?.search?.includes("instrument=true") ? n = !0 : Z.window?.location?.search?.includes("instrument=false") && (r =
    !0), Z.window?.parent === Z.window && !n || r ? e : (Z.window && !Z.window.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__ && (Z.window.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__ =
    new Mt()), (Z.window?.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__).instrument(e, t));
  } catch (n) {
    return qs.warn(n), e;
  }
}
i(Qs, "instrument");
function vs(e, t) {
  let n = e;
  for (; n != null; ) {
    let r = Object.getOwnPropertyDescriptor(n, t);
    if (r)
      return r;
    n = Object.getPrototypeOf(n);
  }
}
i(vs, "getPropertyDescriptor");
function ei(e) {
  if (typeof e != "function")
    return !1;
  let t = Object.getOwnPropertyDescriptor(e, "prototype");
  return t ? !t.writable : !1;
}
i(ei, "isClass");
export {
  Vs as CallStates,
  ne as EVENTS,
  Qs as instrument
};
