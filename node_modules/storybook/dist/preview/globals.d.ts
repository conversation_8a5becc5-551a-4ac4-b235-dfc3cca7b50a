declare const globalsNameReferenceMap: {
    readonly '@storybook/global': "__STORYBOOK_MODULE_GLOBAL__";
    readonly 'storybook/test': "__STORYBOOK_MODULE_TEST__";
    readonly 'storybook/actions': "__STORYBOOK_MODULE_ACTIONS__";
    readonly 'storybook/preview-api': "__STORYBOOK_MODULE_PREVIEW_API__";
    readonly 'storybook/internal/channels': "__STORYBOOK_MODULE_CHANNELS__";
    readonly 'storybook/internal/client-logger': "__STORYBOOK_MODULE_CLIENT_LOGGER__";
    readonly 'storybook/internal/core-events': "__STORYBOOK_MODULE_CORE_EVENTS__";
    readonly 'storybook/internal/preview-errors': "__STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__";
    readonly 'storybook/internal/types': "__STORY<PERSON>OK_MODULE_TYPES__";
    readonly 'storybook/internal/preview-api': "__STORYBOOK_MODULE_PREVIEW_API__";
};
declare const globalPackages: Array<keyof typeof globalsNameReferenceMap>;

export { globalPackages, globalsNameReferenceMap };
