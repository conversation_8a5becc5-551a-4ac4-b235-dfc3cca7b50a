'use strict';

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="13167bc1-8b36-59a0-a3b0-bf8d545520cf")}catch(e){}}();

var chunk6VIOZ7VT_js = require('./chunk-6VIOZ7VT.js');
require('./chunk-2E7ZWKIX.js');
require('./chunk-O2POOKSN.js');
require('./chunk-IM5VGDJQ.js');
require('./chunk-LTE3MQL2.js');
require('./chunk-VWVWVLKU.js');
var chunk6IZZOM5T_js = require('./chunk-6IZZOM5T.js');
require('./chunk-LZXDNZPW.js');
require('./chunk-TKGT252T.js');

function f(e){var s;if(e.message&&(e.message=chunk6IZZOM5T_js.a(e.message)),(s=e.exception)!=null&&s.values)for(let[p,n]of e.exception.values.entries())n.value&&(e.exception.values[p].value=chunk6IZZOM5T_js.a(n.value));return e}function l(e){if(e.category==="console"){if(e.message==="")return null;e.message&&(e.message=chunk6IZZOM5T_js.a(e.message));}return e}chunk6VIOZ7VT_js.c({dsn:"https://<EMAIL>/4507930289373184",release:"12.2.0",dist:"cli",sampleRate:1,environment:"production",enabled:process.env.DISABLE_ERROR_MONITORING!=="true"&&!0,enableTracing:!1,integrations:[],initialScope:{tags:{version:process.env.npm_package_version,index_url:process.env.CHROMATIC_INDEX_URL}},beforeSend:f,beforeBreadcrumb:l});async function g(e){try{let{code:s}=await chunk6VIOZ7VT_js.e({argv:e});process.exitCode=s;}catch(s){chunk6VIOZ7VT_js.a(s);}finally{await chunk6VIOZ7VT_js.b(2500),process.exit();}}

exports.main = g;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=main-GSFOO65I.js.map
//# debugId=13167bc1-8b36-59a0-a3b0-bf8d545520cf
