---
description: 'Disallow `require` statements except in import statements.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-var-requires** for documentation.

:::danger Deprecated

This rule has been deprecated in favour of the [`@typescript-eslint/no-require-imports`](./no-require-imports.mdx) rule.

:::

In other words, the use of forms such as `var foo = require("foo")` are banned. Instead use ES6 style imports or `import foo = require("foo")` imports.

## Examples

<Tabs>
<TabItem value="❌ Incorrect">

```ts
var foo = require('foo');
const foo = require('foo');
let foo = require('foo');
```

</TabItem>
<TabItem value="✅ Correct">

```ts
import foo = require('foo');
require('foo');
import foo from 'foo';
```

</TabItem>
</Tabs>

## Options

### `allow`

{/* insert option description */}

A array of strings. These strings will be compiled into regular expressions with the `u` flag and be used to test against the imported path. A common use case is to allow importing `package.json`. This is because `package.json` commonly lives outside of the TS root directory, so statically importing it would lead to root directory conflicts, especially with `resolveJsonModule` enabled. You can also use it to allow importing any JSON if your environment doesn't support JSON modules, or use it for other cases where `import` statements cannot work.

With `{allow: ['/package\\.json$']}`:

<Tabs>
<TabItem value="❌ Incorrect">

```ts option='{ "allow": ["/package.json$"] }'
const foo = require('../data.json');
```

</TabItem>
<TabItem value="✅ Correct">

```ts option='{ "allow": ["/package.json$"] }'
const foo = require('../package.json');
```

</TabItem>
</Tabs>

## When Not To Use It

If your project frequently uses older CommonJS `require`s, then this rule might not be applicable to you.
If only a subset of your project uses `require`s then you might consider using [ESLint disable comments](https://eslint.org/docs/latest/use/configure/rules#using-configuration-comments-1) for those specific situations instead of completely disabling this rule.

## Related To

- [`no-require-imports`](./no-require-imports.mdx)
