type Values = 'always' | 'in-intersections' | 'in-unions' | 'in-unions-and-intersections' | 'never';
export type Options = [
    {
        allowAliases?: Values;
        allowCallbacks?: 'always' | 'never';
        allowConditionalTypes?: 'always' | 'never';
        allowConstructors?: 'always' | 'never';
        allowGenerics?: 'always' | 'never';
        allowLiterals?: Values;
        allowMappedTypes?: Values;
        allowTupleTypes?: Values;
    }
];
export type MessageIds = 'noCompositionAlias' | 'noTypeAlias';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-type-alias.d.ts.map