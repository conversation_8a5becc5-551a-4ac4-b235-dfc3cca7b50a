{"name": "@storybook/addon-a11y", "version": "9.0.12", "description": "Test component compliance with web accessibility standards", "keywords": ["a11y", "accessibility", "addon", "storybook", "valid", "verify", "test"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/a11y", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/a11y"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./manager": "./dist/manager.js", "./register": "./dist/manager.js", "./package.json": "./package.json", "./postinstall": "./dist/postinstall.js"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "axe-core": "^4.2.0"}, "devDependencies": {"@radix-ui/react-tabs": "1.0.4", "@storybook/icons": "^1.4.0", "@testing-library/react": "^14.0.0", "execa": "^9.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-resize-detector": "^7.1.2", "typescript": "^5.8.3", "vitest-axe": "^0.1.0"}, "peerDependencies": {"storybook": "^9.0.12"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.tsx"], "nodeEntries": ["./src/postinstall.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Accessibility", "icon": "https://user-images.githubusercontent.com/263385/101991665-47042f80-3c7c-11eb-8f00-64b5a18f498a.png", "unsupportedFrameworks": ["react-native"]}}