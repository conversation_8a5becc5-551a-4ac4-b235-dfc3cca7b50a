{"version": 3, "file": "IndentedWriter.js", "sourceRoot": "", "sources": ["../../src/generators/IndentedWriter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,oEAAkF;AAElF;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,MAAa,cAAc;IAoDzB,YAAmB,OAAwB;QAnD3C;;;WAGG;QACI,wBAAmB,GAAW,MAAM,CAAC;QAE5C;;WAEG;QACI,qBAAgB,GAAY,KAAK,CAAC;QAEzC;;;;;;;;;;;;;;;;;;;;;;;;;WAyBG;QACI,sBAAiB,GAAY,KAAK,CAAC;QAexC,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,iCAAa,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;QACtE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,YAAqB;QACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7F,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,cAAc;QACnB,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,KAAiB,EAAE,YAAqB;QACzD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAClC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,MAAM,aAAa,GAAW,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvD,IAAI,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,iBAAiB;QACtB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;OAGG;IACI,uBAAuB;QAC5B,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAe;QAC1B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,sEAAsE;QACtE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,4DAA4D;QAC5D,IAAI,KAAK,GAAY,IAAI,CAAC;QAC1B,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,KAAK,CAAC;YAChB,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,UAAkB,EAAE;QACnC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe;QACpC,IAAI,cAAc,GAAW,OAAO,CAAC;QAErC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAClD,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBACnC,CAAC;YACH,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9B,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,CAAS;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;CACF;AAjPD,wCAiPC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\nimport { StringBuilder, type IStringBuilder } from '@rushstack/node-core-library';\n\n/**\n * A utility for writing indented text.\n *\n * @remarks\n *\n * Note that the indentation is inserted at the last possible opportunity.\n * For example, this code...\n *\n * ```ts\n *   writer.write('begin\\n');\n *   writer.increaseIndent();\n *   writer.write('one\\ntwo\\n');\n *   writer.decreaseIndent();\n *   writer.increaseIndent();\n *   writer.decreaseIndent();\n *   writer.write('end');\n * ```\n *\n * ...would produce this output:\n *\n * ```\n *   begin\n *     one\n *     two\n *   end\n * ```\n */\nexport class IndentedWriter {\n  /**\n   * The text characters used to create one level of indentation.\n   * Two spaces by default.\n   */\n  public defaultIndentPrefix: string = '    ';\n\n  /**\n   * Whether to indent blank lines\n   */\n  public indentBlankLines: boolean = false;\n\n  /**\n   * Trims leading spaces from the input text before applying the indent.\n   *\n   * @remarks\n   * Consider the following example:\n   *\n   * ```ts\n   * indentedWriter.increaseIndent('    '); // four spaces\n   * indentedWriter.write('  a\\n  b  c\\n');\n   * indentedWriter.decreaseIndent();\n   * ```\n   *\n   * Normally the output would be indented by 6 spaces: 4 from `increaseIndent()`, plus the 2 spaces\n   * from `write()`:\n   * ```\n   *       a\n   *       b  c\n   * ```\n   *\n   * Setting `trimLeadingSpaces=true` will trim the leading spaces, so that the lines are indented\n   * by 4 spaces only:\n   * ```\n   *     a\n   *     b  c\n   * ```\n   */\n  public trimLeadingSpaces: boolean = false;\n\n  private readonly _builder: IStringBuilder;\n\n  private _latestChunk: string | undefined;\n  private _previousChunk: string | undefined;\n  private _atStartOfLine: boolean;\n\n  private readonly _indentStack: string[];\n  private _indentText: string;\n\n  private _previousLineIsBlank: boolean;\n  private _currentLineIsBlank: boolean;\n\n  public constructor(builder?: IStringBuilder) {\n    this._builder = builder === undefined ? new StringBuilder() : builder;\n    this._latestChunk = undefined;\n    this._previousChunk = undefined;\n    this._atStartOfLine = true;\n    this._previousLineIsBlank = true;\n    this._currentLineIsBlank = true;\n\n    this._indentStack = [];\n    this._indentText = '';\n  }\n\n  /**\n   * Retrieves the output that was built so far.\n   */\n  public getText(): string {\n    return this._builder.toString();\n  }\n\n  public toString(): string {\n    return this.getText();\n  }\n\n  /**\n   * Increases the indentation.  Normally the indentation is two spaces,\n   * however an arbitrary prefix can optional be specified.  (For example,\n   * the prefix could be \"// \" to indent and comment simultaneously.)\n   * Each call to IndentedWriter.increaseIndent() must be followed by a\n   * corresponding call to IndentedWriter.decreaseIndent().\n   */\n  public increaseIndent(indentPrefix?: string): void {\n    this._indentStack.push(indentPrefix !== undefined ? indentPrefix : this.defaultIndentPrefix);\n    this._updateIndentText();\n  }\n\n  /**\n   * Decreases the indentation, reverting the effect of the corresponding call\n   * to IndentedWriter.increaseIndent().\n   */\n  public decreaseIndent(): void {\n    this._indentStack.pop();\n    this._updateIndentText();\n  }\n\n  /**\n   * A shorthand for ensuring that increaseIndent()/decreaseIndent() occur\n   * in pairs.\n   */\n  public indentScope(scope: () => void, indentPrefix?: string): void {\n    this.increaseIndent(indentPrefix);\n    scope();\n    this.decreaseIndent();\n  }\n\n  /**\n   * Adds a newline if the file pointer is not already at the start of the line (or start of the stream).\n   */\n  public ensureNewLine(): void {\n    const lastCharacter: string = this.peekLastCharacter();\n    if (lastCharacter !== '\\n' && lastCharacter !== '') {\n      this._writeNewLine();\n    }\n  }\n\n  /**\n   * Adds up to two newlines to ensure that there is a blank line above the current position.\n   * The start of the stream is considered to be a blank line, so `ensureSkippedLine()` has no effect\n   * unless some text has been written.\n   */\n  public ensureSkippedLine(): void {\n    this.ensureNewLine();\n    if (!this._previousLineIsBlank) {\n      this._writeNewLine();\n    }\n  }\n\n  /**\n   * Returns the last character that was written, or an empty string if no characters have been written yet.\n   */\n  public peekLastCharacter(): string {\n    if (this._latestChunk !== undefined) {\n      return this._latestChunk.substr(-1, 1);\n    }\n    return '';\n  }\n\n  /**\n   * Returns the second to last character that was written, or an empty string if less than one characters\n   * have been written yet.\n   */\n  public peekSecondLastCharacter(): string {\n    if (this._latestChunk !== undefined) {\n      if (this._latestChunk.length > 1) {\n        return this._latestChunk.substr(-2, 1);\n      }\n      if (this._previousChunk !== undefined) {\n        return this._previousChunk.substr(-1, 1);\n      }\n    }\n    return '';\n  }\n\n  /**\n   * Writes some text to the internal string buffer, applying indentation according\n   * to the current indentation level.  If the string contains multiple newlines,\n   * each line will be indented separately.\n   */\n  public write(message: string): void {\n    if (message.length === 0) {\n      return;\n    }\n\n    // If there are no newline characters, then append the string verbatim\n    if (!/[\\r\\n]/.test(message)) {\n      this._writeLinePart(message);\n      return;\n    }\n\n    // Otherwise split the lines and write each one individually\n    let first: boolean = true;\n    for (const linePart of message.split('\\n')) {\n      if (!first) {\n        this._writeNewLine();\n      } else {\n        first = false;\n      }\n      if (linePart) {\n        this._writeLinePart(linePart.replace(/[\\r]/g, ''));\n      }\n    }\n  }\n\n  /**\n   * A shorthand for writing an optional message, followed by a newline.\n   * Indentation is applied following the semantics of IndentedWriter.write().\n   */\n  public writeLine(message: string = ''): void {\n    if (message.length > 0) {\n      this.write(message);\n    }\n    this._writeNewLine();\n  }\n\n  /**\n   * Writes a string that does not contain any newline characters.\n   */\n  private _writeLinePart(message: string): void {\n    let trimmedMessage: string = message;\n\n    if (this.trimLeadingSpaces && this._atStartOfLine) {\n      trimmedMessage = message.replace(/^ +/, '');\n    }\n\n    if (trimmedMessage.length > 0) {\n      if (this._atStartOfLine && this._indentText.length > 0) {\n        this._write(this._indentText);\n      }\n      this._write(trimmedMessage);\n      if (this._currentLineIsBlank) {\n        if (/\\S/.test(trimmedMessage)) {\n          this._currentLineIsBlank = false;\n        }\n      }\n      this._atStartOfLine = false;\n    }\n  }\n\n  private _writeNewLine(): void {\n    if (this.indentBlankLines) {\n      if (this._atStartOfLine && this._indentText.length > 0) {\n        this._write(this._indentText);\n      }\n    }\n\n    this._previousLineIsBlank = this._currentLineIsBlank;\n    this._write('\\n');\n    this._currentLineIsBlank = true;\n    this._atStartOfLine = true;\n  }\n\n  private _write(s: string): void {\n    this._previousChunk = this._latestChunk;\n    this._latestChunk = s;\n    this._builder.append(s);\n  }\n\n  private _updateIndentText(): void {\n    this._indentText = this._indentStack.join('');\n  }\n}\n"]}