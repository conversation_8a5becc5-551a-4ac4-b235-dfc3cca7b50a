import type { ReleaseTag } from '@microsoft/api-extractor-model';
/**
 * Constructor parameters for `SymbolMetadata`.
 */
export interface ISymbolMetadataOptions {
    maxEffectiveReleaseTag: ReleaseTag;
}
/**
 * Stores the Collector's additional analysis for an `AstSymbol`.  This object is assigned to `AstSymbol.metadata`
 * but consumers must always obtain it by calling `Collector.fetchSymbolMetadata()`.
 */
export declare class SymbolMetadata {
    readonly maxEffectiveReleaseTag: ReleaseTag;
    constructor(options: ISymbolMetadataOptions);
}
//# sourceMappingURL=SymbolMetadata.d.ts.map