{"version": 3, "file": "ApiPropertySignature.js", "sourceRoot": "", "sources": ["../../src/model/ApiPropertySignature.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,kGAKiE;AACjE,8CAA+C;AAC/C,8DAAyF;AAQzF;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,oBAAqB,SAAQ,iCAAe;IACvD,YAAmB,OAAqC;QACtD,KAAK,CAAC,OAAO,CAAC,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,IAAY;QACxC,OAAO,GAAG,IAAI,IAAI,qBAAW,CAAC,iBAAiB,EAAE,CAAC;IACpD,CAAC;IAED,gBAAgB;IAChB,IAAW,IAAI;QACb,OAAO,qBAAW,CAAC,iBAAiB,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACrB,OAAO,oBAAoB,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,sBAAsB;IACf,uBAAuB;QAC5B,MAAM,aAAa,GAAc,2CAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,2CAAoB,CAAC,KAAK,EAAE,CAAC;aACjF,iBAAiB,CAAC,iCAAU,CAAC,OAAO,EAAE,aAAa,CAAC;aACpD,WAAW,CAAC,8BAAO,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;CACF;AA1BD,oDA0BC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport {\n  DeclarationReference,\n  Meaning,\n  Navigation,\n  type Component\n} from '@microsoft/tsdoc/lib-commonjs/beta/DeclarationReference';\nimport { ApiItemKind } from '../items/ApiItem';\nimport { ApiPropertyItem, type IApiPropertyItemOptions } from '../items/ApiPropertyItem';\n\n/**\n * Constructor options for {@link ApiPropertySignature}.\n * @public\n */\nexport interface IApiPropertySignatureOptions extends IApiPropertyItemOptions {}\n\n/**\n * Represents a TypeScript property declaration that belongs to an `ApiInterface`.\n *\n * @remarks\n *\n * This is part of the {@link ApiModel} hierarchy of classes, which are serializable representations of\n * API declarations.\n *\n * `ApiPropertySignature` represents a TypeScript declaration such as the `width` and `height` members in this example:\n *\n * ```ts\n * export interface IWidget {\n *   readonly width: number;\n *   height: number;\n * }\n * ```\n *\n * Compare with {@link ApiProperty}, which represents a property belonging to a class.\n * For example, a class property can be `static` but an interface property cannot.\n *\n * @public\n */\nexport class ApiPropertySignature extends ApiPropertyItem {\n  public constructor(options: IApiPropertySignatureOptions) {\n    super(options);\n  }\n\n  public static getContainerKey(name: string): string {\n    return `${name}|${ApiItemKind.PropertySignature}`;\n  }\n\n  /** @override */\n  public get kind(): ApiItemKind {\n    return ApiItemKind.PropertySignature;\n  }\n\n  /** @override */\n  public get containerKey(): string {\n    return ApiPropertySignature.getContainerKey(this.name);\n  }\n\n  /** @beta @override */\n  public buildCanonicalReference(): DeclarationReference {\n    const nameComponent: Component = DeclarationReference.parseComponent(this.name);\n    return (this.parent ? this.parent.canonicalReference : DeclarationReference.empty())\n      .addNavigationStep(Navigation.Members, nameComponent)\n      .withMeaning(Meaning.Member);\n  }\n}\n"]}