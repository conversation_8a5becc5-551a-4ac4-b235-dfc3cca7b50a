{"version": 3, "file": "ApiTypeAlias.js", "sourceRoot": "", "sources": ["../../src/model/ApiTypeAlias.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,kGAKiE;AAEjE,8CAA+C;AAC/C,8DAIkC;AAClC,qEAAmG;AACnG,yDAAiF;AACjF,mFAI6C;AAE7C,iEAIoC;AAsBpC;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAa,YAAa,SAAQ,IAAA,qDAAyB,EACzD,IAAA,2BAAY,EAAC,IAAA,uCAAkB,EAAC,IAAA,mCAAgB,EAAC,iCAAe,CAAC,CAAC,CAAC,CACpE;IAcC,YAAmB,OAA6B;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC/D,CAAC;IAED,gBAAgB;IACT,MAAM,CAAC,iBAAiB,CAC7B,OAAsC,EACtC,OAA4B,EAC5B,UAA6B;QAE7B,KAAK,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAEtD,OAAO,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;IACrD,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,IAAY;QACxC,OAAO,GAAG,IAAI,IAAI,qBAAW,CAAC,SAAS,EAAE,CAAC;IAC5C,CAAC;IAED,gBAAgB;IAChB,IAAW,IAAI;QACb,OAAO,qBAAW,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACrB,OAAO,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,gBAAgB;IACT,aAAa,CAAC,UAAsC;QACzD,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAEhC,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IAC1D,CAAC;IAED,sBAAsB;IACf,uBAAuB;QAC5B,MAAM,aAAa,GAAc,2CAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,MAAM,UAAU,GAAe,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,iCAAU,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAU,CAAC,MAAM,CAAC;QACxF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,2CAAoB,CAAC,KAAK,EAAE,CAAC;aACjF,iBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC;aAC5C,WAAW,CAAC,8BAAO,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;CACF;AA9DD,oCA8DC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport {\n  DeclarationReference,\n  Meaning,\n  Navigation,\n  type Component\n} from '@microsoft/tsdoc/lib-commonjs/beta/DeclarationReference';\nimport type { Excerpt, IExcerptTokenRange } from '../mixins/Excerpt';\nimport { ApiItemKind } from '../items/ApiItem';\nimport {\n  ApiDeclaredItem,\n  type IApiDeclaredItemOptions,\n  type IApiDeclaredItemJson\n} from '../items/ApiDeclaredItem';\nimport { ApiReleaseTagMixin, type IApiReleaseTagMixinOptions } from '../mixins/ApiReleaseTagMixin';\nimport { type IApiNameMixinOptions, ApiNameMixin } from '../mixins/ApiNameMixin';\nimport {\n  ApiTypeParameterListMixin,\n  type IApiTypeParameterListMixinOptions,\n  type IApiTypeParameterListMixinJson\n} from '../mixins/ApiTypeParameterListMixin';\nimport type { DeserializerContext } from './DeserializerContext';\nimport {\n  type IApiExportedMixinJson,\n  type IApiExportedMixinOptions,\n  ApiExportedMixin\n} from '../mixins/ApiExportedMixin';\n\n/**\n * Constructor options for {@link ApiTypeAlias}.\n * @public\n */\nexport interface IApiTypeAliasOptions\n  extends IApiNameMixinOptions,\n    IApiReleaseTagMixinOptions,\n    IApiDeclaredItemOptions,\n    IApiTypeParameterListMixinOptions,\n    IApiExportedMixinOptions {\n  typeTokenRange: IExcerptTokenRange;\n}\n\nexport interface IApiTypeAliasJson\n  extends IApiDeclaredItemJson,\n    IApiTypeParameterListMixinJson,\n    IApiExportedMixinJson {\n  typeTokenRange: IExcerptTokenRange;\n}\n\n/**\n * Represents a TypeScript type alias declaration.\n *\n * @remarks\n *\n * This is part of the {@link ApiModel} hierarchy of classes, which are serializable representations of\n * API declarations.\n *\n * `ApiTypeAlias` represents a definition such as one of these examples:\n *\n * ```ts\n * // A union type:\n * export type Shape = Square | Triangle | Circle;\n *\n * // A generic type alias:\n * export type BoxedValue<T> = { value: T };\n *\n * export type BoxedArray<T> = { array: T[] };\n *\n * // A conditional type alias:\n * export type Boxed<T> = T extends any[] ? BoxedArray<T[number]> : BoxedValue<T>;\n *\n * ```\n *\n * @public\n */\nexport class ApiTypeAlias extends ApiTypeParameterListMixin(\n  ApiNameMixin(ApiReleaseTagMixin(ApiExportedMixin(ApiDeclaredItem)))\n) {\n  /**\n   * An {@link Excerpt} that describes the type of the alias.\n   *\n   * @remarks\n   * In the example below, the `typeExcerpt` would correspond to the subexpression\n   * `T extends any[] ? BoxedArray<T[number]> : BoxedValue<T>;`:\n   *\n   * ```ts\n   * export type Boxed<T> = T extends any[] ? BoxedArray<T[number]> : BoxedValue<T>;\n   * ```\n   */\n  public readonly typeExcerpt: Excerpt;\n\n  public constructor(options: IApiTypeAliasOptions) {\n    super(options);\n\n    this.typeExcerpt = this.buildExcerpt(options.typeTokenRange);\n  }\n\n  /** @override */\n  public static onDeserializeInto(\n    options: Partial<IApiTypeAliasOptions>,\n    context: DeserializerContext,\n    jsonObject: IApiTypeAliasJson\n  ): void {\n    super.onDeserializeInto(options, context, jsonObject);\n\n    options.typeTokenRange = jsonObject.typeTokenRange;\n  }\n\n  public static getContainerKey(name: string): string {\n    return `${name}|${ApiItemKind.TypeAlias}`;\n  }\n\n  /** @override */\n  public get kind(): ApiItemKind {\n    return ApiItemKind.TypeAlias;\n  }\n\n  /** @override */\n  public get containerKey(): string {\n    return ApiTypeAlias.getContainerKey(this.name);\n  }\n\n  /** @override */\n  public serializeInto(jsonObject: Partial<IApiTypeAliasJson>): void {\n    super.serializeInto(jsonObject);\n\n    jsonObject.typeTokenRange = this.typeExcerpt.tokenRange;\n  }\n\n  /** @beta @override */\n  public buildCanonicalReference(): DeclarationReference {\n    const nameComponent: Component = DeclarationReference.parseComponent(this.name);\n    const navigation: Navigation = this.isExported ? Navigation.Exports : Navigation.Locals;\n    return (this.parent ? this.parent.canonicalReference : DeclarationReference.empty())\n      .addNavigationStep(navigation, nameComponent)\n      .withMeaning(Meaning.TypeAlias);\n  }\n}\n"]}