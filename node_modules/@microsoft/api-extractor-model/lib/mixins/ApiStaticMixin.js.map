{"version": 3, "file": "ApiStaticMixin.js", "sourceRoot": "", "sources": ["../../src/mixins/ApiStaticMixin.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAuD3D,wCAuCC;AA3ED,MAAM,SAAS,GAAkB,MAAM,CAAC,0BAA0B,CAAC,CAAC;AA4BpE;;;;;;;GAOG;AACH,SAAgB,cAAc,CAC5B,SAAqB;AACrB,8DAA8D;;IAE9D,MAAM,UAAW,SAAQ,SAAS;QAGhC,8DAA8D;QAC9D,YAAmB,GAAG,IAAW;YAC/B,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;YAEf,MAAM,OAAO,GAA2B,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC;QACrC,CAAC;QAED,gBAAgB;QACT,MAAM,CAAC,iBAAiB,CAC7B,OAAwC,EACxC,OAA4B,EAC5B,UAA+B;YAE/B,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE1D,OAAO,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QACzC,CAAC;QAED,IAAW,QAAQ;YACjB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;QAED,gBAAgB;QACT,aAAa,CAAC,UAAwC;YAC3D,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAEhC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACtC,CAAC;KACF;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;GAGG;AACH,WAAiB,cAAc;IAC7B;;;;;;;;OAQG;IACH,SAAgB,aAAa,CAAC,OAAgB;QAC5C,OAAO,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAFe,4BAAa,gBAE5B,CAAA;AACH,CAAC,EAbgB,cAAc,8BAAd,cAAc,QAa9B", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON>ICEN<PERSON> in the project root for license information.\n\n/* eslint-disable @typescript-eslint/no-redeclare */\n\nimport type { ApiItem, IApiItemJson, IApiItemConstructor, IApiItemOptions } from '../items/ApiItem';\nimport type { DeserializerContext } from '../model/DeserializerContext';\n\n/**\n * Constructor options for {@link (IApiStaticMixinOptions:interface)}.\n * @public\n */\nexport interface IApiStaticMixinOptions extends IApiItemOptions {\n  isStatic: boolean;\n}\n\nexport interface IApiStaticMixinJson extends IApiItemJson {\n  isStatic: boolean;\n}\n\nconst _isStatic: unique symbol = Symbol('ApiStaticMixin._isStatic');\n\n/**\n * The mixin base class for API items that can have the TypeScript `static` keyword applied to them.\n *\n * @remarks\n *\n * This is part of the {@link ApiModel} hierarchy of classes, which are serializable representations of\n * API declarations.  The non-abstract classes (e.g. `ApiClass`, `ApiEnum`, `ApiInterface`, etc.) use\n * TypeScript \"mixin\" functions (e.g. `ApiDeclaredItem`, `ApiItemContainerMixin`, etc.) to add various\n * features that cannot be represented as a normal inheritance chain (since TypeScript does not allow a child class\n * to extend more than one base class).  The \"mixin\" is a TypeScript merged declaration with three components:\n * the function that generates a subclass, an interface that describes the members of the subclass, and\n * a namespace containing static members of the class.\n *\n * @public\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport interface ApiStaticMixin extends ApiItem {\n  /**\n   * Whether the declaration has the TypeScript `static` keyword.\n   */\n  readonly isStatic: boolean;\n\n  /** @override */\n  serializeInto(jsonObject: Partial<IApiItemJson>): void;\n}\n\n/**\n * Mixin function for {@link (ApiStaticMixin:interface)}.\n *\n * @param baseClass - The base class to be extended\n * @returns A child class that extends baseClass, adding the {@link (ApiStaticMixin:interface)} functionality.\n *\n * @public\n */\nexport function ApiStaticMixin<TBaseClass extends IApiItemConstructor>(\n  baseClass: TBaseClass\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n): TBaseClass & (new (...args: any[]) => ApiStaticMixin) {\n  class MixedClass extends baseClass implements ApiStaticMixin {\n    public [_isStatic]: boolean;\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public constructor(...args: any[]) {\n      super(...args);\n\n      const options: IApiStaticMixinOptions = args[0];\n      this[_isStatic] = options.isStatic;\n    }\n\n    /** @override */\n    public static onDeserializeInto(\n      options: Partial<IApiStaticMixinOptions>,\n      context: DeserializerContext,\n      jsonObject: IApiStaticMixinJson\n    ): void {\n      baseClass.onDeserializeInto(options, context, jsonObject);\n\n      options.isStatic = jsonObject.isStatic;\n    }\n\n    public get isStatic(): boolean {\n      return this[_isStatic];\n    }\n\n    /** @override */\n    public serializeInto(jsonObject: Partial<IApiStaticMixinJson>): void {\n      super.serializeInto(jsonObject);\n\n      jsonObject.isStatic = this.isStatic;\n    }\n  }\n\n  return MixedClass;\n}\n\n/**\n * Static members for {@link (ApiStaticMixin:interface)}.\n * @public\n */\nexport namespace ApiStaticMixin {\n  /**\n   * A type guard that tests whether the specified `ApiItem` subclass extends the `ApiStaticMixin` mixin.\n   *\n   * @remarks\n   *\n   * The JavaScript `instanceof` operator cannot be used to test for mixin inheritance, because each invocation of\n   * the mixin function produces a different subclass.  (This could be mitigated by `Symbol.hasInstance`, however\n   * the TypeScript type system cannot invoke a runtime test.)\n   */\n  export function isBaseClassOf(apiItem: ApiItem): apiItem is ApiStaticMixin {\n    return apiItem.hasOwnProperty(_isStatic);\n  }\n}\n"]}