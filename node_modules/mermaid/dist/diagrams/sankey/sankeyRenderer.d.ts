import type { Diagram } from '../../Diagram.js';
/**
 * Draws Sankey diagram.
 *
 * @param text - The text of the diagram
 * @param id - The id of the diagram which will be used as a DOM element id¨
 * @param _version - Mermaid version from package.json
 * @param diagObj - A standard diagram containing the db and the text and type etc of the diagram
 */
export declare const draw: (text: string, id: string, _version: string, diagObj: Diagram) => void;
declare const _default: {
    draw: (text: string, id: string, _version: string, diagObj: Diagram) => void;
};
export default _default;
