export function setConf(cnf: any): void;
export function drawBoundary(diagram: any, boundary: any, bounds: any): void;
export function drawC4ShapeArray(currentBounds: any, diagram: any, c4ShapeArray: any, c4ShapeKeys: any): void;
export function drawRels(diagram: any, rels: any, getC4ShapeObj: any, diagObj: any): void;
export function draw(_text: any, id: any, _version: any, diagObj: any): void;
declare namespace _default {
    export { drawC4ShapeArray as drawPersonOrSystemArray };
    export { drawBoundary };
    export { setConf };
    export { draw };
}
export default _default;
