import type cytoscape from 'cytoscape';
import type { D3Element } from '../../types.js';
import { type ArchitectureDB, type ArchitectureJunction, type ArchitectureService } from './architectureTypes.js';
export declare const drawEdges: (edgesEl: D3Element, cy: cytoscape.Core) => Promise<void>;
export declare const drawGroups: (groupsEl: D3Element, cy: cytoscape.Core) => Promise<void>;
export declare const drawServices: (db: ArchitectureDB, elem: D3Element, services: ArchitectureService[]) => Promise<number>;
export declare const drawJunctions: (db: ArchitectureDB, elem: D3Element, junctions: ArchitectureJunction[]) => void;
