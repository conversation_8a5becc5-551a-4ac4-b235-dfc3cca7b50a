/**
 * Create a standard string for the dom ID of an item.
 * If a type is given, insert that before the counter, preceded by the type spacer
 *
 * @param itemId
 * @param counter
 * @param {string | null} type
 * @param typeSpacer
 * @returns {string}
 */
export function stateDomId(itemId?: string, counter?: number, type?: string | null, typeSpacer?: string): string;
export function dataFetcher(parent: any, parsedItem: any, diagramStates: any, nodes: any, edges: any, altFlag: any, look: any, classes: any): void;
export function reset(): void;
