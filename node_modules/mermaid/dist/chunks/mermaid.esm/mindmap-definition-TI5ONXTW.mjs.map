{"version": 3, "sources": ["../../../../../node_modules/.pnpm/layout-base@1.0.2/node_modules/layout-base/layout-base.js", "../../../../../node_modules/.pnpm/cose-base@1.0.3/node_modules/cose-base/cose-base.js", "../../../../../node_modules/.pnpm/cytoscape-cose-bilkent@4.1.0_cytoscape@3.31.0/node_modules/cytoscape-cose-bilkent/cytoscape-cose-bilkent.js", "../../../src/diagrams/mindmap/parser/mindmap.jison", "../../../src/diagrams/mindmap/mindmapDb.ts", "../../../src/diagrams/mindmap/mindmapRenderer.ts", "../../../src/diagrams/mindmap/svgDraw.ts", "../../../src/diagrams/mindmap/styles.ts", "../../../src/diagrams/mindmap/mindmap-definition.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"layoutBase\"] = factory();\n\telse\n\t\troot[\"layoutBase\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 26);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LayoutConstants() {}\n\n/**\r\n * Layout Quality: 0:draft, 1:default, 2:proof\r\n */\nLayoutConstants.QUALITY = 1;\n\n/**\r\n * Default parameters\r\n */\nLayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED = false;\nLayoutConstants.DEFAULT_INCREMENTAL = false;\nLayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT = true;\nLayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT = false;\nLayoutConstants.DEFAULT_ANIMATION_PERIOD = 50;\nLayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES = false;\n\n// -----------------------------------------------------------------------------\n// Section: General other constants\n// -----------------------------------------------------------------------------\n/*\r\n * Margins of a graph to be applied on bouding rectangle of its contents. We\r\n * assume margins on all four sides to be uniform.\r\n */\nLayoutConstants.DEFAULT_GRAPH_MARGIN = 15;\n\n/*\r\n * Whether to consider labels in node dimensions or not\r\n */\nLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = false;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_SIZE = 40;\n\n/*\r\n * Default dimension of a non-compound node.\r\n */\nLayoutConstants.SIMPLE_NODE_HALF_SIZE = LayoutConstants.SIMPLE_NODE_SIZE / 2;\n\n/*\r\n * Empty compound node size. When a compound node is empty, its both\r\n * dimensions should be of this value.\r\n */\nLayoutConstants.EMPTY_COMPOUND_NODE_SIZE = 40;\n\n/*\r\n * Minimum length that an edge should take during layout\r\n */\nLayoutConstants.MIN_EDGE_LENGTH = 1;\n\n/*\r\n * World boundaries that layout operates on\r\n */\nLayoutConstants.WORLD_BOUNDARY = 1000000;\n\n/*\r\n * World boundaries that random positioning can be performed with\r\n */\nLayoutConstants.INITIAL_WORLD_BOUNDARY = LayoutConstants.WORLD_BOUNDARY / 1000;\n\n/*\r\n * Coordinates of the world center\r\n */\nLayoutConstants.WORLD_CENTER_X = 1200;\nLayoutConstants.WORLD_CENTER_Y = 900;\n\nmodule.exports = LayoutConstants;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction LEdge(source, target, vEdge) {\n  LGraphObject.call(this, vEdge);\n\n  this.isOverlapingSourceAndTarget = false;\n  this.vGraphObject = vEdge;\n  this.bendpoints = [];\n  this.source = source;\n  this.target = target;\n}\n\nLEdge.prototype = Object.create(LGraphObject.prototype);\n\nfor (var prop in LGraphObject) {\n  LEdge[prop] = LGraphObject[prop];\n}\n\nLEdge.prototype.getSource = function () {\n  return this.source;\n};\n\nLEdge.prototype.getTarget = function () {\n  return this.target;\n};\n\nLEdge.prototype.isInterGraph = function () {\n  return this.isInterGraph;\n};\n\nLEdge.prototype.getLength = function () {\n  return this.length;\n};\n\nLEdge.prototype.isOverlapingSourceAndTarget = function () {\n  return this.isOverlapingSourceAndTarget;\n};\n\nLEdge.prototype.getBendpoints = function () {\n  return this.bendpoints;\n};\n\nLEdge.prototype.getLca = function () {\n  return this.lca;\n};\n\nLEdge.prototype.getSourceInLca = function () {\n  return this.sourceInLca;\n};\n\nLEdge.prototype.getTargetInLca = function () {\n  return this.targetInLca;\n};\n\nLEdge.prototype.getOtherEnd = function (node) {\n  if (this.source === node) {\n    return this.target;\n  } else if (this.target === node) {\n    return this.source;\n  } else {\n    throw \"Node is not incident with this edge\";\n  }\n};\n\nLEdge.prototype.getOtherEndInGraph = function (node, graph) {\n  var otherEnd = this.getOtherEnd(node);\n  var root = graph.getGraphManager().getRoot();\n\n  while (true) {\n    if (otherEnd.getOwner() == graph) {\n      return otherEnd;\n    }\n\n    if (otherEnd.getOwner() == root) {\n      break;\n    }\n\n    otherEnd = otherEnd.getOwner().getParent();\n  }\n\n  return null;\n};\n\nLEdge.prototype.updateLength = function () {\n  var clipPointCoordinates = new Array(4);\n\n  this.isOverlapingSourceAndTarget = IGeometry.getIntersection(this.target.getRect(), this.source.getRect(), clipPointCoordinates);\n\n  if (!this.isOverlapingSourceAndTarget) {\n    this.lengthX = clipPointCoordinates[0] - clipPointCoordinates[2];\n    this.lengthY = clipPointCoordinates[1] - clipPointCoordinates[3];\n\n    if (Math.abs(this.lengthX) < 1.0) {\n      this.lengthX = IMath.sign(this.lengthX);\n    }\n\n    if (Math.abs(this.lengthY) < 1.0) {\n      this.lengthY = IMath.sign(this.lengthY);\n    }\n\n    this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n  }\n};\n\nLEdge.prototype.updateLengthSimple = function () {\n  this.lengthX = this.target.getCenterX() - this.source.getCenterX();\n  this.lengthY = this.target.getCenterY() - this.source.getCenterY();\n\n  if (Math.abs(this.lengthX) < 1.0) {\n    this.lengthX = IMath.sign(this.lengthX);\n  }\n\n  if (Math.abs(this.lengthY) < 1.0) {\n    this.lengthY = IMath.sign(this.lengthY);\n  }\n\n  this.length = Math.sqrt(this.lengthX * this.lengthX + this.lengthY * this.lengthY);\n};\n\nmodule.exports = LEdge;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction LGraphObject(vGraphObject) {\n  this.vGraphObject = vGraphObject;\n}\n\nmodule.exports = LGraphObject;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar RectangleD = __webpack_require__(13);\nvar LayoutConstants = __webpack_require__(0);\nvar RandomSeed = __webpack_require__(16);\nvar PointD = __webpack_require__(4);\n\nfunction LNode(gm, loc, size, vNode) {\n  //Alternative constructor 1 : LNode(LGraphManager gm, Point loc, Dimension size, Object vNode)\n  if (size == null && vNode == null) {\n    vNode = loc;\n  }\n\n  LGraphObject.call(this, vNode);\n\n  //Alternative constructor 2 : LNode(Layout layout, Object vNode)\n  if (gm.graphManager != null) gm = gm.graphManager;\n\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.inclusionTreeDepth = Integer.MAX_VALUE;\n  this.vGraphObject = vNode;\n  this.edges = [];\n  this.graphManager = gm;\n\n  if (size != null && loc != null) this.rect = new RectangleD(loc.x, loc.y, size.width, size.height);else this.rect = new RectangleD();\n}\n\nLNode.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LNode[prop] = LGraphObject[prop];\n}\n\nLNode.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLNode.prototype.getChild = function () {\n  return this.child;\n};\n\nLNode.prototype.getOwner = function () {\n  //  if (this.owner != null) {\n  //    if (!(this.owner == null || this.owner.getNodes().indexOf(this) > -1)) {\n  //      throw \"assert failed\";\n  //    }\n  //  }\n\n  return this.owner;\n};\n\nLNode.prototype.getWidth = function () {\n  return this.rect.width;\n};\n\nLNode.prototype.setWidth = function (width) {\n  this.rect.width = width;\n};\n\nLNode.prototype.getHeight = function () {\n  return this.rect.height;\n};\n\nLNode.prototype.setHeight = function (height) {\n  this.rect.height = height;\n};\n\nLNode.prototype.getCenterX = function () {\n  return this.rect.x + this.rect.width / 2;\n};\n\nLNode.prototype.getCenterY = function () {\n  return this.rect.y + this.rect.height / 2;\n};\n\nLNode.prototype.getCenter = function () {\n  return new PointD(this.rect.x + this.rect.width / 2, this.rect.y + this.rect.height / 2);\n};\n\nLNode.prototype.getLocation = function () {\n  return new PointD(this.rect.x, this.rect.y);\n};\n\nLNode.prototype.getRect = function () {\n  return this.rect;\n};\n\nLNode.prototype.getDiagonal = function () {\n  return Math.sqrt(this.rect.width * this.rect.width + this.rect.height * this.rect.height);\n};\n\n/**\n * This method returns half the diagonal length of this node.\n */\nLNode.prototype.getHalfTheDiagonal = function () {\n  return Math.sqrt(this.rect.height * this.rect.height + this.rect.width * this.rect.width) / 2;\n};\n\nLNode.prototype.setRect = function (upperLeft, dimension) {\n  this.rect.x = upperLeft.x;\n  this.rect.y = upperLeft.y;\n  this.rect.width = dimension.width;\n  this.rect.height = dimension.height;\n};\n\nLNode.prototype.setCenter = function (cx, cy) {\n  this.rect.x = cx - this.rect.width / 2;\n  this.rect.y = cy - this.rect.height / 2;\n};\n\nLNode.prototype.setLocation = function (x, y) {\n  this.rect.x = x;\n  this.rect.y = y;\n};\n\nLNode.prototype.moveBy = function (dx, dy) {\n  this.rect.x += dx;\n  this.rect.y += dy;\n};\n\nLNode.prototype.getEdgeListToNode = function (to) {\n  var edgeList = [];\n  var edge;\n  var self = this;\n\n  self.edges.forEach(function (edge) {\n\n    if (edge.target == to) {\n      if (edge.source != self) throw \"Incorrect edge source!\";\n\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getEdgesBetween = function (other) {\n  var edgeList = [];\n  var edge;\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (!(edge.source == self || edge.target == self)) throw \"Incorrect edge source and/or target\";\n\n    if (edge.target == other || edge.source == other) {\n      edgeList.push(edge);\n    }\n  });\n\n  return edgeList;\n};\n\nLNode.prototype.getNeighborsList = function () {\n  var neighbors = new Set();\n\n  var self = this;\n  self.edges.forEach(function (edge) {\n\n    if (edge.source == self) {\n      neighbors.add(edge.target);\n    } else {\n      if (edge.target != self) {\n        throw \"Incorrect incidency!\";\n      }\n\n      neighbors.add(edge.source);\n    }\n  });\n\n  return neighbors;\n};\n\nLNode.prototype.withChildren = function () {\n  var withNeighborsList = new Set();\n  var childNode;\n  var children;\n\n  withNeighborsList.add(this);\n\n  if (this.child != null) {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n      children = childNode.withChildren();\n      children.forEach(function (node) {\n        withNeighborsList.add(node);\n      });\n    }\n  }\n\n  return withNeighborsList;\n};\n\nLNode.prototype.getNoOfChildren = function () {\n  var noOfChildren = 0;\n  var childNode;\n\n  if (this.child == null) {\n    noOfChildren = 1;\n  } else {\n    var nodes = this.child.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      childNode = nodes[i];\n\n      noOfChildren += childNode.getNoOfChildren();\n    }\n  }\n\n  if (noOfChildren == 0) {\n    noOfChildren = 1;\n  }\n  return noOfChildren;\n};\n\nLNode.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLNode.prototype.calcEstimatedSize = function () {\n  if (this.child == null) {\n    return this.estimatedSize = (this.rect.width + this.rect.height) / 2;\n  } else {\n    this.estimatedSize = this.child.calcEstimatedSize();\n    this.rect.width = this.estimatedSize;\n    this.rect.height = this.estimatedSize;\n\n    return this.estimatedSize;\n  }\n};\n\nLNode.prototype.scatter = function () {\n  var randomCenterX;\n  var randomCenterY;\n\n  var minX = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxX = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterX = LayoutConstants.WORLD_CENTER_X + RandomSeed.nextDouble() * (maxX - minX) + minX;\n\n  var minY = -LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  var maxY = LayoutConstants.INITIAL_WORLD_BOUNDARY;\n  randomCenterY = LayoutConstants.WORLD_CENTER_Y + RandomSeed.nextDouble() * (maxY - minY) + minY;\n\n  this.rect.x = randomCenterX;\n  this.rect.y = randomCenterY;\n};\n\nLNode.prototype.updateBounds = function () {\n  if (this.getChild() == null) {\n    throw \"assert failed\";\n  }\n  if (this.getChild().getNodes().length != 0) {\n    // wrap the children nodes by re-arranging the boundaries\n    var childGraph = this.getChild();\n    childGraph.updateBounds(true);\n\n    this.rect.x = childGraph.getLeft();\n    this.rect.y = childGraph.getTop();\n\n    this.setWidth(childGraph.getRight() - childGraph.getLeft());\n    this.setHeight(childGraph.getBottom() - childGraph.getTop());\n\n    // Update compound bounds considering its label properties    \n    if (LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS) {\n\n      var width = childGraph.getRight() - childGraph.getLeft();\n      var height = childGraph.getBottom() - childGraph.getTop();\n\n      if (this.labelWidth > width) {\n        this.rect.x -= (this.labelWidth - width) / 2;\n        this.setWidth(this.labelWidth);\n      }\n\n      if (this.labelHeight > height) {\n        if (this.labelPos == \"center\") {\n          this.rect.y -= (this.labelHeight - height) / 2;\n        } else if (this.labelPos == \"top\") {\n          this.rect.y -= this.labelHeight - height;\n        }\n        this.setHeight(this.labelHeight);\n      }\n    }\n  }\n};\n\nLNode.prototype.getInclusionTreeDepth = function () {\n  if (this.inclusionTreeDepth == Integer.MAX_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.inclusionTreeDepth;\n};\n\nLNode.prototype.transform = function (trans) {\n  var left = this.rect.x;\n\n  if (left > LayoutConstants.WORLD_BOUNDARY) {\n    left = LayoutConstants.WORLD_BOUNDARY;\n  } else if (left < -LayoutConstants.WORLD_BOUNDARY) {\n    left = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var top = this.rect.y;\n\n  if (top > LayoutConstants.WORLD_BOUNDARY) {\n    top = LayoutConstants.WORLD_BOUNDARY;\n  } else if (top < -LayoutConstants.WORLD_BOUNDARY) {\n    top = -LayoutConstants.WORLD_BOUNDARY;\n  }\n\n  var leftTop = new PointD(left, top);\n  var vLeftTop = trans.inverseTransformPoint(leftTop);\n\n  this.setLocation(vLeftTop.x, vLeftTop.y);\n};\n\nLNode.prototype.getLeft = function () {\n  return this.rect.x;\n};\n\nLNode.prototype.getRight = function () {\n  return this.rect.x + this.rect.width;\n};\n\nLNode.prototype.getTop = function () {\n  return this.rect.y;\n};\n\nLNode.prototype.getBottom = function () {\n  return this.rect.y + this.rect.height;\n};\n\nLNode.prototype.getParent = function () {\n  if (this.owner == null) {\n    return null;\n  }\n\n  return this.owner.getParent();\n};\n\nmodule.exports = LNode;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction PointD(x, y) {\n  if (x == null && y == null) {\n    this.x = 0;\n    this.y = 0;\n  } else {\n    this.x = x;\n    this.y = y;\n  }\n}\n\nPointD.prototype.getX = function () {\n  return this.x;\n};\n\nPointD.prototype.getY = function () {\n  return this.y;\n};\n\nPointD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nPointD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nPointD.prototype.getDifference = function (pt) {\n  return new DimensionD(this.x - pt.x, this.y - pt.y);\n};\n\nPointD.prototype.getCopy = function () {\n  return new PointD(this.x, this.y);\n};\n\nPointD.prototype.translate = function (dim) {\n  this.x += dim.width;\n  this.y += dim.height;\n  return this;\n};\n\nmodule.exports = PointD;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphObject = __webpack_require__(2);\nvar Integer = __webpack_require__(10);\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(6);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar RectangleD = __webpack_require__(13);\nvar Point = __webpack_require__(12);\nvar LinkedList = __webpack_require__(11);\n\nfunction LGraph(parent, obj2, vGraph) {\n  LGraphObject.call(this, vGraph);\n  this.estimatedSize = Integer.MIN_VALUE;\n  this.margin = LayoutConstants.DEFAULT_GRAPH_MARGIN;\n  this.edges = [];\n  this.nodes = [];\n  this.isConnected = false;\n  this.parent = parent;\n\n  if (obj2 != null && obj2 instanceof LGraphManager) {\n    this.graphManager = obj2;\n  } else if (obj2 != null && obj2 instanceof Layout) {\n    this.graphManager = obj2.graphManager;\n  }\n}\n\nLGraph.prototype = Object.create(LGraphObject.prototype);\nfor (var prop in LGraphObject) {\n  LGraph[prop] = LGraphObject[prop];\n}\n\nLGraph.prototype.getNodes = function () {\n  return this.nodes;\n};\n\nLGraph.prototype.getEdges = function () {\n  return this.edges;\n};\n\nLGraph.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLGraph.prototype.getParent = function () {\n  return this.parent;\n};\n\nLGraph.prototype.getLeft = function () {\n  return this.left;\n};\n\nLGraph.prototype.getRight = function () {\n  return this.right;\n};\n\nLGraph.prototype.getTop = function () {\n  return this.top;\n};\n\nLGraph.prototype.getBottom = function () {\n  return this.bottom;\n};\n\nLGraph.prototype.isConnected = function () {\n  return this.isConnected;\n};\n\nLGraph.prototype.add = function (obj1, sourceNode, targetNode) {\n  if (sourceNode == null && targetNode == null) {\n    var newNode = obj1;\n    if (this.graphManager == null) {\n      throw \"Graph has no graph mgr!\";\n    }\n    if (this.getNodes().indexOf(newNode) > -1) {\n      throw \"Node already in graph!\";\n    }\n    newNode.owner = this;\n    this.getNodes().push(newNode);\n\n    return newNode;\n  } else {\n    var newEdge = obj1;\n    if (!(this.getNodes().indexOf(sourceNode) > -1 && this.getNodes().indexOf(targetNode) > -1)) {\n      throw \"Source or target not in graph!\";\n    }\n\n    if (!(sourceNode.owner == targetNode.owner && sourceNode.owner == this)) {\n      throw \"Both owners must be this graph!\";\n    }\n\n    if (sourceNode.owner != targetNode.owner) {\n      return null;\n    }\n\n    // set source and target\n    newEdge.source = sourceNode;\n    newEdge.target = targetNode;\n\n    // set as intra-graph edge\n    newEdge.isInterGraph = false;\n\n    // add to graph edge list\n    this.getEdges().push(newEdge);\n\n    // add to incidency lists\n    sourceNode.edges.push(newEdge);\n\n    if (targetNode != sourceNode) {\n      targetNode.edges.push(newEdge);\n    }\n\n    return newEdge;\n  }\n};\n\nLGraph.prototype.remove = function (obj) {\n  var node = obj;\n  if (obj instanceof LNode) {\n    if (node == null) {\n      throw \"Node is null!\";\n    }\n    if (!(node.owner != null && node.owner == this)) {\n      throw \"Owner graph is invalid!\";\n    }\n    if (this.graphManager == null) {\n      throw \"Owner graph manager is invalid!\";\n    }\n    // remove incident edges first (make a copy to do it safely)\n    var edgesToBeRemoved = node.edges.slice();\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n\n      if (edge.isInterGraph) {\n        this.graphManager.remove(edge);\n      } else {\n        edge.source.owner.remove(edge);\n      }\n    }\n\n    // now the node itself\n    var index = this.nodes.indexOf(node);\n    if (index == -1) {\n      throw \"Node not in owner node list!\";\n    }\n\n    this.nodes.splice(index, 1);\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n    if (!(edge.source.owner != null && edge.target.owner != null && edge.source.owner == this && edge.target.owner == this)) {\n      throw \"Source and/or target owner is invalid!\";\n    }\n\n    var sourceIndex = edge.source.edges.indexOf(edge);\n    var targetIndex = edge.target.edges.indexOf(edge);\n    if (!(sourceIndex > -1 && targetIndex > -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    edge.source.edges.splice(sourceIndex, 1);\n\n    if (edge.target != edge.source) {\n      edge.target.edges.splice(targetIndex, 1);\n    }\n\n    var index = edge.source.owner.getEdges().indexOf(edge);\n    if (index == -1) {\n      throw \"Not in owner's edge list!\";\n    }\n\n    edge.source.owner.getEdges().splice(index, 1);\n  }\n};\n\nLGraph.prototype.updateLeftTop = function () {\n  var top = Integer.MAX_VALUE;\n  var left = Integer.MAX_VALUE;\n  var nodeTop;\n  var nodeLeft;\n  var margin;\n\n  var nodes = this.getNodes();\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeTop = lNode.getTop();\n    nodeLeft = lNode.getLeft();\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n  }\n\n  // Do we have any nodes in this graph?\n  if (top == Integer.MAX_VALUE) {\n    return null;\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = left - margin;\n  this.top = top - margin;\n\n  // Apply the margins and return the result\n  return new Point(this.left, this.top);\n};\n\nLGraph.prototype.updateBounds = function (recursive) {\n  // calculate bounds\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n  var margin;\n\n  var nodes = this.nodes;\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n\n    if (recursive && lNode.child != null) {\n      lNode.updateBounds();\n    }\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n  if (left == Integer.MAX_VALUE) {\n    this.left = this.parent.getLeft();\n    this.right = this.parent.getRight();\n    this.top = this.parent.getTop();\n    this.bottom = this.parent.getBottom();\n  }\n\n  if (nodes[0].getParent().paddingLeft != undefined) {\n    margin = nodes[0].getParent().paddingLeft;\n  } else {\n    margin = this.margin;\n  }\n\n  this.left = boundingRect.x - margin;\n  this.right = boundingRect.x + boundingRect.width + margin;\n  this.top = boundingRect.y - margin;\n  this.bottom = boundingRect.y + boundingRect.height + margin;\n};\n\nLGraph.calculateBounds = function (nodes) {\n  var left = Integer.MAX_VALUE;\n  var right = -Integer.MAX_VALUE;\n  var top = Integer.MAX_VALUE;\n  var bottom = -Integer.MAX_VALUE;\n  var nodeLeft;\n  var nodeRight;\n  var nodeTop;\n  var nodeBottom;\n\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    nodeLeft = lNode.getLeft();\n    nodeRight = lNode.getRight();\n    nodeTop = lNode.getTop();\n    nodeBottom = lNode.getBottom();\n\n    if (left > nodeLeft) {\n      left = nodeLeft;\n    }\n\n    if (right < nodeRight) {\n      right = nodeRight;\n    }\n\n    if (top > nodeTop) {\n      top = nodeTop;\n    }\n\n    if (bottom < nodeBottom) {\n      bottom = nodeBottom;\n    }\n  }\n\n  var boundingRect = new RectangleD(left, top, right - left, bottom - top);\n\n  return boundingRect;\n};\n\nLGraph.prototype.getInclusionTreeDepth = function () {\n  if (this == this.graphManager.getRoot()) {\n    return 1;\n  } else {\n    return this.parent.getInclusionTreeDepth();\n  }\n};\n\nLGraph.prototype.getEstimatedSize = function () {\n  if (this.estimatedSize == Integer.MIN_VALUE) {\n    throw \"assert failed\";\n  }\n  return this.estimatedSize;\n};\n\nLGraph.prototype.calcEstimatedSize = function () {\n  var size = 0;\n  var nodes = this.nodes;\n  var s = nodes.length;\n\n  for (var i = 0; i < s; i++) {\n    var lNode = nodes[i];\n    size += lNode.calcEstimatedSize();\n  }\n\n  if (size == 0) {\n    this.estimatedSize = LayoutConstants.EMPTY_COMPOUND_NODE_SIZE;\n  } else {\n    this.estimatedSize = size / Math.sqrt(this.nodes.length);\n  }\n\n  return this.estimatedSize;\n};\n\nLGraph.prototype.updateConnected = function () {\n  var self = this;\n  if (this.nodes.length == 0) {\n    this.isConnected = true;\n    return;\n  }\n\n  var queue = new LinkedList();\n  var visited = new Set();\n  var currentNode = this.nodes[0];\n  var neighborEdges;\n  var currentNeighbor;\n  var childrenOfNode = currentNode.withChildren();\n  childrenOfNode.forEach(function (node) {\n    queue.push(node);\n    visited.add(node);\n  });\n\n  while (queue.length !== 0) {\n    currentNode = queue.shift();\n\n    // Traverse all neighbors of this node\n    neighborEdges = currentNode.getEdges();\n    var size = neighborEdges.length;\n    for (var i = 0; i < size; i++) {\n      var neighborEdge = neighborEdges[i];\n      currentNeighbor = neighborEdge.getOtherEndInGraph(currentNode, this);\n\n      // Add unvisited neighbors to the list to visit\n      if (currentNeighbor != null && !visited.has(currentNeighbor)) {\n        var childrenOfNeighbor = currentNeighbor.withChildren();\n\n        childrenOfNeighbor.forEach(function (node) {\n          queue.push(node);\n          visited.add(node);\n        });\n      }\n    }\n  }\n\n  this.isConnected = false;\n\n  if (visited.size >= this.nodes.length) {\n    var noOfVisitedInThisGraph = 0;\n\n    visited.forEach(function (visitedNode) {\n      if (visitedNode.owner == self) {\n        noOfVisitedInThisGraph++;\n      }\n    });\n\n    if (noOfVisitedInThisGraph == this.nodes.length) {\n      this.isConnected = true;\n    }\n  }\n};\n\nmodule.exports = LGraph;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph;\nvar LEdge = __webpack_require__(1);\n\nfunction LGraphManager(layout) {\n  LGraph = __webpack_require__(5); // It may be better to initilize this out of this function but it gives an error (Right-hand side of 'instanceof' is not callable) now.\n  this.layout = layout;\n\n  this.graphs = [];\n  this.edges = [];\n}\n\nLGraphManager.prototype.addRoot = function () {\n  var ngraph = this.layout.newGraph();\n  var nnode = this.layout.newNode(null);\n  var root = this.add(ngraph, nnode);\n  this.setRootGraph(root);\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.add = function (newGraph, parentNode, newEdge, sourceNode, targetNode) {\n  //there are just 2 parameters are passed then it adds an LGraph else it adds an LEdge\n  if (newEdge == null && sourceNode == null && targetNode == null) {\n    if (newGraph == null) {\n      throw \"Graph is null!\";\n    }\n    if (parentNode == null) {\n      throw \"Parent node is null!\";\n    }\n    if (this.graphs.indexOf(newGraph) > -1) {\n      throw \"Graph already in this graph mgr!\";\n    }\n\n    this.graphs.push(newGraph);\n\n    if (newGraph.parent != null) {\n      throw \"Already has a parent!\";\n    }\n    if (parentNode.child != null) {\n      throw \"Already has a child!\";\n    }\n\n    newGraph.parent = parentNode;\n    parentNode.child = newGraph;\n\n    return newGraph;\n  } else {\n    //change the order of the parameters\n    targetNode = newEdge;\n    sourceNode = parentNode;\n    newEdge = newGraph;\n    var sourceGraph = sourceNode.getOwner();\n    var targetGraph = targetNode.getOwner();\n\n    if (!(sourceGraph != null && sourceGraph.getGraphManager() == this)) {\n      throw \"Source not in this graph mgr!\";\n    }\n    if (!(targetGraph != null && targetGraph.getGraphManager() == this)) {\n      throw \"Target not in this graph mgr!\";\n    }\n\n    if (sourceGraph == targetGraph) {\n      newEdge.isInterGraph = false;\n      return sourceGraph.add(newEdge, sourceNode, targetNode);\n    } else {\n      newEdge.isInterGraph = true;\n\n      // set source and target\n      newEdge.source = sourceNode;\n      newEdge.target = targetNode;\n\n      // add edge to inter-graph edge list\n      if (this.edges.indexOf(newEdge) > -1) {\n        throw \"Edge already in inter-graph edge list!\";\n      }\n\n      this.edges.push(newEdge);\n\n      // add edge to source and target incidency lists\n      if (!(newEdge.source != null && newEdge.target != null)) {\n        throw \"Edge source and/or target is null!\";\n      }\n\n      if (!(newEdge.source.edges.indexOf(newEdge) == -1 && newEdge.target.edges.indexOf(newEdge) == -1)) {\n        throw \"Edge already in source and/or target incidency list!\";\n      }\n\n      newEdge.source.edges.push(newEdge);\n      newEdge.target.edges.push(newEdge);\n\n      return newEdge;\n    }\n  }\n};\n\nLGraphManager.prototype.remove = function (lObj) {\n  if (lObj instanceof LGraph) {\n    var graph = lObj;\n    if (graph.getGraphManager() != this) {\n      throw \"Graph not in this graph mgr\";\n    }\n    if (!(graph == this.rootGraph || graph.parent != null && graph.parent.graphManager == this)) {\n      throw \"Invalid parent node!\";\n    }\n\n    // first the edges (make a copy to do it safely)\n    var edgesToBeRemoved = [];\n\n    edgesToBeRemoved = edgesToBeRemoved.concat(graph.getEdges());\n\n    var edge;\n    var s = edgesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      edge = edgesToBeRemoved[i];\n      graph.remove(edge);\n    }\n\n    // then the nodes (make a copy to do it safely)\n    var nodesToBeRemoved = [];\n\n    nodesToBeRemoved = nodesToBeRemoved.concat(graph.getNodes());\n\n    var node;\n    s = nodesToBeRemoved.length;\n    for (var i = 0; i < s; i++) {\n      node = nodesToBeRemoved[i];\n      graph.remove(node);\n    }\n\n    // check if graph is the root\n    if (graph == this.rootGraph) {\n      this.setRootGraph(null);\n    }\n\n    // now remove the graph itself\n    var index = this.graphs.indexOf(graph);\n    this.graphs.splice(index, 1);\n\n    // also reset the parent of the graph\n    graph.parent = null;\n  } else if (lObj instanceof LEdge) {\n    edge = lObj;\n    if (edge == null) {\n      throw \"Edge is null!\";\n    }\n    if (!edge.isInterGraph) {\n      throw \"Not an inter-graph edge!\";\n    }\n    if (!(edge.source != null && edge.target != null)) {\n      throw \"Source and/or target is null!\";\n    }\n\n    // remove edge from source and target nodes' incidency lists\n\n    if (!(edge.source.edges.indexOf(edge) != -1 && edge.target.edges.indexOf(edge) != -1)) {\n      throw \"Source and/or target doesn't know this edge!\";\n    }\n\n    var index = edge.source.edges.indexOf(edge);\n    edge.source.edges.splice(index, 1);\n    index = edge.target.edges.indexOf(edge);\n    edge.target.edges.splice(index, 1);\n\n    // remove edge from owner graph manager's inter-graph edge list\n\n    if (!(edge.source.owner != null && edge.source.owner.getGraphManager() != null)) {\n      throw \"Edge owner graph or owner graph manager is null!\";\n    }\n    if (edge.source.owner.getGraphManager().edges.indexOf(edge) == -1) {\n      throw \"Not in owner graph manager's edge list!\";\n    }\n\n    var index = edge.source.owner.getGraphManager().edges.indexOf(edge);\n    edge.source.owner.getGraphManager().edges.splice(index, 1);\n  }\n};\n\nLGraphManager.prototype.updateBounds = function () {\n  this.rootGraph.updateBounds(true);\n};\n\nLGraphManager.prototype.getGraphs = function () {\n  return this.graphs;\n};\n\nLGraphManager.prototype.getAllNodes = function () {\n  if (this.allNodes == null) {\n    var nodeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < s; i++) {\n      nodeList = nodeList.concat(graphs[i].getNodes());\n    }\n    this.allNodes = nodeList;\n  }\n  return this.allNodes;\n};\n\nLGraphManager.prototype.resetAllNodes = function () {\n  this.allNodes = null;\n};\n\nLGraphManager.prototype.resetAllEdges = function () {\n  this.allEdges = null;\n};\n\nLGraphManager.prototype.resetAllNodesToApplyGravitation = function () {\n  this.allNodesToApplyGravitation = null;\n};\n\nLGraphManager.prototype.getAllEdges = function () {\n  if (this.allEdges == null) {\n    var edgeList = [];\n    var graphs = this.getGraphs();\n    var s = graphs.length;\n    for (var i = 0; i < graphs.length; i++) {\n      edgeList = edgeList.concat(graphs[i].getEdges());\n    }\n\n    edgeList = edgeList.concat(this.edges);\n\n    this.allEdges = edgeList;\n  }\n  return this.allEdges;\n};\n\nLGraphManager.prototype.getAllNodesToApplyGravitation = function () {\n  return this.allNodesToApplyGravitation;\n};\n\nLGraphManager.prototype.setAllNodesToApplyGravitation = function (nodeList) {\n  if (this.allNodesToApplyGravitation != null) {\n    throw \"assert failed\";\n  }\n\n  this.allNodesToApplyGravitation = nodeList;\n};\n\nLGraphManager.prototype.getRoot = function () {\n  return this.rootGraph;\n};\n\nLGraphManager.prototype.setRootGraph = function (graph) {\n  if (graph.getGraphManager() != this) {\n    throw \"Root not in this graph mgr!\";\n  }\n\n  this.rootGraph = graph;\n  // root graph must have a root node associated with it for convenience\n  if (graph.parent == null) {\n    graph.parent = this.layout.newNode(\"Root node\");\n  }\n};\n\nLGraphManager.prototype.getLayout = function () {\n  return this.layout;\n};\n\nLGraphManager.prototype.isOneAncestorOfOther = function (firstNode, secondNode) {\n  if (!(firstNode != null && secondNode != null)) {\n    throw \"assert failed\";\n  }\n\n  if (firstNode == secondNode) {\n    return true;\n  }\n  // Is second node an ancestor of the first one?\n  var ownerGraph = firstNode.getOwner();\n  var parentNode;\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == secondNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n  // Is first node an ancestor of the second one?\n  ownerGraph = secondNode.getOwner();\n\n  do {\n    parentNode = ownerGraph.getParent();\n\n    if (parentNode == null) {\n      break;\n    }\n\n    if (parentNode == firstNode) {\n      return true;\n    }\n\n    ownerGraph = parentNode.getOwner();\n    if (ownerGraph == null) {\n      break;\n    }\n  } while (true);\n\n  return false;\n};\n\nLGraphManager.prototype.calcLowestCommonAncestors = function () {\n  var edge;\n  var sourceNode;\n  var targetNode;\n  var sourceAncestorGraph;\n  var targetAncestorGraph;\n\n  var edges = this.getAllEdges();\n  var s = edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = edges[i];\n\n    sourceNode = edge.source;\n    targetNode = edge.target;\n    edge.lca = null;\n    edge.sourceInLca = sourceNode;\n    edge.targetInLca = targetNode;\n\n    if (sourceNode == targetNode) {\n      edge.lca = sourceNode.getOwner();\n      continue;\n    }\n\n    sourceAncestorGraph = sourceNode.getOwner();\n\n    while (edge.lca == null) {\n      edge.targetInLca = targetNode;\n      targetAncestorGraph = targetNode.getOwner();\n\n      while (edge.lca == null) {\n        if (targetAncestorGraph == sourceAncestorGraph) {\n          edge.lca = targetAncestorGraph;\n          break;\n        }\n\n        if (targetAncestorGraph == this.rootGraph) {\n          break;\n        }\n\n        if (edge.lca != null) {\n          throw \"assert failed\";\n        }\n        edge.targetInLca = targetAncestorGraph.getParent();\n        targetAncestorGraph = edge.targetInLca.getOwner();\n      }\n\n      if (sourceAncestorGraph == this.rootGraph) {\n        break;\n      }\n\n      if (edge.lca == null) {\n        edge.sourceInLca = sourceAncestorGraph.getParent();\n        sourceAncestorGraph = edge.sourceInLca.getOwner();\n      }\n    }\n\n    if (edge.lca == null) {\n      throw \"assert failed\";\n    }\n  }\n};\n\nLGraphManager.prototype.calcLowestCommonAncestor = function (firstNode, secondNode) {\n  if (firstNode == secondNode) {\n    return firstNode.getOwner();\n  }\n  var firstOwnerGraph = firstNode.getOwner();\n\n  do {\n    if (firstOwnerGraph == null) {\n      break;\n    }\n    var secondOwnerGraph = secondNode.getOwner();\n\n    do {\n      if (secondOwnerGraph == null) {\n        break;\n      }\n\n      if (secondOwnerGraph == firstOwnerGraph) {\n        return secondOwnerGraph;\n      }\n      secondOwnerGraph = secondOwnerGraph.getParent().getOwner();\n    } while (true);\n\n    firstOwnerGraph = firstOwnerGraph.getParent().getOwner();\n  } while (true);\n\n  return firstOwnerGraph;\n};\n\nLGraphManager.prototype.calcInclusionTreeDepths = function (graph, depth) {\n  if (graph == null && depth == null) {\n    graph = this.rootGraph;\n    depth = 1;\n  }\n  var node;\n\n  var nodes = graph.getNodes();\n  var s = nodes.length;\n  for (var i = 0; i < s; i++) {\n    node = nodes[i];\n    node.inclusionTreeDepth = depth;\n\n    if (node.child != null) {\n      this.calcInclusionTreeDepths(node.child, depth + 1);\n    }\n  }\n};\n\nLGraphManager.prototype.includesInvalidEdge = function () {\n  var edge;\n\n  var s = this.edges.length;\n  for (var i = 0; i < s; i++) {\n    edge = this.edges[i];\n\n    if (this.isOneAncestorOfOther(edge.source, edge.target)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nmodule.exports = LGraphManager;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0);\n\nfunction FDLayoutConstants() {}\n\n//FDLayoutConstants inherits static props in LayoutConstants\nfor (var prop in LayoutConstants) {\n  FDLayoutConstants[prop] = LayoutConstants[prop];\n}\n\nFDLayoutConstants.MAX_ITERATIONS = 2500;\n\nFDLayoutConstants.DEFAULT_EDGE_LENGTH = 50;\nFDLayoutConstants.DEFAULT_SPRING_STRENGTH = 0.45;\nFDLayoutConstants.DEFAULT_REPULSION_STRENGTH = 4500.0;\nFDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = 0.4;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = 1.0;\nFDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = 3.8;\nFDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = 1.5;\nFDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION = true;\nFDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION = true;\nFDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = 0.3;\nFDLayoutConstants.COOLING_ADAPTATION_FACTOR = 0.33;\nFDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT = 1000;\nFDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT = 5000;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL = 100.0;\nFDLayoutConstants.MAX_NODE_DISPLACEMENT = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL * 3;\nFDLayoutConstants.MIN_REPULSION_DIST = FDLayoutConstants.DEFAULT_EDGE_LENGTH / 10.0;\nFDLayoutConstants.CONVERGENCE_CHECK_PERIOD = 100;\nFDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = 0.1;\nFDLayoutConstants.MIN_EDGE_LENGTH = 1;\nFDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD = 10;\n\nmodule.exports = FDLayoutConstants;\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * This class maintains a list of static geometry related utility methods.\n *\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar Point = __webpack_require__(12);\n\nfunction IGeometry() {}\n\n/**\n * This method calculates *half* the amount in x and y directions of the two\n * input rectangles needed to separate them keeping their respective\n * positioning, and returns the result in the input array. An input\n * separation buffer added to the amount in both directions. We assume that\n * the two rectangles do intersect.\n */\nIGeometry.calcSeparationAmount = function (rectA, rectB, overlapAmount, separationBuffer) {\n  if (!rectA.intersects(rectB)) {\n    throw \"assert failed\";\n  }\n\n  var directions = new Array(2);\n\n  this.decideDirectionsForOverlappingNodes(rectA, rectB, directions);\n\n  overlapAmount[0] = Math.min(rectA.getRight(), rectB.getRight()) - Math.max(rectA.x, rectB.x);\n  overlapAmount[1] = Math.min(rectA.getBottom(), rectB.getBottom()) - Math.max(rectA.y, rectB.y);\n\n  // update the overlapping amounts for the following cases:\n  if (rectA.getX() <= rectB.getX() && rectA.getRight() >= rectB.getRight()) {\n    /* Case x.1:\n    *\n    * rectA\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectB\n    */\n    overlapAmount[0] += Math.min(rectB.getX() - rectA.getX(), rectA.getRight() - rectB.getRight());\n  } else if (rectB.getX() <= rectA.getX() && rectB.getRight() >= rectA.getRight()) {\n    /* Case x.2:\n    *\n    * rectB\n    * \t|                       |\n    * \t|        _________      |\n    * \t|        |       |      |\n    * \t|________|_______|______|\n    * \t\t\t |       |\n    *           |       |\n    *        rectA\n    */\n    overlapAmount[0] += Math.min(rectA.getX() - rectB.getX(), rectB.getRight() - rectA.getRight());\n  }\n  if (rectA.getY() <= rectB.getY() && rectA.getBottom() >= rectB.getBottom()) {\n    /* Case y.1:\n     *          ________ rectA\n     *         |\n     *         |\n     *   ______|____  rectB\n     *         |    |\n     *         |    |\n     *   ______|____|\n     *         |\n     *         |\n     *         |________\n     *\n     */\n    overlapAmount[1] += Math.min(rectB.getY() - rectA.getY(), rectA.getBottom() - rectB.getBottom());\n  } else if (rectB.getY() <= rectA.getY() && rectB.getBottom() >= rectA.getBottom()) {\n    /* Case y.2:\n    *          ________ rectB\n    *         |\n    *         |\n    *   ______|____  rectA\n    *         |    |\n    *         |    |\n    *   ______|____|\n    *         |\n    *         |\n    *         |________\n    *\n    */\n    overlapAmount[1] += Math.min(rectA.getY() - rectB.getY(), rectB.getBottom() - rectA.getBottom());\n  }\n\n  // find slope of the line passes two centers\n  var slope = Math.abs((rectB.getCenterY() - rectA.getCenterY()) / (rectB.getCenterX() - rectA.getCenterX()));\n  // if centers are overlapped\n  if (rectB.getCenterY() === rectA.getCenterY() && rectB.getCenterX() === rectA.getCenterX()) {\n    // assume the slope is 1 (45 degree)\n    slope = 1.0;\n  }\n\n  var moveByY = slope * overlapAmount[0];\n  var moveByX = overlapAmount[1] / slope;\n  if (overlapAmount[0] < moveByX) {\n    moveByX = overlapAmount[0];\n  } else {\n    moveByY = overlapAmount[1];\n  }\n  // return half the amount so that if each rectangle is moved by these\n  // amounts in opposite directions, overlap will be resolved\n  overlapAmount[0] = -1 * directions[0] * (moveByX / 2 + separationBuffer);\n  overlapAmount[1] = -1 * directions[1] * (moveByY / 2 + separationBuffer);\n};\n\n/**\n * This method decides the separation direction of overlapping nodes\n *\n * if directions[0] = -1, then rectA goes left\n * if directions[0] = 1,  then rectA goes right\n * if directions[1] = -1, then rectA goes up\n * if directions[1] = 1,  then rectA goes down\n */\nIGeometry.decideDirectionsForOverlappingNodes = function (rectA, rectB, directions) {\n  if (rectA.getCenterX() < rectB.getCenterX()) {\n    directions[0] = -1;\n  } else {\n    directions[0] = 1;\n  }\n\n  if (rectA.getCenterY() < rectB.getCenterY()) {\n    directions[1] = -1;\n  } else {\n    directions[1] = 1;\n  }\n};\n\n/**\n * This method calculates the intersection (clipping) points of the two\n * input rectangles with line segment defined by the centers of these two\n * rectangles. The clipping points are saved in the input double array and\n * whether or not the two rectangles overlap is returned.\n */\nIGeometry.getIntersection2 = function (rectA, rectB, result) {\n  //result[0-1] will contain clipPoint of rectA, result[2-3] will contain clipPoint of rectB\n  var p1x = rectA.getCenterX();\n  var p1y = rectA.getCenterY();\n  var p2x = rectB.getCenterX();\n  var p2y = rectB.getCenterY();\n\n  //if two rectangles intersect, then clipping points are centers\n  if (rectA.intersects(rectB)) {\n    result[0] = p1x;\n    result[1] = p1y;\n    result[2] = p2x;\n    result[3] = p2y;\n    return true;\n  }\n  //variables for rectA\n  var topLeftAx = rectA.getX();\n  var topLeftAy = rectA.getY();\n  var topRightAx = rectA.getRight();\n  var bottomLeftAx = rectA.getX();\n  var bottomLeftAy = rectA.getBottom();\n  var bottomRightAx = rectA.getRight();\n  var halfWidthA = rectA.getWidthHalf();\n  var halfHeightA = rectA.getHeightHalf();\n  //variables for rectB\n  var topLeftBx = rectB.getX();\n  var topLeftBy = rectB.getY();\n  var topRightBx = rectB.getRight();\n  var bottomLeftBx = rectB.getX();\n  var bottomLeftBy = rectB.getBottom();\n  var bottomRightBx = rectB.getRight();\n  var halfWidthB = rectB.getWidthHalf();\n  var halfHeightB = rectB.getHeightHalf();\n\n  //flag whether clipping points are found\n  var clipPointAFound = false;\n  var clipPointBFound = false;\n\n  // line is vertical\n  if (p1x === p2x) {\n    if (p1y > p2y) {\n      result[0] = p1x;\n      result[1] = topLeftAy;\n      result[2] = p2x;\n      result[3] = bottomLeftBy;\n      return false;\n    } else if (p1y < p2y) {\n      result[0] = p1x;\n      result[1] = bottomLeftAy;\n      result[2] = p2x;\n      result[3] = topLeftBy;\n      return false;\n    } else {\n      //not line, return null;\n    }\n  }\n  // line is horizontal\n  else if (p1y === p2y) {\n      if (p1x > p2x) {\n        result[0] = topLeftAx;\n        result[1] = p1y;\n        result[2] = topRightBx;\n        result[3] = p2y;\n        return false;\n      } else if (p1x < p2x) {\n        result[0] = topRightAx;\n        result[1] = p1y;\n        result[2] = topLeftBx;\n        result[3] = p2y;\n        return false;\n      } else {\n        //not valid line, return null;\n      }\n    } else {\n      //slopes of rectA's and rectB's diagonals\n      var slopeA = rectA.height / rectA.width;\n      var slopeB = rectB.height / rectB.width;\n\n      //slope of line between center of rectA and center of rectB\n      var slopePrime = (p2y - p1y) / (p2x - p1x);\n      var cardinalDirectionA = void 0;\n      var cardinalDirectionB = void 0;\n      var tempPointAx = void 0;\n      var tempPointAy = void 0;\n      var tempPointBx = void 0;\n      var tempPointBy = void 0;\n\n      //determine whether clipping point is the corner of nodeA\n      if (-slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = bottomLeftAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = topRightAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        }\n      } else if (slopeA === slopePrime) {\n        if (p1x > p2x) {\n          result[0] = topLeftAx;\n          result[1] = topLeftAy;\n          clipPointAFound = true;\n        } else {\n          result[0] = bottomRightAx;\n          result[1] = bottomLeftAy;\n          clipPointAFound = true;\n        }\n      }\n\n      //determine whether clipping point is the corner of nodeB\n      if (-slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = bottomLeftBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = topRightBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        }\n      } else if (slopeB === slopePrime) {\n        if (p2x > p1x) {\n          result[2] = topLeftBx;\n          result[3] = topLeftBy;\n          clipPointBFound = true;\n        } else {\n          result[2] = bottomRightBx;\n          result[3] = bottomLeftBy;\n          clipPointBFound = true;\n        }\n      }\n\n      //if both clipping points are corners\n      if (clipPointAFound && clipPointBFound) {\n        return false;\n      }\n\n      //determine Cardinal Direction of rectangles\n      if (p1x > p2x) {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 4);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 2);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 3);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 1);\n        }\n      } else {\n        if (p1y > p2y) {\n          cardinalDirectionA = this.getCardinalDirection(-slopeA, slopePrime, 1);\n          cardinalDirectionB = this.getCardinalDirection(-slopeB, slopePrime, 3);\n        } else {\n          cardinalDirectionA = this.getCardinalDirection(slopeA, slopePrime, 2);\n          cardinalDirectionB = this.getCardinalDirection(slopeB, slopePrime, 4);\n        }\n      }\n      //calculate clipping Point if it is not found before\n      if (!clipPointAFound) {\n        switch (cardinalDirectionA) {\n          case 1:\n            tempPointAy = topLeftAy;\n            tempPointAx = p1x + -halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 2:\n            tempPointAx = bottomRightAx;\n            tempPointAy = p1y + halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 3:\n            tempPointAy = bottomLeftAy;\n            tempPointAx = p1x + halfHeightA / slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n          case 4:\n            tempPointAx = bottomLeftAx;\n            tempPointAy = p1y + -halfWidthA * slopePrime;\n            result[0] = tempPointAx;\n            result[1] = tempPointAy;\n            break;\n        }\n      }\n      if (!clipPointBFound) {\n        switch (cardinalDirectionB) {\n          case 1:\n            tempPointBy = topLeftBy;\n            tempPointBx = p2x + -halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 2:\n            tempPointBx = bottomRightBx;\n            tempPointBy = p2y + halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 3:\n            tempPointBy = bottomLeftBy;\n            tempPointBx = p2x + halfHeightB / slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n          case 4:\n            tempPointBx = bottomLeftBx;\n            tempPointBy = p2y + -halfWidthB * slopePrime;\n            result[2] = tempPointBx;\n            result[3] = tempPointBy;\n            break;\n        }\n      }\n    }\n  return false;\n};\n\n/**\n * This method returns in which cardinal direction does input point stays\n * 1: North\n * 2: East\n * 3: South\n * 4: West\n */\nIGeometry.getCardinalDirection = function (slope, slopePrime, line) {\n  if (slope > slopePrime) {\n    return line;\n  } else {\n    return 1 + line % 4;\n  }\n};\n\n/**\n * This method calculates the intersection of the two lines defined by\n * point pairs (s1,s2) and (f1,f2).\n */\nIGeometry.getIntersection = function (s1, s2, f1, f2) {\n  if (f2 == null) {\n    return this.getIntersection2(s1, s2, f1);\n  }\n\n  var x1 = s1.x;\n  var y1 = s1.y;\n  var x2 = s2.x;\n  var y2 = s2.y;\n  var x3 = f1.x;\n  var y3 = f1.y;\n  var x4 = f2.x;\n  var y4 = f2.y;\n  var x = void 0,\n      y = void 0; // intersection point\n  var a1 = void 0,\n      a2 = void 0,\n      b1 = void 0,\n      b2 = void 0,\n      c1 = void 0,\n      c2 = void 0; // coefficients of line eqns.\n  var denom = void 0;\n\n  a1 = y2 - y1;\n  b1 = x1 - x2;\n  c1 = x2 * y1 - x1 * y2; // { a1*x + b1*y + c1 = 0 is line 1 }\n\n  a2 = y4 - y3;\n  b2 = x3 - x4;\n  c2 = x4 * y3 - x3 * y4; // { a2*x + b2*y + c2 = 0 is line 2 }\n\n  denom = a1 * b2 - a2 * b1;\n\n  if (denom === 0) {\n    return null;\n  }\n\n  x = (b1 * c2 - b2 * c1) / denom;\n  y = (a2 * c1 - a1 * c2) / denom;\n\n  return new Point(x, y);\n};\n\n/**\n * This method finds and returns the angle of the vector from the + x-axis\n * in clockwise direction (compatible w/ Java coordinate system!).\n */\nIGeometry.angleOfVector = function (Cx, Cy, Nx, Ny) {\n  var C_angle = void 0;\n\n  if (Cx !== Nx) {\n    C_angle = Math.atan((Ny - Cy) / (Nx - Cx));\n\n    if (Nx < Cx) {\n      C_angle += Math.PI;\n    } else if (Ny < Cy) {\n      C_angle += this.TWO_PI;\n    }\n  } else if (Ny < Cy) {\n    C_angle = this.ONE_AND_HALF_PI; // 270 degrees\n  } else {\n    C_angle = this.HALF_PI; // 90 degrees\n  }\n\n  return C_angle;\n};\n\n/**\n * This method checks whether the given two line segments (one with point\n * p1 and p2, the other with point p3 and p4) intersect at a point other\n * than these points.\n */\nIGeometry.doIntersect = function (p1, p2, p3, p4) {\n  var a = p1.x;\n  var b = p1.y;\n  var c = p2.x;\n  var d = p2.y;\n  var p = p3.x;\n  var q = p3.y;\n  var r = p4.x;\n  var s = p4.y;\n  var det = (c - a) * (s - q) - (r - p) * (d - b);\n\n  if (det === 0) {\n    return false;\n  } else {\n    var lambda = ((s - q) * (r - a) + (p - r) * (s - b)) / det;\n    var gamma = ((b - d) * (r - a) + (c - a) * (s - b)) / det;\n    return 0 < lambda && lambda < 1 && 0 < gamma && gamma < 1;\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Class Constants\n// -----------------------------------------------------------------------------\n/**\n * Some useful pre-calculated constants\n */\nIGeometry.HALF_PI = 0.5 * Math.PI;\nIGeometry.ONE_AND_HALF_PI = 1.5 * Math.PI;\nIGeometry.TWO_PI = 2.0 * Math.PI;\nIGeometry.THREE_PI = 3.0 * Math.PI;\n\nmodule.exports = IGeometry;\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction IMath() {}\n\n/**\n * This method returns the sign of the input value.\n */\nIMath.sign = function (value) {\n  if (value > 0) {\n    return 1;\n  } else if (value < 0) {\n    return -1;\n  } else {\n    return 0;\n  }\n};\n\nIMath.floor = function (value) {\n  return value < 0 ? Math.ceil(value) : Math.floor(value);\n};\n\nIMath.ceil = function (value) {\n  return value < 0 ? Math.floor(value) : Math.ceil(value);\n};\n\nmodule.exports = IMath;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Integer() {}\n\nInteger.MAX_VALUE = 2147483647;\nInteger.MIN_VALUE = -2147483648;\n\nmodule.exports = Integer;\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar nodeFrom = function nodeFrom(value) {\n  return { value: value, next: null, prev: null };\n};\n\nvar add = function add(prev, node, next, list) {\n  if (prev !== null) {\n    prev.next = node;\n  } else {\n    list.head = node;\n  }\n\n  if (next !== null) {\n    next.prev = node;\n  } else {\n    list.tail = node;\n  }\n\n  node.prev = prev;\n  node.next = next;\n\n  list.length++;\n\n  return node;\n};\n\nvar _remove = function _remove(node, list) {\n  var prev = node.prev,\n      next = node.next;\n\n\n  if (prev !== null) {\n    prev.next = next;\n  } else {\n    list.head = next;\n  }\n\n  if (next !== null) {\n    next.prev = prev;\n  } else {\n    list.tail = prev;\n  }\n\n  node.prev = node.next = null;\n\n  list.length--;\n\n  return node;\n};\n\nvar LinkedList = function () {\n  function LinkedList(vals) {\n    var _this = this;\n\n    _classCallCheck(this, LinkedList);\n\n    this.length = 0;\n    this.head = null;\n    this.tail = null;\n\n    if (vals != null) {\n      vals.forEach(function (v) {\n        return _this.push(v);\n      });\n    }\n  }\n\n  _createClass(LinkedList, [{\n    key: \"size\",\n    value: function size() {\n      return this.length;\n    }\n  }, {\n    key: \"insertBefore\",\n    value: function insertBefore(val, otherNode) {\n      return add(otherNode.prev, nodeFrom(val), otherNode, this);\n    }\n  }, {\n    key: \"insertAfter\",\n    value: function insertAfter(val, otherNode) {\n      return add(otherNode, nodeFrom(val), otherNode.next, this);\n    }\n  }, {\n    key: \"insertNodeBefore\",\n    value: function insertNodeBefore(newNode, otherNode) {\n      return add(otherNode.prev, newNode, otherNode, this);\n    }\n  }, {\n    key: \"insertNodeAfter\",\n    value: function insertNodeAfter(newNode, otherNode) {\n      return add(otherNode, newNode, otherNode.next, this);\n    }\n  }, {\n    key: \"push\",\n    value: function push(val) {\n      return add(this.tail, nodeFrom(val), null, this);\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(val) {\n      return add(null, nodeFrom(val), this.head, this);\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(node) {\n      return _remove(node, this);\n    }\n  }, {\n    key: \"pop\",\n    value: function pop() {\n      return _remove(this.tail, this).value;\n    }\n  }, {\n    key: \"popNode\",\n    value: function popNode() {\n      return _remove(this.tail, this);\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      return _remove(this.head, this).value;\n    }\n  }, {\n    key: \"shiftNode\",\n    value: function shiftNode() {\n      return _remove(this.head, this);\n    }\n  }, {\n    key: \"get_object_at\",\n    value: function get_object_at(index) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        return current.value;\n      }\n    }\n  }, {\n    key: \"set_object_at\",\n    value: function set_object_at(index, value) {\n      if (index <= this.length()) {\n        var i = 1;\n        var current = this.head;\n        while (i < index) {\n          current = current.next;\n          i++;\n        }\n        current.value = value;\n      }\n    }\n  }]);\n\n  return LinkedList;\n}();\n\nmodule.exports = LinkedList;\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/*\r\n *This class is the javascript implementation of the Point.java class in jdk\r\n */\nfunction Point(x, y, p) {\n  this.x = null;\n  this.y = null;\n  if (x == null && y == null && p == null) {\n    this.x = 0;\n    this.y = 0;\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    this.x = x;\n    this.y = y;\n  } else if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.x = p.x;\n    this.y = p.y;\n  }\n}\n\nPoint.prototype.getX = function () {\n  return this.x;\n};\n\nPoint.prototype.getY = function () {\n  return this.y;\n};\n\nPoint.prototype.getLocation = function () {\n  return new Point(this.x, this.y);\n};\n\nPoint.prototype.setLocation = function (x, y, p) {\n  if (x.constructor.name == 'Point' && y == null && p == null) {\n    p = x;\n    this.setLocation(p.x, p.y);\n  } else if (typeof x == 'number' && typeof y == 'number' && p == null) {\n    //if both parameters are integer just move (x,y) location\n    if (parseInt(x) == x && parseInt(y) == y) {\n      this.move(x, y);\n    } else {\n      this.x = Math.floor(x + 0.5);\n      this.y = Math.floor(y + 0.5);\n    }\n  }\n};\n\nPoint.prototype.move = function (x, y) {\n  this.x = x;\n  this.y = y;\n};\n\nPoint.prototype.translate = function (dx, dy) {\n  this.x += dx;\n  this.y += dy;\n};\n\nPoint.prototype.equals = function (obj) {\n  if (obj.constructor.name == \"Point\") {\n    var pt = obj;\n    return this.x == pt.x && this.y == pt.y;\n  }\n  return this == obj;\n};\n\nPoint.prototype.toString = function () {\n  return new Point().constructor.name + \"[x=\" + this.x + \",y=\" + this.y + \"]\";\n};\n\nmodule.exports = Point;\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RectangleD(x, y, width, height) {\n  this.x = 0;\n  this.y = 0;\n  this.width = 0;\n  this.height = 0;\n\n  if (x != null && y != null && width != null && height != null) {\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.height = height;\n  }\n}\n\nRectangleD.prototype.getX = function () {\n  return this.x;\n};\n\nRectangleD.prototype.setX = function (x) {\n  this.x = x;\n};\n\nRectangleD.prototype.getY = function () {\n  return this.y;\n};\n\nRectangleD.prototype.setY = function (y) {\n  this.y = y;\n};\n\nRectangleD.prototype.getWidth = function () {\n  return this.width;\n};\n\nRectangleD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nRectangleD.prototype.getHeight = function () {\n  return this.height;\n};\n\nRectangleD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nRectangleD.prototype.getRight = function () {\n  return this.x + this.width;\n};\n\nRectangleD.prototype.getBottom = function () {\n  return this.y + this.height;\n};\n\nRectangleD.prototype.intersects = function (a) {\n  if (this.getRight() < a.x) {\n    return false;\n  }\n\n  if (this.getBottom() < a.y) {\n    return false;\n  }\n\n  if (a.getRight() < this.x) {\n    return false;\n  }\n\n  if (a.getBottom() < this.y) {\n    return false;\n  }\n\n  return true;\n};\n\nRectangleD.prototype.getCenterX = function () {\n  return this.x + this.width / 2;\n};\n\nRectangleD.prototype.getMinX = function () {\n  return this.getX();\n};\n\nRectangleD.prototype.getMaxX = function () {\n  return this.getX() + this.width;\n};\n\nRectangleD.prototype.getCenterY = function () {\n  return this.y + this.height / 2;\n};\n\nRectangleD.prototype.getMinY = function () {\n  return this.getY();\n};\n\nRectangleD.prototype.getMaxY = function () {\n  return this.getY() + this.height;\n};\n\nRectangleD.prototype.getWidthHalf = function () {\n  return this.width / 2;\n};\n\nRectangleD.prototype.getHeightHalf = function () {\n  return this.height / 2;\n};\n\nmodule.exports = RectangleD;\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction UniqueIDGeneretor() {}\n\nUniqueIDGeneretor.lastID = 0;\n\nUniqueIDGeneretor.createID = function (obj) {\n  if (UniqueIDGeneretor.isPrimitive(obj)) {\n    return obj;\n  }\n  if (obj.uniqueID != null) {\n    return obj.uniqueID;\n  }\n  obj.uniqueID = UniqueIDGeneretor.getString();\n  UniqueIDGeneretor.lastID++;\n  return obj.uniqueID;\n};\n\nUniqueIDGeneretor.getString = function (id) {\n  if (id == null) id = UniqueIDGeneretor.lastID;\n  return \"Object#\" + id + \"\";\n};\n\nUniqueIDGeneretor.isPrimitive = function (arg) {\n  var type = typeof arg === \"undefined\" ? \"undefined\" : _typeof(arg);\n  return arg == null || type != \"object\" && type != \"function\";\n};\n\nmodule.exports = UniqueIDGeneretor;\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar LayoutConstants = __webpack_require__(0);\nvar LGraphManager = __webpack_require__(6);\nvar LNode = __webpack_require__(3);\nvar LEdge = __webpack_require__(1);\nvar LGraph = __webpack_require__(5);\nvar PointD = __webpack_require__(4);\nvar Transform = __webpack_require__(17);\nvar Emitter = __webpack_require__(27);\n\nfunction Layout(isRemoteUse) {\n  Emitter.call(this);\n\n  //Layout Quality: 0:draft, 1:default, 2:proof\n  this.layoutQuality = LayoutConstants.QUALITY;\n  //Whether layout should create bendpoints as needed or not\n  this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  //Whether layout should be incremental or not\n  this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n  //Whether we animate from before to after layout node positions\n  this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n  //Whether we animate the layout process or not\n  this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n  //Number iterations that should be done between two successive animations\n  this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n  /**\r\n   * Whether or not leaf nodes (non-compound nodes) are of uniform sizes. When\r\n   * they are, both spring and repulsion forces between two leaf nodes can be\r\n   * calculated without the expensive clipping point calculations, resulting\r\n   * in major speed-up.\r\n   */\n  this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  /**\r\n   * This is used for creation of bendpoints by using dummy nodes and edges.\r\n   * Maps an LEdge to its dummy bendpoint path.\r\n   */\n  this.edgeToDummyNodes = new Map();\n  this.graphManager = new LGraphManager(this);\n  this.isLayoutFinished = false;\n  this.isSubLayout = false;\n  this.isRemoteUse = false;\n\n  if (isRemoteUse != null) {\n    this.isRemoteUse = isRemoteUse;\n  }\n}\n\nLayout.RANDOM_SEED = 1;\n\nLayout.prototype = Object.create(Emitter.prototype);\n\nLayout.prototype.getGraphManager = function () {\n  return this.graphManager;\n};\n\nLayout.prototype.getAllNodes = function () {\n  return this.graphManager.getAllNodes();\n};\n\nLayout.prototype.getAllEdges = function () {\n  return this.graphManager.getAllEdges();\n};\n\nLayout.prototype.getAllNodesToApplyGravitation = function () {\n  return this.graphManager.getAllNodesToApplyGravitation();\n};\n\nLayout.prototype.newGraphManager = function () {\n  var gm = new LGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nLayout.prototype.newGraph = function (vGraph) {\n  return new LGraph(null, this.graphManager, vGraph);\n};\n\nLayout.prototype.newNode = function (vNode) {\n  return new LNode(this.graphManager, vNode);\n};\n\nLayout.prototype.newEdge = function (vEdge) {\n  return new LEdge(null, null, vEdge);\n};\n\nLayout.prototype.checkLayoutSuccess = function () {\n  return this.graphManager.getRoot() == null || this.graphManager.getRoot().getNodes().length == 0 || this.graphManager.includesInvalidEdge();\n};\n\nLayout.prototype.runLayout = function () {\n  this.isLayoutFinished = false;\n\n  if (this.tilingPreLayout) {\n    this.tilingPreLayout();\n  }\n\n  this.initParameters();\n  var isLayoutSuccessfull;\n\n  if (this.checkLayoutSuccess()) {\n    isLayoutSuccessfull = false;\n  } else {\n    isLayoutSuccessfull = this.layout();\n  }\n\n  if (LayoutConstants.ANIMATE === 'during') {\n    // If this is a 'during' layout animation. Layout is not finished yet. \n    // We need to perform these in index.js when layout is really finished.\n    return false;\n  }\n\n  if (isLayoutSuccessfull) {\n    if (!this.isSubLayout) {\n      this.doPostLayout();\n    }\n  }\n\n  if (this.tilingPostLayout) {\n    this.tilingPostLayout();\n  }\n\n  this.isLayoutFinished = true;\n\n  return isLayoutSuccessfull;\n};\n\n/**\r\n * This method performs the operations required after layout.\r\n */\nLayout.prototype.doPostLayout = function () {\n  //assert !isSubLayout : \"Should not be called on sub-layout!\";\n  // Propagate geometric changes to v-level objects\n  if (!this.incremental) {\n    this.transform();\n  }\n  this.update();\n};\n\n/**\r\n * This method updates the geometry of the target graph according to\r\n * calculated layout.\r\n */\nLayout.prototype.update2 = function () {\n  // update bend points\n  if (this.createBendsAsNeeded) {\n    this.createBendpointsFromDummyNodes();\n\n    // reset all edges, since the topology has changed\n    this.graphManager.resetAllEdges();\n  }\n\n  // perform edge, node and root updates if layout is not called\n  // remotely\n  if (!this.isRemoteUse) {\n    // update all edges\n    var edge;\n    var allEdges = this.graphManager.getAllEdges();\n    for (var i = 0; i < allEdges.length; i++) {\n      edge = allEdges[i];\n      //      this.update(edge);\n    }\n\n    // recursively update nodes\n    var node;\n    var nodes = this.graphManager.getRoot().getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      node = nodes[i];\n      //      this.update(node);\n    }\n\n    // update root graph\n    this.update(this.graphManager.getRoot());\n  }\n};\n\nLayout.prototype.update = function (obj) {\n  if (obj == null) {\n    this.update2();\n  } else if (obj instanceof LNode) {\n    var node = obj;\n    if (node.getChild() != null) {\n      // since node is compound, recursively update child nodes\n      var nodes = node.getChild().getNodes();\n      for (var i = 0; i < nodes.length; i++) {\n        update(nodes[i]);\n      }\n    }\n\n    // if the l-level node is associated with a v-level graph object,\n    // then it is assumed that the v-level node implements the\n    // interface Updatable.\n    if (node.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vNode = node.vGraphObject;\n\n      // call the update method of the interface\n      vNode.update(node);\n    }\n  } else if (obj instanceof LEdge) {\n    var edge = obj;\n    // if the l-level edge is associated with a v-level graph object,\n    // then it is assumed that the v-level edge implements the\n    // interface Updatable.\n\n    if (edge.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vEdge = edge.vGraphObject;\n\n      // call the update method of the interface\n      vEdge.update(edge);\n    }\n  } else if (obj instanceof LGraph) {\n    var graph = obj;\n    // if the l-level graph is associated with a v-level graph object,\n    // then it is assumed that the v-level object implements the\n    // interface Updatable.\n\n    if (graph.vGraphObject != null) {\n      // cast to Updatable without any type check\n      var vGraph = graph.vGraphObject;\n\n      // call the update method of the interface\n      vGraph.update(graph);\n    }\n  }\n};\n\n/**\r\n * This method is used to set all layout parameters to default values\r\n * determined at compile time.\r\n */\nLayout.prototype.initParameters = function () {\n  if (!this.isSubLayout) {\n    this.layoutQuality = LayoutConstants.QUALITY;\n    this.animationDuringLayout = LayoutConstants.DEFAULT_ANIMATION_DURING_LAYOUT;\n    this.animationPeriod = LayoutConstants.DEFAULT_ANIMATION_PERIOD;\n    this.animationOnLayout = LayoutConstants.DEFAULT_ANIMATION_ON_LAYOUT;\n    this.incremental = LayoutConstants.DEFAULT_INCREMENTAL;\n    this.createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n    this.uniformLeafNodeSizes = LayoutConstants.DEFAULT_UNIFORM_LEAF_NODE_SIZES;\n  }\n\n  if (this.animationDuringLayout) {\n    this.animationOnLayout = false;\n  }\n};\n\nLayout.prototype.transform = function (newLeftTop) {\n  if (newLeftTop == undefined) {\n    this.transform(new PointD(0, 0));\n  } else {\n    // create a transformation object (from Eclipse to layout). When an\n    // inverse transform is applied, we get upper-left coordinate of the\n    // drawing or the root graph at given input coordinate (some margins\n    // already included in calculation of left-top).\n\n    var trans = new Transform();\n    var leftTop = this.graphManager.getRoot().updateLeftTop();\n\n    if (leftTop != null) {\n      trans.setWorldOrgX(newLeftTop.x);\n      trans.setWorldOrgY(newLeftTop.y);\n\n      trans.setDeviceOrgX(leftTop.x);\n      trans.setDeviceOrgY(leftTop.y);\n\n      var nodes = this.getAllNodes();\n      var node;\n\n      for (var i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.transform(trans);\n      }\n    }\n  }\n};\n\nLayout.prototype.positionNodesRandomly = function (graph) {\n\n  if (graph == undefined) {\n    //assert !this.incremental;\n    this.positionNodesRandomly(this.getGraphManager().getRoot());\n    this.getGraphManager().getRoot().updateBounds(true);\n  } else {\n    var lNode;\n    var childGraph;\n\n    var nodes = graph.getNodes();\n    for (var i = 0; i < nodes.length; i++) {\n      lNode = nodes[i];\n      childGraph = lNode.getChild();\n\n      if (childGraph == null) {\n        lNode.scatter();\n      } else if (childGraph.getNodes().length == 0) {\n        lNode.scatter();\n      } else {\n        this.positionNodesRandomly(childGraph);\n        lNode.updateBounds();\n      }\n    }\n  }\n};\n\n/**\r\n * This method returns a list of trees where each tree is represented as a\r\n * list of l-nodes. The method returns a list of size 0 when:\r\n * - The graph is not flat or\r\n * - One of the component(s) of the graph is not a tree.\r\n */\nLayout.prototype.getFlatForest = function () {\n  var flatForest = [];\n  var isForest = true;\n\n  // Quick reference for all nodes in the graph manager associated with\n  // this layout. The list should not be changed.\n  var allNodes = this.graphManager.getRoot().getNodes();\n\n  // First be sure that the graph is flat\n  var isFlat = true;\n\n  for (var i = 0; i < allNodes.length; i++) {\n    if (allNodes[i].getChild() != null) {\n      isFlat = false;\n    }\n  }\n\n  // Return empty forest if the graph is not flat.\n  if (!isFlat) {\n    return flatForest;\n  }\n\n  // Run BFS for each component of the graph.\n\n  var visited = new Set();\n  var toBeVisited = [];\n  var parents = new Map();\n  var unProcessedNodes = [];\n\n  unProcessedNodes = unProcessedNodes.concat(allNodes);\n\n  // Each iteration of this loop finds a component of the graph and\n  // decides whether it is a tree or not. If it is a tree, adds it to the\n  // forest and continued with the next component.\n\n  while (unProcessedNodes.length > 0 && isForest) {\n    toBeVisited.push(unProcessedNodes[0]);\n\n    // Start the BFS. Each iteration of this loop visits a node in a\n    // BFS manner.\n    while (toBeVisited.length > 0 && isForest) {\n      //pool operation\n      var currentNode = toBeVisited[0];\n      toBeVisited.splice(0, 1);\n      visited.add(currentNode);\n\n      // Traverse all neighbors of this node\n      var neighborEdges = currentNode.getEdges();\n\n      for (var i = 0; i < neighborEdges.length; i++) {\n        var currentNeighbor = neighborEdges[i].getOtherEnd(currentNode);\n\n        // If BFS is not growing from this neighbor.\n        if (parents.get(currentNode) != currentNeighbor) {\n          // We haven't previously visited this neighbor.\n          if (!visited.has(currentNeighbor)) {\n            toBeVisited.push(currentNeighbor);\n            parents.set(currentNeighbor, currentNode);\n          }\n          // Since we have previously visited this neighbor and\n          // this neighbor is not parent of currentNode, given\n          // graph contains a component that is not tree, hence\n          // it is not a forest.\n          else {\n              isForest = false;\n              break;\n            }\n        }\n      }\n    }\n\n    // The graph contains a component that is not a tree. Empty\n    // previously found trees. The method will end.\n    if (!isForest) {\n      flatForest = [];\n    }\n    // Save currently visited nodes as a tree in our forest. Reset\n    // visited and parents lists. Continue with the next component of\n    // the graph, if any.\n    else {\n        var temp = [].concat(_toConsumableArray(visited));\n        flatForest.push(temp);\n        //flatForest = flatForest.concat(temp);\n        //unProcessedNodes.removeAll(visited);\n        for (var i = 0; i < temp.length; i++) {\n          var value = temp[i];\n          var index = unProcessedNodes.indexOf(value);\n          if (index > -1) {\n            unProcessedNodes.splice(index, 1);\n          }\n        }\n        visited = new Set();\n        parents = new Map();\n      }\n  }\n\n  return flatForest;\n};\n\n/**\r\n * This method creates dummy nodes (an l-level node with minimal dimensions)\r\n * for the given edge (one per bendpoint). The existing l-level structure\r\n * is updated accordingly.\r\n */\nLayout.prototype.createDummyNodesForBendpoints = function (edge) {\n  var dummyNodes = [];\n  var prev = edge.source;\n\n  var graph = this.graphManager.calcLowestCommonAncestor(edge.source, edge.target);\n\n  for (var i = 0; i < edge.bendpoints.length; i++) {\n    // create new dummy node\n    var dummyNode = this.newNode(null);\n    dummyNode.setRect(new Point(0, 0), new Dimension(1, 1));\n\n    graph.add(dummyNode);\n\n    // create new dummy edge between prev and dummy node\n    var dummyEdge = this.newEdge(null);\n    this.graphManager.add(dummyEdge, prev, dummyNode);\n\n    dummyNodes.add(dummyNode);\n    prev = dummyNode;\n  }\n\n  var dummyEdge = this.newEdge(null);\n  this.graphManager.add(dummyEdge, prev, edge.target);\n\n  this.edgeToDummyNodes.set(edge, dummyNodes);\n\n  // remove real edge from graph manager if it is inter-graph\n  if (edge.isInterGraph()) {\n    this.graphManager.remove(edge);\n  }\n  // else, remove the edge from the current graph\n  else {\n      graph.remove(edge);\n    }\n\n  return dummyNodes;\n};\n\n/**\r\n * This method creates bendpoints for edges from the dummy nodes\r\n * at l-level.\r\n */\nLayout.prototype.createBendpointsFromDummyNodes = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  edges = [].concat(_toConsumableArray(this.edgeToDummyNodes.keys())).concat(edges);\n\n  for (var k = 0; k < edges.length; k++) {\n    var lEdge = edges[k];\n\n    if (lEdge.bendpoints.length > 0) {\n      var path = this.edgeToDummyNodes.get(lEdge);\n\n      for (var i = 0; i < path.length; i++) {\n        var dummyNode = path[i];\n        var p = new PointD(dummyNode.getCenterX(), dummyNode.getCenterY());\n\n        // update bendpoint's location according to dummy node\n        var ebp = lEdge.bendpoints.get(i);\n        ebp.x = p.x;\n        ebp.y = p.y;\n\n        // remove the dummy node, dummy edges incident with this\n        // dummy node is also removed (within the remove method)\n        dummyNode.getOwner().remove(dummyNode);\n      }\n\n      // add the real edge to graph\n      this.graphManager.add(lEdge, lEdge.source, lEdge.target);\n    }\n  }\n};\n\nLayout.transform = function (sliderValue, defaultValue, minDiv, maxMul) {\n  if (minDiv != undefined && maxMul != undefined) {\n    var value = defaultValue;\n\n    if (sliderValue <= 50) {\n      var minValue = defaultValue / minDiv;\n      value -= (defaultValue - minValue) / 50 * (50 - sliderValue);\n    } else {\n      var maxValue = defaultValue * maxMul;\n      value += (maxValue - defaultValue) / 50 * (sliderValue - 50);\n    }\n\n    return value;\n  } else {\n    var a, b;\n\n    if (sliderValue <= 50) {\n      a = 9.0 * defaultValue / 500.0;\n      b = defaultValue / 10.0;\n    } else {\n      a = 9.0 * defaultValue / 50.0;\n      b = -8 * defaultValue;\n    }\n\n    return a * sliderValue + b;\n  }\n};\n\n/**\r\n * This method finds and returns the center of the given nodes, assuming\r\n * that the given nodes form a tree in themselves.\r\n */\nLayout.findCenterOfTree = function (nodes) {\n  var list = [];\n  list = list.concat(nodes);\n\n  var removedNodes = [];\n  var remainingDegrees = new Map();\n  var foundCenter = false;\n  var centerNode = null;\n\n  if (list.length == 1 || list.length == 2) {\n    foundCenter = true;\n    centerNode = list[0];\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    var node = list[i];\n    var degree = node.getNeighborsList().size;\n    remainingDegrees.set(node, node.getNeighborsList().size);\n\n    if (degree == 1) {\n      removedNodes.push(node);\n    }\n  }\n\n  var tempList = [];\n  tempList = tempList.concat(removedNodes);\n\n  while (!foundCenter) {\n    var tempList2 = [];\n    tempList2 = tempList2.concat(tempList);\n    tempList = [];\n\n    for (var i = 0; i < list.length; i++) {\n      var node = list[i];\n\n      var index = list.indexOf(node);\n      if (index >= 0) {\n        list.splice(index, 1);\n      }\n\n      var neighbours = node.getNeighborsList();\n\n      neighbours.forEach(function (neighbour) {\n        if (removedNodes.indexOf(neighbour) < 0) {\n          var otherDegree = remainingDegrees.get(neighbour);\n          var newDegree = otherDegree - 1;\n\n          if (newDegree == 1) {\n            tempList.push(neighbour);\n          }\n\n          remainingDegrees.set(neighbour, newDegree);\n        }\n      });\n    }\n\n    removedNodes = removedNodes.concat(tempList);\n\n    if (list.length == 1 || list.length == 2) {\n      foundCenter = true;\n      centerNode = list[0];\n    }\n  }\n\n  return centerNode;\n};\n\n/**\r\n * During the coarsening process, this layout may be referenced by two graph managers\r\n * this setter function grants access to change the currently being used graph manager\r\n */\nLayout.prototype.setGraphManager = function (gm) {\n  this.graphManager = gm;\n};\n\nmodule.exports = Layout;\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction RandomSeed() {}\n// adapted from: https://stackoverflow.com/a/19303725\nRandomSeed.seed = 1;\nRandomSeed.x = 0;\n\nRandomSeed.nextDouble = function () {\n  RandomSeed.x = Math.sin(RandomSeed.seed++) * 10000;\n  return RandomSeed.x - Math.floor(RandomSeed.x);\n};\n\nmodule.exports = RandomSeed;\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar PointD = __webpack_require__(4);\n\nfunction Transform(x, y) {\n  this.lworldOrgX = 0.0;\n  this.lworldOrgY = 0.0;\n  this.ldeviceOrgX = 0.0;\n  this.ldeviceOrgY = 0.0;\n  this.lworldExtX = 1.0;\n  this.lworldExtY = 1.0;\n  this.ldeviceExtX = 1.0;\n  this.ldeviceExtY = 1.0;\n}\n\nTransform.prototype.getWorldOrgX = function () {\n  return this.lworldOrgX;\n};\n\nTransform.prototype.setWorldOrgX = function (wox) {\n  this.lworldOrgX = wox;\n};\n\nTransform.prototype.getWorldOrgY = function () {\n  return this.lworldOrgY;\n};\n\nTransform.prototype.setWorldOrgY = function (woy) {\n  this.lworldOrgY = woy;\n};\n\nTransform.prototype.getWorldExtX = function () {\n  return this.lworldExtX;\n};\n\nTransform.prototype.setWorldExtX = function (wex) {\n  this.lworldExtX = wex;\n};\n\nTransform.prototype.getWorldExtY = function () {\n  return this.lworldExtY;\n};\n\nTransform.prototype.setWorldExtY = function (wey) {\n  this.lworldExtY = wey;\n};\n\n/* Device related */\n\nTransform.prototype.getDeviceOrgX = function () {\n  return this.ldeviceOrgX;\n};\n\nTransform.prototype.setDeviceOrgX = function (dox) {\n  this.ldeviceOrgX = dox;\n};\n\nTransform.prototype.getDeviceOrgY = function () {\n  return this.ldeviceOrgY;\n};\n\nTransform.prototype.setDeviceOrgY = function (doy) {\n  this.ldeviceOrgY = doy;\n};\n\nTransform.prototype.getDeviceExtX = function () {\n  return this.ldeviceExtX;\n};\n\nTransform.prototype.setDeviceExtX = function (dex) {\n  this.ldeviceExtX = dex;\n};\n\nTransform.prototype.getDeviceExtY = function () {\n  return this.ldeviceExtY;\n};\n\nTransform.prototype.setDeviceExtY = function (dey) {\n  this.ldeviceExtY = dey;\n};\n\nTransform.prototype.transformX = function (x) {\n  var xDevice = 0.0;\n  var worldExtX = this.lworldExtX;\n  if (worldExtX != 0.0) {\n    xDevice = this.ldeviceOrgX + (x - this.lworldOrgX) * this.ldeviceExtX / worldExtX;\n  }\n\n  return xDevice;\n};\n\nTransform.prototype.transformY = function (y) {\n  var yDevice = 0.0;\n  var worldExtY = this.lworldExtY;\n  if (worldExtY != 0.0) {\n    yDevice = this.ldeviceOrgY + (y - this.lworldOrgY) * this.ldeviceExtY / worldExtY;\n  }\n\n  return yDevice;\n};\n\nTransform.prototype.inverseTransformX = function (x) {\n  var xWorld = 0.0;\n  var deviceExtX = this.ldeviceExtX;\n  if (deviceExtX != 0.0) {\n    xWorld = this.lworldOrgX + (x - this.ldeviceOrgX) * this.lworldExtX / deviceExtX;\n  }\n\n  return xWorld;\n};\n\nTransform.prototype.inverseTransformY = function (y) {\n  var yWorld = 0.0;\n  var deviceExtY = this.ldeviceExtY;\n  if (deviceExtY != 0.0) {\n    yWorld = this.lworldOrgY + (y - this.ldeviceOrgY) * this.lworldExtY / deviceExtY;\n  }\n  return yWorld;\n};\n\nTransform.prototype.inverseTransformPoint = function (inPoint) {\n  var outPoint = new PointD(this.inverseTransformX(inPoint.x), this.inverseTransformY(inPoint.y));\n  return outPoint;\n};\n\nmodule.exports = Transform;\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nvar Layout = __webpack_require__(15);\nvar FDLayoutConstants = __webpack_require__(7);\nvar LayoutConstants = __webpack_require__(0);\nvar IGeometry = __webpack_require__(8);\nvar IMath = __webpack_require__(9);\n\nfunction FDLayout() {\n  Layout.call(this);\n\n  this.useSmartIdealEdgeLengthCalculation = FDLayoutConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n  this.idealEdgeLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n  this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n  this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n  this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n  this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n  this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n  this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n  this.displacementThresholdPerNode = 3.0 * FDLayoutConstants.DEFAULT_EDGE_LENGTH / 100;\n  this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.initialCoolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n  this.totalDisplacement = 0.0;\n  this.oldTotalDisplacement = 0.0;\n  this.maxIterations = FDLayoutConstants.MAX_ITERATIONS;\n}\n\nFDLayout.prototype = Object.create(Layout.prototype);\n\nfor (var prop in Layout) {\n  FDLayout[prop] = Layout[prop];\n}\n\nFDLayout.prototype.initParameters = function () {\n  Layout.prototype.initParameters.call(this, arguments);\n\n  this.totalIterations = 0;\n  this.notAnimatedIterations = 0;\n\n  this.useFRGridVariant = FDLayoutConstants.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION;\n\n  this.grid = [];\n};\n\nFDLayout.prototype.calcIdealEdgeLengths = function () {\n  var edge;\n  var lcaDepth;\n  var source;\n  var target;\n  var sizeOfSourceInLca;\n  var sizeOfTargetInLca;\n\n  var allEdges = this.getGraphManager().getAllEdges();\n  for (var i = 0; i < allEdges.length; i++) {\n    edge = allEdges[i];\n\n    edge.idealLength = this.idealEdgeLength;\n\n    if (edge.isInterGraph) {\n      source = edge.getSource();\n      target = edge.getTarget();\n\n      sizeOfSourceInLca = edge.getSourceInLca().getEstimatedSize();\n      sizeOfTargetInLca = edge.getTargetInLca().getEstimatedSize();\n\n      if (this.useSmartIdealEdgeLengthCalculation) {\n        edge.idealLength += sizeOfSourceInLca + sizeOfTargetInLca - 2 * LayoutConstants.SIMPLE_NODE_SIZE;\n      }\n\n      lcaDepth = edge.getLca().getInclusionTreeDepth();\n\n      edge.idealLength += FDLayoutConstants.DEFAULT_EDGE_LENGTH * FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR * (source.getInclusionTreeDepth() + target.getInclusionTreeDepth() - 2 * lcaDepth);\n    }\n  }\n};\n\nFDLayout.prototype.initSpringEmbedder = function () {\n\n  var s = this.getAllNodes().length;\n  if (this.incremental) {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(this.coolingFactor * FDLayoutConstants.COOLING_ADAPTATION_FACTOR, this.coolingFactor - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * this.coolingFactor * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    }\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT_INCREMENTAL;\n  } else {\n    if (s > FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) {\n      this.coolingFactor = Math.max(FDLayoutConstants.COOLING_ADAPTATION_FACTOR, 1.0 - (s - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) / (FDLayoutConstants.ADAPTATION_UPPER_NODE_LIMIT - FDLayoutConstants.ADAPTATION_LOWER_NODE_LIMIT) * (1 - FDLayoutConstants.COOLING_ADAPTATION_FACTOR));\n    } else {\n      this.coolingFactor = 1.0;\n    }\n    this.initialCoolingFactor = this.coolingFactor;\n    this.maxNodeDisplacement = FDLayoutConstants.MAX_NODE_DISPLACEMENT;\n  }\n\n  this.maxIterations = Math.max(this.getAllNodes().length * 5, this.maxIterations);\n\n  this.totalDisplacementThreshold = this.displacementThresholdPerNode * this.getAllNodes().length;\n\n  this.repulsionRange = this.calcRepulsionRange();\n};\n\nFDLayout.prototype.calcSpringForces = function () {\n  var lEdges = this.getAllEdges();\n  var edge;\n\n  for (var i = 0; i < lEdges.length; i++) {\n    edge = lEdges[i];\n\n    this.calcSpringForce(edge, edge.idealLength);\n  }\n};\n\nFDLayout.prototype.calcRepulsionForces = function () {\n  var gridUpdateAllowed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var forceToNodeSurroundingUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var i, j;\n  var nodeA, nodeB;\n  var lNodes = this.getAllNodes();\n  var processedNodeSet;\n\n  if (this.useFRGridVariant) {\n    if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed) {\n      this.updateGrid();\n    }\n\n    processedNodeSet = new Set();\n\n    // calculate repulsion forces between each nodes and its surrounding\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n      this.calculateRepulsionForceOfANode(nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate);\n      processedNodeSet.add(nodeA);\n    }\n  } else {\n    for (i = 0; i < lNodes.length; i++) {\n      nodeA = lNodes[i];\n\n      for (j = i + 1; j < lNodes.length; j++) {\n        nodeB = lNodes[j];\n\n        // If both nodes are not members of the same graph, skip.\n        if (nodeA.getOwner() != nodeB.getOwner()) {\n          continue;\n        }\n\n        this.calcRepulsionForce(nodeA, nodeB);\n      }\n    }\n  }\n};\n\nFDLayout.prototype.calcGravitationalForces = function () {\n  var node;\n  var lNodes = this.getAllNodesToApplyGravitation();\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    this.calcGravitationalForce(node);\n  }\n};\n\nFDLayout.prototype.moveNodes = function () {\n  var lNodes = this.getAllNodes();\n  var node;\n\n  for (var i = 0; i < lNodes.length; i++) {\n    node = lNodes[i];\n    node.move();\n  }\n};\n\nFDLayout.prototype.calcSpringForce = function (edge, idealLength) {\n  var sourceNode = edge.getSource();\n  var targetNode = edge.getTarget();\n\n  var length;\n  var springForce;\n  var springForceX;\n  var springForceY;\n\n  // Update edge length\n  if (this.uniformLeafNodeSizes && sourceNode.getChild() == null && targetNode.getChild() == null) {\n    edge.updateLengthSimple();\n  } else {\n    edge.updateLength();\n\n    if (edge.isOverlapingSourceAndTarget) {\n      return;\n    }\n  }\n\n  length = edge.getLength();\n\n  if (length == 0) return;\n\n  // Calculate spring forces\n  springForce = this.springConstant * (length - idealLength);\n\n  // Project force onto x and y axes\n  springForceX = springForce * (edge.lengthX / length);\n  springForceY = springForce * (edge.lengthY / length);\n\n  // Apply forces on the end nodes\n  sourceNode.springForceX += springForceX;\n  sourceNode.springForceY += springForceY;\n  targetNode.springForceX -= springForceX;\n  targetNode.springForceY -= springForceY;\n};\n\nFDLayout.prototype.calcRepulsionForce = function (nodeA, nodeB) {\n  var rectA = nodeA.getRect();\n  var rectB = nodeB.getRect();\n  var overlapAmount = new Array(2);\n  var clipPoints = new Array(4);\n  var distanceX;\n  var distanceY;\n  var distanceSquared;\n  var distance;\n  var repulsionForce;\n  var repulsionForceX;\n  var repulsionForceY;\n\n  if (rectA.intersects(rectB)) // two nodes overlap\n    {\n      // calculate separation amount in x and y directions\n      IGeometry.calcSeparationAmount(rectA, rectB, overlapAmount, FDLayoutConstants.DEFAULT_EDGE_LENGTH / 2.0);\n\n      repulsionForceX = 2 * overlapAmount[0];\n      repulsionForceY = 2 * overlapAmount[1];\n\n      var childrenConstant = nodeA.noOfChildren * nodeB.noOfChildren / (nodeA.noOfChildren + nodeB.noOfChildren);\n\n      // Apply forces on the two nodes\n      nodeA.repulsionForceX -= childrenConstant * repulsionForceX;\n      nodeA.repulsionForceY -= childrenConstant * repulsionForceY;\n      nodeB.repulsionForceX += childrenConstant * repulsionForceX;\n      nodeB.repulsionForceY += childrenConstant * repulsionForceY;\n    } else // no overlap\n    {\n      // calculate distance\n\n      if (this.uniformLeafNodeSizes && nodeA.getChild() == null && nodeB.getChild() == null) // simply base repulsion on distance of node centers\n        {\n          distanceX = rectB.getCenterX() - rectA.getCenterX();\n          distanceY = rectB.getCenterY() - rectA.getCenterY();\n        } else // use clipping points\n        {\n          IGeometry.getIntersection(rectA, rectB, clipPoints);\n\n          distanceX = clipPoints[2] - clipPoints[0];\n          distanceY = clipPoints[3] - clipPoints[1];\n        }\n\n      // No repulsion range. FR grid variant should take care of this.\n      if (Math.abs(distanceX) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceX = IMath.sign(distanceX) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      if (Math.abs(distanceY) < FDLayoutConstants.MIN_REPULSION_DIST) {\n        distanceY = IMath.sign(distanceY) * FDLayoutConstants.MIN_REPULSION_DIST;\n      }\n\n      distanceSquared = distanceX * distanceX + distanceY * distanceY;\n      distance = Math.sqrt(distanceSquared);\n\n      repulsionForce = this.repulsionConstant * nodeA.noOfChildren * nodeB.noOfChildren / distanceSquared;\n\n      // Project force onto x and y axes\n      repulsionForceX = repulsionForce * distanceX / distance;\n      repulsionForceY = repulsionForce * distanceY / distance;\n\n      // Apply forces on the two nodes    \n      nodeA.repulsionForceX -= repulsionForceX;\n      nodeA.repulsionForceY -= repulsionForceY;\n      nodeB.repulsionForceX += repulsionForceX;\n      nodeB.repulsionForceY += repulsionForceY;\n    }\n};\n\nFDLayout.prototype.calcGravitationalForce = function (node) {\n  var ownerGraph;\n  var ownerCenterX;\n  var ownerCenterY;\n  var distanceX;\n  var distanceY;\n  var absDistanceX;\n  var absDistanceY;\n  var estimatedSize;\n  ownerGraph = node.getOwner();\n\n  ownerCenterX = (ownerGraph.getRight() + ownerGraph.getLeft()) / 2;\n  ownerCenterY = (ownerGraph.getTop() + ownerGraph.getBottom()) / 2;\n  distanceX = node.getCenterX() - ownerCenterX;\n  distanceY = node.getCenterY() - ownerCenterY;\n  absDistanceX = Math.abs(distanceX) + node.getWidth() / 2;\n  absDistanceY = Math.abs(distanceY) + node.getHeight() / 2;\n\n  if (node.getOwner() == this.graphManager.getRoot()) // in the root graph\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.gravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX;\n        node.gravitationForceY = -this.gravityConstant * distanceY;\n      }\n    } else // inside a compound\n    {\n      estimatedSize = ownerGraph.getEstimatedSize() * this.compoundGravityRangeFactor;\n\n      if (absDistanceX > estimatedSize || absDistanceY > estimatedSize) {\n        node.gravitationForceX = -this.gravityConstant * distanceX * this.compoundGravityConstant;\n        node.gravitationForceY = -this.gravityConstant * distanceY * this.compoundGravityConstant;\n      }\n    }\n};\n\nFDLayout.prototype.isConverged = function () {\n  var converged;\n  var oscilating = false;\n\n  if (this.totalIterations > this.maxIterations / 3) {\n    oscilating = Math.abs(this.totalDisplacement - this.oldTotalDisplacement) < 2;\n  }\n\n  converged = this.totalDisplacement < this.totalDisplacementThreshold;\n\n  this.oldTotalDisplacement = this.totalDisplacement;\n\n  return converged || oscilating;\n};\n\nFDLayout.prototype.animate = function () {\n  if (this.animationDuringLayout && !this.isSubLayout) {\n    if (this.notAnimatedIterations == this.animationPeriod) {\n      this.update();\n      this.notAnimatedIterations = 0;\n    } else {\n      this.notAnimatedIterations++;\n    }\n  }\n};\n\n//This method calculates the number of children (weight) for all nodes\nFDLayout.prototype.calcNoOfChildrenForAllNodes = function () {\n  var node;\n  var allNodes = this.graphManager.getAllNodes();\n\n  for (var i = 0; i < allNodes.length; i++) {\n    node = allNodes[i];\n    node.noOfChildren = node.getNoOfChildren();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: FR-Grid Variant Repulsion Force Calculation\n// -----------------------------------------------------------------------------\n\nFDLayout.prototype.calcGrid = function (graph) {\n\n  var sizeX = 0;\n  var sizeY = 0;\n\n  sizeX = parseInt(Math.ceil((graph.getRight() - graph.getLeft()) / this.repulsionRange));\n  sizeY = parseInt(Math.ceil((graph.getBottom() - graph.getTop()) / this.repulsionRange));\n\n  var grid = new Array(sizeX);\n\n  for (var i = 0; i < sizeX; i++) {\n    grid[i] = new Array(sizeY);\n  }\n\n  for (var i = 0; i < sizeX; i++) {\n    for (var j = 0; j < sizeY; j++) {\n      grid[i][j] = new Array();\n    }\n  }\n\n  return grid;\n};\n\nFDLayout.prototype.addNodeToGrid = function (v, left, top) {\n\n  var startX = 0;\n  var finishX = 0;\n  var startY = 0;\n  var finishY = 0;\n\n  startX = parseInt(Math.floor((v.getRect().x - left) / this.repulsionRange));\n  finishX = parseInt(Math.floor((v.getRect().width + v.getRect().x - left) / this.repulsionRange));\n  startY = parseInt(Math.floor((v.getRect().y - top) / this.repulsionRange));\n  finishY = parseInt(Math.floor((v.getRect().height + v.getRect().y - top) / this.repulsionRange));\n\n  for (var i = startX; i <= finishX; i++) {\n    for (var j = startY; j <= finishY; j++) {\n      this.grid[i][j].push(v);\n      v.setGridCoordinates(startX, finishX, startY, finishY);\n    }\n  }\n};\n\nFDLayout.prototype.updateGrid = function () {\n  var i;\n  var nodeA;\n  var lNodes = this.getAllNodes();\n\n  this.grid = this.calcGrid(this.graphManager.getRoot());\n\n  // put all nodes to proper grid cells\n  for (i = 0; i < lNodes.length; i++) {\n    nodeA = lNodes[i];\n    this.addNodeToGrid(nodeA, this.graphManager.getRoot().getLeft(), this.graphManager.getRoot().getTop());\n  }\n};\n\nFDLayout.prototype.calculateRepulsionForceOfANode = function (nodeA, processedNodeSet, gridUpdateAllowed, forceToNodeSurroundingUpdate) {\n\n  if (this.totalIterations % FDLayoutConstants.GRID_CALCULATION_CHECK_PERIOD == 1 && gridUpdateAllowed || forceToNodeSurroundingUpdate) {\n    var surrounding = new Set();\n    nodeA.surrounding = new Array();\n    var nodeB;\n    var grid = this.grid;\n\n    for (var i = nodeA.startX - 1; i < nodeA.finishX + 2; i++) {\n      for (var j = nodeA.startY - 1; j < nodeA.finishY + 2; j++) {\n        if (!(i < 0 || j < 0 || i >= grid.length || j >= grid[0].length)) {\n          for (var k = 0; k < grid[i][j].length; k++) {\n            nodeB = grid[i][j][k];\n\n            // If both nodes are not members of the same graph, \n            // or both nodes are the same, skip.\n            if (nodeA.getOwner() != nodeB.getOwner() || nodeA == nodeB) {\n              continue;\n            }\n\n            // check if the repulsion force between\n            // nodeA and nodeB has already been calculated\n            if (!processedNodeSet.has(nodeB) && !surrounding.has(nodeB)) {\n              var distanceX = Math.abs(nodeA.getCenterX() - nodeB.getCenterX()) - (nodeA.getWidth() / 2 + nodeB.getWidth() / 2);\n              var distanceY = Math.abs(nodeA.getCenterY() - nodeB.getCenterY()) - (nodeA.getHeight() / 2 + nodeB.getHeight() / 2);\n\n              // if the distance between nodeA and nodeB \n              // is less then calculation range\n              if (distanceX <= this.repulsionRange && distanceY <= this.repulsionRange) {\n                //then add nodeB to surrounding of nodeA\n                surrounding.add(nodeB);\n              }\n            }\n          }\n        }\n      }\n    }\n\n    nodeA.surrounding = [].concat(_toConsumableArray(surrounding));\n  }\n  for (i = 0; i < nodeA.surrounding.length; i++) {\n    this.calcRepulsionForce(nodeA, nodeA.surrounding[i]);\n  }\n};\n\nFDLayout.prototype.calcRepulsionRange = function () {\n  return 0.0;\n};\n\nmodule.exports = FDLayout;\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LEdge = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(7);\n\nfunction FDLayoutEdge(source, target, vEdge) {\n  LEdge.call(this, source, target, vEdge);\n  this.idealLength = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\n}\n\nFDLayoutEdge.prototype = Object.create(LEdge.prototype);\n\nfor (var prop in LEdge) {\n  FDLayoutEdge[prop] = LEdge[prop];\n}\n\nmodule.exports = FDLayoutEdge;\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LNode = __webpack_require__(3);\n\nfunction FDLayoutNode(gm, loc, size, vNode) {\n  // alternative constructor is handled inside LNode\n  LNode.call(this, gm, loc, size, vNode);\n  //Spring, repulsion and gravitational forces acting on this node\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  //Amount by which this node is to be moved in this iteration\n  this.displacementX = 0;\n  this.displacementY = 0;\n\n  //Start and finish grid coordinates that this node is fallen into\n  this.startX = 0;\n  this.finishX = 0;\n  this.startY = 0;\n  this.finishY = 0;\n\n  //Geometric neighbors of this node\n  this.surrounding = [];\n}\n\nFDLayoutNode.prototype = Object.create(LNode.prototype);\n\nfor (var prop in LNode) {\n  FDLayoutNode[prop] = LNode[prop];\n}\n\nFDLayoutNode.prototype.setGridCoordinates = function (_startX, _finishX, _startY, _finishY) {\n  this.startX = _startX;\n  this.finishX = _finishX;\n  this.startY = _startY;\n  this.finishY = _finishY;\n};\n\nmodule.exports = FDLayoutNode;\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction DimensionD(width, height) {\n  this.width = 0;\n  this.height = 0;\n  if (width !== null && height !== null) {\n    this.height = height;\n    this.width = width;\n  }\n}\n\nDimensionD.prototype.getWidth = function () {\n  return this.width;\n};\n\nDimensionD.prototype.setWidth = function (width) {\n  this.width = width;\n};\n\nDimensionD.prototype.getHeight = function () {\n  return this.height;\n};\n\nDimensionD.prototype.setHeight = function (height) {\n  this.height = height;\n};\n\nmodule.exports = DimensionD;\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashMap() {\n  this.map = {};\n  this.keys = [];\n}\n\nHashMap.prototype.put = function (key, value) {\n  var theId = UniqueIDGeneretor.createID(key);\n  if (!this.contains(theId)) {\n    this.map[theId] = value;\n    this.keys.push(key);\n  }\n};\n\nHashMap.prototype.contains = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[key] != null;\n};\n\nHashMap.prototype.get = function (key) {\n  var theId = UniqueIDGeneretor.createID(key);\n  return this.map[theId];\n};\n\nHashMap.prototype.keySet = function () {\n  return this.keys;\n};\n\nmodule.exports = HashMap;\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar UniqueIDGeneretor = __webpack_require__(14);\n\nfunction HashSet() {\n  this.set = {};\n}\n;\n\nHashSet.prototype.add = function (obj) {\n  var theId = UniqueIDGeneretor.createID(obj);\n  if (!this.contains(theId)) this.set[theId] = obj;\n};\n\nHashSet.prototype.remove = function (obj) {\n  delete this.set[UniqueIDGeneretor.createID(obj)];\n};\n\nHashSet.prototype.clear = function () {\n  this.set = {};\n};\n\nHashSet.prototype.contains = function (obj) {\n  return this.set[UniqueIDGeneretor.createID(obj)] == obj;\n};\n\nHashSet.prototype.isEmpty = function () {\n  return this.size() === 0;\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\n//concats this.set to the given list\nHashSet.prototype.addAllTo = function (list) {\n  var keys = Object.keys(this.set);\n  var length = keys.length;\n  for (var i = 0; i < length; i++) {\n    list.push(this.set[keys[i]]);\n  }\n};\n\nHashSet.prototype.size = function () {\n  return Object.keys(this.set).length;\n};\n\nHashSet.prototype.addAll = function (list) {\n  var s = list.length;\n  for (var i = 0; i < s; i++) {\n    var v = list[i];\n    this.add(v);\n  }\n};\n\nmodule.exports = HashSet;\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n * A classic Quicksort algorithm with Hoare's partition\n * - Works also on LinkedList objects\n *\n * Copyright: i-Vis Research Group, Bilkent University, 2007 - present\n */\n\nvar LinkedList = __webpack_require__(11);\n\nvar Quicksort = function () {\n    function Quicksort(A, compareFunction) {\n        _classCallCheck(this, Quicksort);\n\n        if (compareFunction !== null || compareFunction !== undefined) this.compareFunction = this._defaultCompareFunction;\n\n        var length = void 0;\n        if (A instanceof LinkedList) length = A.size();else length = A.length;\n\n        this._quicksort(A, 0, length - 1);\n    }\n\n    _createClass(Quicksort, [{\n        key: '_quicksort',\n        value: function _quicksort(A, p, r) {\n            if (p < r) {\n                var q = this._partition(A, p, r);\n                this._quicksort(A, p, q);\n                this._quicksort(A, q + 1, r);\n            }\n        }\n    }, {\n        key: '_partition',\n        value: function _partition(A, p, r) {\n            var x = this._get(A, p);\n            var i = p;\n            var j = r;\n            while (true) {\n                while (this.compareFunction(x, this._get(A, j))) {\n                    j--;\n                }while (this.compareFunction(this._get(A, i), x)) {\n                    i++;\n                }if (i < j) {\n                    this._swap(A, i, j);\n                    i++;\n                    j--;\n                } else return j;\n            }\n        }\n    }, {\n        key: '_get',\n        value: function _get(object, index) {\n            if (object instanceof LinkedList) return object.get_object_at(index);else return object[index];\n        }\n    }, {\n        key: '_set',\n        value: function _set(object, index, value) {\n            if (object instanceof LinkedList) object.set_object_at(index, value);else object[index] = value;\n        }\n    }, {\n        key: '_swap',\n        value: function _swap(A, i, j) {\n            var temp = this._get(A, i);\n            this._set(A, i, this._get(A, j));\n            this._set(A, j, temp);\n        }\n    }, {\n        key: '_defaultCompareFunction',\n        value: function _defaultCompareFunction(a, b) {\n            return b > a;\n        }\n    }]);\n\n    return Quicksort;\n}();\n\nmodule.exports = Quicksort;\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/**\n *   Needleman-Wunsch algorithm is an procedure to compute the optimal global alignment of two string\n *   sequences by S.B.Needleman and C.D.Wunsch (1970).\n *\n *   Aside from the inputs, you can assign the scores for,\n *   - Match: The two characters at the current index are same.\n *   - Mismatch: The two characters at the current index are different.\n *   - Insertion/Deletion(gaps): The best alignment involves one letter aligning to a gap in the other string.\n */\n\nvar NeedlemanWunsch = function () {\n    function NeedlemanWunsch(sequence1, sequence2) {\n        var match_score = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n        var mismatch_penalty = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : -1;\n        var gap_penalty = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;\n\n        _classCallCheck(this, NeedlemanWunsch);\n\n        this.sequence1 = sequence1;\n        this.sequence2 = sequence2;\n        this.match_score = match_score;\n        this.mismatch_penalty = mismatch_penalty;\n        this.gap_penalty = gap_penalty;\n\n        // Just the remove redundancy\n        this.iMax = sequence1.length + 1;\n        this.jMax = sequence2.length + 1;\n\n        // Grid matrix of scores\n        this.grid = new Array(this.iMax);\n        for (var i = 0; i < this.iMax; i++) {\n            this.grid[i] = new Array(this.jMax);\n\n            for (var j = 0; j < this.jMax; j++) {\n                this.grid[i][j] = 0;\n            }\n        }\n\n        // Traceback matrix (2D array, each cell is an array of boolean values for [`Diag`, `Up`, `Left`] positions)\n        this.tracebackGrid = new Array(this.iMax);\n        for (var _i = 0; _i < this.iMax; _i++) {\n            this.tracebackGrid[_i] = new Array(this.jMax);\n\n            for (var _j = 0; _j < this.jMax; _j++) {\n                this.tracebackGrid[_i][_j] = [null, null, null];\n            }\n        }\n\n        // The aligned sequences (return multiple possibilities)\n        this.alignments = [];\n\n        // Final alignment score\n        this.score = -1;\n\n        // Calculate scores and tracebacks\n        this.computeGrids();\n    }\n\n    _createClass(NeedlemanWunsch, [{\n        key: \"getScore\",\n        value: function getScore() {\n            return this.score;\n        }\n    }, {\n        key: \"getAlignments\",\n        value: function getAlignments() {\n            return this.alignments;\n        }\n\n        // Main dynamic programming procedure\n\n    }, {\n        key: \"computeGrids\",\n        value: function computeGrids() {\n            // Fill in the first row\n            for (var j = 1; j < this.jMax; j++) {\n                this.grid[0][j] = this.grid[0][j - 1] + this.gap_penalty;\n                this.tracebackGrid[0][j] = [false, false, true];\n            }\n\n            // Fill in the first column\n            for (var i = 1; i < this.iMax; i++) {\n                this.grid[i][0] = this.grid[i - 1][0] + this.gap_penalty;\n                this.tracebackGrid[i][0] = [false, true, false];\n            }\n\n            // Fill the rest of the grid\n            for (var _i2 = 1; _i2 < this.iMax; _i2++) {\n                for (var _j2 = 1; _j2 < this.jMax; _j2++) {\n                    // Find the max score(s) among [`Diag`, `Up`, `Left`]\n                    var diag = void 0;\n                    if (this.sequence1[_i2 - 1] === this.sequence2[_j2 - 1]) diag = this.grid[_i2 - 1][_j2 - 1] + this.match_score;else diag = this.grid[_i2 - 1][_j2 - 1] + this.mismatch_penalty;\n\n                    var up = this.grid[_i2 - 1][_j2] + this.gap_penalty;\n                    var left = this.grid[_i2][_j2 - 1] + this.gap_penalty;\n\n                    // If there exists multiple max values, capture them for multiple paths\n                    var maxOf = [diag, up, left];\n                    var indices = this.arrayAllMaxIndexes(maxOf);\n\n                    // Update Grids\n                    this.grid[_i2][_j2] = maxOf[indices[0]];\n                    this.tracebackGrid[_i2][_j2] = [indices.includes(0), indices.includes(1), indices.includes(2)];\n                }\n            }\n\n            // Update alignment score\n            this.score = this.grid[this.iMax - 1][this.jMax - 1];\n        }\n\n        // Gets all possible valid sequence combinations\n\n    }, {\n        key: \"alignmentTraceback\",\n        value: function alignmentTraceback() {\n            var inProcessAlignments = [];\n\n            inProcessAlignments.push({ pos: [this.sequence1.length, this.sequence2.length],\n                seq1: \"\",\n                seq2: \"\"\n            });\n\n            while (inProcessAlignments[0]) {\n                var current = inProcessAlignments[0];\n                var directions = this.tracebackGrid[current.pos[0]][current.pos[1]];\n\n                if (directions[0]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1] - 1],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n                if (directions[1]) {\n                    inProcessAlignments.push({ pos: [current.pos[0] - 1, current.pos[1]],\n                        seq1: this.sequence1[current.pos[0] - 1] + current.seq1,\n                        seq2: '-' + current.seq2\n                    });\n                }\n                if (directions[2]) {\n                    inProcessAlignments.push({ pos: [current.pos[0], current.pos[1] - 1],\n                        seq1: '-' + current.seq1,\n                        seq2: this.sequence2[current.pos[1] - 1] + current.seq2\n                    });\n                }\n\n                if (current.pos[0] === 0 && current.pos[1] === 0) this.alignments.push({ sequence1: current.seq1,\n                    sequence2: current.seq2\n                });\n\n                inProcessAlignments.shift();\n            }\n\n            return this.alignments;\n        }\n\n        // Helper Functions\n\n    }, {\n        key: \"getAllIndexes\",\n        value: function getAllIndexes(arr, val) {\n            var indexes = [],\n                i = -1;\n            while ((i = arr.indexOf(val, i + 1)) !== -1) {\n                indexes.push(i);\n            }\n            return indexes;\n        }\n    }, {\n        key: \"arrayAllMaxIndexes\",\n        value: function arrayAllMaxIndexes(array) {\n            return this.getAllIndexes(array, Math.max.apply(null, array));\n        }\n    }]);\n\n    return NeedlemanWunsch;\n}();\n\nmodule.exports = NeedlemanWunsch;\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar layoutBase = function layoutBase() {\n  return;\n};\n\nlayoutBase.FDLayout = __webpack_require__(18);\nlayoutBase.FDLayoutConstants = __webpack_require__(7);\nlayoutBase.FDLayoutEdge = __webpack_require__(19);\nlayoutBase.FDLayoutNode = __webpack_require__(20);\nlayoutBase.DimensionD = __webpack_require__(21);\nlayoutBase.HashMap = __webpack_require__(22);\nlayoutBase.HashSet = __webpack_require__(23);\nlayoutBase.IGeometry = __webpack_require__(8);\nlayoutBase.IMath = __webpack_require__(9);\nlayoutBase.Integer = __webpack_require__(10);\nlayoutBase.Point = __webpack_require__(12);\nlayoutBase.PointD = __webpack_require__(4);\nlayoutBase.RandomSeed = __webpack_require__(16);\nlayoutBase.RectangleD = __webpack_require__(13);\nlayoutBase.Transform = __webpack_require__(17);\nlayoutBase.UniqueIDGeneretor = __webpack_require__(14);\nlayoutBase.Quicksort = __webpack_require__(24);\nlayoutBase.LinkedList = __webpack_require__(11);\nlayoutBase.LGraphObject = __webpack_require__(2);\nlayoutBase.LGraph = __webpack_require__(5);\nlayoutBase.LEdge = __webpack_require__(1);\nlayoutBase.LGraphManager = __webpack_require__(6);\nlayoutBase.LNode = __webpack_require__(3);\nlayoutBase.Layout = __webpack_require__(15);\nlayoutBase.LayoutConstants = __webpack_require__(0);\nlayoutBase.NeedlemanWunsch = __webpack_require__(25);\n\nmodule.exports = layoutBase;\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nfunction Emitter() {\n  this.listeners = [];\n}\n\nvar p = Emitter.prototype;\n\np.addListener = function (event, callback) {\n  this.listeners.push({\n    event: event,\n    callback: callback\n  });\n};\n\np.removeListener = function (event, callback) {\n  for (var i = this.listeners.length; i >= 0; i--) {\n    var l = this.listeners[i];\n\n    if (l.event === event && l.callback === callback) {\n      this.listeners.splice(i, 1);\n    }\n  }\n};\n\np.emit = function (event, data) {\n  for (var i = 0; i < this.listeners.length; i++) {\n    var l = this.listeners[i];\n\n    if (event === l.event) {\n      l.callback(data);\n    }\n  }\n};\n\nmodule.exports = Emitter;\n\n/***/ })\n/******/ ]);\n});", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"layout-base\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"layout-base\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"coseBase\"] = factory(require(\"layout-base\"));\n\telse\n\t\troot[\"coseBase\"] = factory(root[\"layoutBase\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_0__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 7);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_0__;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayoutConstants = __webpack_require__(0).FDLayoutConstants;\n\nfunction CoSEConstants() {}\n\n//CoSEConstants inherits static props in FDLayoutConstants\nfor (var prop in FDLayoutConstants) {\n  CoSEConstants[prop] = FDLayoutConstants[prop];\n}\n\nCoSEConstants.DEFAULT_USE_MULTI_LEVEL_SCALING = false;\nCoSEConstants.DEFAULT_RADIAL_SEPARATION = FDLayoutConstants.DEFAULT_EDGE_LENGTH;\nCoSEConstants.DEFAULT_COMPONENT_SEPERATION = 60;\nCoSEConstants.TILE = true;\nCoSEConstants.TILING_PADDING_VERTICAL = 10;\nCoSEConstants.TILING_PADDING_HORIZONTAL = 10;\nCoSEConstants.TREE_REDUCTION_ON_INCREMENTAL = false; // make this true when cose is used incrementally as a part of other non-incremental layout\n\nmodule.exports = CoSEConstants;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayoutEdge = __webpack_require__(0).FDLayoutEdge;\n\nfunction CoSEEdge(source, target, vEdge) {\n  FDLayoutEdge.call(this, source, target, vEdge);\n}\n\nCoSEEdge.prototype = Object.create(FDLayoutEdge.prototype);\nfor (var prop in FDLayoutEdge) {\n  CoSEEdge[prop] = FDLayoutEdge[prop];\n}\n\nmodule.exports = CoSEEdge;\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraph = __webpack_require__(0).LGraph;\n\nfunction CoSEGraph(parent, graphMgr, vGraph) {\n  LGraph.call(this, parent, graphMgr, vGraph);\n}\n\nCoSEGraph.prototype = Object.create(LGraph.prototype);\nfor (var prop in LGraph) {\n  CoSEGraph[prop] = LGraph[prop];\n}\n\nmodule.exports = CoSEGraph;\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LGraphManager = __webpack_require__(0).LGraphManager;\n\nfunction CoSEGraphManager(layout) {\n  LGraphManager.call(this, layout);\n}\n\nCoSEGraphManager.prototype = Object.create(LGraphManager.prototype);\nfor (var prop in LGraphManager) {\n  CoSEGraphManager[prop] = LGraphManager[prop];\n}\n\nmodule.exports = CoSEGraphManager;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayoutNode = __webpack_require__(0).FDLayoutNode;\nvar IMath = __webpack_require__(0).IMath;\n\nfunction CoSENode(gm, loc, size, vNode) {\n  FDLayoutNode.call(this, gm, loc, size, vNode);\n}\n\nCoSENode.prototype = Object.create(FDLayoutNode.prototype);\nfor (var prop in FDLayoutNode) {\n  CoSENode[prop] = FDLayoutNode[prop];\n}\n\nCoSENode.prototype.move = function () {\n  var layout = this.graphManager.getLayout();\n  this.displacementX = layout.coolingFactor * (this.springForceX + this.repulsionForceX + this.gravitationForceX) / this.noOfChildren;\n  this.displacementY = layout.coolingFactor * (this.springForceY + this.repulsionForceY + this.gravitationForceY) / this.noOfChildren;\n\n  if (Math.abs(this.displacementX) > layout.coolingFactor * layout.maxNodeDisplacement) {\n    this.displacementX = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementX);\n  }\n\n  if (Math.abs(this.displacementY) > layout.coolingFactor * layout.maxNodeDisplacement) {\n    this.displacementY = layout.coolingFactor * layout.maxNodeDisplacement * IMath.sign(this.displacementY);\n  }\n\n  // a simple node, just move it\n  if (this.child == null) {\n    this.moveBy(this.displacementX, this.displacementY);\n  }\n  // an empty compound node, again just move it\n  else if (this.child.getNodes().length == 0) {\n      this.moveBy(this.displacementX, this.displacementY);\n    }\n    // non-empty compound node, propogate movement to children as well\n    else {\n        this.propogateDisplacementToChildren(this.displacementX, this.displacementY);\n      }\n\n  layout.totalDisplacement += Math.abs(this.displacementX) + Math.abs(this.displacementY);\n\n  this.springForceX = 0;\n  this.springForceY = 0;\n  this.repulsionForceX = 0;\n  this.repulsionForceY = 0;\n  this.gravitationForceX = 0;\n  this.gravitationForceY = 0;\n  this.displacementX = 0;\n  this.displacementY = 0;\n};\n\nCoSENode.prototype.propogateDisplacementToChildren = function (dX, dY) {\n  var nodes = this.getChild().getNodes();\n  var node;\n  for (var i = 0; i < nodes.length; i++) {\n    node = nodes[i];\n    if (node.getChild() == null) {\n      node.moveBy(dX, dY);\n      node.displacementX += dX;\n      node.displacementY += dY;\n    } else {\n      node.propogateDisplacementToChildren(dX, dY);\n    }\n  }\n};\n\nCoSENode.prototype.setPred1 = function (pred1) {\n  this.pred1 = pred1;\n};\n\nCoSENode.prototype.getPred1 = function () {\n  return pred1;\n};\n\nCoSENode.prototype.getPred2 = function () {\n  return pred2;\n};\n\nCoSENode.prototype.setNext = function (next) {\n  this.next = next;\n};\n\nCoSENode.prototype.getNext = function () {\n  return next;\n};\n\nCoSENode.prototype.setProcessed = function (processed) {\n  this.processed = processed;\n};\n\nCoSENode.prototype.isProcessed = function () {\n  return processed;\n};\n\nmodule.exports = CoSENode;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar FDLayout = __webpack_require__(0).FDLayout;\nvar CoSEGraphManager = __webpack_require__(4);\nvar CoSEGraph = __webpack_require__(3);\nvar CoSENode = __webpack_require__(5);\nvar CoSEEdge = __webpack_require__(2);\nvar CoSEConstants = __webpack_require__(1);\nvar FDLayoutConstants = __webpack_require__(0).FDLayoutConstants;\nvar LayoutConstants = __webpack_require__(0).LayoutConstants;\nvar Point = __webpack_require__(0).Point;\nvar PointD = __webpack_require__(0).PointD;\nvar Layout = __webpack_require__(0).Layout;\nvar Integer = __webpack_require__(0).Integer;\nvar IGeometry = __webpack_require__(0).IGeometry;\nvar LGraph = __webpack_require__(0).LGraph;\nvar Transform = __webpack_require__(0).Transform;\n\nfunction CoSELayout() {\n  FDLayout.call(this);\n\n  this.toBeTiled = {}; // Memorize if a node is to be tiled or is tiled\n}\n\nCoSELayout.prototype = Object.create(FDLayout.prototype);\n\nfor (var prop in FDLayout) {\n  CoSELayout[prop] = FDLayout[prop];\n}\n\nCoSELayout.prototype.newGraphManager = function () {\n  var gm = new CoSEGraphManager(this);\n  this.graphManager = gm;\n  return gm;\n};\n\nCoSELayout.prototype.newGraph = function (vGraph) {\n  return new CoSEGraph(null, this.graphManager, vGraph);\n};\n\nCoSELayout.prototype.newNode = function (vNode) {\n  return new CoSENode(this.graphManager, vNode);\n};\n\nCoSELayout.prototype.newEdge = function (vEdge) {\n  return new CoSEEdge(null, null, vEdge);\n};\n\nCoSELayout.prototype.initParameters = function () {\n  FDLayout.prototype.initParameters.call(this, arguments);\n  if (!this.isSubLayout) {\n    if (CoSEConstants.DEFAULT_EDGE_LENGTH < 10) {\n      this.idealEdgeLength = 10;\n    } else {\n      this.idealEdgeLength = CoSEConstants.DEFAULT_EDGE_LENGTH;\n    }\n\n    this.useSmartIdealEdgeLengthCalculation = CoSEConstants.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION;\n    this.springConstant = FDLayoutConstants.DEFAULT_SPRING_STRENGTH;\n    this.repulsionConstant = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH;\n    this.gravityConstant = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH;\n    this.compoundGravityConstant = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH;\n    this.gravityRangeFactor = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR;\n    this.compoundGravityRangeFactor = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR;\n\n    // variables for tree reduction support\n    this.prunedNodesAll = [];\n    this.growTreeIterations = 0;\n    this.afterGrowthIterations = 0;\n    this.isTreeGrowing = false;\n    this.isGrowthFinished = false;\n\n    // variables for cooling\n    this.coolingCycle = 0;\n    this.maxCoolingCycle = this.maxIterations / FDLayoutConstants.CONVERGENCE_CHECK_PERIOD;\n    this.finalTemperature = FDLayoutConstants.CONVERGENCE_CHECK_PERIOD / this.maxIterations;\n    this.coolingAdjuster = 1;\n  }\n};\n\nCoSELayout.prototype.layout = function () {\n  var createBendsAsNeeded = LayoutConstants.DEFAULT_CREATE_BENDS_AS_NEEDED;\n  if (createBendsAsNeeded) {\n    this.createBendpoints();\n    this.graphManager.resetAllEdges();\n  }\n\n  this.level = 0;\n  return this.classicLayout();\n};\n\nCoSELayout.prototype.classicLayout = function () {\n  this.nodesWithGravity = this.calculateNodesToApplyGravitationTo();\n  this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity);\n  this.calcNoOfChildrenForAllNodes();\n  this.graphManager.calcLowestCommonAncestors();\n  this.graphManager.calcInclusionTreeDepths();\n  this.graphManager.getRoot().calcEstimatedSize();\n  this.calcIdealEdgeLengths();\n\n  if (!this.incremental) {\n    var forest = this.getFlatForest();\n\n    // The graph associated with this layout is flat and a forest\n    if (forest.length > 0) {\n      this.positionNodesRadially(forest);\n    }\n    // The graph associated with this layout is not flat or a forest\n    else {\n        // Reduce the trees when incremental mode is not enabled and graph is not a forest \n        this.reduceTrees();\n        // Update nodes that gravity will be applied\n        this.graphManager.resetAllNodesToApplyGravitation();\n        var allNodes = new Set(this.getAllNodes());\n        var intersection = this.nodesWithGravity.filter(function (x) {\n          return allNodes.has(x);\n        });\n        this.graphManager.setAllNodesToApplyGravitation(intersection);\n\n        this.positionNodesRandomly();\n      }\n  } else {\n    if (CoSEConstants.TREE_REDUCTION_ON_INCREMENTAL) {\n      // Reduce the trees in incremental mode if only this constant is set to true \n      this.reduceTrees();\n      // Update nodes that gravity will be applied\n      this.graphManager.resetAllNodesToApplyGravitation();\n      var allNodes = new Set(this.getAllNodes());\n      var intersection = this.nodesWithGravity.filter(function (x) {\n        return allNodes.has(x);\n      });\n      this.graphManager.setAllNodesToApplyGravitation(intersection);\n    }\n  }\n\n  this.initSpringEmbedder();\n  this.runSpringEmbedder();\n\n  return true;\n};\n\nCoSELayout.prototype.tick = function () {\n  this.totalIterations++;\n\n  if (this.totalIterations === this.maxIterations && !this.isTreeGrowing && !this.isGrowthFinished) {\n    if (this.prunedNodesAll.length > 0) {\n      this.isTreeGrowing = true;\n    } else {\n      return true;\n    }\n  }\n\n  if (this.totalIterations % FDLayoutConstants.CONVERGENCE_CHECK_PERIOD == 0 && !this.isTreeGrowing && !this.isGrowthFinished) {\n    if (this.isConverged()) {\n      if (this.prunedNodesAll.length > 0) {\n        this.isTreeGrowing = true;\n      } else {\n        return true;\n      }\n    }\n\n    this.coolingCycle++;\n\n    if (this.layoutQuality == 0) {\n      // quality - \"draft\"\n      this.coolingAdjuster = this.coolingCycle;\n    } else if (this.layoutQuality == 1) {\n      // quality - \"default\"\n      this.coolingAdjuster = this.coolingCycle / 3;\n    }\n\n    // cooling schedule is based on http://www.btluke.com/simanf1.html -> cooling schedule 3\n    this.coolingFactor = Math.max(this.initialCoolingFactor - Math.pow(this.coolingCycle, Math.log(100 * (this.initialCoolingFactor - this.finalTemperature)) / Math.log(this.maxCoolingCycle)) / 100 * this.coolingAdjuster, this.finalTemperature);\n    this.animationPeriod = Math.ceil(this.initialAnimationPeriod * Math.sqrt(this.coolingFactor));\n  }\n  // Operations while tree is growing again \n  if (this.isTreeGrowing) {\n    if (this.growTreeIterations % 10 == 0) {\n      if (this.prunedNodesAll.length > 0) {\n        this.graphManager.updateBounds();\n        this.updateGrid();\n        this.growTree(this.prunedNodesAll);\n        // Update nodes that gravity will be applied\n        this.graphManager.resetAllNodesToApplyGravitation();\n        var allNodes = new Set(this.getAllNodes());\n        var intersection = this.nodesWithGravity.filter(function (x) {\n          return allNodes.has(x);\n        });\n        this.graphManager.setAllNodesToApplyGravitation(intersection);\n\n        this.graphManager.updateBounds();\n        this.updateGrid();\n        this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL;\n      } else {\n        this.isTreeGrowing = false;\n        this.isGrowthFinished = true;\n      }\n    }\n    this.growTreeIterations++;\n  }\n  // Operations after growth is finished\n  if (this.isGrowthFinished) {\n    if (this.isConverged()) {\n      return true;\n    }\n    if (this.afterGrowthIterations % 10 == 0) {\n      this.graphManager.updateBounds();\n      this.updateGrid();\n    }\n    this.coolingFactor = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL * ((100 - this.afterGrowthIterations) / 100);\n    this.afterGrowthIterations++;\n  }\n\n  var gridUpdateAllowed = !this.isTreeGrowing && !this.isGrowthFinished;\n  var forceToNodeSurroundingUpdate = this.growTreeIterations % 10 == 1 && this.isTreeGrowing || this.afterGrowthIterations % 10 == 1 && this.isGrowthFinished;\n\n  this.totalDisplacement = 0;\n  this.graphManager.updateBounds();\n  this.calcSpringForces();\n  this.calcRepulsionForces(gridUpdateAllowed, forceToNodeSurroundingUpdate);\n  this.calcGravitationalForces();\n  this.moveNodes();\n  this.animate();\n\n  return false; // Layout is not ended yet return false\n};\n\nCoSELayout.prototype.getPositionsData = function () {\n  var allNodes = this.graphManager.getAllNodes();\n  var pData = {};\n  for (var i = 0; i < allNodes.length; i++) {\n    var rect = allNodes[i].rect;\n    var id = allNodes[i].id;\n    pData[id] = {\n      id: id,\n      x: rect.getCenterX(),\n      y: rect.getCenterY(),\n      w: rect.width,\n      h: rect.height\n    };\n  }\n\n  return pData;\n};\n\nCoSELayout.prototype.runSpringEmbedder = function () {\n  this.initialAnimationPeriod = 25;\n  this.animationPeriod = this.initialAnimationPeriod;\n  var layoutEnded = false;\n\n  // If aminate option is 'during' signal that layout is supposed to start iterating\n  if (FDLayoutConstants.ANIMATE === 'during') {\n    this.emit('layoutstarted');\n  } else {\n    // If aminate option is 'during' tick() function will be called on index.js\n    while (!layoutEnded) {\n      layoutEnded = this.tick();\n    }\n\n    this.graphManager.updateBounds();\n  }\n};\n\nCoSELayout.prototype.calculateNodesToApplyGravitationTo = function () {\n  var nodeList = [];\n  var graph;\n\n  var graphs = this.graphManager.getGraphs();\n  var size = graphs.length;\n  var i;\n  for (i = 0; i < size; i++) {\n    graph = graphs[i];\n\n    graph.updateConnected();\n\n    if (!graph.isConnected) {\n      nodeList = nodeList.concat(graph.getNodes());\n    }\n  }\n\n  return nodeList;\n};\n\nCoSELayout.prototype.createBendpoints = function () {\n  var edges = [];\n  edges = edges.concat(this.graphManager.getAllEdges());\n  var visited = new Set();\n  var i;\n  for (i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n\n    if (!visited.has(edge)) {\n      var source = edge.getSource();\n      var target = edge.getTarget();\n\n      if (source == target) {\n        edge.getBendpoints().push(new PointD());\n        edge.getBendpoints().push(new PointD());\n        this.createDummyNodesForBendpoints(edge);\n        visited.add(edge);\n      } else {\n        var edgeList = [];\n\n        edgeList = edgeList.concat(source.getEdgeListToNode(target));\n        edgeList = edgeList.concat(target.getEdgeListToNode(source));\n\n        if (!visited.has(edgeList[0])) {\n          if (edgeList.length > 1) {\n            var k;\n            for (k = 0; k < edgeList.length; k++) {\n              var multiEdge = edgeList[k];\n              multiEdge.getBendpoints().push(new PointD());\n              this.createDummyNodesForBendpoints(multiEdge);\n            }\n          }\n          edgeList.forEach(function (edge) {\n            visited.add(edge);\n          });\n        }\n      }\n    }\n\n    if (visited.size == edges.length) {\n      break;\n    }\n  }\n};\n\nCoSELayout.prototype.positionNodesRadially = function (forest) {\n  // We tile the trees to a grid row by row; first tree starts at (0,0)\n  var currentStartingPoint = new Point(0, 0);\n  var numberOfColumns = Math.ceil(Math.sqrt(forest.length));\n  var height = 0;\n  var currentY = 0;\n  var currentX = 0;\n  var point = new PointD(0, 0);\n\n  for (var i = 0; i < forest.length; i++) {\n    if (i % numberOfColumns == 0) {\n      // Start of a new row, make the x coordinate 0, increment the\n      // y coordinate with the max height of the previous row\n      currentX = 0;\n      currentY = height;\n\n      if (i != 0) {\n        currentY += CoSEConstants.DEFAULT_COMPONENT_SEPERATION;\n      }\n\n      height = 0;\n    }\n\n    var tree = forest[i];\n\n    // Find the center of the tree\n    var centerNode = Layout.findCenterOfTree(tree);\n\n    // Set the staring point of the next tree\n    currentStartingPoint.x = currentX;\n    currentStartingPoint.y = currentY;\n\n    // Do a radial layout starting with the center\n    point = CoSELayout.radialLayout(tree, centerNode, currentStartingPoint);\n\n    if (point.y > height) {\n      height = Math.floor(point.y);\n    }\n\n    currentX = Math.floor(point.x + CoSEConstants.DEFAULT_COMPONENT_SEPERATION);\n  }\n\n  this.transform(new PointD(LayoutConstants.WORLD_CENTER_X - point.x / 2, LayoutConstants.WORLD_CENTER_Y - point.y / 2));\n};\n\nCoSELayout.radialLayout = function (tree, centerNode, startingPoint) {\n  var radialSep = Math.max(this.maxDiagonalInTree(tree), CoSEConstants.DEFAULT_RADIAL_SEPARATION);\n  CoSELayout.branchRadialLayout(centerNode, null, 0, 359, 0, radialSep);\n  var bounds = LGraph.calculateBounds(tree);\n\n  var transform = new Transform();\n  transform.setDeviceOrgX(bounds.getMinX());\n  transform.setDeviceOrgY(bounds.getMinY());\n  transform.setWorldOrgX(startingPoint.x);\n  transform.setWorldOrgY(startingPoint.y);\n\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    node.transform(transform);\n  }\n\n  var bottomRight = new PointD(bounds.getMaxX(), bounds.getMaxY());\n\n  return transform.inverseTransformPoint(bottomRight);\n};\n\nCoSELayout.branchRadialLayout = function (node, parentOfNode, startAngle, endAngle, distance, radialSeparation) {\n  // First, position this node by finding its angle.\n  var halfInterval = (endAngle - startAngle + 1) / 2;\n\n  if (halfInterval < 0) {\n    halfInterval += 180;\n  }\n\n  var nodeAngle = (halfInterval + startAngle) % 360;\n  var teta = nodeAngle * IGeometry.TWO_PI / 360;\n\n  // Make polar to java cordinate conversion.\n  var cos_teta = Math.cos(teta);\n  var x_ = distance * Math.cos(teta);\n  var y_ = distance * Math.sin(teta);\n\n  node.setCenter(x_, y_);\n\n  // Traverse all neighbors of this node and recursively call this\n  // function.\n  var neighborEdges = [];\n  neighborEdges = neighborEdges.concat(node.getEdges());\n  var childCount = neighborEdges.length;\n\n  if (parentOfNode != null) {\n    childCount--;\n  }\n\n  var branchCount = 0;\n\n  var incEdgesCount = neighborEdges.length;\n  var startIndex;\n\n  var edges = node.getEdgesBetween(parentOfNode);\n\n  // If there are multiple edges, prune them until there remains only one\n  // edge.\n  while (edges.length > 1) {\n    //neighborEdges.remove(edges.remove(0));\n    var temp = edges[0];\n    edges.splice(0, 1);\n    var index = neighborEdges.indexOf(temp);\n    if (index >= 0) {\n      neighborEdges.splice(index, 1);\n    }\n    incEdgesCount--;\n    childCount--;\n  }\n\n  if (parentOfNode != null) {\n    //assert edges.length == 1;\n    startIndex = (neighborEdges.indexOf(edges[0]) + 1) % incEdgesCount;\n  } else {\n    startIndex = 0;\n  }\n\n  var stepAngle = Math.abs(endAngle - startAngle) / childCount;\n\n  for (var i = startIndex; branchCount != childCount; i = ++i % incEdgesCount) {\n    var currentNeighbor = neighborEdges[i].getOtherEnd(node);\n\n    // Don't back traverse to root node in current tree.\n    if (currentNeighbor == parentOfNode) {\n      continue;\n    }\n\n    var childStartAngle = (startAngle + branchCount * stepAngle) % 360;\n    var childEndAngle = (childStartAngle + stepAngle) % 360;\n\n    CoSELayout.branchRadialLayout(currentNeighbor, node, childStartAngle, childEndAngle, distance + radialSeparation, radialSeparation);\n\n    branchCount++;\n  }\n};\n\nCoSELayout.maxDiagonalInTree = function (tree) {\n  var maxDiagonal = Integer.MIN_VALUE;\n\n  for (var i = 0; i < tree.length; i++) {\n    var node = tree[i];\n    var diagonal = node.getDiagonal();\n\n    if (diagonal > maxDiagonal) {\n      maxDiagonal = diagonal;\n    }\n  }\n\n  return maxDiagonal;\n};\n\nCoSELayout.prototype.calcRepulsionRange = function () {\n  // formula is 2 x (level + 1) x idealEdgeLength\n  return 2 * (this.level + 1) * this.idealEdgeLength;\n};\n\n// Tiling methods\n\n// Group zero degree members whose parents are not to be tiled, create dummy parents where needed and fill memberGroups by their dummp parent id's\nCoSELayout.prototype.groupZeroDegreeMembers = function () {\n  var self = this;\n  // array of [parent_id x oneDegreeNode_id]\n  var tempMemberGroups = {}; // A temporary map of parent node and its zero degree members\n  this.memberGroups = {}; // A map of dummy parent node and its zero degree members whose parents are not to be tiled\n  this.idToDummyNode = {}; // A map of id to dummy node \n\n  var zeroDegree = []; // List of zero degree nodes whose parents are not to be tiled\n  var allNodes = this.graphManager.getAllNodes();\n\n  // Fill zero degree list\n  for (var i = 0; i < allNodes.length; i++) {\n    var node = allNodes[i];\n    var parent = node.getParent();\n    // If a node has zero degree and its parent is not to be tiled if exists add that node to zeroDegres list\n    if (this.getNodeDegreeWithChildren(node) === 0 && (parent.id == undefined || !this.getToBeTiled(parent))) {\n      zeroDegree.push(node);\n    }\n  }\n\n  // Create a map of parent node and its zero degree members\n  for (var i = 0; i < zeroDegree.length; i++) {\n    var node = zeroDegree[i]; // Zero degree node itself\n    var p_id = node.getParent().id; // Parent id\n\n    if (typeof tempMemberGroups[p_id] === \"undefined\") tempMemberGroups[p_id] = [];\n\n    tempMemberGroups[p_id] = tempMemberGroups[p_id].concat(node); // Push node to the list belongs to its parent in tempMemberGroups\n  }\n\n  // If there are at least two nodes at a level, create a dummy compound for them\n  Object.keys(tempMemberGroups).forEach(function (p_id) {\n    if (tempMemberGroups[p_id].length > 1) {\n      var dummyCompoundId = \"DummyCompound_\" + p_id; // The id of dummy compound which will be created soon\n      self.memberGroups[dummyCompoundId] = tempMemberGroups[p_id]; // Add dummy compound to memberGroups\n\n      var parent = tempMemberGroups[p_id][0].getParent(); // The parent of zero degree nodes will be the parent of new dummy compound\n\n      // Create a dummy compound with calculated id\n      var dummyCompound = new CoSENode(self.graphManager);\n      dummyCompound.id = dummyCompoundId;\n      dummyCompound.paddingLeft = parent.paddingLeft || 0;\n      dummyCompound.paddingRight = parent.paddingRight || 0;\n      dummyCompound.paddingBottom = parent.paddingBottom || 0;\n      dummyCompound.paddingTop = parent.paddingTop || 0;\n\n      self.idToDummyNode[dummyCompoundId] = dummyCompound;\n\n      var dummyParentGraph = self.getGraphManager().add(self.newGraph(), dummyCompound);\n      var parentGraph = parent.getChild();\n\n      // Add dummy compound to parent the graph\n      parentGraph.add(dummyCompound);\n\n      // For each zero degree node in this level remove it from its parent graph and add it to the graph of dummy parent\n      for (var i = 0; i < tempMemberGroups[p_id].length; i++) {\n        var node = tempMemberGroups[p_id][i];\n\n        parentGraph.remove(node);\n        dummyParentGraph.add(node);\n      }\n    }\n  });\n};\n\nCoSELayout.prototype.clearCompounds = function () {\n  var childGraphMap = {};\n  var idToNode = {};\n\n  // Get compound ordering by finding the inner one first\n  this.performDFSOnCompounds();\n\n  for (var i = 0; i < this.compoundOrder.length; i++) {\n\n    idToNode[this.compoundOrder[i].id] = this.compoundOrder[i];\n    childGraphMap[this.compoundOrder[i].id] = [].concat(this.compoundOrder[i].getChild().getNodes());\n\n    // Remove children of compounds\n    this.graphManager.remove(this.compoundOrder[i].getChild());\n    this.compoundOrder[i].child = null;\n  }\n\n  this.graphManager.resetAllNodes();\n\n  // Tile the removed children\n  this.tileCompoundMembers(childGraphMap, idToNode);\n};\n\nCoSELayout.prototype.clearZeroDegreeMembers = function () {\n  var self = this;\n  var tiledZeroDegreePack = this.tiledZeroDegreePack = [];\n\n  Object.keys(this.memberGroups).forEach(function (id) {\n    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound\n\n    tiledZeroDegreePack[id] = self.tileNodes(self.memberGroups[id], compoundNode.paddingLeft + compoundNode.paddingRight);\n\n    // Set the width and height of the dummy compound as calculated\n    compoundNode.rect.width = tiledZeroDegreePack[id].width;\n    compoundNode.rect.height = tiledZeroDegreePack[id].height;\n  });\n};\n\nCoSELayout.prototype.repopulateCompounds = function () {\n  for (var i = this.compoundOrder.length - 1; i >= 0; i--) {\n    var lCompoundNode = this.compoundOrder[i];\n    var id = lCompoundNode.id;\n    var horizontalMargin = lCompoundNode.paddingLeft;\n    var verticalMargin = lCompoundNode.paddingTop;\n\n    this.adjustLocations(this.tiledMemberPack[id], lCompoundNode.rect.x, lCompoundNode.rect.y, horizontalMargin, verticalMargin);\n  }\n};\n\nCoSELayout.prototype.repopulateZeroDegreeMembers = function () {\n  var self = this;\n  var tiledPack = this.tiledZeroDegreePack;\n\n  Object.keys(tiledPack).forEach(function (id) {\n    var compoundNode = self.idToDummyNode[id]; // Get the dummy compound by its id\n    var horizontalMargin = compoundNode.paddingLeft;\n    var verticalMargin = compoundNode.paddingTop;\n\n    // Adjust the positions of nodes wrt its compound\n    self.adjustLocations(tiledPack[id], compoundNode.rect.x, compoundNode.rect.y, horizontalMargin, verticalMargin);\n  });\n};\n\nCoSELayout.prototype.getToBeTiled = function (node) {\n  var id = node.id;\n  //firstly check the previous results\n  if (this.toBeTiled[id] != null) {\n    return this.toBeTiled[id];\n  }\n\n  //only compound nodes are to be tiled\n  var childGraph = node.getChild();\n  if (childGraph == null) {\n    this.toBeTiled[id] = false;\n    return false;\n  }\n\n  var children = childGraph.getNodes(); // Get the children nodes\n\n  //a compound node is not to be tiled if all of its compound children are not to be tiled\n  for (var i = 0; i < children.length; i++) {\n    var theChild = children[i];\n\n    if (this.getNodeDegree(theChild) > 0) {\n      this.toBeTiled[id] = false;\n      return false;\n    }\n\n    //pass the children not having the compound structure\n    if (theChild.getChild() == null) {\n      this.toBeTiled[theChild.id] = false;\n      continue;\n    }\n\n    if (!this.getToBeTiled(theChild)) {\n      this.toBeTiled[id] = false;\n      return false;\n    }\n  }\n  this.toBeTiled[id] = true;\n  return true;\n};\n\n// Get degree of a node depending of its edges and independent of its children\nCoSELayout.prototype.getNodeDegree = function (node) {\n  var id = node.id;\n  var edges = node.getEdges();\n  var degree = 0;\n\n  // For the edges connected\n  for (var i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n    if (edge.getSource().id !== edge.getTarget().id) {\n      degree = degree + 1;\n    }\n  }\n  return degree;\n};\n\n// Get degree of a node with its children\nCoSELayout.prototype.getNodeDegreeWithChildren = function (node) {\n  var degree = this.getNodeDegree(node);\n  if (node.getChild() == null) {\n    return degree;\n  }\n  var children = node.getChild().getNodes();\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    degree += this.getNodeDegreeWithChildren(child);\n  }\n  return degree;\n};\n\nCoSELayout.prototype.performDFSOnCompounds = function () {\n  this.compoundOrder = [];\n  this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes());\n};\n\nCoSELayout.prototype.fillCompexOrderByDFS = function (children) {\n  for (var i = 0; i < children.length; i++) {\n    var child = children[i];\n    if (child.getChild() != null) {\n      this.fillCompexOrderByDFS(child.getChild().getNodes());\n    }\n    if (this.getToBeTiled(child)) {\n      this.compoundOrder.push(child);\n    }\n  }\n};\n\n/**\n* This method places each zero degree member wrt given (x,y) coordinates (top left).\n*/\nCoSELayout.prototype.adjustLocations = function (organization, x, y, compoundHorizontalMargin, compoundVerticalMargin) {\n  x += compoundHorizontalMargin;\n  y += compoundVerticalMargin;\n\n  var left = x;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n    var row = organization.rows[i];\n    x = left;\n    var maxHeight = 0;\n\n    for (var j = 0; j < row.length; j++) {\n      var lnode = row[j];\n\n      lnode.rect.x = x; // + lnode.rect.width / 2;\n      lnode.rect.y = y; // + lnode.rect.height / 2;\n\n      x += lnode.rect.width + organization.horizontalPadding;\n\n      if (lnode.rect.height > maxHeight) maxHeight = lnode.rect.height;\n    }\n\n    y += maxHeight + organization.verticalPadding;\n  }\n};\n\nCoSELayout.prototype.tileCompoundMembers = function (childGraphMap, idToNode) {\n  var self = this;\n  this.tiledMemberPack = [];\n\n  Object.keys(childGraphMap).forEach(function (id) {\n    // Get the compound node\n    var compoundNode = idToNode[id];\n\n    self.tiledMemberPack[id] = self.tileNodes(childGraphMap[id], compoundNode.paddingLeft + compoundNode.paddingRight);\n\n    compoundNode.rect.width = self.tiledMemberPack[id].width;\n    compoundNode.rect.height = self.tiledMemberPack[id].height;\n  });\n};\n\nCoSELayout.prototype.tileNodes = function (nodes, minWidth) {\n  var verticalPadding = CoSEConstants.TILING_PADDING_VERTICAL;\n  var horizontalPadding = CoSEConstants.TILING_PADDING_HORIZONTAL;\n  var organization = {\n    rows: [],\n    rowWidth: [],\n    rowHeight: [],\n    width: 0,\n    height: minWidth, // assume minHeight equals to minWidth\n    verticalPadding: verticalPadding,\n    horizontalPadding: horizontalPadding\n  };\n\n  // Sort the nodes in ascending order of their areas\n  nodes.sort(function (n1, n2) {\n    if (n1.rect.width * n1.rect.height > n2.rect.width * n2.rect.height) return -1;\n    if (n1.rect.width * n1.rect.height < n2.rect.width * n2.rect.height) return 1;\n    return 0;\n  });\n\n  // Create the organization -> tile members\n  for (var i = 0; i < nodes.length; i++) {\n    var lNode = nodes[i];\n\n    if (organization.rows.length == 0) {\n      this.insertNodeToRow(organization, lNode, 0, minWidth);\n    } else if (this.canAddHorizontal(organization, lNode.rect.width, lNode.rect.height)) {\n      this.insertNodeToRow(organization, lNode, this.getShortestRowIndex(organization), minWidth);\n    } else {\n      this.insertNodeToRow(organization, lNode, organization.rows.length, minWidth);\n    }\n\n    this.shiftToLastRow(organization);\n  }\n\n  return organization;\n};\n\nCoSELayout.prototype.insertNodeToRow = function (organization, node, rowIndex, minWidth) {\n  var minCompoundSize = minWidth;\n\n  // Add new row if needed\n  if (rowIndex == organization.rows.length) {\n    var secondDimension = [];\n\n    organization.rows.push(secondDimension);\n    organization.rowWidth.push(minCompoundSize);\n    organization.rowHeight.push(0);\n  }\n\n  // Update row width\n  var w = organization.rowWidth[rowIndex] + node.rect.width;\n\n  if (organization.rows[rowIndex].length > 0) {\n    w += organization.horizontalPadding;\n  }\n\n  organization.rowWidth[rowIndex] = w;\n  // Update compound width\n  if (organization.width < w) {\n    organization.width = w;\n  }\n\n  // Update height\n  var h = node.rect.height;\n  if (rowIndex > 0) h += organization.verticalPadding;\n\n  var extraHeight = 0;\n  if (h > organization.rowHeight[rowIndex]) {\n    extraHeight = organization.rowHeight[rowIndex];\n    organization.rowHeight[rowIndex] = h;\n    extraHeight = organization.rowHeight[rowIndex] - extraHeight;\n  }\n\n  organization.height += extraHeight;\n\n  // Insert node\n  organization.rows[rowIndex].push(node);\n};\n\n//Scans the rows of an organization and returns the one with the min width\nCoSELayout.prototype.getShortestRowIndex = function (organization) {\n  var r = -1;\n  var min = Number.MAX_VALUE;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n    if (organization.rowWidth[i] < min) {\n      r = i;\n      min = organization.rowWidth[i];\n    }\n  }\n  return r;\n};\n\n//Scans the rows of an organization and returns the one with the max width\nCoSELayout.prototype.getLongestRowIndex = function (organization) {\n  var r = -1;\n  var max = Number.MIN_VALUE;\n\n  for (var i = 0; i < organization.rows.length; i++) {\n\n    if (organization.rowWidth[i] > max) {\n      r = i;\n      max = organization.rowWidth[i];\n    }\n  }\n\n  return r;\n};\n\n/**\n* This method checks whether adding extra width to the organization violates\n* the aspect ratio(1) or not.\n*/\nCoSELayout.prototype.canAddHorizontal = function (organization, extraWidth, extraHeight) {\n\n  var sri = this.getShortestRowIndex(organization);\n\n  if (sri < 0) {\n    return true;\n  }\n\n  var min = organization.rowWidth[sri];\n\n  if (min + organization.horizontalPadding + extraWidth <= organization.width) return true;\n\n  var hDiff = 0;\n\n  // Adding to an existing row\n  if (organization.rowHeight[sri] < extraHeight) {\n    if (sri > 0) hDiff = extraHeight + organization.verticalPadding - organization.rowHeight[sri];\n  }\n\n  var add_to_row_ratio;\n  if (organization.width - min >= extraWidth + organization.horizontalPadding) {\n    add_to_row_ratio = (organization.height + hDiff) / (min + extraWidth + organization.horizontalPadding);\n  } else {\n    add_to_row_ratio = (organization.height + hDiff) / organization.width;\n  }\n\n  // Adding a new row for this node\n  hDiff = extraHeight + organization.verticalPadding;\n  var add_new_row_ratio;\n  if (organization.width < extraWidth) {\n    add_new_row_ratio = (organization.height + hDiff) / extraWidth;\n  } else {\n    add_new_row_ratio = (organization.height + hDiff) / organization.width;\n  }\n\n  if (add_new_row_ratio < 1) add_new_row_ratio = 1 / add_new_row_ratio;\n\n  if (add_to_row_ratio < 1) add_to_row_ratio = 1 / add_to_row_ratio;\n\n  return add_to_row_ratio < add_new_row_ratio;\n};\n\n//If moving the last node from the longest row and adding it to the last\n//row makes the bounding box smaller, do it.\nCoSELayout.prototype.shiftToLastRow = function (organization) {\n  var longest = this.getLongestRowIndex(organization);\n  var last = organization.rowWidth.length - 1;\n  var row = organization.rows[longest];\n  var node = row[row.length - 1];\n\n  var diff = node.width + organization.horizontalPadding;\n\n  // Check if there is enough space on the last row\n  if (organization.width - organization.rowWidth[last] > diff && longest != last) {\n    // Remove the last element of the longest row\n    row.splice(-1, 1);\n\n    // Push it to the last row\n    organization.rows[last].push(node);\n\n    organization.rowWidth[longest] = organization.rowWidth[longest] - diff;\n    organization.rowWidth[last] = organization.rowWidth[last] + diff;\n    organization.width = organization.rowWidth[instance.getLongestRowIndex(organization)];\n\n    // Update heights of the organization\n    var maxHeight = Number.MIN_VALUE;\n    for (var i = 0; i < row.length; i++) {\n      if (row[i].height > maxHeight) maxHeight = row[i].height;\n    }\n    if (longest > 0) maxHeight += organization.verticalPadding;\n\n    var prevTotal = organization.rowHeight[longest] + organization.rowHeight[last];\n\n    organization.rowHeight[longest] = maxHeight;\n    if (organization.rowHeight[last] < node.height + organization.verticalPadding) organization.rowHeight[last] = node.height + organization.verticalPadding;\n\n    var finalTotal = organization.rowHeight[longest] + organization.rowHeight[last];\n    organization.height += finalTotal - prevTotal;\n\n    this.shiftToLastRow(organization);\n  }\n};\n\nCoSELayout.prototype.tilingPreLayout = function () {\n  if (CoSEConstants.TILE) {\n    // Find zero degree nodes and create a compound for each level\n    this.groupZeroDegreeMembers();\n    // Tile and clear children of each compound\n    this.clearCompounds();\n    // Separately tile and clear zero degree nodes for each level\n    this.clearZeroDegreeMembers();\n  }\n};\n\nCoSELayout.prototype.tilingPostLayout = function () {\n  if (CoSEConstants.TILE) {\n    this.repopulateZeroDegreeMembers();\n    this.repopulateCompounds();\n  }\n};\n\n// -----------------------------------------------------------------------------\n// Section: Tree Reduction methods\n// -----------------------------------------------------------------------------\n// Reduce trees \nCoSELayout.prototype.reduceTrees = function () {\n  var prunedNodesAll = [];\n  var containsLeaf = true;\n  var node;\n\n  while (containsLeaf) {\n    var allNodes = this.graphManager.getAllNodes();\n    var prunedNodesInStepTemp = [];\n    containsLeaf = false;\n\n    for (var i = 0; i < allNodes.length; i++) {\n      node = allNodes[i];\n      if (node.getEdges().length == 1 && !node.getEdges()[0].isInterGraph && node.getChild() == null) {\n        prunedNodesInStepTemp.push([node, node.getEdges()[0], node.getOwner()]);\n        containsLeaf = true;\n      }\n    }\n    if (containsLeaf == true) {\n      var prunedNodesInStep = [];\n      for (var j = 0; j < prunedNodesInStepTemp.length; j++) {\n        if (prunedNodesInStepTemp[j][0].getEdges().length == 1) {\n          prunedNodesInStep.push(prunedNodesInStepTemp[j]);\n          prunedNodesInStepTemp[j][0].getOwner().remove(prunedNodesInStepTemp[j][0]);\n        }\n      }\n      prunedNodesAll.push(prunedNodesInStep);\n      this.graphManager.resetAllNodes();\n      this.graphManager.resetAllEdges();\n    }\n  }\n  this.prunedNodesAll = prunedNodesAll;\n};\n\n// Grow tree one step \nCoSELayout.prototype.growTree = function (prunedNodesAll) {\n  var lengthOfPrunedNodesInStep = prunedNodesAll.length;\n  var prunedNodesInStep = prunedNodesAll[lengthOfPrunedNodesInStep - 1];\n\n  var nodeData;\n  for (var i = 0; i < prunedNodesInStep.length; i++) {\n    nodeData = prunedNodesInStep[i];\n\n    this.findPlaceforPrunedNode(nodeData);\n\n    nodeData[2].add(nodeData[0]);\n    nodeData[2].add(nodeData[1], nodeData[1].source, nodeData[1].target);\n  }\n\n  prunedNodesAll.splice(prunedNodesAll.length - 1, 1);\n  this.graphManager.resetAllNodes();\n  this.graphManager.resetAllEdges();\n};\n\n// Find an appropriate position to replace pruned node, this method can be improved\nCoSELayout.prototype.findPlaceforPrunedNode = function (nodeData) {\n\n  var gridForPrunedNode;\n  var nodeToConnect;\n  var prunedNode = nodeData[0];\n  if (prunedNode == nodeData[1].source) {\n    nodeToConnect = nodeData[1].target;\n  } else {\n    nodeToConnect = nodeData[1].source;\n  }\n  var startGridX = nodeToConnect.startX;\n  var finishGridX = nodeToConnect.finishX;\n  var startGridY = nodeToConnect.startY;\n  var finishGridY = nodeToConnect.finishY;\n\n  var upNodeCount = 0;\n  var downNodeCount = 0;\n  var rightNodeCount = 0;\n  var leftNodeCount = 0;\n  var controlRegions = [upNodeCount, rightNodeCount, downNodeCount, leftNodeCount];\n\n  if (startGridY > 0) {\n    for (var i = startGridX; i <= finishGridX; i++) {\n      controlRegions[0] += this.grid[i][startGridY - 1].length + this.grid[i][startGridY].length - 1;\n    }\n  }\n  if (finishGridX < this.grid.length - 1) {\n    for (var i = startGridY; i <= finishGridY; i++) {\n      controlRegions[1] += this.grid[finishGridX + 1][i].length + this.grid[finishGridX][i].length - 1;\n    }\n  }\n  if (finishGridY < this.grid[0].length - 1) {\n    for (var i = startGridX; i <= finishGridX; i++) {\n      controlRegions[2] += this.grid[i][finishGridY + 1].length + this.grid[i][finishGridY].length - 1;\n    }\n  }\n  if (startGridX > 0) {\n    for (var i = startGridY; i <= finishGridY; i++) {\n      controlRegions[3] += this.grid[startGridX - 1][i].length + this.grid[startGridX][i].length - 1;\n    }\n  }\n  var min = Integer.MAX_VALUE;\n  var minCount;\n  var minIndex;\n  for (var j = 0; j < controlRegions.length; j++) {\n    if (controlRegions[j] < min) {\n      min = controlRegions[j];\n      minCount = 1;\n      minIndex = j;\n    } else if (controlRegions[j] == min) {\n      minCount++;\n    }\n  }\n\n  if (minCount == 3 && min == 0) {\n    if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[2] == 0) {\n      gridForPrunedNode = 1;\n    } else if (controlRegions[0] == 0 && controlRegions[1] == 0 && controlRegions[3] == 0) {\n      gridForPrunedNode = 0;\n    } else if (controlRegions[0] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {\n      gridForPrunedNode = 3;\n    } else if (controlRegions[1] == 0 && controlRegions[2] == 0 && controlRegions[3] == 0) {\n      gridForPrunedNode = 2;\n    }\n  } else if (minCount == 2 && min == 0) {\n    var random = Math.floor(Math.random() * 2);\n    if (controlRegions[0] == 0 && controlRegions[1] == 0) {\n      ;\n      if (random == 0) {\n        gridForPrunedNode = 0;\n      } else {\n        gridForPrunedNode = 1;\n      }\n    } else if (controlRegions[0] == 0 && controlRegions[2] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 0;\n      } else {\n        gridForPrunedNode = 2;\n      }\n    } else if (controlRegions[0] == 0 && controlRegions[3] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 0;\n      } else {\n        gridForPrunedNode = 3;\n      }\n    } else if (controlRegions[1] == 0 && controlRegions[2] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 1;\n      } else {\n        gridForPrunedNode = 2;\n      }\n    } else if (controlRegions[1] == 0 && controlRegions[3] == 0) {\n      if (random == 0) {\n        gridForPrunedNode = 1;\n      } else {\n        gridForPrunedNode = 3;\n      }\n    } else {\n      if (random == 0) {\n        gridForPrunedNode = 2;\n      } else {\n        gridForPrunedNode = 3;\n      }\n    }\n  } else if (minCount == 4 && min == 0) {\n    var random = Math.floor(Math.random() * 4);\n    gridForPrunedNode = random;\n  } else {\n    gridForPrunedNode = minIndex;\n  }\n\n  if (gridForPrunedNode == 0) {\n    prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() - nodeToConnect.getHeight() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getHeight() / 2);\n  } else if (gridForPrunedNode == 1) {\n    prunedNode.setCenter(nodeToConnect.getCenterX() + nodeToConnect.getWidth() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getWidth() / 2, nodeToConnect.getCenterY());\n  } else if (gridForPrunedNode == 2) {\n    prunedNode.setCenter(nodeToConnect.getCenterX(), nodeToConnect.getCenterY() + nodeToConnect.getHeight() / 2 + FDLayoutConstants.DEFAULT_EDGE_LENGTH + prunedNode.getHeight() / 2);\n  } else {\n    prunedNode.setCenter(nodeToConnect.getCenterX() - nodeToConnect.getWidth() / 2 - FDLayoutConstants.DEFAULT_EDGE_LENGTH - prunedNode.getWidth() / 2, nodeToConnect.getCenterY());\n  }\n};\n\nmodule.exports = CoSELayout;\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar coseBase = {};\n\ncoseBase.layoutBase = __webpack_require__(0);\ncoseBase.CoSEConstants = __webpack_require__(1);\ncoseBase.CoSEEdge = __webpack_require__(2);\ncoseBase.CoSEGraph = __webpack_require__(3);\ncoseBase.CoSEGraphManager = __webpack_require__(4);\ncoseBase.CoSELayout = __webpack_require__(6);\ncoseBase.CoSENode = __webpack_require__(5);\n\nmodule.exports = coseBase;\n\n/***/ })\n/******/ ]);\n});", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"cose-base\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"cose-base\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"cytoscapeCoseBilkent\"] = factory(require(\"cose-base\"));\n\telse\n\t\troot[\"cytoscapeCoseBilkent\"] = factory(root[\"coseBase\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_0__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 1);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_0__;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar LayoutConstants = __webpack_require__(0).layoutBase.LayoutConstants;\nvar FDLayoutConstants = __webpack_require__(0).layoutBase.FDLayoutConstants;\nvar CoSEConstants = __webpack_require__(0).CoSEConstants;\nvar CoSELayout = __webpack_require__(0).CoSELayout;\nvar CoSENode = __webpack_require__(0).CoSENode;\nvar PointD = __webpack_require__(0).layoutBase.PointD;\nvar DimensionD = __webpack_require__(0).layoutBase.DimensionD;\n\nvar defaults = {\n  // Called on `layoutready`\n  ready: function ready() {},\n  // Called on `layoutstop`\n  stop: function stop() {},\n  // 'draft', 'default' or 'proof\" \n  // - 'draft' fast cooling rate \n  // - 'default' moderate cooling rate \n  // - \"proof\" slow cooling rate\n  quality: 'default',\n  // include labels in node dimensions\n  nodeDimensionsIncludeLabels: false,\n  // number of ticks per frame; higher is faster but more jerky\n  refresh: 30,\n  // Whether to fit the network view after when done\n  fit: true,\n  // Padding on fit\n  padding: 10,\n  // Whether to enable incremental mode\n  randomize: true,\n  // Node repulsion (non overlapping) multiplier\n  nodeRepulsion: 4500,\n  // Ideal edge (non nested) length\n  idealEdgeLength: 50,\n  // Divisor to compute edge forces\n  edgeElasticity: 0.45,\n  // Nesting factor (multiplier) to compute ideal edge length for nested edges\n  nestingFactor: 0.1,\n  // Gravity force (constant)\n  gravity: 0.25,\n  // Maximum number of iterations to perform\n  numIter: 2500,\n  // For enabling tiling\n  tile: true,\n  // Type of layout animation. The option set is {'during', 'end', false}\n  animate: 'end',\n  // Duration for animate:end\n  animationDuration: 500,\n  // Represents the amount of the vertical space to put between the zero degree members during the tiling operation(can also be a function)\n  tilingPaddingVertical: 10,\n  // Represents the amount of the horizontal space to put between the zero degree members during the tiling operation(can also be a function)\n  tilingPaddingHorizontal: 10,\n  // Gravity range (constant) for compounds\n  gravityRangeCompound: 1.5,\n  // Gravity force (constant) for compounds\n  gravityCompound: 1.0,\n  // Gravity range (constant)\n  gravityRange: 3.8,\n  // Initial cooling factor for incremental layout\n  initialEnergyOnIncremental: 0.5\n};\n\nfunction extend(defaults, options) {\n  var obj = {};\n\n  for (var i in defaults) {\n    obj[i] = defaults[i];\n  }\n\n  for (var i in options) {\n    obj[i] = options[i];\n  }\n\n  return obj;\n};\n\nfunction _CoSELayout(_options) {\n  this.options = extend(defaults, _options);\n  getUserOptions(this.options);\n}\n\nvar getUserOptions = function getUserOptions(options) {\n  if (options.nodeRepulsion != null) CoSEConstants.DEFAULT_REPULSION_STRENGTH = FDLayoutConstants.DEFAULT_REPULSION_STRENGTH = options.nodeRepulsion;\n  if (options.idealEdgeLength != null) CoSEConstants.DEFAULT_EDGE_LENGTH = FDLayoutConstants.DEFAULT_EDGE_LENGTH = options.idealEdgeLength;\n  if (options.edgeElasticity != null) CoSEConstants.DEFAULT_SPRING_STRENGTH = FDLayoutConstants.DEFAULT_SPRING_STRENGTH = options.edgeElasticity;\n  if (options.nestingFactor != null) CoSEConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = FDLayoutConstants.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR = options.nestingFactor;\n  if (options.gravity != null) CoSEConstants.DEFAULT_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_GRAVITY_STRENGTH = options.gravity;\n  if (options.numIter != null) CoSEConstants.MAX_ITERATIONS = FDLayoutConstants.MAX_ITERATIONS = options.numIter;\n  if (options.gravityRange != null) CoSEConstants.DEFAULT_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_GRAVITY_RANGE_FACTOR = options.gravityRange;\n  if (options.gravityCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_STRENGTH = options.gravityCompound;\n  if (options.gravityRangeCompound != null) CoSEConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = FDLayoutConstants.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR = options.gravityRangeCompound;\n  if (options.initialEnergyOnIncremental != null) CoSEConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = FDLayoutConstants.DEFAULT_COOLING_FACTOR_INCREMENTAL = options.initialEnergyOnIncremental;\n\n  if (options.quality == 'draft') LayoutConstants.QUALITY = 0;else if (options.quality == 'proof') LayoutConstants.QUALITY = 2;else LayoutConstants.QUALITY = 1;\n\n  CoSEConstants.NODE_DIMENSIONS_INCLUDE_LABELS = FDLayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = LayoutConstants.NODE_DIMENSIONS_INCLUDE_LABELS = options.nodeDimensionsIncludeLabels;\n  CoSEConstants.DEFAULT_INCREMENTAL = FDLayoutConstants.DEFAULT_INCREMENTAL = LayoutConstants.DEFAULT_INCREMENTAL = !options.randomize;\n  CoSEConstants.ANIMATE = FDLayoutConstants.ANIMATE = LayoutConstants.ANIMATE = options.animate;\n  CoSEConstants.TILE = options.tile;\n  CoSEConstants.TILING_PADDING_VERTICAL = typeof options.tilingPaddingVertical === 'function' ? options.tilingPaddingVertical.call() : options.tilingPaddingVertical;\n  CoSEConstants.TILING_PADDING_HORIZONTAL = typeof options.tilingPaddingHorizontal === 'function' ? options.tilingPaddingHorizontal.call() : options.tilingPaddingHorizontal;\n};\n\n_CoSELayout.prototype.run = function () {\n  var ready;\n  var frameId;\n  var options = this.options;\n  var idToLNode = this.idToLNode = {};\n  var layout = this.layout = new CoSELayout();\n  var self = this;\n\n  self.stopped = false;\n\n  this.cy = this.options.cy;\n\n  this.cy.trigger({ type: 'layoutstart', layout: this });\n\n  var gm = layout.newGraphManager();\n  this.gm = gm;\n\n  var nodes = this.options.eles.nodes();\n  var edges = this.options.eles.edges();\n\n  this.root = gm.addRoot();\n  this.processChildrenList(this.root, this.getTopMostNodes(nodes), layout);\n\n  for (var i = 0; i < edges.length; i++) {\n    var edge = edges[i];\n    var sourceNode = this.idToLNode[edge.data(\"source\")];\n    var targetNode = this.idToLNode[edge.data(\"target\")];\n    if (sourceNode !== targetNode && sourceNode.getEdgesBetween(targetNode).length == 0) {\n      var e1 = gm.add(layout.newEdge(), sourceNode, targetNode);\n      e1.id = edge.id();\n    }\n  }\n\n  var getPositions = function getPositions(ele, i) {\n    if (typeof ele === \"number\") {\n      ele = i;\n    }\n    var theId = ele.data('id');\n    var lNode = self.idToLNode[theId];\n\n    return {\n      x: lNode.getRect().getCenterX(),\n      y: lNode.getRect().getCenterY()\n    };\n  };\n\n  /*\n   * Reposition nodes in iterations animatedly\n   */\n  var iterateAnimated = function iterateAnimated() {\n    // Thigs to perform after nodes are repositioned on screen\n    var afterReposition = function afterReposition() {\n      if (options.fit) {\n        options.cy.fit(options.eles, options.padding);\n      }\n\n      if (!ready) {\n        ready = true;\n        self.cy.one('layoutready', options.ready);\n        self.cy.trigger({ type: 'layoutready', layout: self });\n      }\n    };\n\n    var ticksPerFrame = self.options.refresh;\n    var isDone;\n\n    for (var i = 0; i < ticksPerFrame && !isDone; i++) {\n      isDone = self.stopped || self.layout.tick();\n    }\n\n    // If layout is done\n    if (isDone) {\n      // If the layout is not a sublayout and it is successful perform post layout.\n      if (layout.checkLayoutSuccess() && !layout.isSubLayout) {\n        layout.doPostLayout();\n      }\n\n      // If layout has a tilingPostLayout function property call it.\n      if (layout.tilingPostLayout) {\n        layout.tilingPostLayout();\n      }\n\n      layout.isLayoutFinished = true;\n\n      self.options.eles.nodes().positions(getPositions);\n\n      afterReposition();\n\n      // trigger layoutstop when the layout stops (e.g. finishes)\n      self.cy.one('layoutstop', self.options.stop);\n      self.cy.trigger({ type: 'layoutstop', layout: self });\n\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n\n      ready = false;\n      return;\n    }\n\n    var animationData = self.layout.getPositionsData(); // Get positions of layout nodes note that all nodes may not be layout nodes because of tiling\n\n    // Position nodes, for the nodes whose id does not included in data (because they are removed from their parents and included in dummy compounds)\n    // use position of their ancestors or dummy ancestors\n    options.eles.nodes().positions(function (ele, i) {\n      if (typeof ele === \"number\") {\n        ele = i;\n      }\n      // If ele is a compound node, then its position will be defined by its children\n      if (!ele.isParent()) {\n        var theId = ele.id();\n        var pNode = animationData[theId];\n        var temp = ele;\n        // If pNode is undefined search until finding position data of its first ancestor (It may be dummy as well)\n        while (pNode == null) {\n          pNode = animationData[temp.data('parent')] || animationData['DummyCompound_' + temp.data('parent')];\n          animationData[theId] = pNode;\n          temp = temp.parent()[0];\n          if (temp == undefined) {\n            break;\n          }\n        }\n        if (pNode != null) {\n          return {\n            x: pNode.x,\n            y: pNode.y\n          };\n        } else {\n          return {\n            x: ele.position('x'),\n            y: ele.position('y')\n          };\n        }\n      }\n    });\n\n    afterReposition();\n\n    frameId = requestAnimationFrame(iterateAnimated);\n  };\n\n  /*\n  * Listen 'layoutstarted' event and start animated iteration if animate option is 'during'\n  */\n  layout.addListener('layoutstarted', function () {\n    if (self.options.animate === 'during') {\n      frameId = requestAnimationFrame(iterateAnimated);\n    }\n  });\n\n  layout.runLayout(); // Run cose layout\n\n  /*\n   * If animate option is not 'during' ('end' or false) perform these here (If it is 'during' similar things are already performed)\n   */\n  if (this.options.animate !== \"during\") {\n    self.options.eles.nodes().not(\":parent\").layoutPositions(self, self.options, getPositions); // Use layout positions to reposition the nodes it considers the options parameter\n    ready = false;\n  }\n\n  return this; // chaining\n};\n\n//Get the top most ones of a list of nodes\n_CoSELayout.prototype.getTopMostNodes = function (nodes) {\n  var nodesMap = {};\n  for (var i = 0; i < nodes.length; i++) {\n    nodesMap[nodes[i].id()] = true;\n  }\n  var roots = nodes.filter(function (ele, i) {\n    if (typeof ele === \"number\") {\n      ele = i;\n    }\n    var parent = ele.parent()[0];\n    while (parent != null) {\n      if (nodesMap[parent.id()]) {\n        return false;\n      }\n      parent = parent.parent()[0];\n    }\n    return true;\n  });\n\n  return roots;\n};\n\n_CoSELayout.prototype.processChildrenList = function (parent, children, layout) {\n  var size = children.length;\n  for (var i = 0; i < size; i++) {\n    var theChild = children[i];\n    var children_of_children = theChild.children();\n    var theNode;\n\n    var dimensions = theChild.layoutDimensions({\n      nodeDimensionsIncludeLabels: this.options.nodeDimensionsIncludeLabels\n    });\n\n    if (theChild.outerWidth() != null && theChild.outerHeight() != null) {\n      theNode = parent.add(new CoSENode(layout.graphManager, new PointD(theChild.position('x') - dimensions.w / 2, theChild.position('y') - dimensions.h / 2), new DimensionD(parseFloat(dimensions.w), parseFloat(dimensions.h))));\n    } else {\n      theNode = parent.add(new CoSENode(this.graphManager));\n    }\n    // Attach id to the layout node\n    theNode.id = theChild.data(\"id\");\n    // Attach the paddings of cy node to layout node\n    theNode.paddingLeft = parseInt(theChild.css('padding'));\n    theNode.paddingTop = parseInt(theChild.css('padding'));\n    theNode.paddingRight = parseInt(theChild.css('padding'));\n    theNode.paddingBottom = parseInt(theChild.css('padding'));\n\n    //Attach the label properties to compound if labels will be included in node dimensions  \n    if (this.options.nodeDimensionsIncludeLabels) {\n      if (theChild.isParent()) {\n        var labelWidth = theChild.boundingBox({ includeLabels: true, includeNodes: false }).w;\n        var labelHeight = theChild.boundingBox({ includeLabels: true, includeNodes: false }).h;\n        var labelPos = theChild.css(\"text-halign\");\n        theNode.labelWidth = labelWidth;\n        theNode.labelHeight = labelHeight;\n        theNode.labelPos = labelPos;\n      }\n    }\n\n    // Map the layout node\n    this.idToLNode[theChild.data(\"id\")] = theNode;\n\n    if (isNaN(theNode.rect.x)) {\n      theNode.rect.x = 0;\n    }\n\n    if (isNaN(theNode.rect.y)) {\n      theNode.rect.y = 0;\n    }\n\n    if (children_of_children != null && children_of_children.length > 0) {\n      var theNewGraph;\n      theNewGraph = layout.getGraphManager().add(layout.newGraph(), theNode);\n      this.processChildrenList(theNewGraph, children_of_children, layout);\n    }\n  }\n};\n\n/**\n * @brief : called on continuous layouts to stop them before they finish\n */\n_CoSELayout.prototype.stop = function () {\n  this.stopped = true;\n\n  return this; // chaining\n};\n\nvar register = function register(cytoscape) {\n  //  var Layout = getLayout( cytoscape );\n\n  cytoscape('layout', 'cose-bilkent', _CoSELayout);\n};\n\n// auto reg for globals\nif (typeof cytoscape !== 'undefined') {\n  register(cytoscape);\n}\n\nmodule.exports = register;\n\n/***/ })\n/******/ ]);\n});", "/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,4],$V1=[1,13],$V2=[1,12],$V3=[1,15],$V4=[1,16],$V5=[1,20],$V6=[1,19],$V7=[6,7,8],$V8=[1,26],$V9=[1,24],$Va=[1,25],$Vb=[6,7,11],$Vc=[1,6,13,15,16,19,22],$Vd=[1,33],$Ve=[1,34],$Vf=[1,6,7,11,13,15,16,19,22];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"mindMap\":4,\"spaceLines\":5,\"SPACELINE\":6,\"NL\":7,\"MINDMAP\":8,\"document\":9,\"stop\":10,\"EOF\":11,\"statement\":12,\"SPACELIST\":13,\"node\":14,\"ICON\":15,\"CLASS\":16,\"nodeWithId\":17,\"nodeWithoutId\":18,\"NODE_DSTART\":19,\"NODE_DESCR\":20,\"NODE_DEND\":21,\"NODE_ID\":22,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",6:\"SPACELINE\",7:\"NL\",8:\"MINDMAP\",11:\"EOF\",13:\"SPACELIST\",15:\"ICON\",16:\"CLASS\",19:\"NODE_DSTART\",20:\"NODE_DESCR\",21:\"NODE_DEND\",22:\"NODE_ID\"},\nproductions_: [0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 6: case 7:\n return yy; \nbreak;\ncase 8:\nyy.getLogger().trace('Stop NL ');\nbreak;\ncase 9:\nyy.getLogger().trace('Stop EOF ');\nbreak;\ncase 11:\nyy.getLogger().trace('Stop NL2 ');\nbreak;\ncase 12:\nyy.getLogger().trace('Stop EOF2 ');\nbreak;\ncase 15:\n yy.getLogger().info('Node: ',$$[$0].id);yy.addNode($$[$0-1].length, $$[$0].id, $$[$0].descr, $$[$0].type);  \nbreak;\ncase 16:\n yy.getLogger().trace('Icon: ',$$[$0]);yy.decorateNode({icon: $$[$0]}); \nbreak;\ncase 17: case 21:\n yy.decorateNode({class: $$[$0]}); \nbreak;\ncase 18:\n yy.getLogger().trace('SPACELIST');\nbreak;\ncase 19:\n yy.getLogger().trace('Node: ',$$[$0].id);yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);  \nbreak;\ncase 20:\n yy.decorateNode({icon: $$[$0]}); \nbreak;\ncase 25:\n yy.getLogger().trace(\"node found ..\", $$[$0-2]); this.$ = { id: $$[$0-1], descr: $$[$0-1], type: yy.getType($$[$0-2], $$[$0]) }; \nbreak;\ncase 26:\n this.$ = { id: $$[$0], descr: $$[$0], type: yy.nodeType.DEFAULT }; \nbreak;\ncase 27:\n yy.getLogger().trace(\"node found ..\", $$[$0-3]); this.$ = { id: $$[$0-3], descr: $$[$0-1], type: yy.getType($$[$0-2], $$[$0]) }; \nbreak;\n}\n},\ntable: [{3:1,4:2,5:3,6:[1,5],8:$V0},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:$V0},{6:$V1,7:[1,10],9:9,12:11,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},o($V7,[2,3]),{1:[2,2]},o($V7,[2,4]),o($V7,[2,5]),{1:[2,6],6:$V1,12:21,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},{6:$V1,9:22,12:11,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},{6:$V8,7:$V9,10:23,11:$Va},o($Vb,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:$V5,22:$V6}),o($Vb,[2,18]),o($Vb,[2,19]),o($Vb,[2,20]),o($Vb,[2,21]),o($Vb,[2,23]),o($Vb,[2,24]),o($Vb,[2,26],{19:[1,30]}),{20:[1,31]},{6:$V8,7:$V9,10:32,11:$Va},{1:[2,7],6:$V1,12:21,13:$V2,14:14,15:$V3,16:$V4,17:17,18:18,19:$V5,22:$V6},o($Vc,[2,14],{7:$Vd,11:$Ve}),o($Vf,[2,8]),o($Vf,[2,9]),o($Vf,[2,10]),o($Vb,[2,15]),o($Vb,[2,16]),o($Vb,[2,17]),{20:[1,35]},{21:[1,36]},o($Vc,[2,13],{7:$Vd,11:$Ve}),o($Vf,[2,11]),o($Vf,[2,12]),{21:[1,37]},o($Vb,[2,25]),o($Vb,[2,27])],\ndefaultActions: {2:[2,1],6:[2,2]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\t// Pre-lexer code can go here\n\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:yy.getLogger().trace('Found comment',yy_.yytext); return 6;\nbreak;\ncase 1:return 8;\nbreak;\ncase 2: this.begin('CLASS'); \nbreak;\ncase 3: this.popState();return 16; \nbreak;\ncase 4: this.popState();\nbreak;\ncase 5: yy.getLogger().trace('Begin icon');this.begin('ICON'); \nbreak;\ncase 6:yy.getLogger().trace('SPACELINE');return 6                 /* skip all whitespace */    ;\nbreak;\ncase 7:return 7;\nbreak;\ncase 8: return 15; \nbreak;\ncase 9:yy.getLogger().trace('end icon');this.popState();\nbreak;\ncase 10: yy.getLogger().trace('Exploding node'); this.begin('NODE');return 19; \nbreak;\ncase 11: yy.getLogger().trace('Cloud'); this.begin('NODE');return 19; \nbreak;\ncase 12: yy.getLogger().trace('Explosion Bang'); this.begin('NODE');return 19; \nbreak;\ncase 13: yy.getLogger().trace('Cloud Bang'); this.begin('NODE');return 19; \nbreak;\ncase 14: this.begin('NODE');return 19; \nbreak;\ncase 15: this.begin('NODE');return 19; \nbreak;\ncase 16: this.begin('NODE');return 19; \nbreak;\ncase 17: this.begin('NODE');return 19; \nbreak;\ncase 18:return 13                 /* skip all whitespace */    ;\nbreak;\ncase 19:return 22;\nbreak;\ncase 20:return 11;\nbreak;\ncase 21: this.begin(\"NSTR2\");\nbreak;\ncase 22: return \"NODE_DESCR\";\nbreak;\ncase 23: this.popState();\nbreak;\ncase 24: yy.getLogger().trace('Starting NSTR');this.begin(\"NSTR\");\nbreak;\ncase 25: yy.getLogger().trace('description:', yy_.yytext); return \"NODE_DESCR\";\nbreak;\ncase 26:this.popState();\nbreak;\ncase 27:this.popState();yy.getLogger().trace('node end ))');return \"NODE_DEND\";\nbreak;\ncase 28:this.popState();yy.getLogger().trace('node end )');return \"NODE_DEND\";\nbreak;\ncase 29:this.popState();yy.getLogger().trace('node end ...',yy_.yytext);return \"NODE_DEND\";\nbreak;\ncase 30:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 31:this.popState();yy.getLogger().trace('node end (-');return \"NODE_DEND\";\nbreak;\ncase 32:this.popState();yy.getLogger().trace('node end (-');return \"NODE_DEND\";\nbreak;\ncase 33:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 34:this.popState();yy.getLogger().trace('node end ((');return \"NODE_DEND\";\nbreak;\ncase 35: yy.getLogger().trace('Long description:', yy_.yytext);   return 20;\nbreak;\ncase 36: yy.getLogger().trace('Long description:', yy_.yytext);   return 20;\nbreak;\n}\n},\nrules: [/^(?:\\s*%%.*)/i,/^(?:mindmap\\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\\n)/i,/^(?:::icon\\()/i,/^(?:[\\s]+[\\n])/i,/^(?:[\\n]+)/i,/^(?:[^\\)]+)/i,/^(?:\\))/i,/^(?:-\\))/i,/^(?:\\(-)/i,/^(?:\\)\\))/i,/^(?:\\))/i,/^(?:\\(\\()/i,/^(?:\\{\\{)/i,/^(?:\\()/i,/^(?:\\[)/i,/^(?:[\\s]+)/i,/^(?:[^\\(\\[\\n\\)\\{\\}]+)/i,/^(?:$)/i,/^(?:[\"][`])/i,/^(?:[^`\"]+)/i,/^(?:[`][\"])/i,/^(?:[\"])/i,/^(?:[^\"]+)/i,/^(?:[\"])/i,/^(?:[\\)]\\))/i,/^(?:[\\)])/i,/^(?:[\\]])/i,/^(?:\\}\\})/i,/^(?:\\(-)/i,/^(?:-\\))/i,/^(?:\\(\\()/i,/^(?:\\()/i,/^(?:[^\\)\\]\\(\\}]+)/i,/^(?:.+(?!\\(\\())/i],\nconditions: {\"CLASS\":{\"rules\":[3,4],\"inclusive\":false},\"ICON\":{\"rules\":[8,9],\"inclusive\":false},\"NSTR2\":{\"rules\":[22,23],\"inclusive\":false},\"NSTR\":{\"rules\":[25,26],\"inclusive\":false},\"NODE\":{\"rules\":[21,24,27,28,29,30,31,32,33,34,35,36],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { D3Element } from '../../types.js';\nimport { sanitizeText } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport type { MindmapNode } from './mindmapTypes.js';\nimport defaultConfig from '../../defaultConfig.js';\n\nlet nodes: MindmapNode[] = [];\nlet cnt = 0;\nlet elements: Record<number, D3Element> = {};\n\nconst clear = () => {\n  nodes = [];\n  cnt = 0;\n  elements = {};\n};\n\nconst getParent = function (level: number) {\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    if (nodes[i].level < level) {\n      return nodes[i];\n    }\n  }\n  // No parent found\n  return null;\n};\n\nconst getMindmap = () => {\n  return nodes.length > 0 ? nodes[0] : null;\n};\n\nconst addNode = (level: number, id: string, descr: string, type: number) => {\n  log.info('addNode', level, id, descr, type);\n  const conf = getConfig();\n  let padding: number = conf.mindmap?.padding ?? defaultConfig.mindmap.padding;\n  switch (type) {\n    case nodeType.ROUNDED_RECT:\n    case nodeType.RECT:\n    case nodeType.HEXAGON:\n      padding *= 2;\n  }\n\n  const node = {\n    id: cnt++,\n    nodeId: sanitizeText(id, conf),\n    level,\n    descr: sanitizeText(descr, conf),\n    type,\n    children: [],\n    width: conf.mindmap?.maxNodeWidth ?? defaultConfig.mindmap.maxNodeWidth,\n    padding,\n  } satisfies MindmapNode;\n\n  const parent = getParent(level);\n  if (parent) {\n    parent.children.push(node);\n    // Keep all nodes in the list\n    nodes.push(node);\n  } else {\n    if (nodes.length === 0) {\n      // First node, the root\n      nodes.push(node);\n    } else {\n      // Syntax error ... there can only bee one root\n      throw new Error(\n        'There can be only one root. No parent could be found for (\"' + node.descr + '\")'\n      );\n    }\n  }\n};\n\nconst nodeType = {\n  DEFAULT: 0,\n  NO_BORDER: 0,\n  ROUNDED_RECT: 1,\n  RECT: 2,\n  CIRCLE: 3,\n  CLOUD: 4,\n  BANG: 5,\n  HEXAGON: 6,\n};\n\nconst getType = (startStr: string, endStr: string): number => {\n  log.debug('In get type', startStr, endStr);\n  switch (startStr) {\n    case '[':\n      return nodeType.RECT;\n    case '(':\n      return endStr === ')' ? nodeType.ROUNDED_RECT : nodeType.CLOUD;\n    case '((':\n      return nodeType.CIRCLE;\n    case ')':\n      return nodeType.CLOUD;\n    case '))':\n      return nodeType.BANG;\n    case '{{':\n      return nodeType.HEXAGON;\n    default:\n      return nodeType.DEFAULT;\n  }\n};\n\nconst setElementForId = (id: number, element: D3Element) => {\n  elements[id] = element;\n};\n\nconst decorateNode = (decoration?: { class?: string; icon?: string }) => {\n  if (!decoration) {\n    return;\n  }\n  const config = getConfig();\n  const node = nodes[nodes.length - 1];\n  if (decoration.icon) {\n    node.icon = sanitizeText(decoration.icon, config);\n  }\n  if (decoration.class) {\n    node.class = sanitizeText(decoration.class, config);\n  }\n};\n\nconst type2Str = (type: number) => {\n  switch (type) {\n    case nodeType.DEFAULT:\n      return 'no-border';\n    case nodeType.RECT:\n      return 'rect';\n    case nodeType.ROUNDED_RECT:\n      return 'rounded-rect';\n    case nodeType.CIRCLE:\n      return 'circle';\n    case nodeType.CLOUD:\n      return 'cloud';\n    case nodeType.BANG:\n      return 'bang';\n    case nodeType.HEXAGON:\n      return 'hexgon'; // cspell: disable-line\n    default:\n      return 'no-border';\n  }\n};\n\n// Expose logger to grammar\nconst getLogger = () => log;\nconst getElementById = (id: number) => elements[id];\n\nconst db = {\n  clear,\n  addNode,\n  getMindmap,\n  nodeType,\n  getType,\n  setElementForId,\n  decorateNode,\n  type2Str,\n  getLogger,\n  getElementById,\n} as const;\n\nexport default db;\n", "import cytoscape from 'cytoscape';\n// @ts-expect-error No types available\nimport coseBilkent from 'cytoscape-cose-bilkent';\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DrawDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport type { D3Element } from '../../types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport type { FilledMindMapNode, MindmapDB, MindmapNode } from './mindmapTypes.js';\nimport { drawNode, positionNode } from './svgDraw.js';\nimport defaultConfig from '../../defaultConfig.js';\n\n// Inject the layout algorithm into cytoscape\ncytoscape.use(coseBilkent);\n\nasync function drawNodes(\n  db: MindmapDB,\n  svg: D3Element,\n  mindmap: FilledMindMapNode,\n  section: number,\n  conf: MermaidConfig\n) {\n  await drawNode(db, svg, mindmap, section, conf);\n  if (mindmap.children) {\n    await Promise.all(\n      mindmap.children.map((child, index) =>\n        drawNodes(db, svg, child, section < 0 ? index : section, conf)\n      )\n    );\n  }\n}\n\ndeclare module 'cytoscape' {\n  interface EdgeSingular {\n    _private: {\n      bodyBounds: unknown;\n      rscratch: {\n        startX: number;\n        startY: number;\n        midX: number;\n        midY: number;\n        endX: number;\n        endY: number;\n      };\n    };\n  }\n}\n\nfunction drawEdges(edgesEl: D3Element, cy: cytoscape.Core) {\n  cy.edges().map((edge, id) => {\n    const data = edge.data();\n    if (edge[0]._private.bodyBounds) {\n      const bounds = edge[0]._private.rscratch;\n      log.trace('Edge: ', id, data);\n      edgesEl\n        .insert('path')\n        .attr(\n          'd',\n          `M ${bounds.startX},${bounds.startY} L ${bounds.midX},${bounds.midY} L${bounds.endX},${bounds.endY} `\n        )\n        .attr('class', 'edge section-edge-' + data.section + ' edge-depth-' + data.depth);\n    }\n  });\n}\n\nfunction addNodes(mindmap: MindmapNode, cy: cytoscape.Core, conf: MermaidConfig, level: number) {\n  cy.add({\n    group: 'nodes',\n    data: {\n      id: mindmap.id.toString(),\n      labelText: mindmap.descr,\n      height: mindmap.height,\n      width: mindmap.width,\n      level: level,\n      nodeId: mindmap.id,\n      padding: mindmap.padding,\n      type: mindmap.type,\n    },\n    position: {\n      x: mindmap.x!,\n      y: mindmap.y!,\n    },\n  });\n  if (mindmap.children) {\n    mindmap.children.forEach((child) => {\n      addNodes(child, cy, conf, level + 1);\n      cy.add({\n        group: 'edges',\n        data: {\n          id: `${mindmap.id}_${child.id}`,\n          source: mindmap.id,\n          target: child.id,\n          depth: level,\n          section: child.section,\n        },\n      });\n    });\n  }\n}\n\nfunction layoutMindmap(node: MindmapNode, conf: MermaidConfig): Promise<cytoscape.Core> {\n  return new Promise((resolve) => {\n    // Add temporary render element\n    const renderEl = select('body').append('div').attr('id', 'cy').attr('style', 'display:none');\n    const cy = cytoscape({\n      container: document.getElementById('cy'), // container to render in\n      style: [\n        {\n          selector: 'edge',\n          style: {\n            'curve-style': 'bezier',\n          },\n        },\n      ],\n    });\n    // Remove element after layout\n    renderEl.remove();\n    addNodes(node, cy, conf, 0);\n\n    // Make cytoscape care about the dimensions of the nodes\n    cy.nodes().forEach(function (n) {\n      n.layoutDimensions = () => {\n        const data = n.data();\n        return { w: data.width, h: data.height };\n      };\n    });\n\n    cy.layout({\n      name: 'cose-bilkent',\n      // @ts-ignore Types for cose-bilkent are not correct?\n      quality: 'proof',\n      styleEnabled: false,\n      animate: false,\n    }).run();\n    cy.ready((e) => {\n      log.info('Ready', e);\n      resolve(cy);\n    });\n  });\n}\n\nfunction positionNodes(db: MindmapDB, cy: cytoscape.Core) {\n  cy.nodes().map((node, id) => {\n    const data = node.data();\n    data.x = node.position().x;\n    data.y = node.position().y;\n    positionNode(db, data);\n    const el = db.getElementById(data.nodeId);\n    log.info('Id:', id, 'Position: (', node.position().x, ', ', node.position().y, ')', data);\n    el.attr(\n      'transform',\n      `translate(${node.position().x - data.width / 2}, ${node.position().y - data.height / 2})`\n    );\n    el.attr('attr', `apa-${id})`);\n  });\n}\n\nexport const draw: DrawDefinition = async (text, id, _version, diagObj) => {\n  log.debug('Rendering mindmap diagram\\n' + text);\n\n  const db = diagObj.db as MindmapDB;\n  const mm = db.getMindmap();\n  if (!mm) {\n    return;\n  }\n\n  const conf = getConfig();\n  conf.htmlLabels = false;\n\n  const svg = selectSvgElement(id);\n\n  // Draw the graph and start with drawing the nodes without proper position\n  // this gives us the size of the nodes and we can set the positions later\n\n  const edgesElem = svg.append('g');\n  edgesElem.attr('class', 'mindmap-edges');\n  const nodesElem = svg.append('g');\n  nodesElem.attr('class', 'mindmap-nodes');\n  await drawNodes(db, nodesElem, mm as FilledMindMapNode, -1, conf);\n\n  // Next step is to layout the mindmap, giving each node a position\n\n  const cy = await layoutMindmap(mm, conf);\n\n  // After this we can draw, first the edges and the then nodes with the correct position\n  drawEdges(edgesElem, cy);\n  positionNodes(db, cy);\n\n  // Setup the view box and size of the svg element\n  setupGraphViewbox(\n    undefined,\n    svg,\n    conf.mindmap?.padding ?? defaultConfig.mindmap.padding,\n    conf.mindmap?.useMaxWidth ?? defaultConfig.mindmap.useMaxWidth\n  );\n};\n\nexport default {\n  draw,\n};\n", "import { createText } from '../../rendering-util/createText.js';\nimport type { FilledMindMapNode, MindmapDB } from './mindmapTypes.js';\nimport type { Point, D3Element } from '../../types.js';\nimport { parseFontSize } from '../../utils.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nconst MAX_SECTIONS = 12;\n\ntype ShapeFunction = (\n  db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode,\n  section?: number\n) => void;\n\nconst defaultBkg: ShapeFunction = function (db, elem, node, section) {\n  const rd = 5;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${\n        node.width - 2 * rd\n      } q5,0 5,5 v${node.height - rd} H0 Z`\n    );\n\n  elem\n    .append('line')\n    .attr('class', 'node-line-' + section)\n    .attr('x1', 0)\n    .attr('y1', node.height)\n    .attr('x2', node.width)\n    .attr('y2', node.height);\n};\n\nconst rectBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('rect')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('height', node.height)\n    .attr('width', node.width);\n};\n\nconst cloudBkg: ShapeFunction = function (db, elem, node) {\n  const w = node.width;\n  const h = node.height;\n  const r1 = 0.15 * w;\n  const r2 = 0.25 * w;\n  const r3 = 0.35 * w;\n  const r4 = 0.2 * w;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 0 a${r1},${r1} 0 0,1 ${w * 0.25},${-1 * w * 0.1}\n      a${r3},${r3} 1 0,1 ${w * 0.4},${-1 * w * 0.1}\n      a${r2},${r2} 1 0,1 ${w * 0.35},${1 * w * 0.2}\n\n      a${r1},${r1} 1 0,1 ${w * 0.15},${1 * h * 0.35}\n      a${r4},${r4} 1 0,1 ${-1 * w * 0.15},${1 * h * 0.65}\n\n      a${r2},${r1} 1 0,1 ${-1 * w * 0.25},${w * 0.15}\n      a${r3},${r3} 1 0,1 ${-1 * w * 0.5},${0}\n      a${r1},${r1} 1 0,1 ${-1 * w * 0.25},${-1 * w * 0.15}\n\n      a${r1},${r1} 1 0,1 ${-1 * w * 0.1},${-1 * h * 0.35}\n      a${r4},${r4} 1 0,1 ${w * 0.1},${-1 * h * 0.65}\n\n    H0 V0 Z`\n    );\n};\n\nconst bangBkg: ShapeFunction = function (db, elem, node) {\n  const w = node.width;\n  const h = node.height;\n  const r = 0.15 * w;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr(\n      'd',\n      `M0 0 a${r},${r} 1 0,0 ${w * 0.25},${-1 * h * 0.1}\n      a${r},${r} 1 0,0 ${w * 0.25},${0}\n      a${r},${r} 1 0,0 ${w * 0.25},${0}\n      a${r},${r} 1 0,0 ${w * 0.25},${1 * h * 0.1}\n\n      a${r},${r} 1 0,0 ${w * 0.15},${1 * h * 0.33}\n      a${r * 0.8},${r * 0.8} 1 0,0 ${0},${1 * h * 0.34}\n      a${r},${r} 1 0,0 ${-1 * w * 0.15},${1 * h * 0.33}\n\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${h * 0.15}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${0}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${0}\n      a${r},${r} 1 0,0 ${-1 * w * 0.25},${-1 * h * 0.15}\n\n      a${r},${r} 1 0,0 ${-1 * w * 0.1},${-1 * h * 0.33}\n      a${r * 0.8},${r * 0.8} 1 0,0 ${0},${-1 * h * 0.34}\n      a${r},${r} 1 0,0 ${w * 0.1},${-1 * h * 0.33}\n\n    H0 V0 Z`\n    );\n};\n\nconst circleBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('circle')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('r', node.width / 2);\n};\n\nfunction insertPolygonShape(\n  parent: D3Element,\n  w: number,\n  h: number,\n  points: Point[],\n  node: FilledMindMapNode\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('transform', 'translate(' + (node.width - w) / 2 + ', ' + h + ')');\n}\n\nconst hexagonBkg: ShapeFunction = function (\n  _db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode\n) {\n  const h = node.height;\n  const f = 4;\n  const m = h / f;\n  const w = node.width - node.padding + 2 * m;\n  const points: Point[] = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n  insertPolygonShape(elem, w, h, points, node);\n};\n\nconst roundedRectBkg: ShapeFunction = function (db, elem, node) {\n  elem\n    .append('rect')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + db.type2Str(node.type))\n    .attr('height', node.height)\n    .attr('rx', node.padding)\n    .attr('ry', node.padding)\n    .attr('width', node.width);\n};\n\n/**\n * @param db - The database\n * @param elem - The D3 dom element in which the node is to be added\n * @param node - The node to be added\n * @param fullSection - ?\n * @param conf - The configuration object\n * @returns The height nodes dom element\n */\nexport const drawNode = async function (\n  db: MindmapDB,\n  elem: D3Element,\n  node: FilledMindMapNode,\n  fullSection: number,\n  conf: MermaidConfig\n): Promise<number> {\n  const htmlLabels = conf.htmlLabels;\n  const section = fullSection % (MAX_SECTIONS - 1);\n  const nodeElem = elem.append('g');\n  node.section = section;\n  let sectionClass = 'section-' + section;\n  if (section < 0) {\n    sectionClass += ' section-root';\n  }\n  nodeElem.attr('class', (node.class ? node.class + ' ' : '') + 'mindmap-node ' + sectionClass);\n  const bkgElem = nodeElem.append('g');\n\n  // Create the wrapped text element\n  const textElem = nodeElem.append('g');\n  const description = node.descr.replace(/(<br\\/*>)/g, '\\n');\n  await createText(\n    textElem,\n    description,\n    {\n      useHtmlLabels: htmlLabels,\n      width: node.width,\n      classes: 'mindmap-node-label',\n    },\n    conf\n  );\n\n  if (!htmlLabels) {\n    textElem\n      .attr('dy', '1em')\n      .attr('alignment-baseline', 'middle')\n      .attr('dominant-baseline', 'middle')\n      .attr('text-anchor', 'middle');\n  }\n  const bbox = textElem.node().getBBox();\n  const [fontSize] = parseFontSize(conf.fontSize);\n  node.height = bbox.height + fontSize! * 1.1 * 0.5 + node.padding;\n  node.width = bbox.width + 2 * node.padding;\n  if (node.icon) {\n    if (node.type === db.nodeType.CIRCLE) {\n      node.height += 50;\n      node.width += 50;\n      const icon = nodeElem\n        .append('foreignObject')\n        .attr('height', '50px')\n        .attr('width', node.width)\n        .attr('style', 'text-align: center;');\n      icon\n        .append('div')\n        .attr('class', 'icon-container')\n        .append('i')\n        .attr('class', 'node-icon-' + section + ' ' + node.icon);\n      textElem.attr(\n        'transform',\n        'translate(' + node.width / 2 + ', ' + (node.height / 2 - 1.5 * node.padding) + ')'\n      );\n    } else {\n      node.width += 50;\n      const orgHeight = node.height;\n      node.height = Math.max(orgHeight, 60);\n      const heightDiff = Math.abs(node.height - orgHeight);\n      const icon = nodeElem\n        .append('foreignObject')\n        .attr('width', '60px')\n        .attr('height', node.height)\n        .attr('style', 'text-align: center;margin-top:' + heightDiff / 2 + 'px;');\n\n      icon\n        .append('div')\n        .attr('class', 'icon-container')\n        .append('i')\n        .attr('class', 'node-icon-' + section + ' ' + node.icon);\n      textElem.attr(\n        'transform',\n        'translate(' + (25 + node.width / 2) + ', ' + (heightDiff / 2 + node.padding / 2) + ')'\n      );\n    }\n  } else {\n    if (!htmlLabels) {\n      const dx = node.width / 2;\n      const dy = node.padding / 2;\n      textElem.attr('transform', 'translate(' + dx + ', ' + dy + ')');\n      // textElem.attr('transform', 'translate(' + node.width / 2 + ', ' + node.padding / 2 + ')');\n    } else {\n      const dx = (node.width - bbox.width) / 2;\n      const dy = (node.height - bbox.height) / 2;\n      textElem.attr('transform', 'translate(' + dx + ', ' + dy + ')');\n    }\n  }\n\n  switch (node.type) {\n    case db.nodeType.DEFAULT:\n      defaultBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.ROUNDED_RECT:\n      roundedRectBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.RECT:\n      rectBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.CIRCLE:\n      bkgElem.attr('transform', 'translate(' + node.width / 2 + ', ' + +node.height / 2 + ')');\n      circleBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.CLOUD:\n      cloudBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.BANG:\n      bangBkg(db, bkgElem, node, section);\n      break;\n    case db.nodeType.HEXAGON:\n      hexagonBkg(db, bkgElem, node, section);\n      break;\n  }\n\n  db.setElementForId(node.id, nodeElem);\n  return node.height;\n};\n\nexport const positionNode = function (db: MindmapDB, node: FilledMindMapNode) {\n  const nodeElem = db.getElementById(node.id);\n\n  const x = node.x || 0;\n  const y = node.y || 0;\n  // Position the node to its coordinate\n  nodeElem.attr('transform', 'translate(' + x + ',' + y + ')');\n};\n", "// @ts-expect-error Incorrect khroma types\nimport { darken, lighten, isDark } from 'khroma';\nimport type { DiagramStylesProvider } from '../../diagram-api/types.js';\n\nconst genSections: DiagramStylesProvider = (options) => {\n  let sections = '';\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options['lineColor' + i] = options['lineColor' + i] || options['cScaleInv' + i];\n    if (isDark(options['lineColor' + i])) {\n      options['lineColor' + i] = lighten(options['lineColor' + i], 20);\n    } else {\n      options['lineColor' + i] = darken(options['lineColor' + i], 20);\n    }\n  }\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = '' + (17 - 3 * i);\n    sections += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${\n      i - 1\n    } polygon, .section-${i - 1} path  {\n      fill: ${options['cScale' + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options['cScaleLabel' + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options['cScaleLabel' + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options['cScale' + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options['cScaleInv' + i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections;\n};\n\n// TODO: These options seem incorrect.\nconst getStyles: DiagramStylesProvider = (options) =>\n  `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .mindmap-node-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`;\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/mindmap.jison';\nimport db from './mindmapDb.js';\nimport renderer from './mindmapRenderer.js';\nimport styles from './styles.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  db,\n  renderer,\n  parser,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,KAAC,gCAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ;AAAA,eAClB,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,eACX,OAAO,YAAY;AAC1B,gBAAQ,YAAY,IAAI,QAAQ;AAAA;AAEhC,aAAK,YAAY,IAAI,QAAQ;AAAA,IAC/B,GATC,qCASE,SAAM,WAAW;AACpB;AAAA;AAAA,QAAiB,SAAS,SAAS;AAEzB,cAAI,mBAAmB,CAAC;AAGxB,mBAAS,oBAAoB,UAAU;AAGtC,gBAAG,iBAAiB,QAAQ,GAAG;AAC9B,qBAAO,iBAAiB,QAAQ,EAAE;AAAA,YACnC;AAEA,gBAAIA,UAAS,iBAAiB,QAAQ,IAAI;AAAA;AAAA,cACzC,GAAG;AAAA;AAAA,cACH,GAAG;AAAA;AAAA,cACH,SAAS,CAAC;AAAA;AAAA,YACX;AAGA,oBAAQ,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAGlF,YAAAA,QAAO,IAAI;AAGX,mBAAOA,QAAO;AAAA,UACf;AArBS;AAyBT,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI,SAAS,OAAO;AAAE,mBAAO;AAAA,UAAO;AAGxD,8BAAoB,IAAI,SAASC,UAAS,MAAM,QAAQ;AACvD,gBAAG,CAAC,oBAAoB,EAAEA,UAAS,IAAI,GAAG;AACzC,qBAAO,eAAeA,UAAS,MAAM;AAAA;AAAA,gBACpC,cAAc;AAAA;AAAA,gBACd,YAAY;AAAA;AAAA,gBACZ,KAAK;AAAA;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD;AAGA,8BAAoB,IAAI,SAASD,SAAQ;AACxC,gBAAI,SAASA,WAAUA,QAAO;AAAA;AAAA,cAC7B,gCAAS,aAAa;AAAE,uBAAOA,QAAO,SAAS;AAAA,cAAG,GAAlD;AAAA;AAAA;AAAA,cACA,gCAAS,mBAAmB;AAAE,uBAAOA;AAAA,cAAQ,GAA7C;AAAA;AACD,gCAAoB,EAAE,QAAQ,KAAK,MAAM;AACzC,mBAAO;AAAA,UACR;AAGA,8BAAoB,IAAI,SAAS,QAAQ,UAAU;AAAE,mBAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ;AAAA,UAAG;AAGpH,8BAAoB,IAAI;AAGxB,iBAAO,oBAAoB,oBAAoB,IAAI,EAAE;AAAA,QACtD,EAEC;AAAA;AAAA;AAAA,UAEH,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,kBAAkB;AAAA,YAAC;AAAnB;AAKT,4BAAgB,UAAU;AAK1B,4BAAgB,iCAAiC;AACjD,4BAAgB,sBAAsB;AACtC,4BAAgB,8BAA8B;AAC9C,4BAAgB,kCAAkC;AAClD,4BAAgB,2BAA2B;AAC3C,4BAAgB,kCAAkC;AASlD,4BAAgB,uBAAuB;AAKvC,4BAAgB,iCAAiC;AAKjD,4BAAgB,mBAAmB;AAKnC,4BAAgB,wBAAwB,gBAAgB,mBAAmB;AAM3E,4BAAgB,2BAA2B;AAK3C,4BAAgB,kBAAkB;AAKlC,4BAAgB,iBAAiB;AAKjC,4BAAgB,yBAAyB,gBAAgB,iBAAiB;AAK1E,4BAAgB,iBAAiB;AACjC,4BAAgB,iBAAiB;AAEjC,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,oBAAoB,CAAC;AACxC,gBAAI,YAAY,oBAAoB,CAAC;AACrC,gBAAI,QAAQ,oBAAoB,CAAC;AAEjC,qBAAS,MAAM,QAAQ,QAAQ,OAAO;AACpC,2BAAa,KAAK,MAAM,KAAK;AAE7B,mBAAK,8BAA8B;AACnC,mBAAK,eAAe;AACpB,mBAAK,aAAa,CAAC;AACnB,mBAAK,SAAS;AACd,mBAAK,SAAS;AAAA,YAChB;AARS;AAUT,kBAAM,YAAY,OAAO,OAAO,aAAa,SAAS;AAEtD,qBAAS,QAAQ,cAAc;AAC7B,oBAAM,IAAI,IAAI,aAAa,IAAI;AAAA,YACjC;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,eAAe,WAAY;AACzC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,8BAA8B,WAAY;AACxD,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,gBAAgB,WAAY;AAC1C,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,SAAS,WAAY;AACnC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,iBAAiB,WAAY;AAC3C,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,iBAAiB,WAAY;AAC3C,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,cAAc,SAAU,MAAM;AAC5C,kBAAI,KAAK,WAAW,MAAM;AACxB,uBAAO,KAAK;AAAA,cACd,WAAW,KAAK,WAAW,MAAM;AAC/B,uBAAO,KAAK;AAAA,cACd,OAAO;AACL,sBAAM;AAAA,cACR;AAAA,YACF;AAEA,kBAAM,UAAU,qBAAqB,SAAU,MAAM,OAAO;AAC1D,kBAAI,WAAW,KAAK,YAAY,IAAI;AACpC,kBAAI,OAAO,MAAM,gBAAgB,EAAE,QAAQ;AAE3C,qBAAO,MAAM;AACX,oBAAI,SAAS,SAAS,KAAK,OAAO;AAChC,yBAAO;AAAA,gBACT;AAEA,oBAAI,SAAS,SAAS,KAAK,MAAM;AAC/B;AAAA,gBACF;AAEA,2BAAW,SAAS,SAAS,EAAE,UAAU;AAAA,cAC3C;AAEA,qBAAO;AAAA,YACT;AAEA,kBAAM,UAAU,eAAe,WAAY;AACzC,kBAAI,uBAAuB,IAAI,MAAM,CAAC;AAEtC,mBAAK,8BAA8B,UAAU,gBAAgB,KAAK,OAAO,QAAQ,GAAG,KAAK,OAAO,QAAQ,GAAG,oBAAoB;AAE/H,kBAAI,CAAC,KAAK,6BAA6B;AACrC,qBAAK,UAAU,qBAAqB,CAAC,IAAI,qBAAqB,CAAC;AAC/D,qBAAK,UAAU,qBAAqB,CAAC,IAAI,qBAAqB,CAAC;AAE/D,oBAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAK;AAChC,uBAAK,UAAU,MAAM,KAAK,KAAK,OAAO;AAAA,gBACxC;AAEA,oBAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAK;AAChC,uBAAK,UAAU,MAAM,KAAK,KAAK,OAAO;AAAA,gBACxC;AAEA,qBAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,OAAO;AAAA,cACnF;AAAA,YACF;AAEA,kBAAM,UAAU,qBAAqB,WAAY;AAC/C,mBAAK,UAAU,KAAK,OAAO,WAAW,IAAI,KAAK,OAAO,WAAW;AACjE,mBAAK,UAAU,KAAK,OAAO,WAAW,IAAI,KAAK,OAAO,WAAW;AAEjE,kBAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAK;AAChC,qBAAK,UAAU,MAAM,KAAK,KAAK,OAAO;AAAA,cACxC;AAEA,kBAAI,KAAK,IAAI,KAAK,OAAO,IAAI,GAAK;AAChC,qBAAK,UAAU,MAAM,KAAK,KAAK,OAAO;AAAA,cACxC;AAEA,mBAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,OAAO;AAAA,YACnF;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,aAAa,cAAc;AAClC,mBAAK,eAAe;AAAA,YACtB;AAFS;AAIT,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,oBAAoB,CAAC;AACxC,gBAAI,UAAU,oBAAoB,EAAE;AACpC,gBAAI,aAAa,oBAAoB,EAAE;AACvC,gBAAI,kBAAkB,oBAAoB,CAAC;AAC3C,gBAAI,aAAa,oBAAoB,EAAE;AACvC,gBAAI,SAAS,oBAAoB,CAAC;AAElC,qBAAS,MAAM,IAAI,KAAK,MAAM,OAAO;AAEnC,kBAAI,QAAQ,QAAQ,SAAS,MAAM;AACjC,wBAAQ;AAAA,cACV;AAEA,2BAAa,KAAK,MAAM,KAAK;AAG7B,kBAAI,GAAG,gBAAgB,KAAM,MAAK,GAAG;AAErC,mBAAK,gBAAgB,QAAQ;AAC7B,mBAAK,qBAAqB,QAAQ;AAClC,mBAAK,eAAe;AACpB,mBAAK,QAAQ,CAAC;AACd,mBAAK,eAAe;AAEpB,kBAAI,QAAQ,QAAQ,OAAO,KAAM,MAAK,OAAO,IAAI,WAAW,IAAI,GAAG,IAAI,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,kBAAO,MAAK,OAAO,IAAI,WAAW;AAAA,YACrI;AAlBS;AAoBT,kBAAM,YAAY,OAAO,OAAO,aAAa,SAAS;AACtD,qBAAS,QAAQ,cAAc;AAC7B,oBAAM,IAAI,IAAI,aAAa,IAAI;AAAA,YACjC;AAEA,kBAAM,UAAU,WAAW,WAAY;AACrC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,WAAW,WAAY;AACrC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,WAAW,WAAY;AAOrC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,WAAW,WAAY;AACrC,qBAAO,KAAK,KAAK;AAAA,YACnB;AAEA,kBAAM,UAAU,WAAW,SAAU,OAAO;AAC1C,mBAAK,KAAK,QAAQ;AAAA,YACpB;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,qBAAO,KAAK,KAAK;AAAA,YACnB;AAEA,kBAAM,UAAU,YAAY,SAAU,QAAQ;AAC5C,mBAAK,KAAK,SAAS;AAAA,YACrB;AAEA,kBAAM,UAAU,aAAa,WAAY;AACvC,qBAAO,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ;AAAA,YACzC;AAEA,kBAAM,UAAU,aAAa,WAAY;AACvC,qBAAO,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS;AAAA,YAC1C;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,qBAAO,IAAI,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS,CAAC;AAAA,YACzF;AAEA,kBAAM,UAAU,cAAc,WAAY;AACxC,qBAAO,IAAI,OAAO,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC;AAAA,YAC5C;AAEA,kBAAM,UAAU,UAAU,WAAY;AACpC,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,cAAc,WAAY;AACxC,qBAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,KAAK,MAAM;AAAA,YAC1F;AAKA,kBAAM,UAAU,qBAAqB,WAAY;AAC/C,qBAAO,KAAK,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,IAAI;AAAA,YAC9F;AAEA,kBAAM,UAAU,UAAU,SAAU,WAAW,WAAW;AACxD,mBAAK,KAAK,IAAI,UAAU;AACxB,mBAAK,KAAK,IAAI,UAAU;AACxB,mBAAK,KAAK,QAAQ,UAAU;AAC5B,mBAAK,KAAK,SAAS,UAAU;AAAA,YAC/B;AAEA,kBAAM,UAAU,YAAY,SAAU,IAAI,IAAI;AAC5C,mBAAK,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ;AACrC,mBAAK,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS;AAAA,YACxC;AAEA,kBAAM,UAAU,cAAc,SAAU,GAAG,GAAG;AAC5C,mBAAK,KAAK,IAAI;AACd,mBAAK,KAAK,IAAI;AAAA,YAChB;AAEA,kBAAM,UAAU,SAAS,SAAU,IAAI,IAAI;AACzC,mBAAK,KAAK,KAAK;AACf,mBAAK,KAAK,KAAK;AAAA,YACjB;AAEA,kBAAM,UAAU,oBAAoB,SAAU,IAAI;AAChD,kBAAI,WAAW,CAAC;AAChB,kBAAI;AACJ,kBAAI,OAAO;AAEX,mBAAK,MAAM,QAAQ,SAAUC,OAAM;AAEjC,oBAAIA,MAAK,UAAU,IAAI;AACrB,sBAAIA,MAAK,UAAU,KAAM,OAAM;AAE/B,2BAAS,KAAKA,KAAI;AAAA,gBACpB;AAAA,cACF,CAAC;AAED,qBAAO;AAAA,YACT;AAEA,kBAAM,UAAU,kBAAkB,SAAU,OAAO;AACjD,kBAAI,WAAW,CAAC;AAChB,kBAAI;AAEJ,kBAAI,OAAO;AACX,mBAAK,MAAM,QAAQ,SAAUA,OAAM;AAEjC,oBAAI,EAAEA,MAAK,UAAU,QAAQA,MAAK,UAAU,MAAO,OAAM;AAEzD,oBAAIA,MAAK,UAAU,SAASA,MAAK,UAAU,OAAO;AAChD,2BAAS,KAAKA,KAAI;AAAA,gBACpB;AAAA,cACF,CAAC;AAED,qBAAO;AAAA,YACT;AAEA,kBAAM,UAAU,mBAAmB,WAAY;AAC7C,kBAAI,YAAY,oBAAI,IAAI;AAExB,kBAAI,OAAO;AACX,mBAAK,MAAM,QAAQ,SAAU,MAAM;AAEjC,oBAAI,KAAK,UAAU,MAAM;AACvB,4BAAU,IAAI,KAAK,MAAM;AAAA,gBAC3B,OAAO;AACL,sBAAI,KAAK,UAAU,MAAM;AACvB,0BAAM;AAAA,kBACR;AAEA,4BAAU,IAAI,KAAK,MAAM;AAAA,gBAC3B;AAAA,cACF,CAAC;AAED,qBAAO;AAAA,YACT;AAEA,kBAAM,UAAU,eAAe,WAAY;AACzC,kBAAI,oBAAoB,oBAAI,IAAI;AAChC,kBAAI;AACJ,kBAAI;AAEJ,gCAAkB,IAAI,IAAI;AAE1B,kBAAI,KAAK,SAAS,MAAM;AACtB,oBAAIC,SAAQ,KAAK,MAAM,SAAS;AAChC,yBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,8BAAYA,OAAM,CAAC;AACnB,6BAAW,UAAU,aAAa;AAClC,2BAAS,QAAQ,SAAU,MAAM;AAC/B,sCAAkB,IAAI,IAAI;AAAA,kBAC5B,CAAC;AAAA,gBACH;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAEA,kBAAM,UAAU,kBAAkB,WAAY;AAC5C,kBAAI,eAAe;AACnB,kBAAI;AAEJ,kBAAI,KAAK,SAAS,MAAM;AACtB,+BAAe;AAAA,cACjB,OAAO;AACL,oBAAIA,SAAQ,KAAK,MAAM,SAAS;AAChC,yBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,8BAAYA,OAAM,CAAC;AAEnB,kCAAgB,UAAU,gBAAgB;AAAA,gBAC5C;AAAA,cACF;AAEA,kBAAI,gBAAgB,GAAG;AACrB,+BAAe;AAAA,cACjB;AACA,qBAAO;AAAA,YACT;AAEA,kBAAM,UAAU,mBAAmB,WAAY;AAC7C,kBAAI,KAAK,iBAAiB,QAAQ,WAAW;AAC3C,sBAAM;AAAA,cACR;AACA,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,oBAAoB,WAAY;AAC9C,kBAAI,KAAK,SAAS,MAAM;AACtB,uBAAO,KAAK,iBAAiB,KAAK,KAAK,QAAQ,KAAK,KAAK,UAAU;AAAA,cACrE,OAAO;AACL,qBAAK,gBAAgB,KAAK,MAAM,kBAAkB;AAClD,qBAAK,KAAK,QAAQ,KAAK;AACvB,qBAAK,KAAK,SAAS,KAAK;AAExB,uBAAO,KAAK;AAAA,cACd;AAAA,YACF;AAEA,kBAAM,UAAU,UAAU,WAAY;AACpC,kBAAI;AACJ,kBAAI;AAEJ,kBAAI,OAAO,CAAC,gBAAgB;AAC5B,kBAAI,OAAO,gBAAgB;AAC3B,8BAAgB,gBAAgB,iBAAiB,WAAW,WAAW,KAAK,OAAO,QAAQ;AAE3F,kBAAI,OAAO,CAAC,gBAAgB;AAC5B,kBAAI,OAAO,gBAAgB;AAC3B,8BAAgB,gBAAgB,iBAAiB,WAAW,WAAW,KAAK,OAAO,QAAQ;AAE3F,mBAAK,KAAK,IAAI;AACd,mBAAK,KAAK,IAAI;AAAA,YAChB;AAEA,kBAAM,UAAU,eAAe,WAAY;AACzC,kBAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,sBAAM;AAAA,cACR;AACA,kBAAI,KAAK,SAAS,EAAE,SAAS,EAAE,UAAU,GAAG;AAE1C,oBAAI,aAAa,KAAK,SAAS;AAC/B,2BAAW,aAAa,IAAI;AAE5B,qBAAK,KAAK,IAAI,WAAW,QAAQ;AACjC,qBAAK,KAAK,IAAI,WAAW,OAAO;AAEhC,qBAAK,SAAS,WAAW,SAAS,IAAI,WAAW,QAAQ,CAAC;AAC1D,qBAAK,UAAU,WAAW,UAAU,IAAI,WAAW,OAAO,CAAC;AAG3D,oBAAI,gBAAgB,gCAAgC;AAElD,sBAAI,QAAQ,WAAW,SAAS,IAAI,WAAW,QAAQ;AACvD,sBAAI,SAAS,WAAW,UAAU,IAAI,WAAW,OAAO;AAExD,sBAAI,KAAK,aAAa,OAAO;AAC3B,yBAAK,KAAK,MAAM,KAAK,aAAa,SAAS;AAC3C,yBAAK,SAAS,KAAK,UAAU;AAAA,kBAC/B;AAEA,sBAAI,KAAK,cAAc,QAAQ;AAC7B,wBAAI,KAAK,YAAY,UAAU;AAC7B,2BAAK,KAAK,MAAM,KAAK,cAAc,UAAU;AAAA,oBAC/C,WAAW,KAAK,YAAY,OAAO;AACjC,2BAAK,KAAK,KAAK,KAAK,cAAc;AAAA,oBACpC;AACA,yBAAK,UAAU,KAAK,WAAW;AAAA,kBACjC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,kBAAM,UAAU,wBAAwB,WAAY;AAClD,kBAAI,KAAK,sBAAsB,QAAQ,WAAW;AAChD,sBAAM;AAAA,cACR;AACA,qBAAO,KAAK;AAAA,YACd;AAEA,kBAAM,UAAU,YAAY,SAAU,OAAO;AAC3C,kBAAI,OAAO,KAAK,KAAK;AAErB,kBAAI,OAAO,gBAAgB,gBAAgB;AACzC,uBAAO,gBAAgB;AAAA,cACzB,WAAW,OAAO,CAAC,gBAAgB,gBAAgB;AACjD,uBAAO,CAAC,gBAAgB;AAAA,cAC1B;AAEA,kBAAI,MAAM,KAAK,KAAK;AAEpB,kBAAI,MAAM,gBAAgB,gBAAgB;AACxC,sBAAM,gBAAgB;AAAA,cACxB,WAAW,MAAM,CAAC,gBAAgB,gBAAgB;AAChD,sBAAM,CAAC,gBAAgB;AAAA,cACzB;AAEA,kBAAI,UAAU,IAAI,OAAO,MAAM,GAAG;AAClC,kBAAI,WAAW,MAAM,sBAAsB,OAAO;AAElD,mBAAK,YAAY,SAAS,GAAG,SAAS,CAAC;AAAA,YACzC;AAEA,kBAAM,UAAU,UAAU,WAAY;AACpC,qBAAO,KAAK,KAAK;AAAA,YACnB;AAEA,kBAAM,UAAU,WAAW,WAAY;AACrC,qBAAO,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,YACjC;AAEA,kBAAM,UAAU,SAAS,WAAY;AACnC,qBAAO,KAAK,KAAK;AAAA,YACnB;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,qBAAO,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,YACjC;AAEA,kBAAM,UAAU,YAAY,WAAY;AACtC,kBAAI,KAAK,SAAS,MAAM;AACtB,uBAAO;AAAA,cACT;AAEA,qBAAO,KAAK,MAAM,UAAU;AAAA,YAC9B;AAEA,YAAAH,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,OAAO,GAAG,GAAG;AACpB,kBAAI,KAAK,QAAQ,KAAK,MAAM;AAC1B,qBAAK,IAAI;AACT,qBAAK,IAAI;AAAA,cACX,OAAO;AACL,qBAAK,IAAI;AACT,qBAAK,IAAI;AAAA,cACX;AAAA,YACF;AARS;AAUT,mBAAO,UAAU,OAAO,WAAY;AAClC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,OAAO,WAAY;AAClC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,OAAO,SAAU,GAAG;AACnC,mBAAK,IAAI;AAAA,YACX;AAEA,mBAAO,UAAU,OAAO,SAAU,GAAG;AACnC,mBAAK,IAAI;AAAA,YACX;AAEA,mBAAO,UAAU,gBAAgB,SAAU,IAAI;AAC7C,qBAAO,IAAI,WAAW,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAAA,YACpD;AAEA,mBAAO,UAAU,UAAU,WAAY;AACrC,qBAAO,IAAI,OAAO,KAAK,GAAG,KAAK,CAAC;AAAA,YAClC;AAEA,mBAAO,UAAU,YAAY,SAAU,KAAK;AAC1C,mBAAK,KAAK,IAAI;AACd,mBAAK,KAAK,IAAI;AACd,qBAAO;AAAA,YACT;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,oBAAoB,CAAC;AACxC,gBAAI,UAAU,oBAAoB,EAAE;AACpC,gBAAI,kBAAkB,oBAAoB,CAAC;AAC3C,gBAAI,gBAAgB,oBAAoB,CAAC;AACzC,gBAAI,QAAQ,oBAAoB,CAAC;AACjC,gBAAI,QAAQ,oBAAoB,CAAC;AACjC,gBAAI,aAAa,oBAAoB,EAAE;AACvC,gBAAIG,SAAQ,oBAAoB,EAAE;AAClC,gBAAI,aAAa,oBAAoB,EAAE;AAEvC,qBAAS,OAAO,QAAQ,MAAM,QAAQ;AACpC,2BAAa,KAAK,MAAM,MAAM;AAC9B,mBAAK,gBAAgB,QAAQ;AAC7B,mBAAK,SAAS,gBAAgB;AAC9B,mBAAK,QAAQ,CAAC;AACd,mBAAK,QAAQ,CAAC;AACd,mBAAK,cAAc;AACnB,mBAAK,SAAS;AAEd,kBAAI,QAAQ,QAAQ,gBAAgB,eAAe;AACjD,qBAAK,eAAe;AAAA,cACtB,WAAW,QAAQ,QAAQ,gBAAgB,QAAQ;AACjD,qBAAK,eAAe,KAAK;AAAA,cAC3B;AAAA,YACF;AAdS;AAgBT,mBAAO,YAAY,OAAO,OAAO,aAAa,SAAS;AACvD,qBAAS,QAAQ,cAAc;AAC7B,qBAAO,IAAI,IAAI,aAAa,IAAI;AAAA,YAClC;AAEA,mBAAO,UAAU,WAAW,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,WAAW,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,kBAAkB,WAAY;AAC7C,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,YAAY,WAAY;AACvC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,UAAU,WAAY;AACrC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,WAAW,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,SAAS,WAAY;AACpC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,YAAY,WAAY;AACvC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,cAAc,WAAY;AACzC,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,MAAM,SAAU,MAAM,YAAY,YAAY;AAC7D,kBAAI,cAAc,QAAQ,cAAc,MAAM;AAC5C,oBAAI,UAAU;AACd,oBAAI,KAAK,gBAAgB,MAAM;AAC7B,wBAAM;AAAA,gBACR;AACA,oBAAI,KAAK,SAAS,EAAE,QAAQ,OAAO,IAAI,IAAI;AACzC,wBAAM;AAAA,gBACR;AACA,wBAAQ,QAAQ;AAChB,qBAAK,SAAS,EAAE,KAAK,OAAO;AAE5B,uBAAO;AAAA,cACT,OAAO;AACL,oBAAI,UAAU;AACd,oBAAI,EAAE,KAAK,SAAS,EAAE,QAAQ,UAAU,IAAI,MAAM,KAAK,SAAS,EAAE,QAAQ,UAAU,IAAI,KAAK;AAC3F,wBAAM;AAAA,gBACR;AAEA,oBAAI,EAAE,WAAW,SAAS,WAAW,SAAS,WAAW,SAAS,OAAO;AACvE,wBAAM;AAAA,gBACR;AAEA,oBAAI,WAAW,SAAS,WAAW,OAAO;AACxC,yBAAO;AAAA,gBACT;AAGA,wBAAQ,SAAS;AACjB,wBAAQ,SAAS;AAGjB,wBAAQ,eAAe;AAGvB,qBAAK,SAAS,EAAE,KAAK,OAAO;AAG5B,2BAAW,MAAM,KAAK,OAAO;AAE7B,oBAAI,cAAc,YAAY;AAC5B,6BAAW,MAAM,KAAK,OAAO;AAAA,gBAC/B;AAEA,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,mBAAO,UAAU,SAAS,SAAU,KAAK;AACvC,kBAAI,OAAO;AACX,kBAAI,eAAe,OAAO;AACxB,oBAAI,QAAQ,MAAM;AAChB,wBAAM;AAAA,gBACR;AACA,oBAAI,EAAE,KAAK,SAAS,QAAQ,KAAK,SAAS,OAAO;AAC/C,wBAAM;AAAA,gBACR;AACA,oBAAI,KAAK,gBAAgB,MAAM;AAC7B,wBAAM;AAAA,gBACR;AAEA,oBAAI,mBAAmB,KAAK,MAAM,MAAM;AACxC,oBAAI;AACJ,oBAAI,IAAI,iBAAiB;AACzB,yBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,yBAAO,iBAAiB,CAAC;AAEzB,sBAAI,KAAK,cAAc;AACrB,yBAAK,aAAa,OAAO,IAAI;AAAA,kBAC/B,OAAO;AACL,yBAAK,OAAO,MAAM,OAAO,IAAI;AAAA,kBAC/B;AAAA,gBACF;AAGA,oBAAI,QAAQ,KAAK,MAAM,QAAQ,IAAI;AACnC,oBAAI,SAAS,IAAI;AACf,wBAAM;AAAA,gBACR;AAEA,qBAAK,MAAM,OAAO,OAAO,CAAC;AAAA,cAC5B,WAAW,eAAe,OAAO;AAC/B,oBAAI,OAAO;AACX,oBAAI,QAAQ,MAAM;AAChB,wBAAM;AAAA,gBACR;AACA,oBAAI,EAAE,KAAK,UAAU,QAAQ,KAAK,UAAU,OAAO;AACjD,wBAAM;AAAA,gBACR;AACA,oBAAI,EAAE,KAAK,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,OAAO;AACvH,wBAAM;AAAA,gBACR;AAEA,oBAAI,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAChD,oBAAI,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAChD,oBAAI,EAAE,cAAc,MAAM,cAAc,KAAK;AAC3C,wBAAM;AAAA,gBACR;AAEA,qBAAK,OAAO,MAAM,OAAO,aAAa,CAAC;AAEvC,oBAAI,KAAK,UAAU,KAAK,QAAQ;AAC9B,uBAAK,OAAO,MAAM,OAAO,aAAa,CAAC;AAAA,gBACzC;AAEA,oBAAI,QAAQ,KAAK,OAAO,MAAM,SAAS,EAAE,QAAQ,IAAI;AACrD,oBAAI,SAAS,IAAI;AACf,wBAAM;AAAA,gBACR;AAEA,qBAAK,OAAO,MAAM,SAAS,EAAE,OAAO,OAAO,CAAC;AAAA,cAC9C;AAAA,YACF;AAEA,mBAAO,UAAU,gBAAgB,WAAY;AAC3C,kBAAI,MAAM,QAAQ;AAClB,kBAAI,OAAO,QAAQ;AACnB,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAEJ,kBAAID,SAAQ,KAAK,SAAS;AAC1B,kBAAI,IAAIA,OAAM;AAEd,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,QAAQA,OAAM,CAAC;AACnB,0BAAU,MAAM,OAAO;AACvB,2BAAW,MAAM,QAAQ;AAEzB,oBAAI,MAAM,SAAS;AACjB,wBAAM;AAAA,gBACR;AAEA,oBAAI,OAAO,UAAU;AACnB,yBAAO;AAAA,gBACT;AAAA,cACF;AAGA,kBAAI,OAAO,QAAQ,WAAW;AAC5B,uBAAO;AAAA,cACT;AAEA,kBAAIA,OAAM,CAAC,EAAE,UAAU,EAAE,eAAe,QAAW;AACjD,yBAASA,OAAM,CAAC,EAAE,UAAU,EAAE;AAAA,cAChC,OAAO;AACL,yBAAS,KAAK;AAAA,cAChB;AAEA,mBAAK,OAAO,OAAO;AACnB,mBAAK,MAAM,MAAM;AAGjB,qBAAO,IAAIC,OAAM,KAAK,MAAM,KAAK,GAAG;AAAA,YACtC;AAEA,mBAAO,UAAU,eAAe,SAAU,WAAW;AAEnD,kBAAI,OAAO,QAAQ;AACnB,kBAAI,QAAQ,CAAC,QAAQ;AACrB,kBAAI,MAAM,QAAQ;AAClB,kBAAI,SAAS,CAAC,QAAQ;AACtB,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAEJ,kBAAID,SAAQ,KAAK;AACjB,kBAAI,IAAIA,OAAM;AACd,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,QAAQA,OAAM,CAAC;AAEnB,oBAAI,aAAa,MAAM,SAAS,MAAM;AACpC,wBAAM,aAAa;AAAA,gBACrB;AACA,2BAAW,MAAM,QAAQ;AACzB,4BAAY,MAAM,SAAS;AAC3B,0BAAU,MAAM,OAAO;AACvB,6BAAa,MAAM,UAAU;AAE7B,oBAAI,OAAO,UAAU;AACnB,yBAAO;AAAA,gBACT;AAEA,oBAAI,QAAQ,WAAW;AACrB,0BAAQ;AAAA,gBACV;AAEA,oBAAI,MAAM,SAAS;AACjB,wBAAM;AAAA,gBACR;AAEA,oBAAI,SAAS,YAAY;AACvB,2BAAS;AAAA,gBACX;AAAA,cACF;AAEA,kBAAI,eAAe,IAAI,WAAW,MAAM,KAAK,QAAQ,MAAM,SAAS,GAAG;AACvE,kBAAI,QAAQ,QAAQ,WAAW;AAC7B,qBAAK,OAAO,KAAK,OAAO,QAAQ;AAChC,qBAAK,QAAQ,KAAK,OAAO,SAAS;AAClC,qBAAK,MAAM,KAAK,OAAO,OAAO;AAC9B,qBAAK,SAAS,KAAK,OAAO,UAAU;AAAA,cACtC;AAEA,kBAAIA,OAAM,CAAC,EAAE,UAAU,EAAE,eAAe,QAAW;AACjD,yBAASA,OAAM,CAAC,EAAE,UAAU,EAAE;AAAA,cAChC,OAAO;AACL,yBAAS,KAAK;AAAA,cAChB;AAEA,mBAAK,OAAO,aAAa,IAAI;AAC7B,mBAAK,QAAQ,aAAa,IAAI,aAAa,QAAQ;AACnD,mBAAK,MAAM,aAAa,IAAI;AAC5B,mBAAK,SAAS,aAAa,IAAI,aAAa,SAAS;AAAA,YACvD;AAEA,mBAAO,kBAAkB,SAAUA,QAAO;AACxC,kBAAI,OAAO,QAAQ;AACnB,kBAAI,QAAQ,CAAC,QAAQ;AACrB,kBAAI,MAAM,QAAQ;AAClB,kBAAI,SAAS,CAAC,QAAQ;AACtB,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAEJ,kBAAI,IAAIA,OAAM;AAEd,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,QAAQA,OAAM,CAAC;AACnB,2BAAW,MAAM,QAAQ;AACzB,4BAAY,MAAM,SAAS;AAC3B,0BAAU,MAAM,OAAO;AACvB,6BAAa,MAAM,UAAU;AAE7B,oBAAI,OAAO,UAAU;AACnB,yBAAO;AAAA,gBACT;AAEA,oBAAI,QAAQ,WAAW;AACrB,0BAAQ;AAAA,gBACV;AAEA,oBAAI,MAAM,SAAS;AACjB,wBAAM;AAAA,gBACR;AAEA,oBAAI,SAAS,YAAY;AACvB,2BAAS;AAAA,gBACX;AAAA,cACF;AAEA,kBAAI,eAAe,IAAI,WAAW,MAAM,KAAK,QAAQ,MAAM,SAAS,GAAG;AAEvE,qBAAO;AAAA,YACT;AAEA,mBAAO,UAAU,wBAAwB,WAAY;AACnD,kBAAI,QAAQ,KAAK,aAAa,QAAQ,GAAG;AACvC,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO,KAAK,OAAO,sBAAsB;AAAA,cAC3C;AAAA,YACF;AAEA,mBAAO,UAAU,mBAAmB,WAAY;AAC9C,kBAAI,KAAK,iBAAiB,QAAQ,WAAW;AAC3C,sBAAM;AAAA,cACR;AACA,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,oBAAoB,WAAY;AAC/C,kBAAI,OAAO;AACX,kBAAIA,SAAQ,KAAK;AACjB,kBAAI,IAAIA,OAAM;AAEd,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,QAAQA,OAAM,CAAC;AACnB,wBAAQ,MAAM,kBAAkB;AAAA,cAClC;AAEA,kBAAI,QAAQ,GAAG;AACb,qBAAK,gBAAgB,gBAAgB;AAAA,cACvC,OAAO;AACL,qBAAK,gBAAgB,OAAO,KAAK,KAAK,KAAK,MAAM,MAAM;AAAA,cACzD;AAEA,qBAAO,KAAK;AAAA,YACd;AAEA,mBAAO,UAAU,kBAAkB,WAAY;AAC7C,kBAAI,OAAO;AACX,kBAAI,KAAK,MAAM,UAAU,GAAG;AAC1B,qBAAK,cAAc;AACnB;AAAA,cACF;AAEA,kBAAI,QAAQ,IAAI,WAAW;AAC3B,kBAAI,UAAU,oBAAI,IAAI;AACtB,kBAAI,cAAc,KAAK,MAAM,CAAC;AAC9B,kBAAI;AACJ,kBAAI;AACJ,kBAAI,iBAAiB,YAAY,aAAa;AAC9C,6BAAe,QAAQ,SAAU,MAAM;AACrC,sBAAM,KAAK,IAAI;AACf,wBAAQ,IAAI,IAAI;AAAA,cAClB,CAAC;AAED,qBAAO,MAAM,WAAW,GAAG;AACzB,8BAAc,MAAM,MAAM;AAG1B,gCAAgB,YAAY,SAAS;AACrC,oBAAI,OAAO,cAAc;AACzB,yBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,sBAAI,eAAe,cAAc,CAAC;AAClC,oCAAkB,aAAa,mBAAmB,aAAa,IAAI;AAGnE,sBAAI,mBAAmB,QAAQ,CAAC,QAAQ,IAAI,eAAe,GAAG;AAC5D,wBAAI,qBAAqB,gBAAgB,aAAa;AAEtD,uCAAmB,QAAQ,SAAU,MAAM;AACzC,4BAAM,KAAK,IAAI;AACf,8BAAQ,IAAI,IAAI;AAAA,oBAClB,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,cACF;AAEA,mBAAK,cAAc;AAEnB,kBAAI,QAAQ,QAAQ,KAAK,MAAM,QAAQ;AACrC,oBAAI,yBAAyB;AAE7B,wBAAQ,QAAQ,SAAU,aAAa;AACrC,sBAAI,YAAY,SAAS,MAAM;AAC7B;AAAA,kBACF;AAAA,gBACF,CAAC;AAED,oBAAI,0BAA0B,KAAK,MAAM,QAAQ;AAC/C,uBAAK,cAAc;AAAA,gBACrB;AAAA,cACF;AAAA,YACF;AAEA,YAAAH,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI;AACJ,gBAAI,QAAQ,oBAAoB,CAAC;AAEjC,qBAAS,cAAc,QAAQ;AAC7B,uBAAS,oBAAoB,CAAC;AAC9B,mBAAK,SAAS;AAEd,mBAAK,SAAS,CAAC;AACf,mBAAK,QAAQ,CAAC;AAAA,YAChB;AANS;AAQT,0BAAc,UAAU,UAAU,WAAY;AAC5C,kBAAI,SAAS,KAAK,OAAO,SAAS;AAClC,kBAAI,QAAQ,KAAK,OAAO,QAAQ,IAAI;AACpC,kBAAI,OAAO,KAAK,IAAI,QAAQ,KAAK;AACjC,mBAAK,aAAa,IAAI;AACtB,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,MAAM,SAAU,UAAU,YAAY,SAAS,YAAY,YAAY;AAE7F,kBAAI,WAAW,QAAQ,cAAc,QAAQ,cAAc,MAAM;AAC/D,oBAAI,YAAY,MAAM;AACpB,wBAAM;AAAA,gBACR;AACA,oBAAI,cAAc,MAAM;AACtB,wBAAM;AAAA,gBACR;AACA,oBAAI,KAAK,OAAO,QAAQ,QAAQ,IAAI,IAAI;AACtC,wBAAM;AAAA,gBACR;AAEA,qBAAK,OAAO,KAAK,QAAQ;AAEzB,oBAAI,SAAS,UAAU,MAAM;AAC3B,wBAAM;AAAA,gBACR;AACA,oBAAI,WAAW,SAAS,MAAM;AAC5B,wBAAM;AAAA,gBACR;AAEA,yBAAS,SAAS;AAClB,2BAAW,QAAQ;AAEnB,uBAAO;AAAA,cACT,OAAO;AAEL,6BAAa;AACb,6BAAa;AACb,0BAAU;AACV,oBAAI,cAAc,WAAW,SAAS;AACtC,oBAAI,cAAc,WAAW,SAAS;AAEtC,oBAAI,EAAE,eAAe,QAAQ,YAAY,gBAAgB,KAAK,OAAO;AACnE,wBAAM;AAAA,gBACR;AACA,oBAAI,EAAE,eAAe,QAAQ,YAAY,gBAAgB,KAAK,OAAO;AACnE,wBAAM;AAAA,gBACR;AAEA,oBAAI,eAAe,aAAa;AAC9B,0BAAQ,eAAe;AACvB,yBAAO,YAAY,IAAI,SAAS,YAAY,UAAU;AAAA,gBACxD,OAAO;AACL,0BAAQ,eAAe;AAGvB,0BAAQ,SAAS;AACjB,0BAAQ,SAAS;AAGjB,sBAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,IAAI;AACpC,0BAAM;AAAA,kBACR;AAEA,uBAAK,MAAM,KAAK,OAAO;AAGvB,sBAAI,EAAE,QAAQ,UAAU,QAAQ,QAAQ,UAAU,OAAO;AACvD,0BAAM;AAAA,kBACR;AAEA,sBAAI,EAAE,QAAQ,OAAO,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,OAAO,MAAM,QAAQ,OAAO,KAAK,KAAK;AACjG,0BAAM;AAAA,kBACR;AAEA,0BAAQ,OAAO,MAAM,KAAK,OAAO;AACjC,0BAAQ,OAAO,MAAM,KAAK,OAAO;AAEjC,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAEA,0BAAc,UAAU,SAAS,SAAU,MAAM;AAC/C,kBAAI,gBAAgB,QAAQ;AAC1B,oBAAI,QAAQ;AACZ,oBAAI,MAAM,gBAAgB,KAAK,MAAM;AACnC,wBAAM;AAAA,gBACR;AACA,oBAAI,EAAE,SAAS,KAAK,aAAa,MAAM,UAAU,QAAQ,MAAM,OAAO,gBAAgB,OAAO;AAC3F,wBAAM;AAAA,gBACR;AAGA,oBAAI,mBAAmB,CAAC;AAExB,mCAAmB,iBAAiB,OAAO,MAAM,SAAS,CAAC;AAE3D,oBAAI;AACJ,oBAAI,IAAI,iBAAiB;AACzB,yBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,yBAAO,iBAAiB,CAAC;AACzB,wBAAM,OAAO,IAAI;AAAA,gBACnB;AAGA,oBAAI,mBAAmB,CAAC;AAExB,mCAAmB,iBAAiB,OAAO,MAAM,SAAS,CAAC;AAE3D,oBAAI;AACJ,oBAAI,iBAAiB;AACrB,yBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,yBAAO,iBAAiB,CAAC;AACzB,wBAAM,OAAO,IAAI;AAAA,gBACnB;AAGA,oBAAI,SAAS,KAAK,WAAW;AAC3B,uBAAK,aAAa,IAAI;AAAA,gBACxB;AAGA,oBAAI,QAAQ,KAAK,OAAO,QAAQ,KAAK;AACrC,qBAAK,OAAO,OAAO,OAAO,CAAC;AAG3B,sBAAM,SAAS;AAAA,cACjB,WAAW,gBAAgB,OAAO;AAChC,uBAAO;AACP,oBAAI,QAAQ,MAAM;AAChB,wBAAM;AAAA,gBACR;AACA,oBAAI,CAAC,KAAK,cAAc;AACtB,wBAAM;AAAA,gBACR;AACA,oBAAI,EAAE,KAAK,UAAU,QAAQ,KAAK,UAAU,OAAO;AACjD,wBAAM;AAAA,gBACR;AAIA,oBAAI,EAAE,KAAK,OAAO,MAAM,QAAQ,IAAI,KAAK,MAAM,KAAK,OAAO,MAAM,QAAQ,IAAI,KAAK,KAAK;AACrF,wBAAM;AAAA,gBACR;AAEA,oBAAI,QAAQ,KAAK,OAAO,MAAM,QAAQ,IAAI;AAC1C,qBAAK,OAAO,MAAM,OAAO,OAAO,CAAC;AACjC,wBAAQ,KAAK,OAAO,MAAM,QAAQ,IAAI;AACtC,qBAAK,OAAO,MAAM,OAAO,OAAO,CAAC;AAIjC,oBAAI,EAAE,KAAK,OAAO,SAAS,QAAQ,KAAK,OAAO,MAAM,gBAAgB,KAAK,OAAO;AAC/E,wBAAM;AAAA,gBACR;AACA,oBAAI,KAAK,OAAO,MAAM,gBAAgB,EAAE,MAAM,QAAQ,IAAI,KAAK,IAAI;AACjE,wBAAM;AAAA,gBACR;AAEA,oBAAI,QAAQ,KAAK,OAAO,MAAM,gBAAgB,EAAE,MAAM,QAAQ,IAAI;AAClE,qBAAK,OAAO,MAAM,gBAAgB,EAAE,MAAM,OAAO,OAAO,CAAC;AAAA,cAC3D;AAAA,YACF;AAEA,0BAAc,UAAU,eAAe,WAAY;AACjD,mBAAK,UAAU,aAAa,IAAI;AAAA,YAClC;AAEA,0BAAc,UAAU,YAAY,WAAY;AAC9C,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,cAAc,WAAY;AAChD,kBAAI,KAAK,YAAY,MAAM;AACzB,oBAAI,WAAW,CAAC;AAChB,oBAAI,SAAS,KAAK,UAAU;AAC5B,oBAAI,IAAI,OAAO;AACf,yBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,6BAAW,SAAS,OAAO,OAAO,CAAC,EAAE,SAAS,CAAC;AAAA,gBACjD;AACA,qBAAK,WAAW;AAAA,cAClB;AACA,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,gBAAgB,WAAY;AAClD,mBAAK,WAAW;AAAA,YAClB;AAEA,0BAAc,UAAU,gBAAgB,WAAY;AAClD,mBAAK,WAAW;AAAA,YAClB;AAEA,0BAAc,UAAU,kCAAkC,WAAY;AACpE,mBAAK,6BAA6B;AAAA,YACpC;AAEA,0BAAc,UAAU,cAAc,WAAY;AAChD,kBAAI,KAAK,YAAY,MAAM;AACzB,oBAAI,WAAW,CAAC;AAChB,oBAAI,SAAS,KAAK,UAAU;AAC5B,oBAAI,IAAI,OAAO;AACf,yBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,6BAAW,SAAS,OAAO,OAAO,CAAC,EAAE,SAAS,CAAC;AAAA,gBACjD;AAEA,2BAAW,SAAS,OAAO,KAAK,KAAK;AAErC,qBAAK,WAAW;AAAA,cAClB;AACA,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,gCAAgC,WAAY;AAClE,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,gCAAgC,SAAU,UAAU;AAC1E,kBAAI,KAAK,8BAA8B,MAAM;AAC3C,sBAAM;AAAA,cACR;AAEA,mBAAK,6BAA6B;AAAA,YACpC;AAEA,0BAAc,UAAU,UAAU,WAAY;AAC5C,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,eAAe,SAAU,OAAO;AACtD,kBAAI,MAAM,gBAAgB,KAAK,MAAM;AACnC,sBAAM;AAAA,cACR;AAEA,mBAAK,YAAY;AAEjB,kBAAI,MAAM,UAAU,MAAM;AACxB,sBAAM,SAAS,KAAK,OAAO,QAAQ,WAAW;AAAA,cAChD;AAAA,YACF;AAEA,0BAAc,UAAU,YAAY,WAAY;AAC9C,qBAAO,KAAK;AAAA,YACd;AAEA,0BAAc,UAAU,uBAAuB,SAAU,WAAW,YAAY;AAC9E,kBAAI,EAAE,aAAa,QAAQ,cAAc,OAAO;AAC9C,sBAAM;AAAA,cACR;AAEA,kBAAI,aAAa,YAAY;AAC3B,uBAAO;AAAA,cACT;AAEA,kBAAI,aAAa,UAAU,SAAS;AACpC,kBAAI;AAEJ,iBAAG;AACD,6BAAa,WAAW,UAAU;AAElC,oBAAI,cAAc,MAAM;AACtB;AAAA,gBACF;AAEA,oBAAI,cAAc,YAAY;AAC5B,yBAAO;AAAA,gBACT;AAEA,6BAAa,WAAW,SAAS;AACjC,oBAAI,cAAc,MAAM;AACtB;AAAA,gBACF;AAAA,cACF,SAAS;AAET,2BAAa,WAAW,SAAS;AAEjC,iBAAG;AACD,6BAAa,WAAW,UAAU;AAElC,oBAAI,cAAc,MAAM;AACtB;AAAA,gBACF;AAEA,oBAAI,cAAc,WAAW;AAC3B,yBAAO;AAAA,gBACT;AAEA,6BAAa,WAAW,SAAS;AACjC,oBAAI,cAAc,MAAM;AACtB;AAAA,gBACF;AAAA,cACF,SAAS;AAET,qBAAO;AAAA,YACT;AAEA,0BAAc,UAAU,4BAA4B,WAAY;AAC9D,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAEJ,kBAAI,QAAQ,KAAK,YAAY;AAC7B,kBAAI,IAAI,MAAM;AACd,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,uBAAO,MAAM,CAAC;AAEd,6BAAa,KAAK;AAClB,6BAAa,KAAK;AAClB,qBAAK,MAAM;AACX,qBAAK,cAAc;AACnB,qBAAK,cAAc;AAEnB,oBAAI,cAAc,YAAY;AAC5B,uBAAK,MAAM,WAAW,SAAS;AAC/B;AAAA,gBACF;AAEA,sCAAsB,WAAW,SAAS;AAE1C,uBAAO,KAAK,OAAO,MAAM;AACvB,uBAAK,cAAc;AACnB,wCAAsB,WAAW,SAAS;AAE1C,yBAAO,KAAK,OAAO,MAAM;AACvB,wBAAI,uBAAuB,qBAAqB;AAC9C,2BAAK,MAAM;AACX;AAAA,oBACF;AAEA,wBAAI,uBAAuB,KAAK,WAAW;AACzC;AAAA,oBACF;AAEA,wBAAI,KAAK,OAAO,MAAM;AACpB,4BAAM;AAAA,oBACR;AACA,yBAAK,cAAc,oBAAoB,UAAU;AACjD,0CAAsB,KAAK,YAAY,SAAS;AAAA,kBAClD;AAEA,sBAAI,uBAAuB,KAAK,WAAW;AACzC;AAAA,kBACF;AAEA,sBAAI,KAAK,OAAO,MAAM;AACpB,yBAAK,cAAc,oBAAoB,UAAU;AACjD,0CAAsB,KAAK,YAAY,SAAS;AAAA,kBAClD;AAAA,gBACF;AAEA,oBAAI,KAAK,OAAO,MAAM;AACpB,wBAAM;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AAEA,0BAAc,UAAU,2BAA2B,SAAU,WAAW,YAAY;AAClF,kBAAI,aAAa,YAAY;AAC3B,uBAAO,UAAU,SAAS;AAAA,cAC5B;AACA,kBAAI,kBAAkB,UAAU,SAAS;AAEzC,iBAAG;AACD,oBAAI,mBAAmB,MAAM;AAC3B;AAAA,gBACF;AACA,oBAAI,mBAAmB,WAAW,SAAS;AAE3C,mBAAG;AACD,sBAAI,oBAAoB,MAAM;AAC5B;AAAA,kBACF;AAEA,sBAAI,oBAAoB,iBAAiB;AACvC,2BAAO;AAAA,kBACT;AACA,qCAAmB,iBAAiB,UAAU,EAAE,SAAS;AAAA,gBAC3D,SAAS;AAET,kCAAkB,gBAAgB,UAAU,EAAE,SAAS;AAAA,cACzD,SAAS;AAET,qBAAO;AAAA,YACT;AAEA,0BAAc,UAAU,0BAA0B,SAAU,OAAO,OAAO;AACxE,kBAAI,SAAS,QAAQ,SAAS,MAAM;AAClC,wBAAQ,KAAK;AACb,wBAAQ;AAAA,cACV;AACA,kBAAI;AAEJ,kBAAIE,SAAQ,MAAM,SAAS;AAC3B,kBAAI,IAAIA,OAAM;AACd,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,uBAAOA,OAAM,CAAC;AACd,qBAAK,qBAAqB;AAE1B,oBAAI,KAAK,SAAS,MAAM;AACtB,uBAAK,wBAAwB,KAAK,OAAO,QAAQ,CAAC;AAAA,gBACpD;AAAA,cACF;AAAA,YACF;AAEA,0BAAc,UAAU,sBAAsB,WAAY;AACxD,kBAAI;AAEJ,kBAAI,IAAI,KAAK,MAAM;AACnB,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,uBAAO,KAAK,MAAM,CAAC;AAEnB,oBAAI,KAAK,qBAAqB,KAAK,QAAQ,KAAK,MAAM,GAAG;AACvD,yBAAO;AAAA,gBACT;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAEA,YAAAH,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,kBAAkB,oBAAoB,CAAC;AAE3C,qBAAS,oBAAoB;AAAA,YAAC;AAArB;AAGT,qBAAS,QAAQ,iBAAiB;AAChC,gCAAkB,IAAI,IAAI,gBAAgB,IAAI;AAAA,YAChD;AAEA,8BAAkB,iBAAiB;AAEnC,8BAAkB,sBAAsB;AACxC,8BAAkB,0BAA0B;AAC5C,8BAAkB,6BAA6B;AAC/C,8BAAkB,2BAA2B;AAC7C,8BAAkB,oCAAoC;AACtD,8BAAkB,+BAA+B;AACjD,8BAAkB,wCAAwC;AAC1D,8BAAkB,kDAAkD;AACpE,8BAAkB,gDAAgD;AAClE,8BAAkB,qCAAqC;AACvD,8BAAkB,4BAA4B;AAC9C,8BAAkB,8BAA8B;AAChD,8BAAkB,8BAA8B;AAChD,8BAAkB,oCAAoC;AACtD,8BAAkB,wBAAwB,kBAAkB,oCAAoC;AAChG,8BAAkB,qBAAqB,kBAAkB,sBAAsB;AAC/E,8BAAkB,2BAA2B;AAC7C,8BAAkB,qCAAqC;AACvD,8BAAkB,kBAAkB;AACpC,8BAAkB,gCAAgC;AAElD,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAUA,gBAAIG,SAAQ,oBAAoB,EAAE;AAElC,qBAAS,YAAY;AAAA,YAAC;AAAb;AAST,sBAAU,uBAAuB,SAAU,OAAO,OAAO,eAAe,kBAAkB;AACxF,kBAAI,CAAC,MAAM,WAAW,KAAK,GAAG;AAC5B,sBAAM;AAAA,cACR;AAEA,kBAAI,aAAa,IAAI,MAAM,CAAC;AAE5B,mBAAK,oCAAoC,OAAO,OAAO,UAAU;AAEjE,4BAAc,CAAC,IAAI,KAAK,IAAI,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;AAC3F,4BAAc,CAAC,IAAI,KAAK,IAAI,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;AAG7F,kBAAI,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,SAAS,KAAK,MAAM,SAAS,GAAG;AAYxE,8BAAc,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,GAAG,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC;AAAA,cAC/F,WAAW,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,SAAS,KAAK,MAAM,SAAS,GAAG;AAY/E,8BAAc,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,GAAG,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC;AAAA,cAC/F;AACA,kBAAI,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,UAAU,KAAK,MAAM,UAAU,GAAG;AAc1E,8BAAc,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,GAAG,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC;AAAA,cACjG,WAAW,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,UAAU,KAAK,MAAM,UAAU,GAAG;AAcjF,8BAAc,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,GAAG,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC;AAAA,cACjG;AAGA,kBAAI,QAAQ,KAAK,KAAK,MAAM,WAAW,IAAI,MAAM,WAAW,MAAM,MAAM,WAAW,IAAI,MAAM,WAAW,EAAE;AAE1G,kBAAI,MAAM,WAAW,MAAM,MAAM,WAAW,KAAK,MAAM,WAAW,MAAM,MAAM,WAAW,GAAG;AAE1F,wBAAQ;AAAA,cACV;AAEA,kBAAI,UAAU,QAAQ,cAAc,CAAC;AACrC,kBAAI,UAAU,cAAc,CAAC,IAAI;AACjC,kBAAI,cAAc,CAAC,IAAI,SAAS;AAC9B,0BAAU,cAAc,CAAC;AAAA,cAC3B,OAAO;AACL,0BAAU,cAAc,CAAC;AAAA,cAC3B;AAGA,4BAAc,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,UAAU,IAAI;AACvD,4BAAc,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,UAAU,IAAI;AAAA,YACzD;AAUA,sBAAU,sCAAsC,SAAU,OAAO,OAAO,YAAY;AAClF,kBAAI,MAAM,WAAW,IAAI,MAAM,WAAW,GAAG;AAC3C,2BAAW,CAAC,IAAI;AAAA,cAClB,OAAO;AACL,2BAAW,CAAC,IAAI;AAAA,cAClB;AAEA,kBAAI,MAAM,WAAW,IAAI,MAAM,WAAW,GAAG;AAC3C,2BAAW,CAAC,IAAI;AAAA,cAClB,OAAO;AACL,2BAAW,CAAC,IAAI;AAAA,cAClB;AAAA,YACF;AAQA,sBAAU,mBAAmB,SAAU,OAAO,OAAO,QAAQ;AAE3D,kBAAI,MAAM,MAAM,WAAW;AAC3B,kBAAI,MAAM,MAAM,WAAW;AAC3B,kBAAI,MAAM,MAAM,WAAW;AAC3B,kBAAI,MAAM,MAAM,WAAW;AAG3B,kBAAI,MAAM,WAAW,KAAK,GAAG;AAC3B,uBAAO,CAAC,IAAI;AACZ,uBAAO,CAAC,IAAI;AACZ,uBAAO,CAAC,IAAI;AACZ,uBAAO,CAAC,IAAI;AACZ,uBAAO;AAAA,cACT;AAEA,kBAAI,YAAY,MAAM,KAAK;AAC3B,kBAAI,YAAY,MAAM,KAAK;AAC3B,kBAAI,aAAa,MAAM,SAAS;AAChC,kBAAI,eAAe,MAAM,KAAK;AAC9B,kBAAI,eAAe,MAAM,UAAU;AACnC,kBAAI,gBAAgB,MAAM,SAAS;AACnC,kBAAI,aAAa,MAAM,aAAa;AACpC,kBAAI,cAAc,MAAM,cAAc;AAEtC,kBAAI,YAAY,MAAM,KAAK;AAC3B,kBAAI,YAAY,MAAM,KAAK;AAC3B,kBAAI,aAAa,MAAM,SAAS;AAChC,kBAAI,eAAe,MAAM,KAAK;AAC9B,kBAAI,eAAe,MAAM,UAAU;AACnC,kBAAI,gBAAgB,MAAM,SAAS;AACnC,kBAAI,aAAa,MAAM,aAAa;AACpC,kBAAI,cAAc,MAAM,cAAc;AAGtC,kBAAI,kBAAkB;AACtB,kBAAI,kBAAkB;AAGtB,kBAAI,QAAQ,KAAK;AACf,oBAAI,MAAM,KAAK;AACb,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO;AAAA,gBACT,WAAW,MAAM,KAAK;AACpB,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO;AAAA,gBACT,OAAO;AAAA,gBAEP;AAAA,cACF,WAES,QAAQ,KAAK;AAClB,oBAAI,MAAM,KAAK;AACb,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO;AAAA,gBACT,WAAW,MAAM,KAAK;AACpB,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO,CAAC,IAAI;AACZ,yBAAO;AAAA,gBACT,OAAO;AAAA,gBAEP;AAAA,cACF,OAAO;AAEL,oBAAI,SAAS,MAAM,SAAS,MAAM;AAClC,oBAAI,SAAS,MAAM,SAAS,MAAM;AAGlC,oBAAI,cAAc,MAAM,QAAQ,MAAM;AACtC,oBAAI,qBAAqB;AACzB,oBAAI,qBAAqB;AACzB,oBAAI,cAAc;AAClB,oBAAI,cAAc;AAClB,oBAAI,cAAc;AAClB,oBAAI,cAAc;AAGlB,oBAAI,CAAC,WAAW,YAAY;AAC1B,sBAAI,MAAM,KAAK;AACb,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB,OAAO;AACL,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB;AAAA,gBACF,WAAW,WAAW,YAAY;AAChC,sBAAI,MAAM,KAAK;AACb,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB,OAAO;AACL,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB;AAAA,gBACF;AAGA,oBAAI,CAAC,WAAW,YAAY;AAC1B,sBAAI,MAAM,KAAK;AACb,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB,OAAO;AACL,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB;AAAA,gBACF,WAAW,WAAW,YAAY;AAChC,sBAAI,MAAM,KAAK;AACb,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB,OAAO;AACL,2BAAO,CAAC,IAAI;AACZ,2BAAO,CAAC,IAAI;AACZ,sCAAkB;AAAA,kBACpB;AAAA,gBACF;AAGA,oBAAI,mBAAmB,iBAAiB;AACtC,yBAAO;AAAA,gBACT;AAGA,oBAAI,MAAM,KAAK;AACb,sBAAI,MAAM,KAAK;AACb,yCAAqB,KAAK,qBAAqB,QAAQ,YAAY,CAAC;AACpE,yCAAqB,KAAK,qBAAqB,QAAQ,YAAY,CAAC;AAAA,kBACtE,OAAO;AACL,yCAAqB,KAAK,qBAAqB,CAAC,QAAQ,YAAY,CAAC;AACrE,yCAAqB,KAAK,qBAAqB,CAAC,QAAQ,YAAY,CAAC;AAAA,kBACvE;AAAA,gBACF,OAAO;AACL,sBAAI,MAAM,KAAK;AACb,yCAAqB,KAAK,qBAAqB,CAAC,QAAQ,YAAY,CAAC;AACrE,yCAAqB,KAAK,qBAAqB,CAAC,QAAQ,YAAY,CAAC;AAAA,kBACvE,OAAO;AACL,yCAAqB,KAAK,qBAAqB,QAAQ,YAAY,CAAC;AACpE,yCAAqB,KAAK,qBAAqB,QAAQ,YAAY,CAAC;AAAA,kBACtE;AAAA,gBACF;AAEA,oBAAI,CAAC,iBAAiB;AACpB,0BAAQ,oBAAoB;AAAA,oBAC1B,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,CAAC,cAAc;AACnC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,oBACF,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,aAAa;AACjC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,oBACF,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,cAAc;AAClC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,oBACF,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,CAAC,aAAa;AAClC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,kBACJ;AAAA,gBACF;AACA,oBAAI,CAAC,iBAAiB;AACpB,0BAAQ,oBAAoB;AAAA,oBAC1B,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,CAAC,cAAc;AACnC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,oBACF,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,aAAa;AACjC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,oBACF,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,cAAc;AAClC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,oBACF,KAAK;AACH,oCAAc;AACd,oCAAc,MAAM,CAAC,aAAa;AAClC,6BAAO,CAAC,IAAI;AACZ,6BAAO,CAAC,IAAI;AACZ;AAAA,kBACJ;AAAA,gBACF;AAAA,cACF;AACF,qBAAO;AAAA,YACT;AASA,sBAAU,uBAAuB,SAAU,OAAO,YAAY,MAAM;AAClE,kBAAI,QAAQ,YAAY;AACtB,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO,IAAI,OAAO;AAAA,cACpB;AAAA,YACF;AAMA,sBAAU,kBAAkB,SAAU,IAAI,IAAI,IAAI,IAAI;AACpD,kBAAI,MAAM,MAAM;AACd,uBAAO,KAAK,iBAAiB,IAAI,IAAI,EAAE;AAAA,cACzC;AAEA,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,KAAK,GAAG;AACZ,kBAAI,IAAI,QACJ,IAAI;AACR,kBAAI,KAAK,QACL,KAAK,QACL,KAAK,QACL,KAAK,QACL,KAAK,QACL,KAAK;AACT,kBAAI,QAAQ;AAEZ,mBAAK,KAAK;AACV,mBAAK,KAAK;AACV,mBAAK,KAAK,KAAK,KAAK;AAEpB,mBAAK,KAAK;AACV,mBAAK,KAAK;AACV,mBAAK,KAAK,KAAK,KAAK;AAEpB,sBAAQ,KAAK,KAAK,KAAK;AAEvB,kBAAI,UAAU,GAAG;AACf,uBAAO;AAAA,cACT;AAEA,mBAAK,KAAK,KAAK,KAAK,MAAM;AAC1B,mBAAK,KAAK,KAAK,KAAK,MAAM;AAE1B,qBAAO,IAAIA,OAAM,GAAG,CAAC;AAAA,YACvB;AAMA,sBAAU,gBAAgB,SAAU,IAAI,IAAI,IAAI,IAAI;AAClD,kBAAI,UAAU;AAEd,kBAAI,OAAO,IAAI;AACb,0BAAU,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;AAEzC,oBAAI,KAAK,IAAI;AACX,6BAAW,KAAK;AAAA,gBAClB,WAAW,KAAK,IAAI;AAClB,6BAAW,KAAK;AAAA,gBAClB;AAAA,cACF,WAAW,KAAK,IAAI;AAClB,0BAAU,KAAK;AAAA,cACjB,OAAO;AACL,0BAAU,KAAK;AAAA,cACjB;AAEA,qBAAO;AAAA,YACT;AAOA,sBAAU,cAAc,SAAU,IAAI,IAAI,IAAI,IAAI;AAChD,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,IAAI,GAAG;AACX,kBAAI,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AAE7C,kBAAI,QAAQ,GAAG;AACb,uBAAO;AAAA,cACT,OAAO;AACL,oBAAI,WAAW,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AACvD,oBAAI,UAAU,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AACtD,uBAAO,IAAI,UAAU,SAAS,KAAK,IAAI,SAAS,QAAQ;AAAA,cAC1D;AAAA,YACF;AAQA,sBAAU,UAAU,MAAM,KAAK;AAC/B,sBAAU,kBAAkB,MAAM,KAAK;AACvC,sBAAU,SAAS,IAAM,KAAK;AAC9B,sBAAU,WAAW,IAAM,KAAK;AAEhC,YAAAJ,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,QAAQ;AAAA,YAAC;AAAT;AAKT,kBAAM,OAAO,SAAU,OAAO;AAC5B,kBAAI,QAAQ,GAAG;AACb,uBAAO;AAAA,cACT,WAAW,QAAQ,GAAG;AACpB,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,kBAAM,QAAQ,SAAU,OAAO;AAC7B,qBAAO,QAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,KAAK;AAAA,YACxD;AAEA,kBAAM,OAAO,SAAU,OAAO;AAC5B,qBAAO,QAAQ,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK;AAAA,YACxD;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,UAAU;AAAA,YAAC;AAAX;AAET,oBAAQ,YAAY;AACpB,oBAAQ,YAAY;AAEpB,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,2BAAY;AAAE,uBAAS,iBAAiB,QAAQ,OAAO;AAAE,yBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,sBAAI,aAAa,MAAM,CAAC;AAAG,6BAAW,aAAa,WAAW,cAAc;AAAO,6BAAW,eAAe;AAAM,sBAAI,WAAW,WAAY,YAAW,WAAW;AAAM,yBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,gBAAG;AAAA,cAAE;AAAlT;AAAoT,qBAAO,SAAU,aAAa,YAAY,aAAa;AAAE,oBAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,oBAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,uBAAO;AAAA,cAAa;AAAA,YAAG,EAAE;AAEljB,qBAAS,gBAAgBI,WAAU,aAAa;AAAE,kBAAI,EAAEA,qBAAoB,cAAc;AAAE,sBAAM,IAAI,UAAU,mCAAmC;AAAA,cAAG;AAAA,YAAE;AAA/I;AAET,gBAAI,WAAW,gCAASC,UAAS,OAAO;AACtC,qBAAO,EAAE,OAAc,MAAM,MAAM,MAAM,KAAK;AAAA,YAChD,GAFe;AAIf,gBAAI,MAAM,gCAASC,KAAI,MAAM,MAAMC,OAAM,MAAM;AAC7C,kBAAI,SAAS,MAAM;AACjB,qBAAK,OAAO;AAAA,cACd,OAAO;AACL,qBAAK,OAAO;AAAA,cACd;AAEA,kBAAIA,UAAS,MAAM;AACjB,gBAAAA,MAAK,OAAO;AAAA,cACd,OAAO;AACL,qBAAK,OAAO;AAAA,cACd;AAEA,mBAAK,OAAO;AACZ,mBAAK,OAAOA;AAEZ,mBAAK;AAEL,qBAAO;AAAA,YACT,GAnBU;AAqBV,gBAAI,UAAU,gCAASC,SAAQ,MAAM,MAAM;AACzC,kBAAI,OAAO,KAAK,MACZD,QAAO,KAAK;AAGhB,kBAAI,SAAS,MAAM;AACjB,qBAAK,OAAOA;AAAA,cACd,OAAO;AACL,qBAAK,OAAOA;AAAA,cACd;AAEA,kBAAIA,UAAS,MAAM;AACjB,gBAAAA,MAAK,OAAO;AAAA,cACd,OAAO;AACL,qBAAK,OAAO;AAAA,cACd;AAEA,mBAAK,OAAO,KAAK,OAAO;AAExB,mBAAK;AAEL,qBAAO;AAAA,YACT,GAtBc;AAwBd,gBAAI,aAAa,WAAY;AAC3B,uBAASE,YAAW,MAAM;AACxB,oBAAI,QAAQ;AAEZ,gCAAgB,MAAMA,WAAU;AAEhC,qBAAK,SAAS;AACd,qBAAK,OAAO;AACZ,qBAAK,OAAO;AAEZ,oBAAI,QAAQ,MAAM;AAChB,uBAAK,QAAQ,SAAU,GAAG;AACxB,2BAAO,MAAM,KAAK,CAAC;AAAA,kBACrB,CAAC;AAAA,gBACH;AAAA,cACF;AAdS,qBAAAA,aAAA;AAgBT,2BAAaA,aAAY,CAAC;AAAA,gBACxB,KAAK;AAAA,gBACL,OAAO,gCAAS,OAAO;AACrB,yBAAO,KAAK;AAAA,gBACd,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,aAAa,KAAK,WAAW;AAC3C,yBAAO,IAAI,UAAU,MAAM,SAAS,GAAG,GAAG,WAAW,IAAI;AAAA,gBAC3D,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,YAAY,KAAK,WAAW;AAC1C,yBAAO,IAAI,WAAW,SAAS,GAAG,GAAG,UAAU,MAAM,IAAI;AAAA,gBAC3D,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,iBAAiB,SAAS,WAAW;AACnD,yBAAO,IAAI,UAAU,MAAM,SAAS,WAAW,IAAI;AAAA,gBACrD,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,gBAAgB,SAAS,WAAW;AAClD,yBAAO,IAAI,WAAW,SAAS,UAAU,MAAM,IAAI;AAAA,gBACrD,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,KAAK,KAAK;AACxB,yBAAO,IAAI,KAAK,MAAM,SAAS,GAAG,GAAG,MAAM,IAAI;AAAA,gBACjD,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,QAAQ,KAAK;AAC3B,yBAAO,IAAI,MAAM,SAAS,GAAG,GAAG,KAAK,MAAM,IAAI;AAAA,gBACjD,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,OAAO,MAAM;AAC3B,yBAAO,QAAQ,MAAM,IAAI;AAAA,gBAC3B,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,MAAM;AACpB,yBAAO,QAAQ,KAAK,MAAM,IAAI,EAAE;AAAA,gBAClC,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,UAAU;AACxB,yBAAO,QAAQ,KAAK,MAAM,IAAI;AAAA,gBAChC,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,QAAQ;AACtB,yBAAO,QAAQ,KAAK,MAAM,IAAI,EAAE;AAAA,gBAClC,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,YAAY;AAC1B,yBAAO,QAAQ,KAAK,MAAM,IAAI;AAAA,gBAChC,GAFO;AAAA,cAGT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,cAAc,OAAO;AACnC,sBAAI,SAAS,KAAK,OAAO,GAAG;AAC1B,wBAAI,IAAI;AACR,wBAAI,UAAU,KAAK;AACnB,2BAAO,IAAI,OAAO;AAChB,gCAAU,QAAQ;AAClB;AAAA,oBACF;AACA,2BAAO,QAAQ;AAAA,kBACjB;AAAA,gBACF,GAVO;AAAA,cAWT,GAAG;AAAA,gBACD,KAAK;AAAA,gBACL,OAAO,gCAAS,cAAc,OAAO,OAAO;AAC1C,sBAAI,SAAS,KAAK,OAAO,GAAG;AAC1B,wBAAI,IAAI;AACR,wBAAI,UAAU,KAAK;AACnB,2BAAO,IAAI,OAAO;AAChB,gCAAU,QAAQ;AAClB;AAAA,oBACF;AACA,4BAAQ,QAAQ;AAAA,kBAClB;AAAA,gBACF,GAVO;AAAA,cAWT,CAAC,CAAC;AAEF,qBAAOA;AAAA,YACT,EAAE;AAEF,YAAAV,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAMA,qBAASG,OAAM,GAAG,GAAG,GAAG;AACtB,mBAAK,IAAI;AACT,mBAAK,IAAI;AACT,kBAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM;AACvC,qBAAK,IAAI;AACT,qBAAK,IAAI;AAAA,cACX,WAAW,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,KAAK,MAAM;AACpE,qBAAK,IAAI;AACT,qBAAK,IAAI;AAAA,cACX,WAAW,EAAE,YAAY,QAAQ,WAAW,KAAK,QAAQ,KAAK,MAAM;AAClE,oBAAI;AACJ,qBAAK,IAAI,EAAE;AACX,qBAAK,IAAI,EAAE;AAAA,cACb;AAAA,YACF;AAdS,mBAAAA,QAAA;AAgBT,YAAAA,OAAM,UAAU,OAAO,WAAY;AACjC,qBAAO,KAAK;AAAA,YACd;AAEA,YAAAA,OAAM,UAAU,OAAO,WAAY;AACjC,qBAAO,KAAK;AAAA,YACd;AAEA,YAAAA,OAAM,UAAU,cAAc,WAAY;AACxC,qBAAO,IAAIA,OAAM,KAAK,GAAG,KAAK,CAAC;AAAA,YACjC;AAEA,YAAAA,OAAM,UAAU,cAAc,SAAU,GAAG,GAAG,GAAG;AAC/C,kBAAI,EAAE,YAAY,QAAQ,WAAW,KAAK,QAAQ,KAAK,MAAM;AAC3D,oBAAI;AACJ,qBAAK,YAAY,EAAE,GAAG,EAAE,CAAC;AAAA,cAC3B,WAAW,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,KAAK,MAAM;AAEpE,oBAAI,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,GAAG;AACxC,uBAAK,KAAK,GAAG,CAAC;AAAA,gBAChB,OAAO;AACL,uBAAK,IAAI,KAAK,MAAM,IAAI,GAAG;AAC3B,uBAAK,IAAI,KAAK,MAAM,IAAI,GAAG;AAAA,gBAC7B;AAAA,cACF;AAAA,YACF;AAEA,YAAAA,OAAM,UAAU,OAAO,SAAU,GAAG,GAAG;AACrC,mBAAK,IAAI;AACT,mBAAK,IAAI;AAAA,YACX;AAEA,YAAAA,OAAM,UAAU,YAAY,SAAU,IAAI,IAAI;AAC5C,mBAAK,KAAK;AACV,mBAAK,KAAK;AAAA,YACZ;AAEA,YAAAA,OAAM,UAAU,SAAS,SAAU,KAAK;AACtC,kBAAI,IAAI,YAAY,QAAQ,SAAS;AACnC,oBAAI,KAAK;AACT,uBAAO,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG;AAAA,cACxC;AACA,qBAAO,QAAQ;AAAA,YACjB;AAEA,YAAAA,OAAM,UAAU,WAAW,WAAY;AACrC,qBAAO,IAAIA,OAAM,EAAE,YAAY,OAAO,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI;AAAA,YAC1E;AAEA,YAAAJ,QAAO,UAAUI;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASJ,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,WAAW,GAAG,GAAG,OAAO,QAAQ;AACvC,mBAAK,IAAI;AACT,mBAAK,IAAI;AACT,mBAAK,QAAQ;AACb,mBAAK,SAAS;AAEd,kBAAI,KAAK,QAAQ,KAAK,QAAQ,SAAS,QAAQ,UAAU,MAAM;AAC7D,qBAAK,IAAI;AACT,qBAAK,IAAI;AACT,qBAAK,QAAQ;AACb,qBAAK,SAAS;AAAA,cAChB;AAAA,YACF;AAZS;AAcT,uBAAW,UAAU,OAAO,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,uBAAW,UAAU,OAAO,SAAU,GAAG;AACvC,mBAAK,IAAI;AAAA,YACX;AAEA,uBAAW,UAAU,OAAO,WAAY;AACtC,qBAAO,KAAK;AAAA,YACd;AAEA,uBAAW,UAAU,OAAO,SAAU,GAAG;AACvC,mBAAK,IAAI;AAAA,YACX;AAEA,uBAAW,UAAU,WAAW,WAAY;AAC1C,qBAAO,KAAK;AAAA,YACd;AAEA,uBAAW,UAAU,WAAW,SAAU,OAAO;AAC/C,mBAAK,QAAQ;AAAA,YACf;AAEA,uBAAW,UAAU,YAAY,WAAY;AAC3C,qBAAO,KAAK;AAAA,YACd;AAEA,uBAAW,UAAU,YAAY,SAAU,QAAQ;AACjD,mBAAK,SAAS;AAAA,YAChB;AAEA,uBAAW,UAAU,WAAW,WAAY;AAC1C,qBAAO,KAAK,IAAI,KAAK;AAAA,YACvB;AAEA,uBAAW,UAAU,YAAY,WAAY;AAC3C,qBAAO,KAAK,IAAI,KAAK;AAAA,YACvB;AAEA,uBAAW,UAAU,aAAa,SAAU,GAAG;AAC7C,kBAAI,KAAK,SAAS,IAAI,EAAE,GAAG;AACzB,uBAAO;AAAA,cACT;AAEA,kBAAI,KAAK,UAAU,IAAI,EAAE,GAAG;AAC1B,uBAAO;AAAA,cACT;AAEA,kBAAI,EAAE,SAAS,IAAI,KAAK,GAAG;AACzB,uBAAO;AAAA,cACT;AAEA,kBAAI,EAAE,UAAU,IAAI,KAAK,GAAG;AAC1B,uBAAO;AAAA,cACT;AAEA,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,aAAa,WAAY;AAC5C,qBAAO,KAAK,IAAI,KAAK,QAAQ;AAAA,YAC/B;AAEA,uBAAW,UAAU,UAAU,WAAY;AACzC,qBAAO,KAAK,KAAK;AAAA,YACnB;AAEA,uBAAW,UAAU,UAAU,WAAY;AACzC,qBAAO,KAAK,KAAK,IAAI,KAAK;AAAA,YAC5B;AAEA,uBAAW,UAAU,aAAa,WAAY;AAC5C,qBAAO,KAAK,IAAI,KAAK,SAAS;AAAA,YAChC;AAEA,uBAAW,UAAU,UAAU,WAAY;AACzC,qBAAO,KAAK,KAAK;AAAA,YACnB;AAEA,uBAAW,UAAU,UAAU,WAAY;AACzC,qBAAO,KAAK,KAAK,IAAI,KAAK;AAAA,YAC5B;AAEA,uBAAW,UAAU,eAAe,WAAY;AAC9C,qBAAO,KAAK,QAAQ;AAAA,YACtB;AAEA,uBAAW,UAAU,gBAAgB,WAAY;AAC/C,qBAAO,KAAK,SAAS;AAAA,YACvB;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,qBAAO,OAAO;AAAA,YAAK,IAAI,SAAU,KAAK;AAAE,qBAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,YAAK;AAE3Q,qBAAS,oBAAoB;AAAA,YAAC;AAArB;AAET,8BAAkB,SAAS;AAE3B,8BAAkB,WAAW,SAAU,KAAK;AAC1C,kBAAI,kBAAkB,YAAY,GAAG,GAAG;AACtC,uBAAO;AAAA,cACT;AACA,kBAAI,IAAI,YAAY,MAAM;AACxB,uBAAO,IAAI;AAAA,cACb;AACA,kBAAI,WAAW,kBAAkB,UAAU;AAC3C,gCAAkB;AAClB,qBAAO,IAAI;AAAA,YACb;AAEA,8BAAkB,YAAY,SAAU,IAAI;AAC1C,kBAAI,MAAM,KAAM,MAAK,kBAAkB;AACvC,qBAAO,YAAY;AAAA,YACrB;AAEA,8BAAkB,cAAc,SAAU,KAAK;AAC7C,kBAAI,OAAO,OAAO,QAAQ,cAAc,cAAc,QAAQ,GAAG;AACjE,qBAAO,OAAO,QAAQ,QAAQ,YAAY,QAAQ;AAAA,YACpD;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,mBAAmB,KAAK;AAAE,kBAAI,MAAM,QAAQ,GAAG,GAAG;AAAE,yBAAS,IAAI,GAAG,OAAO,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAK;AAAE,uBAAK,CAAC,IAAI,IAAI,CAAC;AAAA,gBAAG;AAAE,uBAAO;AAAA,cAAM,OAAO;AAAE,uBAAO,MAAM,KAAK,GAAG;AAAA,cAAG;AAAA,YAAE;AAAzL;AAET,gBAAI,kBAAkB,oBAAoB,CAAC;AAC3C,gBAAI,gBAAgB,oBAAoB,CAAC;AACzC,gBAAI,QAAQ,oBAAoB,CAAC;AACjC,gBAAI,QAAQ,oBAAoB,CAAC;AACjC,gBAAI,SAAS,oBAAoB,CAAC;AAClC,gBAAI,SAAS,oBAAoB,CAAC;AAClC,gBAAI,YAAY,oBAAoB,EAAE;AACtC,gBAAI,UAAU,oBAAoB,EAAE;AAEpC,qBAASU,QAAO,aAAa;AAC3B,sBAAQ,KAAK,IAAI;AAGjB,mBAAK,gBAAgB,gBAAgB;AAErC,mBAAK,sBAAsB,gBAAgB;AAE3C,mBAAK,cAAc,gBAAgB;AAEnC,mBAAK,oBAAoB,gBAAgB;AAEzC,mBAAK,wBAAwB,gBAAgB;AAE7C,mBAAK,kBAAkB,gBAAgB;AAOvC,mBAAK,uBAAuB,gBAAgB;AAK5C,mBAAK,mBAAmB,oBAAI,IAAI;AAChC,mBAAK,eAAe,IAAI,cAAc,IAAI;AAC1C,mBAAK,mBAAmB;AACxB,mBAAK,cAAc;AACnB,mBAAK,cAAc;AAEnB,kBAAI,eAAe,MAAM;AACvB,qBAAK,cAAc;AAAA,cACrB;AAAA,YACF;AAnCS,mBAAAA,SAAA;AAqCT,YAAAA,QAAO,cAAc;AAErB,YAAAA,QAAO,YAAY,OAAO,OAAO,QAAQ,SAAS;AAElD,YAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,qBAAO,KAAK;AAAA,YACd;AAEA,YAAAA,QAAO,UAAU,cAAc,WAAY;AACzC,qBAAO,KAAK,aAAa,YAAY;AAAA,YACvC;AAEA,YAAAA,QAAO,UAAU,cAAc,WAAY;AACzC,qBAAO,KAAK,aAAa,YAAY;AAAA,YACvC;AAEA,YAAAA,QAAO,UAAU,gCAAgC,WAAY;AAC3D,qBAAO,KAAK,aAAa,8BAA8B;AAAA,YACzD;AAEA,YAAAA,QAAO,UAAU,kBAAkB,WAAY;AAC7C,kBAAI,KAAK,IAAI,cAAc,IAAI;AAC/B,mBAAK,eAAe;AACpB,qBAAO;AAAA,YACT;AAEA,YAAAA,QAAO,UAAU,WAAW,SAAU,QAAQ;AAC5C,qBAAO,IAAI,OAAO,MAAM,KAAK,cAAc,MAAM;AAAA,YACnD;AAEA,YAAAA,QAAO,UAAU,UAAU,SAAU,OAAO;AAC1C,qBAAO,IAAI,MAAM,KAAK,cAAc,KAAK;AAAA,YAC3C;AAEA,YAAAA,QAAO,UAAU,UAAU,SAAU,OAAO;AAC1C,qBAAO,IAAI,MAAM,MAAM,MAAM,KAAK;AAAA,YACpC;AAEA,YAAAA,QAAO,UAAU,qBAAqB,WAAY;AAChD,qBAAO,KAAK,aAAa,QAAQ,KAAK,QAAQ,KAAK,aAAa,QAAQ,EAAE,SAAS,EAAE,UAAU,KAAK,KAAK,aAAa,oBAAoB;AAAA,YAC5I;AAEA,YAAAA,QAAO,UAAU,YAAY,WAAY;AACvC,mBAAK,mBAAmB;AAExB,kBAAI,KAAK,iBAAiB;AACxB,qBAAK,gBAAgB;AAAA,cACvB;AAEA,mBAAK,eAAe;AACpB,kBAAI;AAEJ,kBAAI,KAAK,mBAAmB,GAAG;AAC7B,sCAAsB;AAAA,cACxB,OAAO;AACL,sCAAsB,KAAK,OAAO;AAAA,cACpC;AAEA,kBAAI,gBAAgB,YAAY,UAAU;AAGxC,uBAAO;AAAA,cACT;AAEA,kBAAI,qBAAqB;AACvB,oBAAI,CAAC,KAAK,aAAa;AACrB,uBAAK,aAAa;AAAA,gBACpB;AAAA,cACF;AAEA,kBAAI,KAAK,kBAAkB;AACzB,qBAAK,iBAAiB;AAAA,cACxB;AAEA,mBAAK,mBAAmB;AAExB,qBAAO;AAAA,YACT;AAKA,YAAAA,QAAO,UAAU,eAAe,WAAY;AAG1C,kBAAI,CAAC,KAAK,aAAa;AACrB,qBAAK,UAAU;AAAA,cACjB;AACA,mBAAK,OAAO;AAAA,YACd;AAMA,YAAAA,QAAO,UAAU,UAAU,WAAY;AAErC,kBAAI,KAAK,qBAAqB;AAC5B,qBAAK,+BAA+B;AAGpC,qBAAK,aAAa,cAAc;AAAA,cAClC;AAIA,kBAAI,CAAC,KAAK,aAAa;AAErB,oBAAI;AACJ,oBAAI,WAAW,KAAK,aAAa,YAAY;AAC7C,yBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,yBAAO,SAAS,CAAC;AAAA,gBAEnB;AAGA,oBAAI;AACJ,oBAAIR,SAAQ,KAAK,aAAa,QAAQ,EAAE,SAAS;AACjD,yBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,yBAAOA,OAAM,CAAC;AAAA,gBAEhB;AAGA,qBAAK,OAAO,KAAK,aAAa,QAAQ,CAAC;AAAA,cACzC;AAAA,YACF;AAEA,YAAAQ,QAAO,UAAU,SAAS,SAAU,KAAK;AACvC,kBAAI,OAAO,MAAM;AACf,qBAAK,QAAQ;AAAA,cACf,WAAW,eAAe,OAAO;AAC/B,oBAAI,OAAO;AACX,oBAAI,KAAK,SAAS,KAAK,MAAM;AAE3B,sBAAIR,SAAQ,KAAK,SAAS,EAAE,SAAS;AACrC,2BAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,2BAAOA,OAAM,CAAC,CAAC;AAAA,kBACjB;AAAA,gBACF;AAKA,oBAAI,KAAK,gBAAgB,MAAM;AAE7B,sBAAI,QAAQ,KAAK;AAGjB,wBAAM,OAAO,IAAI;AAAA,gBACnB;AAAA,cACF,WAAW,eAAe,OAAO;AAC/B,oBAAI,OAAO;AAKX,oBAAI,KAAK,gBAAgB,MAAM;AAE7B,sBAAI,QAAQ,KAAK;AAGjB,wBAAM,OAAO,IAAI;AAAA,gBACnB;AAAA,cACF,WAAW,eAAe,QAAQ;AAChC,oBAAI,QAAQ;AAKZ,oBAAI,MAAM,gBAAgB,MAAM;AAE9B,sBAAI,SAAS,MAAM;AAGnB,yBAAO,OAAO,KAAK;AAAA,gBACrB;AAAA,cACF;AAAA,YACF;AAMA,YAAAQ,QAAO,UAAU,iBAAiB,WAAY;AAC5C,kBAAI,CAAC,KAAK,aAAa;AACrB,qBAAK,gBAAgB,gBAAgB;AACrC,qBAAK,wBAAwB,gBAAgB;AAC7C,qBAAK,kBAAkB,gBAAgB;AACvC,qBAAK,oBAAoB,gBAAgB;AACzC,qBAAK,cAAc,gBAAgB;AACnC,qBAAK,sBAAsB,gBAAgB;AAC3C,qBAAK,uBAAuB,gBAAgB;AAAA,cAC9C;AAEA,kBAAI,KAAK,uBAAuB;AAC9B,qBAAK,oBAAoB;AAAA,cAC3B;AAAA,YACF;AAEA,YAAAA,QAAO,UAAU,YAAY,SAAU,YAAY;AACjD,kBAAI,cAAc,QAAW;AAC3B,qBAAK,UAAU,IAAI,OAAO,GAAG,CAAC,CAAC;AAAA,cACjC,OAAO;AAML,oBAAI,QAAQ,IAAI,UAAU;AAC1B,oBAAI,UAAU,KAAK,aAAa,QAAQ,EAAE,cAAc;AAExD,oBAAI,WAAW,MAAM;AACnB,wBAAM,aAAa,WAAW,CAAC;AAC/B,wBAAM,aAAa,WAAW,CAAC;AAE/B,wBAAM,cAAc,QAAQ,CAAC;AAC7B,wBAAM,cAAc,QAAQ,CAAC;AAE7B,sBAAIR,SAAQ,KAAK,YAAY;AAC7B,sBAAI;AAEJ,2BAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,2BAAOA,OAAM,CAAC;AACd,yBAAK,UAAU,KAAK;AAAA,kBACtB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,YAAAQ,QAAO,UAAU,wBAAwB,SAAU,OAAO;AAExD,kBAAI,SAAS,QAAW;AAEtB,qBAAK,sBAAsB,KAAK,gBAAgB,EAAE,QAAQ,CAAC;AAC3D,qBAAK,gBAAgB,EAAE,QAAQ,EAAE,aAAa,IAAI;AAAA,cACpD,OAAO;AACL,oBAAI;AACJ,oBAAI;AAEJ,oBAAIR,SAAQ,MAAM,SAAS;AAC3B,yBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,0BAAQA,OAAM,CAAC;AACf,+BAAa,MAAM,SAAS;AAE5B,sBAAI,cAAc,MAAM;AACtB,0BAAM,QAAQ;AAAA,kBAChB,WAAW,WAAW,SAAS,EAAE,UAAU,GAAG;AAC5C,0BAAM,QAAQ;AAAA,kBAChB,OAAO;AACL,yBAAK,sBAAsB,UAAU;AACrC,0BAAM,aAAa;AAAA,kBACrB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAQA,YAAAQ,QAAO,UAAU,gBAAgB,WAAY;AAC3C,kBAAI,aAAa,CAAC;AAClB,kBAAI,WAAW;AAIf,kBAAI,WAAW,KAAK,aAAa,QAAQ,EAAE,SAAS;AAGpD,kBAAI,SAAS;AAEb,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,SAAS,CAAC,EAAE,SAAS,KAAK,MAAM;AAClC,2BAAS;AAAA,gBACX;AAAA,cACF;AAGA,kBAAI,CAAC,QAAQ;AACX,uBAAO;AAAA,cACT;AAIA,kBAAI,UAAU,oBAAI,IAAI;AACtB,kBAAI,cAAc,CAAC;AACnB,kBAAI,UAAU,oBAAI,IAAI;AACtB,kBAAI,mBAAmB,CAAC;AAExB,iCAAmB,iBAAiB,OAAO,QAAQ;AAMnD,qBAAO,iBAAiB,SAAS,KAAK,UAAU;AAC9C,4BAAY,KAAK,iBAAiB,CAAC,CAAC;AAIpC,uBAAO,YAAY,SAAS,KAAK,UAAU;AAEzC,sBAAI,cAAc,YAAY,CAAC;AAC/B,8BAAY,OAAO,GAAG,CAAC;AACvB,0BAAQ,IAAI,WAAW;AAGvB,sBAAI,gBAAgB,YAAY,SAAS;AAEzC,2BAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,wBAAI,kBAAkB,cAAc,CAAC,EAAE,YAAY,WAAW;AAG9D,wBAAI,QAAQ,IAAI,WAAW,KAAK,iBAAiB;AAE/C,0BAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,oCAAY,KAAK,eAAe;AAChC,gCAAQ,IAAI,iBAAiB,WAAW;AAAA,sBAC1C,OAKK;AACD,mCAAW;AACX;AAAA,sBACF;AAAA,oBACJ;AAAA,kBACF;AAAA,gBACF;AAIA,oBAAI,CAAC,UAAU;AACb,+BAAa,CAAC;AAAA,gBAChB,OAIK;AACD,sBAAI,OAAO,CAAC,EAAE,OAAO,mBAAmB,OAAO,CAAC;AAChD,6BAAW,KAAK,IAAI;AAGpB,2BAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,wBAAI,QAAQ,KAAK,CAAC;AAClB,wBAAI,QAAQ,iBAAiB,QAAQ,KAAK;AAC1C,wBAAI,QAAQ,IAAI;AACd,uCAAiB,OAAO,OAAO,CAAC;AAAA,oBAClC;AAAA,kBACF;AACA,4BAAU,oBAAI,IAAI;AAClB,4BAAU,oBAAI,IAAI;AAAA,gBACpB;AAAA,cACJ;AAEA,qBAAO;AAAA,YACT;AAOA,YAAAA,QAAO,UAAU,gCAAgC,SAAU,MAAM;AAC/D,kBAAI,aAAa,CAAC;AAClB,kBAAI,OAAO,KAAK;AAEhB,kBAAI,QAAQ,KAAK,aAAa,yBAAyB,KAAK,QAAQ,KAAK,MAAM;AAE/E,uBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAE/C,oBAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,0BAAU,QAAQ,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,UAAU,GAAG,CAAC,CAAC;AAEtD,sBAAM,IAAI,SAAS;AAGnB,oBAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,qBAAK,aAAa,IAAI,WAAW,MAAM,SAAS;AAEhD,2BAAW,IAAI,SAAS;AACxB,uBAAO;AAAA,cACT;AAEA,kBAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,mBAAK,aAAa,IAAI,WAAW,MAAM,KAAK,MAAM;AAElD,mBAAK,iBAAiB,IAAI,MAAM,UAAU;AAG1C,kBAAI,KAAK,aAAa,GAAG;AACvB,qBAAK,aAAa,OAAO,IAAI;AAAA,cAC/B,OAEK;AACD,sBAAM,OAAO,IAAI;AAAA,cACnB;AAEF,qBAAO;AAAA,YACT;AAMA,YAAAA,QAAO,UAAU,iCAAiC,WAAY;AAC5D,kBAAI,QAAQ,CAAC;AACb,sBAAQ,MAAM,OAAO,KAAK,aAAa,YAAY,CAAC;AACpD,sBAAQ,CAAC,EAAE,OAAO,mBAAmB,KAAK,iBAAiB,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;AAEhF,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAI,QAAQ,MAAM,CAAC;AAEnB,oBAAI,MAAM,WAAW,SAAS,GAAG;AAC/B,sBAAI,OAAO,KAAK,iBAAiB,IAAI,KAAK;AAE1C,2BAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,wBAAI,YAAY,KAAK,CAAC;AACtB,wBAAI,IAAI,IAAI,OAAO,UAAU,WAAW,GAAG,UAAU,WAAW,CAAC;AAGjE,wBAAI,MAAM,MAAM,WAAW,IAAI,CAAC;AAChC,wBAAI,IAAI,EAAE;AACV,wBAAI,IAAI,EAAE;AAIV,8BAAU,SAAS,EAAE,OAAO,SAAS;AAAA,kBACvC;AAGA,uBAAK,aAAa,IAAI,OAAO,MAAM,QAAQ,MAAM,MAAM;AAAA,gBACzD;AAAA,cACF;AAAA,YACF;AAEA,YAAAA,QAAO,YAAY,SAAU,aAAa,cAAc,QAAQ,QAAQ;AACtE,kBAAI,UAAU,UAAa,UAAU,QAAW;AAC9C,oBAAI,QAAQ;AAEZ,oBAAI,eAAe,IAAI;AACrB,sBAAI,WAAW,eAAe;AAC9B,4BAAU,eAAe,YAAY,MAAM,KAAK;AAAA,gBAClD,OAAO;AACL,sBAAI,WAAW,eAAe;AAC9B,4BAAU,WAAW,gBAAgB,MAAM,cAAc;AAAA,gBAC3D;AAEA,uBAAO;AAAA,cACT,OAAO;AACL,oBAAI,GAAG;AAEP,oBAAI,eAAe,IAAI;AACrB,sBAAI,IAAM,eAAe;AACzB,sBAAI,eAAe;AAAA,gBACrB,OAAO;AACL,sBAAI,IAAM,eAAe;AACzB,sBAAI,KAAK;AAAA,gBACX;AAEA,uBAAO,IAAI,cAAc;AAAA,cAC3B;AAAA,YACF;AAMA,YAAAA,QAAO,mBAAmB,SAAUR,QAAO;AACzC,kBAAI,OAAO,CAAC;AACZ,qBAAO,KAAK,OAAOA,MAAK;AAExB,kBAAI,eAAe,CAAC;AACpB,kBAAI,mBAAmB,oBAAI,IAAI;AAC/B,kBAAI,cAAc;AAClB,kBAAI,aAAa;AAEjB,kBAAI,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG;AACxC,8BAAc;AACd,6BAAa,KAAK,CAAC;AAAA,cACrB;AAEA,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,oBAAI,OAAO,KAAK,CAAC;AACjB,oBAAI,SAAS,KAAK,iBAAiB,EAAE;AACrC,iCAAiB,IAAI,MAAM,KAAK,iBAAiB,EAAE,IAAI;AAEvD,oBAAI,UAAU,GAAG;AACf,+BAAa,KAAK,IAAI;AAAA,gBACxB;AAAA,cACF;AAEA,kBAAI,WAAW,CAAC;AAChB,yBAAW,SAAS,OAAO,YAAY;AAEvC,qBAAO,CAAC,aAAa;AACnB,oBAAI,YAAY,CAAC;AACjB,4BAAY,UAAU,OAAO,QAAQ;AACrC,2BAAW,CAAC;AAEZ,yBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,sBAAI,OAAO,KAAK,CAAC;AAEjB,sBAAI,QAAQ,KAAK,QAAQ,IAAI;AAC7B,sBAAI,SAAS,GAAG;AACd,yBAAK,OAAO,OAAO,CAAC;AAAA,kBACtB;AAEA,sBAAI,aAAa,KAAK,iBAAiB;AAEvC,6BAAW,QAAQ,SAAU,WAAW;AACtC,wBAAI,aAAa,QAAQ,SAAS,IAAI,GAAG;AACvC,0BAAI,cAAc,iBAAiB,IAAI,SAAS;AAChD,0BAAI,YAAY,cAAc;AAE9B,0BAAI,aAAa,GAAG;AAClB,iCAAS,KAAK,SAAS;AAAA,sBACzB;AAEA,uCAAiB,IAAI,WAAW,SAAS;AAAA,oBAC3C;AAAA,kBACF,CAAC;AAAA,gBACH;AAEA,+BAAe,aAAa,OAAO,QAAQ;AAE3C,oBAAI,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG;AACxC,gCAAc;AACd,+BAAa,KAAK,CAAC;AAAA,gBACrB;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAMA,YAAAQ,QAAO,UAAU,kBAAkB,SAAU,IAAI;AAC/C,mBAAK,eAAe;AAAA,YACtB;AAEA,YAAAX,QAAO,UAAUW;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASX,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,aAAa;AAAA,YAAC;AAAd;AAET,uBAAW,OAAO;AAClB,uBAAW,IAAI;AAEf,uBAAW,aAAa,WAAY;AAClC,yBAAW,IAAI,KAAK,IAAI,WAAW,MAAM,IAAI;AAC7C,qBAAO,WAAW,IAAI,KAAK,MAAM,WAAW,CAAC;AAAA,YAC/C;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,SAAS,oBAAoB,CAAC;AAElC,qBAAS,UAAU,GAAG,GAAG;AACvB,mBAAK,aAAa;AAClB,mBAAK,aAAa;AAClB,mBAAK,cAAc;AACnB,mBAAK,cAAc;AACnB,mBAAK,aAAa;AAClB,mBAAK,aAAa;AAClB,mBAAK,cAAc;AACnB,mBAAK,cAAc;AAAA,YACrB;AATS;AAWT,sBAAU,UAAU,eAAe,WAAY;AAC7C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,eAAe,SAAU,KAAK;AAChD,mBAAK,aAAa;AAAA,YACpB;AAEA,sBAAU,UAAU,eAAe,WAAY;AAC7C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,eAAe,SAAU,KAAK;AAChD,mBAAK,aAAa;AAAA,YACpB;AAEA,sBAAU,UAAU,eAAe,WAAY;AAC7C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,eAAe,SAAU,KAAK;AAChD,mBAAK,aAAa;AAAA,YACpB;AAEA,sBAAU,UAAU,eAAe,WAAY;AAC7C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,eAAe,SAAU,KAAK;AAChD,mBAAK,aAAa;AAAA,YACpB;AAIA,sBAAU,UAAU,gBAAgB,WAAY;AAC9C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,gBAAgB,SAAU,KAAK;AACjD,mBAAK,cAAc;AAAA,YACrB;AAEA,sBAAU,UAAU,gBAAgB,WAAY;AAC9C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,gBAAgB,SAAU,KAAK;AACjD,mBAAK,cAAc;AAAA,YACrB;AAEA,sBAAU,UAAU,gBAAgB,WAAY;AAC9C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,gBAAgB,SAAU,KAAK;AACjD,mBAAK,cAAc;AAAA,YACrB;AAEA,sBAAU,UAAU,gBAAgB,WAAY;AAC9C,qBAAO,KAAK;AAAA,YACd;AAEA,sBAAU,UAAU,gBAAgB,SAAU,KAAK;AACjD,mBAAK,cAAc;AAAA,YACrB;AAEA,sBAAU,UAAU,aAAa,SAAU,GAAG;AAC5C,kBAAI,UAAU;AACd,kBAAI,YAAY,KAAK;AACrB,kBAAI,aAAa,GAAK;AACpB,0BAAU,KAAK,eAAe,IAAI,KAAK,cAAc,KAAK,cAAc;AAAA,cAC1E;AAEA,qBAAO;AAAA,YACT;AAEA,sBAAU,UAAU,aAAa,SAAU,GAAG;AAC5C,kBAAI,UAAU;AACd,kBAAI,YAAY,KAAK;AACrB,kBAAI,aAAa,GAAK;AACpB,0BAAU,KAAK,eAAe,IAAI,KAAK,cAAc,KAAK,cAAc;AAAA,cAC1E;AAEA,qBAAO;AAAA,YACT;AAEA,sBAAU,UAAU,oBAAoB,SAAU,GAAG;AACnD,kBAAI,SAAS;AACb,kBAAI,aAAa,KAAK;AACtB,kBAAI,cAAc,GAAK;AACrB,yBAAS,KAAK,cAAc,IAAI,KAAK,eAAe,KAAK,aAAa;AAAA,cACxE;AAEA,qBAAO;AAAA,YACT;AAEA,sBAAU,UAAU,oBAAoB,SAAU,GAAG;AACnD,kBAAI,SAAS;AACb,kBAAI,aAAa,KAAK;AACtB,kBAAI,cAAc,GAAK;AACrB,yBAAS,KAAK,cAAc,IAAI,KAAK,eAAe,KAAK,aAAa;AAAA,cACxE;AACA,qBAAO;AAAA,YACT;AAEA,sBAAU,UAAU,wBAAwB,SAAU,SAAS;AAC7D,kBAAI,WAAW,IAAI,OAAO,KAAK,kBAAkB,QAAQ,CAAC,GAAG,KAAK,kBAAkB,QAAQ,CAAC,CAAC;AAC9F,qBAAO;AAAA,YACT;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,mBAAmB,KAAK;AAAE,kBAAI,MAAM,QAAQ,GAAG,GAAG;AAAE,yBAAS,IAAI,GAAG,OAAO,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAK;AAAE,uBAAK,CAAC,IAAI,IAAI,CAAC;AAAA,gBAAG;AAAE,uBAAO;AAAA,cAAM,OAAO;AAAE,uBAAO,MAAM,KAAK,GAAG;AAAA,cAAG;AAAA,YAAE;AAAzL;AAET,gBAAIU,UAAS,oBAAoB,EAAE;AACnC,gBAAI,oBAAoB,oBAAoB,CAAC;AAC7C,gBAAI,kBAAkB,oBAAoB,CAAC;AAC3C,gBAAI,YAAY,oBAAoB,CAAC;AACrC,gBAAI,QAAQ,oBAAoB,CAAC;AAEjC,qBAAS,WAAW;AAClB,cAAAA,QAAO,KAAK,IAAI;AAEhB,mBAAK,qCAAqC,kBAAkB;AAC5D,mBAAK,kBAAkB,kBAAkB;AACzC,mBAAK,iBAAiB,kBAAkB;AACxC,mBAAK,oBAAoB,kBAAkB;AAC3C,mBAAK,kBAAkB,kBAAkB;AACzC,mBAAK,0BAA0B,kBAAkB;AACjD,mBAAK,qBAAqB,kBAAkB;AAC5C,mBAAK,6BAA6B,kBAAkB;AACpD,mBAAK,+BAA+B,IAAM,kBAAkB,sBAAsB;AAClF,mBAAK,gBAAgB,kBAAkB;AACvC,mBAAK,uBAAuB,kBAAkB;AAC9C,mBAAK,oBAAoB;AACzB,mBAAK,uBAAuB;AAC5B,mBAAK,gBAAgB,kBAAkB;AAAA,YACzC;AAjBS;AAmBT,qBAAS,YAAY,OAAO,OAAOA,QAAO,SAAS;AAEnD,qBAAS,QAAQA,SAAQ;AACvB,uBAAS,IAAI,IAAIA,QAAO,IAAI;AAAA,YAC9B;AAEA,qBAAS,UAAU,iBAAiB,WAAY;AAC9C,cAAAA,QAAO,UAAU,eAAe,KAAK,MAAM,SAAS;AAEpD,mBAAK,kBAAkB;AACvB,mBAAK,wBAAwB;AAE7B,mBAAK,mBAAmB,kBAAkB;AAE1C,mBAAK,OAAO,CAAC;AAAA,YACf;AAEA,qBAAS,UAAU,uBAAuB,WAAY;AACpD,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAEJ,kBAAI,WAAW,KAAK,gBAAgB,EAAE,YAAY;AAClD,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,uBAAO,SAAS,CAAC;AAEjB,qBAAK,cAAc,KAAK;AAExB,oBAAI,KAAK,cAAc;AACrB,2BAAS,KAAK,UAAU;AACxB,2BAAS,KAAK,UAAU;AAExB,sCAAoB,KAAK,eAAe,EAAE,iBAAiB;AAC3D,sCAAoB,KAAK,eAAe,EAAE,iBAAiB;AAE3D,sBAAI,KAAK,oCAAoC;AAC3C,yBAAK,eAAe,oBAAoB,oBAAoB,IAAI,gBAAgB;AAAA,kBAClF;AAEA,6BAAW,KAAK,OAAO,EAAE,sBAAsB;AAE/C,uBAAK,eAAe,kBAAkB,sBAAsB,kBAAkB,sCAAsC,OAAO,sBAAsB,IAAI,OAAO,sBAAsB,IAAI,IAAI;AAAA,gBAC5L;AAAA,cACF;AAAA,YACF;AAEA,qBAAS,UAAU,qBAAqB,WAAY;AAElD,kBAAI,IAAI,KAAK,YAAY,EAAE;AAC3B,kBAAI,KAAK,aAAa;AACpB,oBAAI,IAAI,kBAAkB,6BAA6B;AACrD,uBAAK,gBAAgB,KAAK,IAAI,KAAK,gBAAgB,kBAAkB,2BAA2B,KAAK,iBAAiB,IAAI,kBAAkB,gCAAgC,kBAAkB,8BAA8B,kBAAkB,+BAA+B,KAAK,iBAAiB,IAAI,kBAAkB,0BAA0B;AAAA,gBACrV;AACA,qBAAK,sBAAsB,kBAAkB;AAAA,cAC/C,OAAO;AACL,oBAAI,IAAI,kBAAkB,6BAA6B;AACrD,uBAAK,gBAAgB,KAAK,IAAI,kBAAkB,2BAA2B,KAAO,IAAI,kBAAkB,gCAAgC,kBAAkB,8BAA8B,kBAAkB,gCAAgC,IAAI,kBAAkB,0BAA0B;AAAA,gBAC5R,OAAO;AACL,uBAAK,gBAAgB;AAAA,gBACvB;AACA,qBAAK,uBAAuB,KAAK;AACjC,qBAAK,sBAAsB,kBAAkB;AAAA,cAC/C;AAEA,mBAAK,gBAAgB,KAAK,IAAI,KAAK,YAAY,EAAE,SAAS,GAAG,KAAK,aAAa;AAE/E,mBAAK,6BAA6B,KAAK,+BAA+B,KAAK,YAAY,EAAE;AAEzF,mBAAK,iBAAiB,KAAK,mBAAmB;AAAA,YAChD;AAEA,qBAAS,UAAU,mBAAmB,WAAY;AAChD,kBAAI,SAAS,KAAK,YAAY;AAC9B,kBAAI;AAEJ,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,uBAAO,OAAO,CAAC;AAEf,qBAAK,gBAAgB,MAAM,KAAK,WAAW;AAAA,cAC7C;AAAA,YACF;AAEA,qBAAS,UAAU,sBAAsB,WAAY;AACnD,kBAAI,oBAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC5F,kBAAI,+BAA+B,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEvG,kBAAI,GAAG;AACP,kBAAI,OAAO;AACX,kBAAI,SAAS,KAAK,YAAY;AAC9B,kBAAI;AAEJ,kBAAI,KAAK,kBAAkB;AACzB,oBAAI,KAAK,kBAAkB,kBAAkB,iCAAiC,KAAK,mBAAmB;AACpG,uBAAK,WAAW;AAAA,gBAClB;AAEA,mCAAmB,oBAAI,IAAI;AAG3B,qBAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,0BAAQ,OAAO,CAAC;AAChB,uBAAK,+BAA+B,OAAO,kBAAkB,mBAAmB,4BAA4B;AAC5G,mCAAiB,IAAI,KAAK;AAAA,gBAC5B;AAAA,cACF,OAAO;AACL,qBAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,0BAAQ,OAAO,CAAC;AAEhB,uBAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,4BAAQ,OAAO,CAAC;AAGhB,wBAAI,MAAM,SAAS,KAAK,MAAM,SAAS,GAAG;AACxC;AAAA,oBACF;AAEA,yBAAK,mBAAmB,OAAO,KAAK;AAAA,kBACtC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,qBAAS,UAAU,0BAA0B,WAAY;AACvD,kBAAI;AACJ,kBAAI,SAAS,KAAK,8BAA8B;AAEhD,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,uBAAO,OAAO,CAAC;AACf,qBAAK,uBAAuB,IAAI;AAAA,cAClC;AAAA,YACF;AAEA,qBAAS,UAAU,YAAY,WAAY;AACzC,kBAAI,SAAS,KAAK,YAAY;AAC9B,kBAAI;AAEJ,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,uBAAO,OAAO,CAAC;AACf,qBAAK,KAAK;AAAA,cACZ;AAAA,YACF;AAEA,qBAAS,UAAU,kBAAkB,SAAU,MAAM,aAAa;AAChE,kBAAI,aAAa,KAAK,UAAU;AAChC,kBAAI,aAAa,KAAK,UAAU;AAEhC,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAGJ,kBAAI,KAAK,wBAAwB,WAAW,SAAS,KAAK,QAAQ,WAAW,SAAS,KAAK,MAAM;AAC/F,qBAAK,mBAAmB;AAAA,cAC1B,OAAO;AACL,qBAAK,aAAa;AAElB,oBAAI,KAAK,6BAA6B;AACpC;AAAA,gBACF;AAAA,cACF;AAEA,uBAAS,KAAK,UAAU;AAExB,kBAAI,UAAU,EAAG;AAGjB,4BAAc,KAAK,kBAAkB,SAAS;AAG9C,6BAAe,eAAe,KAAK,UAAU;AAC7C,6BAAe,eAAe,KAAK,UAAU;AAG7C,yBAAW,gBAAgB;AAC3B,yBAAW,gBAAgB;AAC3B,yBAAW,gBAAgB;AAC3B,yBAAW,gBAAgB;AAAA,YAC7B;AAEA,qBAAS,UAAU,qBAAqB,SAAU,OAAO,OAAO;AAC9D,kBAAI,QAAQ,MAAM,QAAQ;AAC1B,kBAAI,QAAQ,MAAM,QAAQ;AAC1B,kBAAI,gBAAgB,IAAI,MAAM,CAAC;AAC/B,kBAAI,aAAa,IAAI,MAAM,CAAC;AAC5B,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AAEJ,kBAAI,MAAM,WAAW,KAAK,GACxB;AAEE,0BAAU,qBAAqB,OAAO,OAAO,eAAe,kBAAkB,sBAAsB,CAAG;AAEvG,kCAAkB,IAAI,cAAc,CAAC;AACrC,kCAAkB,IAAI,cAAc,CAAC;AAErC,oBAAI,mBAAmB,MAAM,eAAe,MAAM,gBAAgB,MAAM,eAAe,MAAM;AAG7F,sBAAM,mBAAmB,mBAAmB;AAC5C,sBAAM,mBAAmB,mBAAmB;AAC5C,sBAAM,mBAAmB,mBAAmB;AAC5C,sBAAM,mBAAmB,mBAAmB;AAAA,cAC9C,OACA;AAGE,oBAAI,KAAK,wBAAwB,MAAM,SAAS,KAAK,QAAQ,MAAM,SAAS,KAAK,MAC/E;AACE,8BAAY,MAAM,WAAW,IAAI,MAAM,WAAW;AAClD,8BAAY,MAAM,WAAW,IAAI,MAAM,WAAW;AAAA,gBACpD,OACA;AACE,4BAAU,gBAAgB,OAAO,OAAO,UAAU;AAElD,8BAAY,WAAW,CAAC,IAAI,WAAW,CAAC;AACxC,8BAAY,WAAW,CAAC,IAAI,WAAW,CAAC;AAAA,gBAC1C;AAGF,oBAAI,KAAK,IAAI,SAAS,IAAI,kBAAkB,oBAAoB;AAC9D,8BAAY,MAAM,KAAK,SAAS,IAAI,kBAAkB;AAAA,gBACxD;AAEA,oBAAI,KAAK,IAAI,SAAS,IAAI,kBAAkB,oBAAoB;AAC9D,8BAAY,MAAM,KAAK,SAAS,IAAI,kBAAkB;AAAA,gBACxD;AAEA,kCAAkB,YAAY,YAAY,YAAY;AACtD,2BAAW,KAAK,KAAK,eAAe;AAEpC,iCAAiB,KAAK,oBAAoB,MAAM,eAAe,MAAM,eAAe;AAGpF,kCAAkB,iBAAiB,YAAY;AAC/C,kCAAkB,iBAAiB,YAAY;AAG/C,sBAAM,mBAAmB;AACzB,sBAAM,mBAAmB;AACzB,sBAAM,mBAAmB;AACzB,sBAAM,mBAAmB;AAAA,cAC3B;AAAA,YACJ;AAEA,qBAAS,UAAU,yBAAyB,SAAU,MAAM;AAC1D,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,kBAAI;AACJ,2BAAa,KAAK,SAAS;AAE3B,8BAAgB,WAAW,SAAS,IAAI,WAAW,QAAQ,KAAK;AAChE,8BAAgB,WAAW,OAAO,IAAI,WAAW,UAAU,KAAK;AAChE,0BAAY,KAAK,WAAW,IAAI;AAChC,0BAAY,KAAK,WAAW,IAAI;AAChC,6BAAe,KAAK,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI;AACvD,6BAAe,KAAK,IAAI,SAAS,IAAI,KAAK,UAAU,IAAI;AAExD,kBAAI,KAAK,SAAS,KAAK,KAAK,aAAa,QAAQ,GAC/C;AACE,gCAAgB,WAAW,iBAAiB,IAAI,KAAK;AAErD,oBAAI,eAAe,iBAAiB,eAAe,eAAe;AAChE,uBAAK,oBAAoB,CAAC,KAAK,kBAAkB;AACjD,uBAAK,oBAAoB,CAAC,KAAK,kBAAkB;AAAA,gBACnD;AAAA,cACF,OACA;AACE,gCAAgB,WAAW,iBAAiB,IAAI,KAAK;AAErD,oBAAI,eAAe,iBAAiB,eAAe,eAAe;AAChE,uBAAK,oBAAoB,CAAC,KAAK,kBAAkB,YAAY,KAAK;AAClE,uBAAK,oBAAoB,CAAC,KAAK,kBAAkB,YAAY,KAAK;AAAA,gBACpE;AAAA,cACF;AAAA,YACJ;AAEA,qBAAS,UAAU,cAAc,WAAY;AAC3C,kBAAI;AACJ,kBAAI,aAAa;AAEjB,kBAAI,KAAK,kBAAkB,KAAK,gBAAgB,GAAG;AACjD,6BAAa,KAAK,IAAI,KAAK,oBAAoB,KAAK,oBAAoB,IAAI;AAAA,cAC9E;AAEA,0BAAY,KAAK,oBAAoB,KAAK;AAE1C,mBAAK,uBAAuB,KAAK;AAEjC,qBAAO,aAAa;AAAA,YACtB;AAEA,qBAAS,UAAU,UAAU,WAAY;AACvC,kBAAI,KAAK,yBAAyB,CAAC,KAAK,aAAa;AACnD,oBAAI,KAAK,yBAAyB,KAAK,iBAAiB;AACtD,uBAAK,OAAO;AACZ,uBAAK,wBAAwB;AAAA,gBAC/B,OAAO;AACL,uBAAK;AAAA,gBACP;AAAA,cACF;AAAA,YACF;AAGA,qBAAS,UAAU,8BAA8B,WAAY;AAC3D,kBAAI;AACJ,kBAAI,WAAW,KAAK,aAAa,YAAY;AAE7C,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,uBAAO,SAAS,CAAC;AACjB,qBAAK,eAAe,KAAK,gBAAgB;AAAA,cAC3C;AAAA,YACF;AAMA,qBAAS,UAAU,WAAW,SAAU,OAAO;AAE7C,kBAAI,QAAQ;AACZ,kBAAI,QAAQ;AAEZ,sBAAQ,SAAS,KAAK,MAAM,MAAM,SAAS,IAAI,MAAM,QAAQ,KAAK,KAAK,cAAc,CAAC;AACtF,sBAAQ,SAAS,KAAK,MAAM,MAAM,UAAU,IAAI,MAAM,OAAO,KAAK,KAAK,cAAc,CAAC;AAEtF,kBAAI,OAAO,IAAI,MAAM,KAAK;AAE1B,uBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,qBAAK,CAAC,IAAI,IAAI,MAAM,KAAK;AAAA,cAC3B;AAEA,uBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,yBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,uBAAK,CAAC,EAAE,CAAC,IAAI,IAAI,MAAM;AAAA,gBACzB;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAEA,qBAAS,UAAU,gBAAgB,SAAU,GAAG,MAAM,KAAK;AAEzD,kBAAI,SAAS;AACb,kBAAI,UAAU;AACd,kBAAI,SAAS;AACb,kBAAI,UAAU;AAEd,uBAAS,SAAS,KAAK,OAAO,EAAE,QAAQ,EAAE,IAAI,QAAQ,KAAK,cAAc,CAAC;AAC1E,wBAAU,SAAS,KAAK,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,QAAQ,KAAK,cAAc,CAAC;AAC/F,uBAAS,SAAS,KAAK,OAAO,EAAE,QAAQ,EAAE,IAAI,OAAO,KAAK,cAAc,CAAC;AACzE,wBAAU,SAAS,KAAK,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,OAAO,KAAK,cAAc,CAAC;AAE/F,uBAAS,IAAI,QAAQ,KAAK,SAAS,KAAK;AACtC,yBAAS,IAAI,QAAQ,KAAK,SAAS,KAAK;AACtC,uBAAK,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;AACtB,oBAAE,mBAAmB,QAAQ,SAAS,QAAQ,OAAO;AAAA,gBACvD;AAAA,cACF;AAAA,YACF;AAEA,qBAAS,UAAU,aAAa,WAAY;AAC1C,kBAAI;AACJ,kBAAI;AACJ,kBAAI,SAAS,KAAK,YAAY;AAE9B,mBAAK,OAAO,KAAK,SAAS,KAAK,aAAa,QAAQ,CAAC;AAGrD,mBAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,wBAAQ,OAAO,CAAC;AAChB,qBAAK,cAAc,OAAO,KAAK,aAAa,QAAQ,EAAE,QAAQ,GAAG,KAAK,aAAa,QAAQ,EAAE,OAAO,CAAC;AAAA,cACvG;AAAA,YACF;AAEA,qBAAS,UAAU,iCAAiC,SAAU,OAAO,kBAAkB,mBAAmB,8BAA8B;AAEtI,kBAAI,KAAK,kBAAkB,kBAAkB,iCAAiC,KAAK,qBAAqB,8BAA8B;AACpI,oBAAI,cAAc,oBAAI,IAAI;AAC1B,sBAAM,cAAc,IAAI,MAAM;AAC9B,oBAAI;AACJ,oBAAI,OAAO,KAAK;AAEhB,yBAAS,IAAI,MAAM,SAAS,GAAG,IAAI,MAAM,UAAU,GAAG,KAAK;AACzD,2BAAS,IAAI,MAAM,SAAS,GAAG,IAAI,MAAM,UAAU,GAAG,KAAK;AACzD,wBAAI,EAAE,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,UAAU,KAAK,KAAK,CAAC,EAAE,SAAS;AAChE,+BAAS,IAAI,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK;AAC1C,gCAAQ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAIpB,4BAAI,MAAM,SAAS,KAAK,MAAM,SAAS,KAAK,SAAS,OAAO;AAC1D;AAAA,wBACF;AAIA,4BAAI,CAAC,iBAAiB,IAAI,KAAK,KAAK,CAAC,YAAY,IAAI,KAAK,GAAG;AAC3D,8BAAI,YAAY,KAAK,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,KAAK,MAAM,SAAS,IAAI,IAAI,MAAM,SAAS,IAAI;AAC/G,8BAAI,YAAY,KAAK,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,KAAK,MAAM,UAAU,IAAI,IAAI,MAAM,UAAU,IAAI;AAIjH,8BAAI,aAAa,KAAK,kBAAkB,aAAa,KAAK,gBAAgB;AAExE,wCAAY,IAAI,KAAK;AAAA,0BACvB;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAEA,sBAAM,cAAc,CAAC,EAAE,OAAO,mBAAmB,WAAW,CAAC;AAAA,cAC/D;AACA,mBAAK,IAAI,GAAG,IAAI,MAAM,YAAY,QAAQ,KAAK;AAC7C,qBAAK,mBAAmB,OAAO,MAAM,YAAY,CAAC,CAAC;AAAA,cACrD;AAAA,YACF;AAEA,qBAAS,UAAU,qBAAqB,WAAY;AAClD,qBAAO;AAAA,YACT;AAEA,YAAAX,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,QAAQ,oBAAoB,CAAC;AACjC,gBAAI,oBAAoB,oBAAoB,CAAC;AAE7C,qBAAS,aAAa,QAAQ,QAAQ,OAAO;AAC3C,oBAAM,KAAK,MAAM,QAAQ,QAAQ,KAAK;AACtC,mBAAK,cAAc,kBAAkB;AAAA,YACvC;AAHS;AAKT,yBAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAEtD,qBAAS,QAAQ,OAAO;AACtB,2BAAa,IAAI,IAAI,MAAM,IAAI;AAAA,YACjC;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,QAAQ,oBAAoB,CAAC;AAEjC,qBAAS,aAAa,IAAI,KAAK,MAAM,OAAO;AAE1C,oBAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAErC,mBAAK,eAAe;AACpB,mBAAK,eAAe;AACpB,mBAAK,kBAAkB;AACvB,mBAAK,kBAAkB;AACvB,mBAAK,oBAAoB;AACzB,mBAAK,oBAAoB;AAEzB,mBAAK,gBAAgB;AACrB,mBAAK,gBAAgB;AAGrB,mBAAK,SAAS;AACd,mBAAK,UAAU;AACf,mBAAK,SAAS;AACd,mBAAK,UAAU;AAGf,mBAAK,cAAc,CAAC;AAAA,YACtB;AAtBS;AAwBT,yBAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAEtD,qBAAS,QAAQ,OAAO;AACtB,2BAAa,IAAI,IAAI,MAAM,IAAI;AAAA,YACjC;AAEA,yBAAa,UAAU,qBAAqB,SAAU,SAAS,UAAU,SAAS,UAAU;AAC1F,mBAAK,SAAS;AACd,mBAAK,UAAU;AACf,mBAAK,SAAS;AACd,mBAAK,UAAU;AAAA,YACjB;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAASW,YAAW,OAAO,QAAQ;AACjC,mBAAK,QAAQ;AACb,mBAAK,SAAS;AACd,kBAAI,UAAU,QAAQ,WAAW,MAAM;AACrC,qBAAK,SAAS;AACd,qBAAK,QAAQ;AAAA,cACf;AAAA,YACF;AAPS,mBAAAA,aAAA;AAST,YAAAA,YAAW,UAAU,WAAW,WAAY;AAC1C,qBAAO,KAAK;AAAA,YACd;AAEA,YAAAA,YAAW,UAAU,WAAW,SAAU,OAAO;AAC/C,mBAAK,QAAQ;AAAA,YACf;AAEA,YAAAA,YAAW,UAAU,YAAY,WAAY;AAC3C,qBAAO,KAAK;AAAA,YACd;AAEA,YAAAA,YAAW,UAAU,YAAY,SAAU,QAAQ;AACjD,mBAAK,SAAS;AAAA,YAChB;AAEA,YAAAZ,QAAO,UAAUY;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASZ,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,oBAAoB,oBAAoB,EAAE;AAE9C,qBAAS,UAAU;AACjB,mBAAK,MAAM,CAAC;AACZ,mBAAK,OAAO,CAAC;AAAA,YACf;AAHS;AAKT,oBAAQ,UAAU,MAAM,SAAU,KAAK,OAAO;AAC5C,kBAAI,QAAQ,kBAAkB,SAAS,GAAG;AAC1C,kBAAI,CAAC,KAAK,SAAS,KAAK,GAAG;AACzB,qBAAK,IAAI,KAAK,IAAI;AAClB,qBAAK,KAAK,KAAK,GAAG;AAAA,cACpB;AAAA,YACF;AAEA,oBAAQ,UAAU,WAAW,SAAU,KAAK;AAC1C,kBAAI,QAAQ,kBAAkB,SAAS,GAAG;AAC1C,qBAAO,KAAK,IAAI,GAAG,KAAK;AAAA,YAC1B;AAEA,oBAAQ,UAAU,MAAM,SAAU,KAAK;AACrC,kBAAI,QAAQ,kBAAkB,SAAS,GAAG;AAC1C,qBAAO,KAAK,IAAI,KAAK;AAAA,YACvB;AAEA,oBAAQ,UAAU,SAAS,WAAY;AACrC,qBAAO,KAAK;AAAA,YACd;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,oBAAoB,oBAAoB,EAAE;AAE9C,qBAAS,UAAU;AACjB,mBAAK,MAAM,CAAC;AAAA,YACd;AAFS;AAGT;AAEA,oBAAQ,UAAU,MAAM,SAAU,KAAK;AACrC,kBAAI,QAAQ,kBAAkB,SAAS,GAAG;AAC1C,kBAAI,CAAC,KAAK,SAAS,KAAK,EAAG,MAAK,IAAI,KAAK,IAAI;AAAA,YAC/C;AAEA,oBAAQ,UAAU,SAAS,SAAU,KAAK;AACxC,qBAAO,KAAK,IAAI,kBAAkB,SAAS,GAAG,CAAC;AAAA,YACjD;AAEA,oBAAQ,UAAU,QAAQ,WAAY;AACpC,mBAAK,MAAM,CAAC;AAAA,YACd;AAEA,oBAAQ,UAAU,WAAW,SAAU,KAAK;AAC1C,qBAAO,KAAK,IAAI,kBAAkB,SAAS,GAAG,CAAC,KAAK;AAAA,YACtD;AAEA,oBAAQ,UAAU,UAAU,WAAY;AACtC,qBAAO,KAAK,KAAK,MAAM;AAAA,YACzB;AAEA,oBAAQ,UAAU,OAAO,WAAY;AACnC,qBAAO,OAAO,KAAK,KAAK,GAAG,EAAE;AAAA,YAC/B;AAGA,oBAAQ,UAAU,WAAW,SAAU,MAAM;AAC3C,kBAAI,OAAO,OAAO,KAAK,KAAK,GAAG;AAC/B,kBAAI,SAAS,KAAK;AAClB,uBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,qBAAK,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,cAC7B;AAAA,YACF;AAEA,oBAAQ,UAAU,OAAO,WAAY;AACnC,qBAAO,OAAO,KAAK,KAAK,GAAG,EAAE;AAAA,YAC/B;AAEA,oBAAQ,UAAU,SAAS,SAAU,MAAM;AACzC,kBAAI,IAAI,KAAK;AACb,uBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAI,IAAI,KAAK,CAAC;AACd,qBAAK,IAAI,CAAC;AAAA,cACZ;AAAA,YACF;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,2BAAY;AAAE,uBAAS,iBAAiB,QAAQ,OAAO;AAAE,yBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,sBAAI,aAAa,MAAM,CAAC;AAAG,6BAAW,aAAa,WAAW,cAAc;AAAO,6BAAW,eAAe;AAAM,sBAAI,WAAW,WAAY,YAAW,WAAW;AAAM,yBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,gBAAG;AAAA,cAAE;AAAlT;AAAoT,qBAAO,SAAU,aAAa,YAAY,aAAa;AAAE,oBAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,oBAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,uBAAO;AAAA,cAAa;AAAA,YAAG,EAAE;AAEljB,qBAAS,gBAAgBI,WAAU,aAAa;AAAE,kBAAI,EAAEA,qBAAoB,cAAc;AAAE,sBAAM,IAAI,UAAU,mCAAmC;AAAA,cAAG;AAAA,YAAE;AAA/I;AAST,gBAAI,aAAa,oBAAoB,EAAE;AAEvC,gBAAI,YAAY,WAAY;AACxB,uBAASQ,WAAU,GAAG,iBAAiB;AACnC,gCAAgB,MAAMA,UAAS;AAE/B,oBAAI,oBAAoB,QAAQ,oBAAoB,OAAW,MAAK,kBAAkB,KAAK;AAE3F,oBAAI,SAAS;AACb,oBAAI,aAAa,WAAY,UAAS,EAAE,KAAK;AAAA,oBAAO,UAAS,EAAE;AAE/D,qBAAK,WAAW,GAAG,GAAG,SAAS,CAAC;AAAA,cACpC;AATS,qBAAAA,YAAA;AAWT,2BAAaA,YAAW,CAAC;AAAA,gBACrB,KAAK;AAAA,gBACL,OAAO,gCAAS,WAAW,GAAG,GAAG,GAAG;AAChC,sBAAI,IAAI,GAAG;AACP,wBAAI,IAAI,KAAK,WAAW,GAAG,GAAG,CAAC;AAC/B,yBAAK,WAAW,GAAG,GAAG,CAAC;AACvB,yBAAK,WAAW,GAAG,IAAI,GAAG,CAAC;AAAA,kBAC/B;AAAA,gBACJ,GANO;AAAA,cAOX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,WAAW,GAAG,GAAG,GAAG;AAChC,sBAAI,IAAI,KAAK,KAAK,GAAG,CAAC;AACtB,sBAAI,IAAI;AACR,sBAAI,IAAI;AACR,yBAAO,MAAM;AACT,2BAAO,KAAK,gBAAgB,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG;AAC7C;AAAA,oBACJ;AAAC,2BAAO,KAAK,gBAAgB,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG;AAC9C;AAAA,oBACJ;AAAC,wBAAI,IAAI,GAAG;AACR,2BAAK,MAAM,GAAG,GAAG,CAAC;AAClB;AACA;AAAA,oBACJ,MAAO,QAAO;AAAA,kBAClB;AAAA,gBACJ,GAfO;AAAA,cAgBX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,KAAK,QAAQ,OAAO;AAChC,sBAAI,kBAAkB,WAAY,QAAO,OAAO,cAAc,KAAK;AAAA,sBAAO,QAAO,OAAO,KAAK;AAAA,gBACjG,GAFO;AAAA,cAGX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,KAAK,QAAQ,OAAO,OAAO;AACvC,sBAAI,kBAAkB,WAAY,QAAO,cAAc,OAAO,KAAK;AAAA,sBAAO,QAAO,KAAK,IAAI;AAAA,gBAC9F,GAFO;AAAA,cAGX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,MAAM,GAAG,GAAG,GAAG;AAC3B,sBAAI,OAAO,KAAK,KAAK,GAAG,CAAC;AACzB,uBAAK,KAAK,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AAC/B,uBAAK,KAAK,GAAG,GAAG,IAAI;AAAA,gBACxB,GAJO;AAAA,cAKX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,wBAAwB,GAAG,GAAG;AAC1C,yBAAO,IAAI;AAAA,gBACf,GAFO;AAAA,cAGX,CAAC,CAAC;AAEF,qBAAOA;AAAA,YACX,EAAE;AAEF,YAAAb,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,2BAAY;AAAE,uBAAS,iBAAiB,QAAQ,OAAO;AAAE,yBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,sBAAI,aAAa,MAAM,CAAC;AAAG,6BAAW,aAAa,WAAW,cAAc;AAAO,6BAAW,eAAe;AAAM,sBAAI,WAAW,WAAY,YAAW,WAAW;AAAM,yBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,gBAAG;AAAA,cAAE;AAAlT;AAAoT,qBAAO,SAAU,aAAa,YAAY,aAAa;AAAE,oBAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,oBAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,uBAAO;AAAA,cAAa;AAAA,YAAG,EAAE;AAEljB,qBAAS,gBAAgBI,WAAU,aAAa;AAAE,kBAAI,EAAEA,qBAAoB,cAAc;AAAE,sBAAM,IAAI,UAAU,mCAAmC;AAAA,cAAG;AAAA,YAAE;AAA/I;AAYT,gBAAI,kBAAkB,WAAY;AAC9B,uBAASS,iBAAgB,WAAW,WAAW;AAC3C,oBAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACtF,oBAAI,mBAAmB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC3F,oBAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEtF,gCAAgB,MAAMA,gBAAe;AAErC,qBAAK,YAAY;AACjB,qBAAK,YAAY;AACjB,qBAAK,cAAc;AACnB,qBAAK,mBAAmB;AACxB,qBAAK,cAAc;AAGnB,qBAAK,OAAO,UAAU,SAAS;AAC/B,qBAAK,OAAO,UAAU,SAAS;AAG/B,qBAAK,OAAO,IAAI,MAAM,KAAK,IAAI;AAC/B,yBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,uBAAK,KAAK,CAAC,IAAI,IAAI,MAAM,KAAK,IAAI;AAElC,2BAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,yBAAK,KAAK,CAAC,EAAE,CAAC,IAAI;AAAA,kBACtB;AAAA,gBACJ;AAGA,qBAAK,gBAAgB,IAAI,MAAM,KAAK,IAAI;AACxC,yBAAS,KAAK,GAAG,KAAK,KAAK,MAAM,MAAM;AACnC,uBAAK,cAAc,EAAE,IAAI,IAAI,MAAM,KAAK,IAAI;AAE5C,2BAAS,KAAK,GAAG,KAAK,KAAK,MAAM,MAAM;AACnC,yBAAK,cAAc,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,MAAM,IAAI;AAAA,kBAClD;AAAA,gBACJ;AAGA,qBAAK,aAAa,CAAC;AAGnB,qBAAK,QAAQ;AAGb,qBAAK,aAAa;AAAA,cACtB;AA7CS,qBAAAA,kBAAA;AA+CT,2BAAaA,kBAAiB,CAAC;AAAA,gBAC3B,KAAK;AAAA,gBACL,OAAO,gCAAS,WAAW;AACvB,yBAAO,KAAK;AAAA,gBAChB,GAFO;AAAA,cAGX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,gBAAgB;AAC5B,yBAAO,KAAK;AAAA,gBAChB,GAFO;AAAA;AAAA,cAMX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,eAAe;AAE3B,2BAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,yBAAK,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK;AAC7C,yBAAK,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,OAAO,IAAI;AAAA,kBAClD;AAGA,2BAAS,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK;AAChC,yBAAK,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK;AAC7C,yBAAK,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,MAAM,KAAK;AAAA,kBAClD;AAGA,2BAAS,MAAM,GAAG,MAAM,KAAK,MAAM,OAAO;AACtC,6BAAS,MAAM,GAAG,MAAM,KAAK,MAAM,OAAO;AAEtC,0BAAI,OAAO;AACX,0BAAI,KAAK,UAAU,MAAM,CAAC,MAAM,KAAK,UAAU,MAAM,CAAC,EAAG,QAAO,KAAK,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,KAAK;AAAA,0BAAiB,QAAO,KAAK,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,KAAK;AAE9J,0BAAI,KAAK,KAAK,KAAK,MAAM,CAAC,EAAE,GAAG,IAAI,KAAK;AACxC,0BAAI,OAAO,KAAK,KAAK,GAAG,EAAE,MAAM,CAAC,IAAI,KAAK;AAG1C,0BAAI,QAAQ,CAAC,MAAM,IAAI,IAAI;AAC3B,0BAAI,UAAU,KAAK,mBAAmB,KAAK;AAG3C,2BAAK,KAAK,GAAG,EAAE,GAAG,IAAI,MAAM,QAAQ,CAAC,CAAC;AACtC,2BAAK,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,SAAS,CAAC,GAAG,QAAQ,SAAS,CAAC,GAAG,QAAQ,SAAS,CAAC,CAAC;AAAA,oBACjG;AAAA,kBACJ;AAGA,uBAAK,QAAQ,KAAK,KAAK,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC;AAAA,gBACvD,GAnCO;AAAA;AAAA,cAuCX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,qBAAqB;AACjC,sBAAI,sBAAsB,CAAC;AAE3B,sCAAoB,KAAK;AAAA,oBAAE,KAAK,CAAC,KAAK,UAAU,QAAQ,KAAK,UAAU,MAAM;AAAA,oBACzE,MAAM;AAAA,oBACN,MAAM;AAAA,kBACV,CAAC;AAED,yBAAO,oBAAoB,CAAC,GAAG;AAC3B,wBAAI,UAAU,oBAAoB,CAAC;AACnC,wBAAI,aAAa,KAAK,cAAc,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAC;AAElE,wBAAI,WAAW,CAAC,GAAG;AACf,0CAAoB,KAAK;AAAA,wBAAE,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,wBACnE,MAAM,KAAK,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ;AAAA,wBACnD,MAAM,KAAK,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ;AAAA,sBACvD,CAAC;AAAA,oBACL;AACA,wBAAI,WAAW,CAAC,GAAG;AACf,0CAAoB,KAAK;AAAA,wBAAE,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,CAAC;AAAA,wBAC/D,MAAM,KAAK,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ;AAAA,wBACnD,MAAM,MAAM,QAAQ;AAAA,sBACxB,CAAC;AAAA,oBACL;AACA,wBAAI,WAAW,CAAC,GAAG;AACf,0CAAoB,KAAK;AAAA,wBAAE,KAAK,CAAC,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,wBAC/D,MAAM,MAAM,QAAQ;AAAA,wBACpB,MAAM,KAAK,UAAU,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ;AAAA,sBACvD,CAAC;AAAA,oBACL;AAEA,wBAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAG,MAAK,WAAW,KAAK;AAAA,sBAAE,WAAW,QAAQ;AAAA,sBACxF,WAAW,QAAQ;AAAA,oBACvB,CAAC;AAED,wCAAoB,MAAM;AAAA,kBAC9B;AAEA,yBAAO,KAAK;AAAA,gBAChB,GAvCO;AAAA;AAAA,cA2CX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,cAAc,KAAK,KAAK;AACpC,sBAAI,UAAU,CAAC,GACX,IAAI;AACR,0BAAQ,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,IAAI;AACzC,4BAAQ,KAAK,CAAC;AAAA,kBAClB;AACA,yBAAO;AAAA,gBACX,GAPO;AAAA,cAQX,GAAG;AAAA,gBACC,KAAK;AAAA,gBACL,OAAO,gCAAS,mBAAmB,OAAO;AACtC,yBAAO,KAAK,cAAc,OAAO,KAAK,IAAI,MAAM,MAAM,KAAK,CAAC;AAAA,gBAChE,GAFO;AAAA,cAGX,CAAC,CAAC;AAEF,qBAAOA;AAAA,YACX,EAAE;AAEF,YAAAd,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,aAAa,gCAASc,cAAa;AACrC;AAAA,YACF,GAFiB;AAIjB,uBAAW,WAAW,oBAAoB,EAAE;AAC5C,uBAAW,oBAAoB,oBAAoB,CAAC;AACpD,uBAAW,eAAe,oBAAoB,EAAE;AAChD,uBAAW,eAAe,oBAAoB,EAAE;AAChD,uBAAW,aAAa,oBAAoB,EAAE;AAC9C,uBAAW,UAAU,oBAAoB,EAAE;AAC3C,uBAAW,UAAU,oBAAoB,EAAE;AAC3C,uBAAW,YAAY,oBAAoB,CAAC;AAC5C,uBAAW,QAAQ,oBAAoB,CAAC;AACxC,uBAAW,UAAU,oBAAoB,EAAE;AAC3C,uBAAW,QAAQ,oBAAoB,EAAE;AACzC,uBAAW,SAAS,oBAAoB,CAAC;AACzC,uBAAW,aAAa,oBAAoB,EAAE;AAC9C,uBAAW,aAAa,oBAAoB,EAAE;AAC9C,uBAAW,YAAY,oBAAoB,EAAE;AAC7C,uBAAW,oBAAoB,oBAAoB,EAAE;AACrD,uBAAW,YAAY,oBAAoB,EAAE;AAC7C,uBAAW,aAAa,oBAAoB,EAAE;AAC9C,uBAAW,eAAe,oBAAoB,CAAC;AAC/C,uBAAW,SAAS,oBAAoB,CAAC;AACzC,uBAAW,QAAQ,oBAAoB,CAAC;AACxC,uBAAW,gBAAgB,oBAAoB,CAAC;AAChD,uBAAW,QAAQ,oBAAoB,CAAC;AACxC,uBAAW,SAAS,oBAAoB,EAAE;AAC1C,uBAAW,kBAAkB,oBAAoB,CAAC;AAClD,uBAAW,kBAAkB,oBAAoB,EAAE;AAEnD,YAAAf,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,qBAAS,UAAU;AACjB,mBAAK,YAAY,CAAC;AAAA,YACpB;AAFS;AAIT,gBAAI,IAAI,QAAQ;AAEhB,cAAE,cAAc,SAAU,OAAO,UAAU;AACzC,mBAAK,UAAU,KAAK;AAAA,gBAClB;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAEA,cAAE,iBAAiB,SAAU,OAAO,UAAU;AAC5C,uBAAS,IAAI,KAAK,UAAU,QAAQ,KAAK,GAAG,KAAK;AAC/C,oBAAI,IAAI,KAAK,UAAU,CAAC;AAExB,oBAAI,EAAE,UAAU,SAAS,EAAE,aAAa,UAAU;AAChD,uBAAK,UAAU,OAAO,GAAG,CAAC;AAAA,gBAC5B;AAAA,cACF;AAAA,YACF;AAEA,cAAE,OAAO,SAAU,OAAO,MAAM;AAC9B,uBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,oBAAI,IAAI,KAAK,UAAU,CAAC;AAExB,oBAAI,UAAU,EAAE,OAAO;AACrB,oBAAE,SAAS,IAAI;AAAA,gBACjB;AAAA,cACF;AAAA,YACF;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA,QACG,CAAC;AAAA;AAAA,IACV,CAAC;AAAA;AAAA;;;AC7uID;AAAA;AAAA;AAAA,KAAC,gCAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ,qBAAsB;AAAA,eACxC,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,aAAa,GAAG,OAAO;AAAA,eACxB,OAAO,YAAY;AAC1B,gBAAQ,UAAU,IAAI,QAAQ,qBAAsB;AAAA;AAEpD,aAAK,UAAU,IAAI,QAAQ,KAAK,YAAY,CAAC;AAAA,IAC/C,GATC,qCASE,SAAM,SAAS,+BAA+B;AACjD;AAAA;AAAA,QAAiB,SAAS,SAAS;AAEzB,cAAI,mBAAmB,CAAC;AAGxB,mBAAS,oBAAoB,UAAU;AAGtC,gBAAG,iBAAiB,QAAQ,GAAG;AAC9B,qBAAO,iBAAiB,QAAQ,EAAE;AAAA,YACnC;AAEA,gBAAIgB,UAAS,iBAAiB,QAAQ,IAAI;AAAA;AAAA,cACzC,GAAG;AAAA;AAAA,cACH,GAAG;AAAA;AAAA,cACH,SAAS,CAAC;AAAA;AAAA,YACX;AAGA,oBAAQ,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAGlF,YAAAA,QAAO,IAAI;AAGX,mBAAOA,QAAO;AAAA,UACf;AArBS;AAyBT,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI,SAAS,OAAO;AAAE,mBAAO;AAAA,UAAO;AAGxD,8BAAoB,IAAI,SAASC,UAAS,MAAM,QAAQ;AACvD,gBAAG,CAAC,oBAAoB,EAAEA,UAAS,IAAI,GAAG;AACzC,qBAAO,eAAeA,UAAS,MAAM;AAAA;AAAA,gBACpC,cAAc;AAAA;AAAA,gBACd,YAAY;AAAA;AAAA,gBACZ,KAAK;AAAA;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD;AAGA,8BAAoB,IAAI,SAASD,SAAQ;AACxC,gBAAI,SAASA,WAAUA,QAAO;AAAA;AAAA,cAC7B,gCAAS,aAAa;AAAE,uBAAOA,QAAO,SAAS;AAAA,cAAG,GAAlD;AAAA;AAAA;AAAA,cACA,gCAAS,mBAAmB;AAAE,uBAAOA;AAAA,cAAQ,GAA7C;AAAA;AACD,gCAAoB,EAAE,QAAQ,KAAK,MAAM;AACzC,mBAAO;AAAA,UACR;AAGA,8BAAoB,IAAI,SAAS,QAAQ,UAAU;AAAE,mBAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ;AAAA,UAAG;AAGpH,8BAAoB,IAAI;AAGxB,iBAAO,oBAAoB,oBAAoB,IAAI,CAAC;AAAA,QACrD,EAEC;AAAA;AAAA;AAAA,UAEH,SAASA,SAAQC,UAAS;AAEjC,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,oBAAoB,oBAAoB,CAAC,EAAE;AAE/C,qBAAS,gBAAgB;AAAA,YAAC;AAAjB;AAGT,qBAAS,QAAQ,mBAAmB;AAClC,4BAAc,IAAI,IAAI,kBAAkB,IAAI;AAAA,YAC9C;AAEA,0BAAc,kCAAkC;AAChD,0BAAc,4BAA4B,kBAAkB;AAC5D,0BAAc,+BAA+B;AAC7C,0BAAc,OAAO;AACrB,0BAAc,0BAA0B;AACxC,0BAAc,4BAA4B;AAC1C,0BAAc,gCAAgC;AAE9C,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,oBAAoB,CAAC,EAAE;AAE1C,qBAAS,SAAS,QAAQ,QAAQ,OAAO;AACvC,2BAAa,KAAK,MAAM,QAAQ,QAAQ,KAAK;AAAA,YAC/C;AAFS;AAIT,qBAAS,YAAY,OAAO,OAAO,aAAa,SAAS;AACzD,qBAAS,QAAQ,cAAc;AAC7B,uBAAS,IAAI,IAAI,aAAa,IAAI;AAAA,YACpC;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,SAAS,oBAAoB,CAAC,EAAE;AAEpC,qBAAS,UAAU,QAAQ,UAAU,QAAQ;AAC3C,qBAAO,KAAK,MAAM,QAAQ,UAAU,MAAM;AAAA,YAC5C;AAFS;AAIT,sBAAU,YAAY,OAAO,OAAO,OAAO,SAAS;AACpD,qBAAS,QAAQ,QAAQ;AACvB,wBAAU,IAAI,IAAI,OAAO,IAAI;AAAA,YAC/B;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,gBAAgB,oBAAoB,CAAC,EAAE;AAE3C,qBAAS,iBAAiB,QAAQ;AAChC,4BAAc,KAAK,MAAM,MAAM;AAAA,YACjC;AAFS;AAIT,6BAAiB,YAAY,OAAO,OAAO,cAAc,SAAS;AAClE,qBAAS,QAAQ,eAAe;AAC9B,+BAAiB,IAAI,IAAI,cAAc,IAAI;AAAA,YAC7C;AAEA,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,eAAe,oBAAoB,CAAC,EAAE;AAC1C,gBAAI,QAAQ,oBAAoB,CAAC,EAAE;AAEnC,qBAAS,SAAS,IAAI,KAAK,MAAM,OAAO;AACtC,2BAAa,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAAA,YAC9C;AAFS;AAIT,qBAAS,YAAY,OAAO,OAAO,aAAa,SAAS;AACzD,qBAAS,QAAQ,cAAc;AAC7B,uBAAS,IAAI,IAAI,aAAa,IAAI;AAAA,YACpC;AAEA,qBAAS,UAAU,OAAO,WAAY;AACpC,kBAAI,SAAS,KAAK,aAAa,UAAU;AACzC,mBAAK,gBAAgB,OAAO,iBAAiB,KAAK,eAAe,KAAK,kBAAkB,KAAK,qBAAqB,KAAK;AACvH,mBAAK,gBAAgB,OAAO,iBAAiB,KAAK,eAAe,KAAK,kBAAkB,KAAK,qBAAqB,KAAK;AAEvH,kBAAI,KAAK,IAAI,KAAK,aAAa,IAAI,OAAO,gBAAgB,OAAO,qBAAqB;AACpF,qBAAK,gBAAgB,OAAO,gBAAgB,OAAO,sBAAsB,MAAM,KAAK,KAAK,aAAa;AAAA,cACxG;AAEA,kBAAI,KAAK,IAAI,KAAK,aAAa,IAAI,OAAO,gBAAgB,OAAO,qBAAqB;AACpF,qBAAK,gBAAgB,OAAO,gBAAgB,OAAO,sBAAsB,MAAM,KAAK,KAAK,aAAa;AAAA,cACxG;AAGA,kBAAI,KAAK,SAAS,MAAM;AACtB,qBAAK,OAAO,KAAK,eAAe,KAAK,aAAa;AAAA,cACpD,WAES,KAAK,MAAM,SAAS,EAAE,UAAU,GAAG;AACxC,qBAAK,OAAO,KAAK,eAAe,KAAK,aAAa;AAAA,cACpD,OAEK;AACD,qBAAK,gCAAgC,KAAK,eAAe,KAAK,aAAa;AAAA,cAC7E;AAEJ,qBAAO,qBAAqB,KAAK,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,KAAK,aAAa;AAEtF,mBAAK,eAAe;AACpB,mBAAK,eAAe;AACpB,mBAAK,kBAAkB;AACvB,mBAAK,kBAAkB;AACvB,mBAAK,oBAAoB;AACzB,mBAAK,oBAAoB;AACzB,mBAAK,gBAAgB;AACrB,mBAAK,gBAAgB;AAAA,YACvB;AAEA,qBAAS,UAAU,kCAAkC,SAAU,IAAI,IAAI;AACrE,kBAAIC,SAAQ,KAAK,SAAS,EAAE,SAAS;AACrC,kBAAI;AACJ,uBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,uBAAOA,OAAM,CAAC;AACd,oBAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,uBAAK,OAAO,IAAI,EAAE;AAClB,uBAAK,iBAAiB;AACtB,uBAAK,iBAAiB;AAAA,gBACxB,OAAO;AACL,uBAAK,gCAAgC,IAAI,EAAE;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAEA,qBAAS,UAAU,WAAW,SAAUC,QAAO;AAC7C,mBAAK,QAAQA;AAAA,YACf;AAEA,qBAAS,UAAU,WAAW,WAAY;AACxC,qBAAO;AAAA,YACT;AAEA,qBAAS,UAAU,WAAW,WAAY;AACxC,qBAAO;AAAA,YACT;AAEA,qBAAS,UAAU,UAAU,SAAUC,OAAM;AAC3C,mBAAK,OAAOA;AAAA,YACd;AAEA,qBAAS,UAAU,UAAU,WAAY;AACvC,qBAAO;AAAA,YACT;AAEA,qBAAS,UAAU,eAAe,SAAUC,YAAW;AACrD,mBAAK,YAAYA;AAAA,YACnB;AAEA,qBAAS,UAAU,cAAc,WAAY;AAC3C,qBAAO;AAAA,YACT;AAEA,YAAAL,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,WAAW,oBAAoB,CAAC,EAAE;AACtC,gBAAI,mBAAmB,oBAAoB,CAAC;AAC5C,gBAAI,YAAY,oBAAoB,CAAC;AACrC,gBAAI,WAAW,oBAAoB,CAAC;AACpC,gBAAI,WAAW,oBAAoB,CAAC;AACpC,gBAAI,gBAAgB,oBAAoB,CAAC;AACzC,gBAAI,oBAAoB,oBAAoB,CAAC,EAAE;AAC/C,gBAAI,kBAAkB,oBAAoB,CAAC,EAAE;AAC7C,gBAAIK,SAAQ,oBAAoB,CAAC,EAAE;AACnC,gBAAI,SAAS,oBAAoB,CAAC,EAAE;AACpC,gBAAIC,UAAS,oBAAoB,CAAC,EAAE;AACpC,gBAAI,UAAU,oBAAoB,CAAC,EAAE;AACrC,gBAAI,YAAY,oBAAoB,CAAC,EAAE;AACvC,gBAAI,SAAS,oBAAoB,CAAC,EAAE;AACpC,gBAAI,YAAY,oBAAoB,CAAC,EAAE;AAEvC,qBAAS,aAAa;AACpB,uBAAS,KAAK,IAAI;AAElB,mBAAK,YAAY,CAAC;AAAA,YACpB;AAJS;AAMT,uBAAW,YAAY,OAAO,OAAO,SAAS,SAAS;AAEvD,qBAAS,QAAQ,UAAU;AACzB,yBAAW,IAAI,IAAI,SAAS,IAAI;AAAA,YAClC;AAEA,uBAAW,UAAU,kBAAkB,WAAY;AACjD,kBAAI,KAAK,IAAI,iBAAiB,IAAI;AAClC,mBAAK,eAAe;AACpB,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,WAAW,SAAU,QAAQ;AAChD,qBAAO,IAAI,UAAU,MAAM,KAAK,cAAc,MAAM;AAAA,YACtD;AAEA,uBAAW,UAAU,UAAU,SAAU,OAAO;AAC9C,qBAAO,IAAI,SAAS,KAAK,cAAc,KAAK;AAAA,YAC9C;AAEA,uBAAW,UAAU,UAAU,SAAU,OAAO;AAC9C,qBAAO,IAAI,SAAS,MAAM,MAAM,KAAK;AAAA,YACvC;AAEA,uBAAW,UAAU,iBAAiB,WAAY;AAChD,uBAAS,UAAU,eAAe,KAAK,MAAM,SAAS;AACtD,kBAAI,CAAC,KAAK,aAAa;AACrB,oBAAI,cAAc,sBAAsB,IAAI;AAC1C,uBAAK,kBAAkB;AAAA,gBACzB,OAAO;AACL,uBAAK,kBAAkB,cAAc;AAAA,gBACvC;AAEA,qBAAK,qCAAqC,cAAc;AACxD,qBAAK,iBAAiB,kBAAkB;AACxC,qBAAK,oBAAoB,kBAAkB;AAC3C,qBAAK,kBAAkB,kBAAkB;AACzC,qBAAK,0BAA0B,kBAAkB;AACjD,qBAAK,qBAAqB,kBAAkB;AAC5C,qBAAK,6BAA6B,kBAAkB;AAGpD,qBAAK,iBAAiB,CAAC;AACvB,qBAAK,qBAAqB;AAC1B,qBAAK,wBAAwB;AAC7B,qBAAK,gBAAgB;AACrB,qBAAK,mBAAmB;AAGxB,qBAAK,eAAe;AACpB,qBAAK,kBAAkB,KAAK,gBAAgB,kBAAkB;AAC9D,qBAAK,mBAAmB,kBAAkB,2BAA2B,KAAK;AAC1E,qBAAK,kBAAkB;AAAA,cACzB;AAAA,YACF;AAEA,uBAAW,UAAU,SAAS,WAAY;AACxC,kBAAI,sBAAsB,gBAAgB;AAC1C,kBAAI,qBAAqB;AACvB,qBAAK,iBAAiB;AACtB,qBAAK,aAAa,cAAc;AAAA,cAClC;AAEA,mBAAK,QAAQ;AACb,qBAAO,KAAK,cAAc;AAAA,YAC5B;AAEA,uBAAW,UAAU,gBAAgB,WAAY;AAC/C,mBAAK,mBAAmB,KAAK,mCAAmC;AAChE,mBAAK,aAAa,8BAA8B,KAAK,gBAAgB;AACrE,mBAAK,4BAA4B;AACjC,mBAAK,aAAa,0BAA0B;AAC5C,mBAAK,aAAa,wBAAwB;AAC1C,mBAAK,aAAa,QAAQ,EAAE,kBAAkB;AAC9C,mBAAK,qBAAqB;AAE1B,kBAAI,CAAC,KAAK,aAAa;AACrB,oBAAI,SAAS,KAAK,cAAc;AAGhC,oBAAI,OAAO,SAAS,GAAG;AACrB,uBAAK,sBAAsB,MAAM;AAAA,gBACnC,OAEK;AAED,uBAAK,YAAY;AAEjB,uBAAK,aAAa,gCAAgC;AAClD,sBAAI,WAAW,IAAI,IAAI,KAAK,YAAY,CAAC;AACzC,sBAAI,eAAe,KAAK,iBAAiB,OAAO,SAAU,GAAG;AAC3D,2BAAO,SAAS,IAAI,CAAC;AAAA,kBACvB,CAAC;AACD,uBAAK,aAAa,8BAA8B,YAAY;AAE5D,uBAAK,sBAAsB;AAAA,gBAC7B;AAAA,cACJ,OAAO;AACL,oBAAI,cAAc,+BAA+B;AAE/C,uBAAK,YAAY;AAEjB,uBAAK,aAAa,gCAAgC;AAClD,sBAAI,WAAW,IAAI,IAAI,KAAK,YAAY,CAAC;AACzC,sBAAI,eAAe,KAAK,iBAAiB,OAAO,SAAU,GAAG;AAC3D,2BAAO,SAAS,IAAI,CAAC;AAAA,kBACvB,CAAC;AACD,uBAAK,aAAa,8BAA8B,YAAY;AAAA,gBAC9D;AAAA,cACF;AAEA,mBAAK,mBAAmB;AACxB,mBAAK,kBAAkB;AAEvB,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,OAAO,WAAY;AACtC,mBAAK;AAEL,kBAAI,KAAK,oBAAoB,KAAK,iBAAiB,CAAC,KAAK,iBAAiB,CAAC,KAAK,kBAAkB;AAChG,oBAAI,KAAK,eAAe,SAAS,GAAG;AAClC,uBAAK,gBAAgB;AAAA,gBACvB,OAAO;AACL,yBAAO;AAAA,gBACT;AAAA,cACF;AAEA,kBAAI,KAAK,kBAAkB,kBAAkB,4BAA4B,KAAK,CAAC,KAAK,iBAAiB,CAAC,KAAK,kBAAkB;AAC3H,oBAAI,KAAK,YAAY,GAAG;AACtB,sBAAI,KAAK,eAAe,SAAS,GAAG;AAClC,yBAAK,gBAAgB;AAAA,kBACvB,OAAO;AACL,2BAAO;AAAA,kBACT;AAAA,gBACF;AAEA,qBAAK;AAEL,oBAAI,KAAK,iBAAiB,GAAG;AAE3B,uBAAK,kBAAkB,KAAK;AAAA,gBAC9B,WAAW,KAAK,iBAAiB,GAAG;AAElC,uBAAK,kBAAkB,KAAK,eAAe;AAAA,gBAC7C;AAGA,qBAAK,gBAAgB,KAAK,IAAI,KAAK,uBAAuB,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,OAAO,KAAK,uBAAuB,KAAK,iBAAiB,IAAI,KAAK,IAAI,KAAK,eAAe,CAAC,IAAI,MAAM,KAAK,iBAAiB,KAAK,gBAAgB;AAC/O,qBAAK,kBAAkB,KAAK,KAAK,KAAK,yBAAyB,KAAK,KAAK,KAAK,aAAa,CAAC;AAAA,cAC9F;AAEA,kBAAI,KAAK,eAAe;AACtB,oBAAI,KAAK,qBAAqB,MAAM,GAAG;AACrC,sBAAI,KAAK,eAAe,SAAS,GAAG;AAClC,yBAAK,aAAa,aAAa;AAC/B,yBAAK,WAAW;AAChB,yBAAK,SAAS,KAAK,cAAc;AAEjC,yBAAK,aAAa,gCAAgC;AAClD,wBAAI,WAAW,IAAI,IAAI,KAAK,YAAY,CAAC;AACzC,wBAAI,eAAe,KAAK,iBAAiB,OAAO,SAAU,GAAG;AAC3D,6BAAO,SAAS,IAAI,CAAC;AAAA,oBACvB,CAAC;AACD,yBAAK,aAAa,8BAA8B,YAAY;AAE5D,yBAAK,aAAa,aAAa;AAC/B,yBAAK,WAAW;AAChB,yBAAK,gBAAgB,kBAAkB;AAAA,kBACzC,OAAO;AACL,yBAAK,gBAAgB;AACrB,yBAAK,mBAAmB;AAAA,kBAC1B;AAAA,gBACF;AACA,qBAAK;AAAA,cACP;AAEA,kBAAI,KAAK,kBAAkB;AACzB,oBAAI,KAAK,YAAY,GAAG;AACtB,yBAAO;AAAA,gBACT;AACA,oBAAI,KAAK,wBAAwB,MAAM,GAAG;AACxC,uBAAK,aAAa,aAAa;AAC/B,uBAAK,WAAW;AAAA,gBAClB;AACA,qBAAK,gBAAgB,kBAAkB,uCAAuC,MAAM,KAAK,yBAAyB;AAClH,qBAAK;AAAA,cACP;AAEA,kBAAI,oBAAoB,CAAC,KAAK,iBAAiB,CAAC,KAAK;AACrD,kBAAI,+BAA+B,KAAK,qBAAqB,MAAM,KAAK,KAAK,iBAAiB,KAAK,wBAAwB,MAAM,KAAK,KAAK;AAE3I,mBAAK,oBAAoB;AACzB,mBAAK,aAAa,aAAa;AAC/B,mBAAK,iBAAiB;AACtB,mBAAK,oBAAoB,mBAAmB,4BAA4B;AACxE,mBAAK,wBAAwB;AAC7B,mBAAK,UAAU;AACf,mBAAK,QAAQ;AAEb,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,mBAAmB,WAAY;AAClD,kBAAI,WAAW,KAAK,aAAa,YAAY;AAC7C,kBAAI,QAAQ,CAAC;AACb,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,OAAO,SAAS,CAAC,EAAE;AACvB,oBAAI,KAAK,SAAS,CAAC,EAAE;AACrB,sBAAM,EAAE,IAAI;AAAA,kBACV;AAAA,kBACA,GAAG,KAAK,WAAW;AAAA,kBACnB,GAAG,KAAK,WAAW;AAAA,kBACnB,GAAG,KAAK;AAAA,kBACR,GAAG,KAAK;AAAA,gBACV;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,oBAAoB,WAAY;AACnD,mBAAK,yBAAyB;AAC9B,mBAAK,kBAAkB,KAAK;AAC5B,kBAAI,cAAc;AAGlB,kBAAI,kBAAkB,YAAY,UAAU;AAC1C,qBAAK,KAAK,eAAe;AAAA,cAC3B,OAAO;AAEL,uBAAO,CAAC,aAAa;AACnB,gCAAc,KAAK,KAAK;AAAA,gBAC1B;AAEA,qBAAK,aAAa,aAAa;AAAA,cACjC;AAAA,YACF;AAEA,uBAAW,UAAU,qCAAqC,WAAY;AACpE,kBAAI,WAAW,CAAC;AAChB,kBAAI;AAEJ,kBAAI,SAAS,KAAK,aAAa,UAAU;AACzC,kBAAI,OAAO,OAAO;AAClB,kBAAI;AACJ,mBAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,wBAAQ,OAAO,CAAC;AAEhB,sBAAM,gBAAgB;AAEtB,oBAAI,CAAC,MAAM,aAAa;AACtB,6BAAW,SAAS,OAAO,MAAM,SAAS,CAAC;AAAA,gBAC7C;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,mBAAmB,WAAY;AAClD,kBAAI,QAAQ,CAAC;AACb,sBAAQ,MAAM,OAAO,KAAK,aAAa,YAAY,CAAC;AACpD,kBAAI,UAAU,oBAAI,IAAI;AACtB,kBAAI;AACJ,mBAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,oBAAI,OAAO,MAAM,CAAC;AAElB,oBAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AACtB,sBAAI,SAAS,KAAK,UAAU;AAC5B,sBAAI,SAAS,KAAK,UAAU;AAE5B,sBAAI,UAAU,QAAQ;AACpB,yBAAK,cAAc,EAAE,KAAK,IAAI,OAAO,CAAC;AACtC,yBAAK,cAAc,EAAE,KAAK,IAAI,OAAO,CAAC;AACtC,yBAAK,8BAA8B,IAAI;AACvC,4BAAQ,IAAI,IAAI;AAAA,kBAClB,OAAO;AACL,wBAAI,WAAW,CAAC;AAEhB,+BAAW,SAAS,OAAO,OAAO,kBAAkB,MAAM,CAAC;AAC3D,+BAAW,SAAS,OAAO,OAAO,kBAAkB,MAAM,CAAC;AAE3D,wBAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC,GAAG;AAC7B,0BAAI,SAAS,SAAS,GAAG;AACvB,4BAAI;AACJ,6BAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACpC,8BAAI,YAAY,SAAS,CAAC;AAC1B,oCAAU,cAAc,EAAE,KAAK,IAAI,OAAO,CAAC;AAC3C,+BAAK,8BAA8B,SAAS;AAAA,wBAC9C;AAAA,sBACF;AACA,+BAAS,QAAQ,SAAUC,OAAM;AAC/B,gCAAQ,IAAIA,KAAI;AAAA,sBAClB,CAAC;AAAA,oBACH;AAAA,kBACF;AAAA,gBACF;AAEA,oBAAI,QAAQ,QAAQ,MAAM,QAAQ;AAChC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,uBAAW,UAAU,wBAAwB,SAAU,QAAQ;AAE7D,kBAAI,uBAAuB,IAAIF,OAAM,GAAG,CAAC;AACzC,kBAAI,kBAAkB,KAAK,KAAK,KAAK,KAAK,OAAO,MAAM,CAAC;AACxD,kBAAI,SAAS;AACb,kBAAI,WAAW;AACf,kBAAI,WAAW;AACf,kBAAI,QAAQ,IAAI,OAAO,GAAG,CAAC;AAE3B,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,oBAAI,IAAI,mBAAmB,GAAG;AAG5B,6BAAW;AACX,6BAAW;AAEX,sBAAI,KAAK,GAAG;AACV,gCAAY,cAAc;AAAA,kBAC5B;AAEA,2BAAS;AAAA,gBACX;AAEA,oBAAI,OAAO,OAAO,CAAC;AAGnB,oBAAI,aAAaC,QAAO,iBAAiB,IAAI;AAG7C,qCAAqB,IAAI;AACzB,qCAAqB,IAAI;AAGzB,wBAAQ,WAAW,aAAa,MAAM,YAAY,oBAAoB;AAEtE,oBAAI,MAAM,IAAI,QAAQ;AACpB,2BAAS,KAAK,MAAM,MAAM,CAAC;AAAA,gBAC7B;AAEA,2BAAW,KAAK,MAAM,MAAM,IAAI,cAAc,4BAA4B;AAAA,cAC5E;AAEA,mBAAK,UAAU,IAAI,OAAO,gBAAgB,iBAAiB,MAAM,IAAI,GAAG,gBAAgB,iBAAiB,MAAM,IAAI,CAAC,CAAC;AAAA,YACvH;AAEA,uBAAW,eAAe,SAAU,MAAM,YAAY,eAAe;AACnE,kBAAI,YAAY,KAAK,IAAI,KAAK,kBAAkB,IAAI,GAAG,cAAc,yBAAyB;AAC9F,yBAAW,mBAAmB,YAAY,MAAM,GAAG,KAAK,GAAG,SAAS;AACpE,kBAAI,SAAS,OAAO,gBAAgB,IAAI;AAExC,kBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAU,cAAc,OAAO,QAAQ,CAAC;AACxC,wBAAU,cAAc,OAAO,QAAQ,CAAC;AACxC,wBAAU,aAAa,cAAc,CAAC;AACtC,wBAAU,aAAa,cAAc,CAAC;AAEtC,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,oBAAI,OAAO,KAAK,CAAC;AACjB,qBAAK,UAAU,SAAS;AAAA,cAC1B;AAEA,kBAAI,cAAc,IAAI,OAAO,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC;AAE/D,qBAAO,UAAU,sBAAsB,WAAW;AAAA,YACpD;AAEA,uBAAW,qBAAqB,SAAU,MAAM,cAAc,YAAY,UAAU,UAAU,kBAAkB;AAE9G,kBAAI,gBAAgB,WAAW,aAAa,KAAK;AAEjD,kBAAI,eAAe,GAAG;AACpB,gCAAgB;AAAA,cAClB;AAEA,kBAAI,aAAa,eAAe,cAAc;AAC9C,kBAAI,OAAO,YAAY,UAAU,SAAS;AAG1C,kBAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,kBAAI,KAAK,WAAW,KAAK,IAAI,IAAI;AACjC,kBAAI,KAAK,WAAW,KAAK,IAAI,IAAI;AAEjC,mBAAK,UAAU,IAAI,EAAE;AAIrB,kBAAI,gBAAgB,CAAC;AACrB,8BAAgB,cAAc,OAAO,KAAK,SAAS,CAAC;AACpD,kBAAI,aAAa,cAAc;AAE/B,kBAAI,gBAAgB,MAAM;AACxB;AAAA,cACF;AAEA,kBAAI,cAAc;AAElB,kBAAI,gBAAgB,cAAc;AAClC,kBAAI;AAEJ,kBAAI,QAAQ,KAAK,gBAAgB,YAAY;AAI7C,qBAAO,MAAM,SAAS,GAAG;AAEvB,oBAAI,OAAO,MAAM,CAAC;AAClB,sBAAM,OAAO,GAAG,CAAC;AACjB,oBAAI,QAAQ,cAAc,QAAQ,IAAI;AACtC,oBAAI,SAAS,GAAG;AACd,gCAAc,OAAO,OAAO,CAAC;AAAA,gBAC/B;AACA;AACA;AAAA,cACF;AAEA,kBAAI,gBAAgB,MAAM;AAExB,8BAAc,cAAc,QAAQ,MAAM,CAAC,CAAC,IAAI,KAAK;AAAA,cACvD,OAAO;AACL,6BAAa;AAAA,cACf;AAEA,kBAAI,YAAY,KAAK,IAAI,WAAW,UAAU,IAAI;AAElD,uBAAS,IAAI,YAAY,eAAe,YAAY,IAAI,EAAE,IAAI,eAAe;AAC3E,oBAAI,kBAAkB,cAAc,CAAC,EAAE,YAAY,IAAI;AAGvD,oBAAI,mBAAmB,cAAc;AACnC;AAAA,gBACF;AAEA,oBAAI,mBAAmB,aAAa,cAAc,aAAa;AAC/D,oBAAI,iBAAiB,kBAAkB,aAAa;AAEpD,2BAAW,mBAAmB,iBAAiB,MAAM,iBAAiB,eAAe,WAAW,kBAAkB,gBAAgB;AAElI;AAAA,cACF;AAAA,YACF;AAEA,uBAAW,oBAAoB,SAAU,MAAM;AAC7C,kBAAI,cAAc,QAAQ;AAE1B,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,oBAAI,OAAO,KAAK,CAAC;AACjB,oBAAI,WAAW,KAAK,YAAY;AAEhC,oBAAI,WAAW,aAAa;AAC1B,gCAAc;AAAA,gBAChB;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,qBAAqB,WAAY;AAEpD,qBAAO,KAAK,KAAK,QAAQ,KAAK,KAAK;AAAA,YACrC;AAKA,uBAAW,UAAU,yBAAyB,WAAY;AACxD,kBAAI,OAAO;AAEX,kBAAI,mBAAmB,CAAC;AACxB,mBAAK,eAAe,CAAC;AACrB,mBAAK,gBAAgB,CAAC;AAEtB,kBAAI,aAAa,CAAC;AAClB,kBAAI,WAAW,KAAK,aAAa,YAAY;AAG7C,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,OAAO,SAAS,CAAC;AACrB,oBAAI,SAAS,KAAK,UAAU;AAE5B,oBAAI,KAAK,0BAA0B,IAAI,MAAM,MAAM,OAAO,MAAM,UAAa,CAAC,KAAK,aAAa,MAAM,IAAI;AACxG,6BAAW,KAAK,IAAI;AAAA,gBACtB;AAAA,cACF;AAGA,uBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,oBAAI,OAAO,WAAW,CAAC;AACvB,oBAAI,OAAO,KAAK,UAAU,EAAE;AAE5B,oBAAI,OAAO,iBAAiB,IAAI,MAAM,YAAa,kBAAiB,IAAI,IAAI,CAAC;AAE7E,iCAAiB,IAAI,IAAI,iBAAiB,IAAI,EAAE,OAAO,IAAI;AAAA,cAC7D;AAGA,qBAAO,KAAK,gBAAgB,EAAE,QAAQ,SAAUE,OAAM;AACpD,oBAAI,iBAAiBA,KAAI,EAAE,SAAS,GAAG;AACrC,sBAAI,kBAAkB,mBAAmBA;AACzC,uBAAK,aAAa,eAAe,IAAI,iBAAiBA,KAAI;AAE1D,sBAAIC,UAAS,iBAAiBD,KAAI,EAAE,CAAC,EAAE,UAAU;AAGjD,sBAAI,gBAAgB,IAAI,SAAS,KAAK,YAAY;AAClD,gCAAc,KAAK;AACnB,gCAAc,cAAcC,QAAO,eAAe;AAClD,gCAAc,eAAeA,QAAO,gBAAgB;AACpD,gCAAc,gBAAgBA,QAAO,iBAAiB;AACtD,gCAAc,aAAaA,QAAO,cAAc;AAEhD,uBAAK,cAAc,eAAe,IAAI;AAEtC,sBAAI,mBAAmB,KAAK,gBAAgB,EAAE,IAAI,KAAK,SAAS,GAAG,aAAa;AAChF,sBAAI,cAAcA,QAAO,SAAS;AAGlC,8BAAY,IAAI,aAAa;AAG7B,2BAASC,KAAI,GAAGA,KAAI,iBAAiBF,KAAI,EAAE,QAAQE,MAAK;AACtD,wBAAIC,QAAO,iBAAiBH,KAAI,EAAEE,EAAC;AAEnC,gCAAY,OAAOC,KAAI;AACvB,qCAAiB,IAAIA,KAAI;AAAA,kBAC3B;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAEA,uBAAW,UAAU,iBAAiB,WAAY;AAChD,kBAAI,gBAAgB,CAAC;AACrB,kBAAI,WAAW,CAAC;AAGhB,mBAAK,sBAAsB;AAE3B,uBAAS,IAAI,GAAG,IAAI,KAAK,cAAc,QAAQ,KAAK;AAElD,yBAAS,KAAK,cAAc,CAAC,EAAE,EAAE,IAAI,KAAK,cAAc,CAAC;AACzD,8BAAc,KAAK,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC;AAG/F,qBAAK,aAAa,OAAO,KAAK,cAAc,CAAC,EAAE,SAAS,CAAC;AACzD,qBAAK,cAAc,CAAC,EAAE,QAAQ;AAAA,cAChC;AAEA,mBAAK,aAAa,cAAc;AAGhC,mBAAK,oBAAoB,eAAe,QAAQ;AAAA,YAClD;AAEA,uBAAW,UAAU,yBAAyB,WAAY;AACxD,kBAAI,OAAO;AACX,kBAAI,sBAAsB,KAAK,sBAAsB,CAAC;AAEtD,qBAAO,KAAK,KAAK,YAAY,EAAE,QAAQ,SAAU,IAAI;AACnD,oBAAI,eAAe,KAAK,cAAc,EAAE;AAExC,oCAAoB,EAAE,IAAI,KAAK,UAAU,KAAK,aAAa,EAAE,GAAG,aAAa,cAAc,aAAa,YAAY;AAGpH,6BAAa,KAAK,QAAQ,oBAAoB,EAAE,EAAE;AAClD,6BAAa,KAAK,SAAS,oBAAoB,EAAE,EAAE;AAAA,cACrD,CAAC;AAAA,YACH;AAEA,uBAAW,UAAU,sBAAsB,WAAY;AACrD,uBAAS,IAAI,KAAK,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AACvD,oBAAI,gBAAgB,KAAK,cAAc,CAAC;AACxC,oBAAI,KAAK,cAAc;AACvB,oBAAI,mBAAmB,cAAc;AACrC,oBAAI,iBAAiB,cAAc;AAEnC,qBAAK,gBAAgB,KAAK,gBAAgB,EAAE,GAAG,cAAc,KAAK,GAAG,cAAc,KAAK,GAAG,kBAAkB,cAAc;AAAA,cAC7H;AAAA,YACF;AAEA,uBAAW,UAAU,8BAA8B,WAAY;AAC7D,kBAAI,OAAO;AACX,kBAAI,YAAY,KAAK;AAErB,qBAAO,KAAK,SAAS,EAAE,QAAQ,SAAU,IAAI;AAC3C,oBAAI,eAAe,KAAK,cAAc,EAAE;AACxC,oBAAI,mBAAmB,aAAa;AACpC,oBAAI,iBAAiB,aAAa;AAGlC,qBAAK,gBAAgB,UAAU,EAAE,GAAG,aAAa,KAAK,GAAG,aAAa,KAAK,GAAG,kBAAkB,cAAc;AAAA,cAChH,CAAC;AAAA,YACH;AAEA,uBAAW,UAAU,eAAe,SAAU,MAAM;AAClD,kBAAI,KAAK,KAAK;AAEd,kBAAI,KAAK,UAAU,EAAE,KAAK,MAAM;AAC9B,uBAAO,KAAK,UAAU,EAAE;AAAA,cAC1B;AAGA,kBAAI,aAAa,KAAK,SAAS;AAC/B,kBAAI,cAAc,MAAM;AACtB,qBAAK,UAAU,EAAE,IAAI;AACrB,uBAAO;AAAA,cACT;AAEA,kBAAI,WAAW,WAAW,SAAS;AAGnC,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,WAAW,SAAS,CAAC;AAEzB,oBAAI,KAAK,cAAc,QAAQ,IAAI,GAAG;AACpC,uBAAK,UAAU,EAAE,IAAI;AACrB,yBAAO;AAAA,gBACT;AAGA,oBAAI,SAAS,SAAS,KAAK,MAAM;AAC/B,uBAAK,UAAU,SAAS,EAAE,IAAI;AAC9B;AAAA,gBACF;AAEA,oBAAI,CAAC,KAAK,aAAa,QAAQ,GAAG;AAChC,uBAAK,UAAU,EAAE,IAAI;AACrB,yBAAO;AAAA,gBACT;AAAA,cACF;AACA,mBAAK,UAAU,EAAE,IAAI;AACrB,qBAAO;AAAA,YACT;AAGA,uBAAW,UAAU,gBAAgB,SAAU,MAAM;AACnD,kBAAI,KAAK,KAAK;AACd,kBAAI,QAAQ,KAAK,SAAS;AAC1B,kBAAI,SAAS;AAGb,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAI,OAAO,MAAM,CAAC;AAClB,oBAAI,KAAK,UAAU,EAAE,OAAO,KAAK,UAAU,EAAE,IAAI;AAC/C,2BAAS,SAAS;AAAA,gBACpB;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAGA,uBAAW,UAAU,4BAA4B,SAAU,MAAM;AAC/D,kBAAI,SAAS,KAAK,cAAc,IAAI;AACpC,kBAAI,KAAK,SAAS,KAAK,MAAM;AAC3B,uBAAO;AAAA,cACT;AACA,kBAAI,WAAW,KAAK,SAAS,EAAE,SAAS;AACxC,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,QAAQ,SAAS,CAAC;AACtB,0BAAU,KAAK,0BAA0B,KAAK;AAAA,cAChD;AACA,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,wBAAwB,WAAY;AACvD,mBAAK,gBAAgB,CAAC;AACtB,mBAAK,qBAAqB,KAAK,aAAa,QAAQ,EAAE,SAAS,CAAC;AAAA,YAClE;AAEA,uBAAW,UAAU,uBAAuB,SAAU,UAAU;AAC9D,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAI,QAAQ,SAAS,CAAC;AACtB,oBAAI,MAAM,SAAS,KAAK,MAAM;AAC5B,uBAAK,qBAAqB,MAAM,SAAS,EAAE,SAAS,CAAC;AAAA,gBACvD;AACA,oBAAI,KAAK,aAAa,KAAK,GAAG;AAC5B,uBAAK,cAAc,KAAK,KAAK;AAAA,gBAC/B;AAAA,cACF;AAAA,YACF;AAKA,uBAAW,UAAU,kBAAkB,SAAU,cAAc,GAAG,GAAG,0BAA0B,wBAAwB;AACrH,mBAAK;AACL,mBAAK;AAEL,kBAAI,OAAO;AAEX,uBAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AACjD,oBAAI,MAAM,aAAa,KAAK,CAAC;AAC7B,oBAAI;AACJ,oBAAI,YAAY;AAEhB,yBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,sBAAI,QAAQ,IAAI,CAAC;AAEjB,wBAAM,KAAK,IAAI;AACf,wBAAM,KAAK,IAAI;AAEf,uBAAK,MAAM,KAAK,QAAQ,aAAa;AAErC,sBAAI,MAAM,KAAK,SAAS,UAAW,aAAY,MAAM,KAAK;AAAA,gBAC5D;AAEA,qBAAK,YAAY,aAAa;AAAA,cAChC;AAAA,YACF;AAEA,uBAAW,UAAU,sBAAsB,SAAU,eAAe,UAAU;AAC5E,kBAAI,OAAO;AACX,mBAAK,kBAAkB,CAAC;AAExB,qBAAO,KAAK,aAAa,EAAE,QAAQ,SAAU,IAAI;AAE/C,oBAAI,eAAe,SAAS,EAAE;AAE9B,qBAAK,gBAAgB,EAAE,IAAI,KAAK,UAAU,cAAc,EAAE,GAAG,aAAa,cAAc,aAAa,YAAY;AAEjH,6BAAa,KAAK,QAAQ,KAAK,gBAAgB,EAAE,EAAE;AACnD,6BAAa,KAAK,SAAS,KAAK,gBAAgB,EAAE,EAAE;AAAA,cACtD,CAAC;AAAA,YACH;AAEA,uBAAW,UAAU,YAAY,SAAUV,QAAO,UAAU;AAC1D,kBAAI,kBAAkB,cAAc;AACpC,kBAAI,oBAAoB,cAAc;AACtC,kBAAI,eAAe;AAAA,gBACjB,MAAM,CAAC;AAAA,gBACP,UAAU,CAAC;AAAA,gBACX,WAAW,CAAC;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ;AAAA;AAAA,gBACR;AAAA,gBACA;AAAA,cACF;AAGA,cAAAA,OAAM,KAAK,SAAU,IAAI,IAAI;AAC3B,oBAAI,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,OAAQ,QAAO;AAC5E,oBAAI,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,OAAQ,QAAO;AAC5E,uBAAO;AAAA,cACT,CAAC;AAGD,uBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,oBAAI,QAAQA,OAAM,CAAC;AAEnB,oBAAI,aAAa,KAAK,UAAU,GAAG;AACjC,uBAAK,gBAAgB,cAAc,OAAO,GAAG,QAAQ;AAAA,gBACvD,WAAW,KAAK,iBAAiB,cAAc,MAAM,KAAK,OAAO,MAAM,KAAK,MAAM,GAAG;AACnF,uBAAK,gBAAgB,cAAc,OAAO,KAAK,oBAAoB,YAAY,GAAG,QAAQ;AAAA,gBAC5F,OAAO;AACL,uBAAK,gBAAgB,cAAc,OAAO,aAAa,KAAK,QAAQ,QAAQ;AAAA,gBAC9E;AAEA,qBAAK,eAAe,YAAY;AAAA,cAClC;AAEA,qBAAO;AAAA,YACT;AAEA,uBAAW,UAAU,kBAAkB,SAAU,cAAc,MAAM,UAAU,UAAU;AACvF,kBAAI,kBAAkB;AAGtB,kBAAI,YAAY,aAAa,KAAK,QAAQ;AACxC,oBAAI,kBAAkB,CAAC;AAEvB,6BAAa,KAAK,KAAK,eAAe;AACtC,6BAAa,SAAS,KAAK,eAAe;AAC1C,6BAAa,UAAU,KAAK,CAAC;AAAA,cAC/B;AAGA,kBAAI,IAAI,aAAa,SAAS,QAAQ,IAAI,KAAK,KAAK;AAEpD,kBAAI,aAAa,KAAK,QAAQ,EAAE,SAAS,GAAG;AAC1C,qBAAK,aAAa;AAAA,cACpB;AAEA,2BAAa,SAAS,QAAQ,IAAI;AAElC,kBAAI,aAAa,QAAQ,GAAG;AAC1B,6BAAa,QAAQ;AAAA,cACvB;AAGA,kBAAI,IAAI,KAAK,KAAK;AAClB,kBAAI,WAAW,EAAG,MAAK,aAAa;AAEpC,kBAAI,cAAc;AAClB,kBAAI,IAAI,aAAa,UAAU,QAAQ,GAAG;AACxC,8BAAc,aAAa,UAAU,QAAQ;AAC7C,6BAAa,UAAU,QAAQ,IAAI;AACnC,8BAAc,aAAa,UAAU,QAAQ,IAAI;AAAA,cACnD;AAEA,2BAAa,UAAU;AAGvB,2BAAa,KAAK,QAAQ,EAAE,KAAK,IAAI;AAAA,YACvC;AAGA,uBAAW,UAAU,sBAAsB,SAAU,cAAc;AACjE,kBAAI,IAAI;AACR,kBAAI,MAAM,OAAO;AAEjB,uBAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AACjD,oBAAI,aAAa,SAAS,CAAC,IAAI,KAAK;AAClC,sBAAI;AACJ,wBAAM,aAAa,SAAS,CAAC;AAAA,gBAC/B;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAGA,uBAAW,UAAU,qBAAqB,SAAU,cAAc;AAChE,kBAAI,IAAI;AACR,kBAAI,MAAM,OAAO;AAEjB,uBAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AAEjD,oBAAI,aAAa,SAAS,CAAC,IAAI,KAAK;AAClC,sBAAI;AACJ,wBAAM,aAAa,SAAS,CAAC;AAAA,gBAC/B;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAMA,uBAAW,UAAU,mBAAmB,SAAU,cAAc,YAAY,aAAa;AAEvF,kBAAI,MAAM,KAAK,oBAAoB,YAAY;AAE/C,kBAAI,MAAM,GAAG;AACX,uBAAO;AAAA,cACT;AAEA,kBAAI,MAAM,aAAa,SAAS,GAAG;AAEnC,kBAAI,MAAM,aAAa,oBAAoB,cAAc,aAAa,MAAO,QAAO;AAEpF,kBAAI,QAAQ;AAGZ,kBAAI,aAAa,UAAU,GAAG,IAAI,aAAa;AAC7C,oBAAI,MAAM,EAAG,SAAQ,cAAc,aAAa,kBAAkB,aAAa,UAAU,GAAG;AAAA,cAC9F;AAEA,kBAAI;AACJ,kBAAI,aAAa,QAAQ,OAAO,aAAa,aAAa,mBAAmB;AAC3E,oCAAoB,aAAa,SAAS,UAAU,MAAM,aAAa,aAAa;AAAA,cACtF,OAAO;AACL,oCAAoB,aAAa,SAAS,SAAS,aAAa;AAAA,cAClE;AAGA,sBAAQ,cAAc,aAAa;AACnC,kBAAI;AACJ,kBAAI,aAAa,QAAQ,YAAY;AACnC,qCAAqB,aAAa,SAAS,SAAS;AAAA,cACtD,OAAO;AACL,qCAAqB,aAAa,SAAS,SAAS,aAAa;AAAA,cACnE;AAEA,kBAAI,oBAAoB,EAAG,qBAAoB,IAAI;AAEnD,kBAAI,mBAAmB,EAAG,oBAAmB,IAAI;AAEjD,qBAAO,mBAAmB;AAAA,YAC5B;AAIA,uBAAW,UAAU,iBAAiB,SAAU,cAAc;AAC5D,kBAAI,UAAU,KAAK,mBAAmB,YAAY;AAClD,kBAAI,OAAO,aAAa,SAAS,SAAS;AAC1C,kBAAI,MAAM,aAAa,KAAK,OAAO;AACnC,kBAAI,OAAO,IAAI,IAAI,SAAS,CAAC;AAE7B,kBAAI,OAAO,KAAK,QAAQ,aAAa;AAGrC,kBAAI,aAAa,QAAQ,aAAa,SAAS,IAAI,IAAI,QAAQ,WAAW,MAAM;AAE9E,oBAAI,OAAO,IAAI,CAAC;AAGhB,6BAAa,KAAK,IAAI,EAAE,KAAK,IAAI;AAEjC,6BAAa,SAAS,OAAO,IAAI,aAAa,SAAS,OAAO,IAAI;AAClE,6BAAa,SAAS,IAAI,IAAI,aAAa,SAAS,IAAI,IAAI;AAC5D,6BAAa,QAAQ,aAAa,SAAS,SAAS,mBAAmB,YAAY,CAAC;AAGpF,oBAAI,YAAY,OAAO;AACvB,yBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,sBAAI,IAAI,CAAC,EAAE,SAAS,UAAW,aAAY,IAAI,CAAC,EAAE;AAAA,gBACpD;AACA,oBAAI,UAAU,EAAG,cAAa,aAAa;AAE3C,oBAAI,YAAY,aAAa,UAAU,OAAO,IAAI,aAAa,UAAU,IAAI;AAE7E,6BAAa,UAAU,OAAO,IAAI;AAClC,oBAAI,aAAa,UAAU,IAAI,IAAI,KAAK,SAAS,aAAa,gBAAiB,cAAa,UAAU,IAAI,IAAI,KAAK,SAAS,aAAa;AAEzI,oBAAI,aAAa,aAAa,UAAU,OAAO,IAAI,aAAa,UAAU,IAAI;AAC9E,6BAAa,UAAU,aAAa;AAEpC,qBAAK,eAAe,YAAY;AAAA,cAClC;AAAA,YACF;AAEA,uBAAW,UAAU,kBAAkB,WAAY;AACjD,kBAAI,cAAc,MAAM;AAEtB,qBAAK,uBAAuB;AAE5B,qBAAK,eAAe;AAEpB,qBAAK,uBAAuB;AAAA,cAC9B;AAAA,YACF;AAEA,uBAAW,UAAU,mBAAmB,WAAY;AAClD,kBAAI,cAAc,MAAM;AACtB,qBAAK,4BAA4B;AACjC,qBAAK,oBAAoB;AAAA,cAC3B;AAAA,YACF;AAMA,uBAAW,UAAU,cAAc,WAAY;AAC7C,kBAAI,iBAAiB,CAAC;AACtB,kBAAI,eAAe;AACnB,kBAAI;AAEJ,qBAAO,cAAc;AACnB,oBAAI,WAAW,KAAK,aAAa,YAAY;AAC7C,oBAAI,wBAAwB,CAAC;AAC7B,+BAAe;AAEf,yBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,yBAAO,SAAS,CAAC;AACjB,sBAAI,KAAK,SAAS,EAAE,UAAU,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC,EAAE,gBAAgB,KAAK,SAAS,KAAK,MAAM;AAC9F,0CAAsB,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC;AACtE,mCAAe;AAAA,kBACjB;AAAA,gBACF;AACA,oBAAI,gBAAgB,MAAM;AACxB,sBAAI,oBAAoB,CAAC;AACzB,2BAAS,IAAI,GAAG,IAAI,sBAAsB,QAAQ,KAAK;AACrD,wBAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,GAAG;AACtD,wCAAkB,KAAK,sBAAsB,CAAC,CAAC;AAC/C,4CAAsB,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,sBAAsB,CAAC,EAAE,CAAC,CAAC;AAAA,oBAC3E;AAAA,kBACF;AACA,iCAAe,KAAK,iBAAiB;AACrC,uBAAK,aAAa,cAAc;AAChC,uBAAK,aAAa,cAAc;AAAA,gBAClC;AAAA,cACF;AACA,mBAAK,iBAAiB;AAAA,YACxB;AAGA,uBAAW,UAAU,WAAW,SAAU,gBAAgB;AACxD,kBAAI,4BAA4B,eAAe;AAC/C,kBAAI,oBAAoB,eAAe,4BAA4B,CAAC;AAEpE,kBAAI;AACJ,uBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,2BAAW,kBAAkB,CAAC;AAE9B,qBAAK,uBAAuB,QAAQ;AAEpC,yBAAS,CAAC,EAAE,IAAI,SAAS,CAAC,CAAC;AAC3B,yBAAS,CAAC,EAAE,IAAI,SAAS,CAAC,GAAG,SAAS,CAAC,EAAE,QAAQ,SAAS,CAAC,EAAE,MAAM;AAAA,cACrE;AAEA,6BAAe,OAAO,eAAe,SAAS,GAAG,CAAC;AAClD,mBAAK,aAAa,cAAc;AAChC,mBAAK,aAAa,cAAc;AAAA,YAClC;AAGA,uBAAW,UAAU,yBAAyB,SAAU,UAAU;AAEhE,kBAAI;AACJ,kBAAI;AACJ,kBAAI,aAAa,SAAS,CAAC;AAC3B,kBAAI,cAAc,SAAS,CAAC,EAAE,QAAQ;AACpC,gCAAgB,SAAS,CAAC,EAAE;AAAA,cAC9B,OAAO;AACL,gCAAgB,SAAS,CAAC,EAAE;AAAA,cAC9B;AACA,kBAAI,aAAa,cAAc;AAC/B,kBAAI,cAAc,cAAc;AAChC,kBAAI,aAAa,cAAc;AAC/B,kBAAI,cAAc,cAAc;AAEhC,kBAAI,cAAc;AAClB,kBAAI,gBAAgB;AACpB,kBAAI,iBAAiB;AACrB,kBAAI,gBAAgB;AACpB,kBAAI,iBAAiB,CAAC,aAAa,gBAAgB,eAAe,aAAa;AAE/E,kBAAI,aAAa,GAAG;AAClB,yBAAS,IAAI,YAAY,KAAK,aAAa,KAAK;AAC9C,iCAAe,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,aAAa,CAAC,EAAE,SAAS,KAAK,KAAK,CAAC,EAAE,UAAU,EAAE,SAAS;AAAA,gBAC/F;AAAA,cACF;AACA,kBAAI,cAAc,KAAK,KAAK,SAAS,GAAG;AACtC,yBAAS,IAAI,YAAY,KAAK,aAAa,KAAK;AAC9C,iCAAe,CAAC,KAAK,KAAK,KAAK,cAAc,CAAC,EAAE,CAAC,EAAE,SAAS,KAAK,KAAK,WAAW,EAAE,CAAC,EAAE,SAAS;AAAA,gBACjG;AAAA,cACF;AACA,kBAAI,cAAc,KAAK,KAAK,CAAC,EAAE,SAAS,GAAG;AACzC,yBAAS,IAAI,YAAY,KAAK,aAAa,KAAK;AAC9C,iCAAe,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,cAAc,CAAC,EAAE,SAAS,KAAK,KAAK,CAAC,EAAE,WAAW,EAAE,SAAS;AAAA,gBACjG;AAAA,cACF;AACA,kBAAI,aAAa,GAAG;AAClB,yBAAS,IAAI,YAAY,KAAK,aAAa,KAAK;AAC9C,iCAAe,CAAC,KAAK,KAAK,KAAK,aAAa,CAAC,EAAE,CAAC,EAAE,SAAS,KAAK,KAAK,UAAU,EAAE,CAAC,EAAE,SAAS;AAAA,gBAC/F;AAAA,cACF;AACA,kBAAI,MAAM,QAAQ;AAClB,kBAAI;AACJ,kBAAI;AACJ,uBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,oBAAI,eAAe,CAAC,IAAI,KAAK;AAC3B,wBAAM,eAAe,CAAC;AACtB,6BAAW;AACX,6BAAW;AAAA,gBACb,WAAW,eAAe,CAAC,KAAK,KAAK;AACnC;AAAA,gBACF;AAAA,cACF;AAEA,kBAAI,YAAY,KAAK,OAAO,GAAG;AAC7B,oBAAI,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AAC9E,sCAAoB;AAAA,gBACtB,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AACrF,sCAAoB;AAAA,gBACtB,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AACrF,sCAAoB;AAAA,gBACtB,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AACrF,sCAAoB;AAAA,gBACtB;AAAA,cACF,WAAW,YAAY,KAAK,OAAO,GAAG;AACpC,oBAAI,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC;AACzC,oBAAI,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AACpD;AACA,sBAAI,UAAU,GAAG;AACf,wCAAoB;AAAA,kBACtB,OAAO;AACL,wCAAoB;AAAA,kBACtB;AAAA,gBACF,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AAC3D,sBAAI,UAAU,GAAG;AACf,wCAAoB;AAAA,kBACtB,OAAO;AACL,wCAAoB;AAAA,kBACtB;AAAA,gBACF,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AAC3D,sBAAI,UAAU,GAAG;AACf,wCAAoB;AAAA,kBACtB,OAAO;AACL,wCAAoB;AAAA,kBACtB;AAAA,gBACF,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AAC3D,sBAAI,UAAU,GAAG;AACf,wCAAoB;AAAA,kBACtB,OAAO;AACL,wCAAoB;AAAA,kBACtB;AAAA,gBACF,WAAW,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,GAAG;AAC3D,sBAAI,UAAU,GAAG;AACf,wCAAoB;AAAA,kBACtB,OAAO;AACL,wCAAoB;AAAA,kBACtB;AAAA,gBACF,OAAO;AACL,sBAAI,UAAU,GAAG;AACf,wCAAoB;AAAA,kBACtB,OAAO;AACL,wCAAoB;AAAA,kBACtB;AAAA,gBACF;AAAA,cACF,WAAW,YAAY,KAAK,OAAO,GAAG;AACpC,oBAAI,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC;AACzC,oCAAoB;AAAA,cACtB,OAAO;AACL,oCAAoB;AAAA,cACtB;AAEA,kBAAI,qBAAqB,GAAG;AAC1B,2BAAW,UAAU,cAAc,WAAW,GAAG,cAAc,WAAW,IAAI,cAAc,UAAU,IAAI,IAAI,kBAAkB,sBAAsB,WAAW,UAAU,IAAI,CAAC;AAAA,cAClL,WAAW,qBAAqB,GAAG;AACjC,2BAAW,UAAU,cAAc,WAAW,IAAI,cAAc,SAAS,IAAI,IAAI,kBAAkB,sBAAsB,WAAW,SAAS,IAAI,GAAG,cAAc,WAAW,CAAC;AAAA,cAChL,WAAW,qBAAqB,GAAG;AACjC,2BAAW,UAAU,cAAc,WAAW,GAAG,cAAc,WAAW,IAAI,cAAc,UAAU,IAAI,IAAI,kBAAkB,sBAAsB,WAAW,UAAU,IAAI,CAAC;AAAA,cAClL,OAAO;AACL,2BAAW,UAAU,cAAc,WAAW,IAAI,cAAc,SAAS,IAAI,IAAI,kBAAkB,sBAAsB,WAAW,SAAS,IAAI,GAAG,cAAc,WAAW,CAAC;AAAA,cAChL;AAAA,YACF;AAEA,YAAAF,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,WAAW,CAAC;AAEhB,qBAAS,aAAa,oBAAoB,CAAC;AAC3C,qBAAS,gBAAgB,oBAAoB,CAAC;AAC9C,qBAAS,WAAW,oBAAoB,CAAC;AACzC,qBAAS,YAAY,oBAAoB,CAAC;AAC1C,qBAAS,mBAAmB,oBAAoB,CAAC;AACjD,qBAAS,aAAa,oBAAoB,CAAC;AAC3C,qBAAS,WAAW,oBAAoB,CAAC;AAEzC,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA,QACG,CAAC;AAAA;AAAA,IACV,CAAC;AAAA;AAAA;;;ACt6CD;AAAA;AAAA;AAAA,KAAC,gCAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ,mBAAoB;AAAA,eACtC,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,WAAW,GAAG,OAAO;AAAA,eACtB,OAAO,YAAY;AAC1B,gBAAQ,sBAAsB,IAAI,QAAQ,mBAAoB;AAAA;AAE9D,aAAK,sBAAsB,IAAI,QAAQ,KAAK,UAAU,CAAC;AAAA,IACzD,GATC,qCASE,SAAM,SAAS,+BAA+B;AACjD;AAAA;AAAA,QAAiB,SAAS,SAAS;AAEzB,cAAI,mBAAmB,CAAC;AAGxB,mBAAS,oBAAoB,UAAU;AAGtC,gBAAG,iBAAiB,QAAQ,GAAG;AAC9B,qBAAO,iBAAiB,QAAQ,EAAE;AAAA,YACnC;AAEA,gBAAIa,UAAS,iBAAiB,QAAQ,IAAI;AAAA;AAAA,cACzC,GAAG;AAAA;AAAA,cACH,GAAG;AAAA;AAAA,cACH,SAAS,CAAC;AAAA;AAAA,YACX;AAGA,oBAAQ,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAGlF,YAAAA,QAAO,IAAI;AAGX,mBAAOA,QAAO;AAAA,UACf;AArBS;AAyBT,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI,SAAS,OAAO;AAAE,mBAAO;AAAA,UAAO;AAGxD,8BAAoB,IAAI,SAASC,UAAS,MAAM,QAAQ;AACvD,gBAAG,CAAC,oBAAoB,EAAEA,UAAS,IAAI,GAAG;AACzC,qBAAO,eAAeA,UAAS,MAAM;AAAA;AAAA,gBACpC,cAAc;AAAA;AAAA,gBACd,YAAY;AAAA;AAAA,gBACZ,KAAK;AAAA;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD;AAGA,8BAAoB,IAAI,SAASD,SAAQ;AACxC,gBAAI,SAASA,WAAUA,QAAO;AAAA;AAAA,cAC7B,gCAAS,aAAa;AAAE,uBAAOA,QAAO,SAAS;AAAA,cAAG,GAAlD;AAAA;AAAA;AAAA,cACA,gCAAS,mBAAmB;AAAE,uBAAOA;AAAA,cAAQ,GAA7C;AAAA;AACD,gCAAoB,EAAE,QAAQ,KAAK,MAAM;AACzC,mBAAO;AAAA,UACR;AAGA,8BAAoB,IAAI,SAAS,QAAQ,UAAU;AAAE,mBAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,QAAQ;AAAA,UAAG;AAGpH,8BAAoB,IAAI;AAGxB,iBAAO,oBAAoB,oBAAoB,IAAI,CAAC;AAAA,QACrD,EAEC;AAAA;AAAA;AAAA,UAEH,SAASA,SAAQC,UAAS;AAEjC,YAAAD,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAEtD;AAGA,gBAAI,kBAAkB,oBAAoB,CAAC,EAAE,WAAW;AACxD,gBAAI,oBAAoB,oBAAoB,CAAC,EAAE,WAAW;AAC1D,gBAAI,gBAAgB,oBAAoB,CAAC,EAAE;AAC3C,gBAAI,aAAa,oBAAoB,CAAC,EAAE;AACxC,gBAAI,WAAW,oBAAoB,CAAC,EAAE;AACtC,gBAAI,SAAS,oBAAoB,CAAC,EAAE,WAAW;AAC/C,gBAAIC,cAAa,oBAAoB,CAAC,EAAE,WAAW;AAEnD,gBAAI,WAAW;AAAA;AAAA,cAEb,OAAO,gCAAS,QAAQ;AAAA,cAAC,GAAlB;AAAA;AAAA,cAEP,MAAM,gCAAS,OAAO;AAAA,cAAC,GAAjB;AAAA;AAAA;AAAA;AAAA;AAAA,cAKN,SAAS;AAAA;AAAA,cAET,6BAA6B;AAAA;AAAA,cAE7B,SAAS;AAAA;AAAA,cAET,KAAK;AAAA;AAAA,cAEL,SAAS;AAAA;AAAA,cAET,WAAW;AAAA;AAAA,cAEX,eAAe;AAAA;AAAA,cAEf,iBAAiB;AAAA;AAAA,cAEjB,gBAAgB;AAAA;AAAA,cAEhB,eAAe;AAAA;AAAA,cAEf,SAAS;AAAA;AAAA,cAET,SAAS;AAAA;AAAA,cAET,MAAM;AAAA;AAAA,cAEN,SAAS;AAAA;AAAA,cAET,mBAAmB;AAAA;AAAA,cAEnB,uBAAuB;AAAA;AAAA,cAEvB,yBAAyB;AAAA;AAAA,cAEzB,sBAAsB;AAAA;AAAA,cAEtB,iBAAiB;AAAA;AAAA,cAEjB,cAAc;AAAA;AAAA,cAEd,4BAA4B;AAAA,YAC9B;AAEA,qBAAS,OAAOC,WAAU,SAAS;AACjC,kBAAI,MAAM,CAAC;AAEX,uBAAS,KAAKA,WAAU;AACtB,oBAAI,CAAC,IAAIA,UAAS,CAAC;AAAA,cACrB;AAEA,uBAAS,KAAK,SAAS;AACrB,oBAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,cACpB;AAEA,qBAAO;AAAA,YACT;AAZS;AAYR;AAED,qBAAS,YAAY,UAAU;AAC7B,mBAAK,UAAU,OAAO,UAAU,QAAQ;AACxC,6BAAe,KAAK,OAAO;AAAA,YAC7B;AAHS;AAKT,gBAAI,iBAAiB,gCAASC,gBAAe,SAAS;AACpD,kBAAI,QAAQ,iBAAiB,KAAM,eAAc,6BAA6B,kBAAkB,6BAA6B,QAAQ;AACrI,kBAAI,QAAQ,mBAAmB,KAAM,eAAc,sBAAsB,kBAAkB,sBAAsB,QAAQ;AACzH,kBAAI,QAAQ,kBAAkB,KAAM,eAAc,0BAA0B,kBAAkB,0BAA0B,QAAQ;AAChI,kBAAI,QAAQ,iBAAiB,KAAM,eAAc,qCAAqC,kBAAkB,qCAAqC,QAAQ;AACrJ,kBAAI,QAAQ,WAAW,KAAM,eAAc,2BAA2B,kBAAkB,2BAA2B,QAAQ;AAC3H,kBAAI,QAAQ,WAAW,KAAM,eAAc,iBAAiB,kBAAkB,iBAAiB,QAAQ;AACvG,kBAAI,QAAQ,gBAAgB,KAAM,eAAc,+BAA+B,kBAAkB,+BAA+B,QAAQ;AACxI,kBAAI,QAAQ,mBAAmB,KAAM,eAAc,oCAAoC,kBAAkB,oCAAoC,QAAQ;AACrJ,kBAAI,QAAQ,wBAAwB,KAAM,eAAc,wCAAwC,kBAAkB,wCAAwC,QAAQ;AAClK,kBAAI,QAAQ,8BAA8B,KAAM,eAAc,qCAAqC,kBAAkB,qCAAqC,QAAQ;AAElK,kBAAI,QAAQ,WAAW,QAAS,iBAAgB,UAAU;AAAA,uBAAW,QAAQ,WAAW,QAAS,iBAAgB,UAAU;AAAA,kBAAO,iBAAgB,UAAU;AAE5J,4BAAc,iCAAiC,kBAAkB,iCAAiC,gBAAgB,iCAAiC,QAAQ;AAC3J,4BAAc,sBAAsB,kBAAkB,sBAAsB,gBAAgB,sBAAsB,CAAC,QAAQ;AAC3H,4BAAc,UAAU,kBAAkB,UAAU,gBAAgB,UAAU,QAAQ;AACtF,4BAAc,OAAO,QAAQ;AAC7B,4BAAc,0BAA0B,OAAO,QAAQ,0BAA0B,aAAa,QAAQ,sBAAsB,KAAK,IAAI,QAAQ;AAC7I,4BAAc,4BAA4B,OAAO,QAAQ,4BAA4B,aAAa,QAAQ,wBAAwB,KAAK,IAAI,QAAQ;AAAA,YACrJ,GApBqB;AAsBrB,wBAAY,UAAU,MAAM,WAAY;AACtC,kBAAI;AACJ,kBAAI;AACJ,kBAAI,UAAU,KAAK;AACnB,kBAAI,YAAY,KAAK,YAAY,CAAC;AAClC,kBAAI,SAAS,KAAK,SAAS,IAAI,WAAW;AAC1C,kBAAI,OAAO;AAEX,mBAAK,UAAU;AAEf,mBAAK,KAAK,KAAK,QAAQ;AAEvB,mBAAK,GAAG,QAAQ,EAAE,MAAM,eAAe,QAAQ,KAAK,CAAC;AAErD,kBAAI,KAAK,OAAO,gBAAgB;AAChC,mBAAK,KAAK;AAEV,kBAAIC,SAAQ,KAAK,QAAQ,KAAK,MAAM;AACpC,kBAAI,QAAQ,KAAK,QAAQ,KAAK,MAAM;AAEpC,mBAAK,OAAO,GAAG,QAAQ;AACvB,mBAAK,oBAAoB,KAAK,MAAM,KAAK,gBAAgBA,MAAK,GAAG,MAAM;AAEvE,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAI,OAAO,MAAM,CAAC;AAClB,oBAAI,aAAa,KAAK,UAAU,KAAK,KAAK,QAAQ,CAAC;AACnD,oBAAI,aAAa,KAAK,UAAU,KAAK,KAAK,QAAQ,CAAC;AACnD,oBAAI,eAAe,cAAc,WAAW,gBAAgB,UAAU,EAAE,UAAU,GAAG;AACnF,sBAAI,KAAK,GAAG,IAAI,OAAO,QAAQ,GAAG,YAAY,UAAU;AACxD,qBAAG,KAAK,KAAK,GAAG;AAAA,gBAClB;AAAA,cACF;AAEA,kBAAI,eAAe,gCAASC,cAAa,KAAKC,IAAG;AAC/C,oBAAI,OAAO,QAAQ,UAAU;AAC3B,wBAAMA;AAAA,gBACR;AACA,oBAAI,QAAQ,IAAI,KAAK,IAAI;AACzB,oBAAI,QAAQ,KAAK,UAAU,KAAK;AAEhC,uBAAO;AAAA,kBACL,GAAG,MAAM,QAAQ,EAAE,WAAW;AAAA,kBAC9B,GAAG,MAAM,QAAQ,EAAE,WAAW;AAAA,gBAChC;AAAA,cACF,GAXmB;AAgBnB,kBAAI,kBAAkB,gCAASC,mBAAkB;AAE/C,oBAAI,kBAAkB,gCAASC,mBAAkB;AAC/C,sBAAI,QAAQ,KAAK;AACf,4BAAQ,GAAG,IAAI,QAAQ,MAAM,QAAQ,OAAO;AAAA,kBAC9C;AAEA,sBAAI,CAAC,OAAO;AACV,4BAAQ;AACR,yBAAK,GAAG,IAAI,eAAe,QAAQ,KAAK;AACxC,yBAAK,GAAG,QAAQ,EAAE,MAAM,eAAe,QAAQ,KAAK,CAAC;AAAA,kBACvD;AAAA,gBACF,GAVsB;AAYtB,oBAAI,gBAAgB,KAAK,QAAQ;AACjC,oBAAI;AAEJ,yBAASF,KAAI,GAAGA,KAAI,iBAAiB,CAAC,QAAQA,MAAK;AACjD,2BAAS,KAAK,WAAW,KAAK,OAAO,KAAK;AAAA,gBAC5C;AAGA,oBAAI,QAAQ;AAEV,sBAAI,OAAO,mBAAmB,KAAK,CAAC,OAAO,aAAa;AACtD,2BAAO,aAAa;AAAA,kBACtB;AAGA,sBAAI,OAAO,kBAAkB;AAC3B,2BAAO,iBAAiB;AAAA,kBAC1B;AAEA,yBAAO,mBAAmB;AAE1B,uBAAK,QAAQ,KAAK,MAAM,EAAE,UAAU,YAAY;AAEhD,kCAAgB;AAGhB,uBAAK,GAAG,IAAI,cAAc,KAAK,QAAQ,IAAI;AAC3C,uBAAK,GAAG,QAAQ,EAAE,MAAM,cAAc,QAAQ,KAAK,CAAC;AAEpD,sBAAI,SAAS;AACX,yCAAqB,OAAO;AAAA,kBAC9B;AAEA,0BAAQ;AACR;AAAA,gBACF;AAEA,oBAAI,gBAAgB,KAAK,OAAO,iBAAiB;AAIjD,wBAAQ,KAAK,MAAM,EAAE,UAAU,SAAU,KAAKA,IAAG;AAC/C,sBAAI,OAAO,QAAQ,UAAU;AAC3B,0BAAMA;AAAA,kBACR;AAEA,sBAAI,CAAC,IAAI,SAAS,GAAG;AACnB,wBAAI,QAAQ,IAAI,GAAG;AACnB,wBAAI,QAAQ,cAAc,KAAK;AAC/B,wBAAI,OAAO;AAEX,2BAAO,SAAS,MAAM;AACpB,8BAAQ,cAAc,KAAK,KAAK,QAAQ,CAAC,KAAK,cAAc,mBAAmB,KAAK,KAAK,QAAQ,CAAC;AAClG,oCAAc,KAAK,IAAI;AACvB,6BAAO,KAAK,OAAO,EAAE,CAAC;AACtB,0BAAI,QAAQ,QAAW;AACrB;AAAA,sBACF;AAAA,oBACF;AACA,wBAAI,SAAS,MAAM;AACjB,6BAAO;AAAA,wBACL,GAAG,MAAM;AAAA,wBACT,GAAG,MAAM;AAAA,sBACX;AAAA,oBACF,OAAO;AACL,6BAAO;AAAA,wBACL,GAAG,IAAI,SAAS,GAAG;AAAA,wBACnB,GAAG,IAAI,SAAS,GAAG;AAAA,sBACrB;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,CAAC;AAED,gCAAgB;AAEhB,0BAAU,sBAAsBC,gBAAe;AAAA,cACjD,GA1FsB;AA+FtB,qBAAO,YAAY,iBAAiB,WAAY;AAC9C,oBAAI,KAAK,QAAQ,YAAY,UAAU;AACrC,4BAAU,sBAAsB,eAAe;AAAA,gBACjD;AAAA,cACF,CAAC;AAED,qBAAO,UAAU;AAKjB,kBAAI,KAAK,QAAQ,YAAY,UAAU;AACrC,qBAAK,QAAQ,KAAK,MAAM,EAAE,IAAI,SAAS,EAAE,gBAAgB,MAAM,KAAK,SAAS,YAAY;AACzF,wBAAQ;AAAA,cACV;AAEA,qBAAO;AAAA,YACT;AAGA,wBAAY,UAAU,kBAAkB,SAAUH,QAAO;AACvD,kBAAI,WAAW,CAAC;AAChB,uBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,yBAASA,OAAM,CAAC,EAAE,GAAG,CAAC,IAAI;AAAA,cAC5B;AACA,kBAAI,QAAQA,OAAM,OAAO,SAAU,KAAKE,IAAG;AACzC,oBAAI,OAAO,QAAQ,UAAU;AAC3B,wBAAMA;AAAA,gBACR;AACA,oBAAI,SAAS,IAAI,OAAO,EAAE,CAAC;AAC3B,uBAAO,UAAU,MAAM;AACrB,sBAAI,SAAS,OAAO,GAAG,CAAC,GAAG;AACzB,2BAAO;AAAA,kBACT;AACA,2BAAS,OAAO,OAAO,EAAE,CAAC;AAAA,gBAC5B;AACA,uBAAO;AAAA,cACT,CAAC;AAED,qBAAO;AAAA,YACT;AAEA,wBAAY,UAAU,sBAAsB,SAAU,QAAQ,UAAU,QAAQ;AAC9E,kBAAI,OAAO,SAAS;AACpB,uBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,oBAAI,WAAW,SAAS,CAAC;AACzB,oBAAI,uBAAuB,SAAS,SAAS;AAC7C,oBAAI;AAEJ,oBAAI,aAAa,SAAS,iBAAiB;AAAA,kBACzC,6BAA6B,KAAK,QAAQ;AAAA,gBAC5C,CAAC;AAED,oBAAI,SAAS,WAAW,KAAK,QAAQ,SAAS,YAAY,KAAK,MAAM;AACnE,4BAAU,OAAO,IAAI,IAAI,SAAS,OAAO,cAAc,IAAI,OAAO,SAAS,SAAS,GAAG,IAAI,WAAW,IAAI,GAAG,SAAS,SAAS,GAAG,IAAI,WAAW,IAAI,CAAC,GAAG,IAAIL,YAAW,WAAW,WAAW,CAAC,GAAG,WAAW,WAAW,CAAC,CAAC,CAAC,CAAC;AAAA,gBAC9N,OAAO;AACL,4BAAU,OAAO,IAAI,IAAI,SAAS,KAAK,YAAY,CAAC;AAAA,gBACtD;AAEA,wBAAQ,KAAK,SAAS,KAAK,IAAI;AAE/B,wBAAQ,cAAc,SAAS,SAAS,IAAI,SAAS,CAAC;AACtD,wBAAQ,aAAa,SAAS,SAAS,IAAI,SAAS,CAAC;AACrD,wBAAQ,eAAe,SAAS,SAAS,IAAI,SAAS,CAAC;AACvD,wBAAQ,gBAAgB,SAAS,SAAS,IAAI,SAAS,CAAC;AAGxD,oBAAI,KAAK,QAAQ,6BAA6B;AAC5C,sBAAI,SAAS,SAAS,GAAG;AACvB,wBAAI,aAAa,SAAS,YAAY,EAAE,eAAe,MAAM,cAAc,MAAM,CAAC,EAAE;AACpF,wBAAI,cAAc,SAAS,YAAY,EAAE,eAAe,MAAM,cAAc,MAAM,CAAC,EAAE;AACrF,wBAAI,WAAW,SAAS,IAAI,aAAa;AACzC,4BAAQ,aAAa;AACrB,4BAAQ,cAAc;AACtB,4BAAQ,WAAW;AAAA,kBACrB;AAAA,gBACF;AAGA,qBAAK,UAAU,SAAS,KAAK,IAAI,CAAC,IAAI;AAEtC,oBAAI,MAAM,QAAQ,KAAK,CAAC,GAAG;AACzB,0BAAQ,KAAK,IAAI;AAAA,gBACnB;AAEA,oBAAI,MAAM,QAAQ,KAAK,CAAC,GAAG;AACzB,0BAAQ,KAAK,IAAI;AAAA,gBACnB;AAEA,oBAAI,wBAAwB,QAAQ,qBAAqB,SAAS,GAAG;AACnE,sBAAI;AACJ,gCAAc,OAAO,gBAAgB,EAAE,IAAI,OAAO,SAAS,GAAG,OAAO;AACrE,uBAAK,oBAAoB,aAAa,sBAAsB,MAAM;AAAA,gBACpE;AAAA,cACF;AAAA,YACF;AAKA,wBAAY,UAAU,OAAO,WAAY;AACvC,mBAAK,UAAU;AAEf,qBAAO;AAAA,YACT;AAEA,gBAAI,WAAW,gCAASQ,UAASC,YAAW;AAG1C,cAAAA,WAAU,UAAU,gBAAgB,WAAW;AAAA,YACjD,GAJe;AAOf,gBAAI,OAAO,cAAc,aAAa;AACpC,uBAAS,SAAS;AAAA,YACpB;AAEA,YAAAX,QAAO,UAAU;AAAA,UAEX;AAAA;AAAA,QACG,CAAC;AAAA;AAAA,IACV,CAAC;AAAA;AAAA;;;AChYD,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEY,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AACzR,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,WAAU,GAAE,cAAa,GAAE,aAAY,GAAE,MAAK,GAAE,WAAU,GAAE,YAAW,GAAE,QAAO,IAAG,OAAM,IAAG,aAAY,IAAG,aAAY,IAAG,QAAO,IAAG,QAAO,IAAG,SAAQ,IAAG,cAAa,IAAG,iBAAgB,IAAG,eAAc,IAAG,cAAa,IAAG,aAAY,IAAG,WAAU,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IAC5S,YAAY,EAAC,GAAE,SAAQ,GAAE,aAAY,GAAE,MAAK,GAAE,WAAU,IAAG,OAAM,IAAG,aAAY,IAAG,QAAO,IAAG,SAAQ,IAAG,eAAc,IAAG,cAAa,IAAG,aAAY,IAAG,UAAS;AAAA,IACjK,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACpM,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AAAA,QAAG,KAAK;AACZ,iBAAO;AACR;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,UAAU;AAC/B;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,WAAW;AAChC;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,WAAW;AAChC;AAAA,QACA,KAAK;AACL,aAAG,UAAU,EAAE,MAAM,YAAY;AACjC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,KAAK,UAAS,GAAG,EAAE,EAAE,EAAE;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,EAAE,QAAQ,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI;AACzG;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,UAAS,GAAG,EAAE,CAAC;AAAE,aAAG,aAAa,EAAC,MAAM,GAAG,EAAE,EAAC,CAAC;AACrE;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,aAAG,aAAa,EAAC,OAAO,GAAG,EAAE,EAAC,CAAC;AAChC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,WAAW;AACjC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,UAAS,GAAG,EAAE,EAAE,EAAE;AAAE,aAAG,QAAQ,GAAG,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,IAAI;AAC5F;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,EAAC,MAAM,GAAG,EAAE,EAAC,CAAC;AAC/B;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,iBAAiB,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAI,EAAE,IAAI,GAAG,KAAG,CAAC,GAAG,OAAO,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC/H;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAE,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS,QAAQ;AACjE;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,EAAE,MAAM,iBAAiB,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAI,EAAE,IAAI,GAAG,KAAG,CAAC,GAAG,OAAO,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAC/H;AAAA,MACA;AAAA,IACA,GAhDe;AAAA,IAiDf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,IAAG,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,IAAG,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAC54B,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC;AAAA,IAChC,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAIC,QAAO,KAAK;AAChB,YAAIA,MAAK,SAAS,IAAI;AAClB,UAAAA,SAAQ,KAAK,OAAO,OAAO,GAAG,KAAGA,MAAK,MAAM;AAAA,QAChD;AACA,gBAAQA,MAAK,OAAO,GAAE,EAAE,KAAKA,MAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAG7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,iBAAgB,IAAI,MAAM;AAAG,mBAAO;AAChE;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,OAAO;AAC1B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,mBAAO;AAC/B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,eAAG,UAAU,EAAE,MAAM,YAAY;AAAE,iBAAK,MAAM,MAAM;AAC5D;AAAA,UACA,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,WAAW;AAAE,mBAAO;AAChD;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAE,eAAG,UAAU,EAAE,MAAM,UAAU;AAAE,iBAAK,SAAS;AACtD;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AAC3E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,OAAO;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AAClE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AAC3E;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,YAAY;AAAG,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACvE;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAC3B;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,eAAe;AAAE,iBAAK,MAAM,MAAM;AAChE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,gBAAgB,IAAI,MAAM;AAAG,mBAAO;AAClE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,YAAY;AAAE,mBAAO;AAClE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,gBAAe,IAAI,MAAM;AAAE,mBAAO;AAC/E;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAE,eAAG,UAAU,EAAE,MAAM,aAAa;AAAE,mBAAO;AACnE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,qBAAqB,IAAI,MAAM;AAAK,mBAAO;AACzE;AAAA,UACA,KAAK;AAAI,eAAG,UAAU,EAAE,MAAM,qBAAqB,IAAI,MAAM;AAAK,mBAAO;AACzE;AAAA,QACA;AAAA,MACA,GAhFe;AAAA,MAiFf,OAAO,CAAC,iBAAgB,mBAAkB,aAAY,YAAW,YAAW,kBAAiB,mBAAkB,eAAc,gBAAe,YAAW,aAAY,aAAY,cAAa,YAAW,cAAa,cAAa,YAAW,YAAW,eAAc,0BAAyB,WAAU,gBAAe,gBAAe,gBAAe,aAAY,eAAc,aAAY,gBAAe,cAAa,cAAa,cAAa,aAAY,aAAY,cAAa,YAAW,sBAAqB,kBAAkB;AAAA,MACxgB,YAAY,EAAC,SAAQ,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IACnV;AACA,WAAOD;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,kBAAQ;;;ACtrBhB,IAAI,QAAuB,CAAC;AAC5B,IAAI,MAAM;AACV,IAAI,WAAsC,CAAC;AAE3C,IAAM,QAAQ,6BAAM;AAClB,UAAQ,CAAC;AACT,QAAM;AACN,aAAW,CAAC;AACd,GAJc;AAMd,IAAM,YAAY,gCAAU,OAAe;AACzC,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,QAAI,MAAM,CAAC,EAAE,QAAQ,OAAO;AAC1B,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT,GARkB;AAUlB,IAAM,aAAa,6BAAM;AACvB,SAAO,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AACvC,GAFmB;AAInB,IAAM,UAAU,wBAAC,OAAe,IAAY,OAAe,SAAiB;AAC1E,MAAI,KAAK,WAAW,OAAO,IAAI,OAAO,IAAI;AAC1C,QAAM,OAAO,UAAU;AACvB,MAAI,UAAkB,KAAK,SAAS,WAAW,sBAAc,QAAQ;AACrE,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AAAA,IACd,KAAK,SAAS;AACZ,iBAAW;AAAA,EACf;AAEA,QAAM,OAAO;AAAA,IACX,IAAI;AAAA,IACJ,QAAQ,aAAa,IAAI,IAAI;AAAA,IAC7B;AAAA,IACA,OAAO,aAAa,OAAO,IAAI;AAAA,IAC/B;AAAA,IACA,UAAU,CAAC;AAAA,IACX,OAAO,KAAK,SAAS,gBAAgB,sBAAc,QAAQ;AAAA,IAC3D;AAAA,EACF;AAEA,QAAM,SAAS,UAAU,KAAK;AAC9B,MAAI,QAAQ;AACV,WAAO,SAAS,KAAK,IAAI;AAEzB,UAAM,KAAK,IAAI;AAAA,EACjB,OAAO;AACL,QAAI,MAAM,WAAW,GAAG;AAEtB,YAAM,KAAK,IAAI;AAAA,IACjB,OAAO;AAEL,YAAM,IAAI;AAAA,QACR,gEAAgE,KAAK,QAAQ;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AACF,GAtCgB;AAwChB,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AACX;AAEA,IAAM,UAAU,wBAAC,UAAkB,WAA2B;AAC5D,MAAI,MAAM,eAAe,UAAU,MAAM;AACzC,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,WAAW,MAAM,SAAS,eAAe,SAAS;AAAA,IAC3D,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB;AACE,aAAO,SAAS;AAAA,EACpB;AACF,GAlBgB;AAoBhB,IAAM,kBAAkB,wBAAC,IAAY,YAAuB;AAC1D,WAAS,EAAE,IAAI;AACjB,GAFwB;AAIxB,IAAM,eAAe,wBAAC,eAAmD;AACvE,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AACA,QAAM,SAAS,UAAU;AACzB,QAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AACnC,MAAI,WAAW,MAAM;AACnB,SAAK,OAAO,aAAa,WAAW,MAAM,MAAM;AAAA,EAClD;AACA,MAAI,WAAW,OAAO;AACpB,SAAK,QAAQ,aAAa,WAAW,OAAO,MAAM;AAAA,EACpD;AACF,GAZqB;AAcrB,IAAM,WAAW,wBAAC,SAAiB;AACjC,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF,GAnBiB;AAsBjB,IAAM,YAAY,6BAAM,KAAN;AAClB,IAAM,iBAAiB,wBAAC,OAAe,SAAS,EAAE,GAA3B;AAEvB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,oBAAQ;;;AC5Jf,oCAAwB;;;ACIxB,IAAM,eAAe;AASrB,IAAM,aAA4B,gCAAUG,KAAI,MAAM,MAAM,SAAS;AACnE,QAAM,KAAK;AACX,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD;AAAA,IACC;AAAA,IACA,MAAM,KAAK,SAAS,EAAE,KAAK,CAAC,KAAK,SAAS,IAAI,EAAE,gBAC9C,KAAK,QAAQ,IAAI,EACnB,cAAc,KAAK,SAAS,EAAE;AAAA,EAChC;AAEF,OACG,OAAO,MAAM,EACb,KAAK,SAAS,eAAe,OAAO,EACpC,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,KAAK,MAAM,EACtB,KAAK,MAAM,KAAK,KAAK,EACrB,KAAK,MAAM,KAAK,MAAM;AAC3B,GApBkC;AAsBlC,IAAM,UAAyB,gCAAUA,KAAI,MAAM,MAAM;AACvD,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD,KAAK,UAAU,KAAK,MAAM,EAC1B,KAAK,SAAS,KAAK,KAAK;AAC7B,GAP+B;AAS/B,IAAM,WAA0B,gCAAUA,KAAI,MAAM,MAAM;AACxD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,MAAM;AACjB,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD;AAAA,IACC;AAAA,IACA,SAAS,EAAE,IAAI,EAAE,UAAU,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,SAChD,EAAE,IAAI,EAAE,UAAU,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,SACzC,EAAE,IAAI,EAAE,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA;AAAA,SAEzC,EAAE,IAAI,EAAE,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SAC1C,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA;AAAA,SAE/C,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SAC3C,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI,CAAC;AAAA,SACnC,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA,SAEhD,EAAE,IAAI,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA,SAC/C,EAAE,IAAI,EAAE,UAAU,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA;AAAA,EAG/C;AACJ,GA7BgC;AA+BhC,IAAM,UAAyB,gCAAUA,KAAI,MAAM,MAAM;AACvD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,OAAO;AACjB,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD;AAAA,IACC;AAAA,IACA,SAAS,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,SAC9C,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,SAC7B,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,CAAC;AAAA,SAC7B,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG;AAAA;AAAA,SAEvC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SACxC,IAAI,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,IAAI,IAAI;AAAA,SAC7C,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA;AAAA,SAE7C,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,SACzC,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,SAClC,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,SAClC,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA,SAE9C,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA,SAC7C,IAAI,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,KAAK,IAAI,IAAI;AAAA,SAC9C,CAAC,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAAA;AAAA;AAAA,EAG7C;AACJ,GA9B+B;AAgC/B,IAAM,YAA2B,gCAAUA,KAAI,MAAM,MAAM;AACzD,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD,KAAK,KAAK,KAAK,QAAQ,CAAC;AAC7B,GANiC;AAQjC,SAAS,mBACP,QACA,GACA,GACA,QACA,MACA;AACA,SAAO,OACJ,OAAO,WAAW,cAAc,EAChC;AAAA,IACC;AAAA,IACA,OACG,IAAI,SAAU,GAAG;AAChB,aAAO,EAAE,IAAI,MAAM,EAAE;AAAA,IACvB,CAAC,EACA,KAAK,GAAG;AAAA,EACb,EACC,KAAK,aAAa,gBAAgB,KAAK,QAAQ,KAAK,IAAI,OAAO,IAAI,GAAG;AAC3E;AAlBS;AAoBT,IAAM,aAA4B,gCAChC,KACA,MACA,MACA;AACA,QAAM,IAAI,KAAK;AACf,QAAM,IAAI;AACV,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,QAAQ,KAAK,UAAU,IAAI;AAC1C,QAAM,SAAkB;AAAA,IACtB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACb,EAAE,GAAG,IAAI,GAAG,GAAG,EAAE;AAAA,IACjB,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,IAClB,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,EAAE;AAAA,IAClB,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IACd,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE;AAAA,EACpB;AACA,qBAAmB,MAAM,GAAG,GAAG,QAAQ,IAAI;AAC7C,GAlBkC;AAoBlC,IAAM,iBAAgC,gCAAUA,KAAI,MAAM,MAAM;AAC9D,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmBA,IAAG,SAAS,KAAK,IAAI,CAAC,EACvD,KAAK,UAAU,KAAK,MAAM,EAC1B,KAAK,MAAM,KAAK,OAAO,EACvB,KAAK,MAAM,KAAK,OAAO,EACvB,KAAK,SAAS,KAAK,KAAK;AAC7B,GATsC;AAmB/B,IAAM,WAAW,sCACtBA,KACA,MACA,MACA,aACA,MACiB;AACjB,QAAM,aAAa,KAAK;AACxB,QAAM,UAAU,eAAe,eAAe;AAC9C,QAAM,WAAW,KAAK,OAAO,GAAG;AAChC,OAAK,UAAU;AACf,MAAI,eAAe,aAAa;AAChC,MAAI,UAAU,GAAG;AACf,oBAAgB;AAAA,EAClB;AACA,WAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,QAAQ,MAAM,MAAM,kBAAkB,YAAY;AAC5F,QAAM,UAAU,SAAS,OAAO,GAAG;AAGnC,QAAM,WAAW,SAAS,OAAO,GAAG;AACpC,QAAM,cAAc,KAAK,MAAM,QAAQ,cAAc,IAAI;AACzD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,MACE,eAAe;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA;AAAA,EACF;AAEA,MAAI,CAAC,YAAY;AACf,aACG,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ;AAAA,EACjC;AACA,QAAM,OAAO,SAAS,KAAK,EAAE,QAAQ;AACrC,QAAM,CAAC,QAAQ,IAAI,cAAc,KAAK,QAAQ;AAC9C,OAAK,SAAS,KAAK,SAAS,WAAY,MAAM,MAAM,KAAK;AACzD,OAAK,QAAQ,KAAK,QAAQ,IAAI,KAAK;AACnC,MAAI,KAAK,MAAM;AACb,QAAI,KAAK,SAASA,IAAG,SAAS,QAAQ;AACpC,WAAK,UAAU;AACf,WAAK,SAAS;AACd,YAAM,OAAO,SACV,OAAO,eAAe,EACtB,KAAK,UAAU,MAAM,EACrB,KAAK,SAAS,KAAK,KAAK,EACxB,KAAK,SAAS,qBAAqB;AACtC,WACG,OAAO,KAAK,EACZ,KAAK,SAAS,gBAAgB,EAC9B,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,UAAU,MAAM,KAAK,IAAI;AACzD,eAAS;AAAA,QACP;AAAA,QACA,eAAe,KAAK,QAAQ,IAAI,QAAQ,KAAK,SAAS,IAAI,MAAM,KAAK,WAAW;AAAA,MAClF;AAAA,IACF,OAAO;AACL,WAAK,SAAS;AACd,YAAM,YAAY,KAAK;AACvB,WAAK,SAAS,KAAK,IAAI,WAAW,EAAE;AACpC,YAAM,aAAa,KAAK,IAAI,KAAK,SAAS,SAAS;AACnD,YAAM,OAAO,SACV,OAAO,eAAe,EACtB,KAAK,SAAS,MAAM,EACpB,KAAK,UAAU,KAAK,MAAM,EAC1B,KAAK,SAAS,mCAAmC,aAAa,IAAI,KAAK;AAE1E,WACG,OAAO,KAAK,EACZ,KAAK,SAAS,gBAAgB,EAC9B,OAAO,GAAG,EACV,KAAK,SAAS,eAAe,UAAU,MAAM,KAAK,IAAI;AACzD,eAAS;AAAA,QACP;AAAA,QACA,gBAAgB,KAAK,KAAK,QAAQ,KAAK,QAAQ,aAAa,IAAI,KAAK,UAAU,KAAK;AAAA,MACtF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,CAAC,YAAY;AACf,YAAM,KAAK,KAAK,QAAQ;AACxB,YAAM,KAAK,KAAK,UAAU;AAC1B,eAAS,KAAK,aAAa,eAAe,KAAK,OAAO,KAAK,GAAG;AAAA,IAEhE,OAAO;AACL,YAAM,MAAM,KAAK,QAAQ,KAAK,SAAS;AACvC,YAAM,MAAM,KAAK,SAAS,KAAK,UAAU;AACzC,eAAS,KAAK,aAAa,eAAe,KAAK,OAAO,KAAK,GAAG;AAAA,IAChE;AAAA,EACF;AAEA,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAKA,IAAG,SAAS;AACf,iBAAWA,KAAI,SAAS,MAAM,OAAO;AACrC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,qBAAeA,KAAI,SAAS,MAAM,OAAO;AACzC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,cAAQA,KAAI,SAAS,MAAM,OAAO;AAClC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,cAAQ,KAAK,aAAa,eAAe,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AACvF,gBAAUA,KAAI,SAAS,MAAM,OAAO;AACpC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,eAASA,KAAI,SAAS,MAAM,OAAO;AACnC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,cAAQA,KAAI,SAAS,MAAM,OAAO;AAClC;AAAA,IACF,KAAKA,IAAG,SAAS;AACf,iBAAWA,KAAI,SAAS,MAAM,OAAO;AACrC;AAAA,EACJ;AAEA,EAAAA,IAAG,gBAAgB,KAAK,IAAI,QAAQ;AACpC,SAAO,KAAK;AACd,GA1HwB;AA4HjB,IAAM,eAAe,gCAAUA,KAAe,MAAyB;AAC5E,QAAM,WAAWA,IAAG,eAAe,KAAK,EAAE;AAE1C,QAAM,IAAI,KAAK,KAAK;AACpB,QAAM,IAAI,KAAK,KAAK;AAEpB,WAAS,KAAK,aAAa,eAAe,IAAI,MAAM,IAAI,GAAG;AAC7D,GAP4B;;;AD5R5BC,WAAU,IAAI,8BAAAC,OAAW;AAEzB,eAAe,UACbC,KACA,KACA,SACA,SACA,MACA;AACA,QAAM,SAASA,KAAI,KAAK,SAAS,SAAS,IAAI;AAC9C,MAAI,QAAQ,UAAU;AACpB,UAAM,QAAQ;AAAA,MACZ,QAAQ,SAAS;AAAA,QAAI,CAAC,OAAO,UAC3B,UAAUA,KAAI,KAAK,OAAO,UAAU,IAAI,QAAQ,SAAS,IAAI;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF;AAfe;AAiCf,SAAS,UAAU,SAAoB,IAAoB;AACzD,KAAG,MAAM,EAAE,IAAI,CAAC,MAAM,OAAO;AAC3B,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,KAAK,CAAC,EAAE,SAAS,YAAY;AAC/B,YAAM,SAAS,KAAK,CAAC,EAAE,SAAS;AAChC,UAAI,MAAM,UAAU,IAAI,IAAI;AAC5B,cACG,OAAO,MAAM,EACb;AAAA,QACC;AAAA,QACA,KAAK,OAAO,MAAM,IAAI,OAAO,MAAM,MAAM,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MACpG,EACC,KAAK,SAAS,uBAAuB,KAAK,UAAU,iBAAiB,KAAK,KAAK;AAAA,IACpF;AAAA,EACF,CAAC;AACH;AAfS;AAiBT,SAAS,SAAS,SAAsB,IAAoB,MAAqB,OAAe;AAC9F,KAAG,IAAI;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,MACJ,IAAI,QAAQ,GAAG,SAAS;AAAA,MACxB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,QAAQ,QAAQ;AAAA,MAChB,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ;AAAA,IAChB;AAAA,IACA,UAAU;AAAA,MACR,GAAG,QAAQ;AAAA,MACX,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,UAAU;AACpB,YAAQ,SAAS,QAAQ,CAAC,UAAU;AAClC,eAAS,OAAO,IAAI,MAAM,QAAQ,CAAC;AACnC,SAAG,IAAI;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,IAAI,GAAG,QAAQ,EAAE,IAAI,MAAM,EAAE;AAAA,UAC7B,QAAQ,QAAQ;AAAA,UAChB,QAAQ,MAAM;AAAA,UACd,OAAO;AAAA,UACP,SAAS,MAAM;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAjCS;AAmCT,SAAS,cAAc,MAAmB,MAA8C;AACtF,SAAO,IAAI,QAAQ,CAAC,YAAY;AAE9B,UAAM,WAAW,eAAO,MAAM,EAAE,OAAO,KAAK,EAAE,KAAK,MAAM,IAAI,EAAE,KAAK,SAAS,cAAc;AAC3F,UAAM,KAAKF,WAAU;AAAA,MACnB,WAAW,SAAS,eAAe,IAAI;AAAA;AAAA,MACvC,OAAO;AAAA,QACL;AAAA,UACE,UAAU;AAAA,UACV,OAAO;AAAA,YACL,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAED,aAAS,OAAO;AAChB,aAAS,MAAM,IAAI,MAAM,CAAC;AAG1B,OAAG,MAAM,EAAE,QAAQ,SAAU,GAAG;AAC9B,QAAE,mBAAmB,MAAM;AACzB,cAAM,OAAO,EAAE,KAAK;AACpB,eAAO,EAAE,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO;AAAA,MACzC;AAAA,IACF,CAAC;AAED,OAAG,OAAO;AAAA,MACR,MAAM;AAAA;AAAA,MAEN,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,IACX,CAAC,EAAE,IAAI;AACP,OAAG,MAAM,CAAC,MAAM;AACd,UAAI,KAAK,SAAS,CAAC;AACnB,cAAQ,EAAE;AAAA,IACZ,CAAC;AAAA,EACH,CAAC;AACH;AAvCS;AAyCT,SAAS,cAAcE,KAAe,IAAoB;AACxD,KAAG,MAAM,EAAE,IAAI,CAAC,MAAM,OAAO;AAC3B,UAAM,OAAO,KAAK,KAAK;AACvB,SAAK,IAAI,KAAK,SAAS,EAAE;AACzB,SAAK,IAAI,KAAK,SAAS,EAAE;AACzB,iBAAaA,KAAI,IAAI;AACrB,UAAM,KAAKA,IAAG,eAAe,KAAK,MAAM;AACxC,QAAI,KAAK,OAAO,IAAI,eAAe,KAAK,SAAS,EAAE,GAAG,MAAM,KAAK,SAAS,EAAE,GAAG,KAAK,IAAI;AACxF,OAAG;AAAA,MACD;AAAA,MACA,aAAa,KAAK,SAAS,EAAE,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE,IAAI,KAAK,SAAS,CAAC;AAAA,IACzF;AACA,OAAG,KAAK,QAAQ,OAAO,EAAE,GAAG;AAAA,EAC9B,CAAC;AACH;AAdS;AAgBF,IAAM,OAAuB,8BAAO,MAAM,IAAI,UAAU,YAAY;AACzE,MAAI,MAAM,gCAAgC,IAAI;AAE9C,QAAMA,MAAK,QAAQ;AACnB,QAAM,KAAKA,IAAG,WAAW;AACzB,MAAI,CAAC,IAAI;AACP;AAAA,EACF;AAEA,QAAM,OAAO,UAAU;AACvB,OAAK,aAAa;AAElB,QAAM,MAAM,iBAAiB,EAAE;AAK/B,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAU,KAAK,SAAS,eAAe;AACvC,QAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAU,KAAK,SAAS,eAAe;AACvC,QAAM,UAAUA,KAAI,WAAW,IAAyB,IAAI,IAAI;AAIhE,QAAM,KAAK,MAAM,cAAc,IAAI,IAAI;AAGvC,YAAU,WAAW,EAAE;AACvB,gBAAcA,KAAI,EAAE;AAGpB;AAAA,IACE;AAAA,IACA;AAAA,IACA,KAAK,SAAS,WAAW,sBAAc,QAAQ;AAAA,IAC/C,KAAK,SAAS,eAAe,sBAAc,QAAQ;AAAA,EACrD;AACF,GAtCoC;AAwCpC,IAAO,0BAAQ;AAAA,EACb;AACF;;;AEtMA,IAAM,cAAqC,wBAAC,YAAY;AACtD,MAAI,WAAW;AAEf,WAAS,IAAI,GAAG,IAAI,QAAQ,mBAAmB,KAAK;AAClD,YAAQ,cAAc,CAAC,IAAI,QAAQ,cAAc,CAAC,KAAK,QAAQ,cAAc,CAAC;AAC9E,QAAI,gBAAO,QAAQ,cAAc,CAAC,CAAC,GAAG;AACpC,cAAQ,cAAc,CAAC,IAAI,gBAAQ,QAAQ,cAAc,CAAC,GAAG,EAAE;AAAA,IACjE,OAAO;AACL,cAAQ,cAAc,CAAC,IAAI,eAAO,QAAQ,cAAc,CAAC,GAAG,EAAE;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,mBAAmB,KAAK;AAClD,UAAM,KAAK,MAAM,KAAK,IAAI;AAC1B,gBAAY;AAAA,eACD,IAAI,CAAC,mBAAmB,IAAI,CAAC,mBAAmB,IAAI,CAAC,qBAC9D,IAAI,CACN,sBAAsB,IAAI,CAAC;AAAA,cACjB,QAAQ,WAAW,CAAC,CAAC;AAAA;AAAA,eAEpB,IAAI,CAAC;AAAA,aACP,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA,iBAEtB,IAAI,CAAC;AAAA;AAAA,eAEP,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA,oBAErB,IAAI,CAAC;AAAA,gBACT,QAAQ,WAAW,CAAC,CAAC;AAAA;AAAA,kBAEnB,IAAI,CAAC;AAAA,sBACD,EAAE;AAAA;AAAA,eAET,IAAI,CAAC;AAAA,gBACJ,QAAQ,cAAc,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtC;AACA,SAAO;AACT,GA/C2C;AAkD3C,IAAM,YAAmC,wBAAC,YACxC;AAAA;AAAA;AAAA;AAAA,IAIE,YAAY,OAAO,CAAC;AAAA;AAAA,YAEZ,QAAQ,IAAI;AAAA;AAAA;AAAA,YAGZ,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAVM;AA6BzC,IAAO,iBAAQ;;;AC5ER,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["module", "exports", "edge", "nodes", "Point", "instance", "nodeFrom", "add", "next", "_remove", "LinkedList", "Layout", "DimensionD", "Quicksort", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layoutBase", "module", "exports", "nodes", "pred1", "next", "processed", "Point", "Layout", "edge", "p_id", "parent", "i", "node", "module", "exports", "DimensionD", "defaults", "getUserOptions", "nodes", "getPositions", "i", "iterateAnimated", "afterReposition", "register", "cytoscape", "o", "parser", "lexer", "next", "db", "cytoscape", "coseBilkent", "db"]}