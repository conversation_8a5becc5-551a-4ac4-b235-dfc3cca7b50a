{"version": 3, "sources": ["../../../src/diagrams/timeline/parser/timeline.jison", "../../../src/diagrams/timeline/timelineDb.js", "../../../src/diagrams/timeline/timelineRenderer.ts", "../../../src/diagrams/timeline/svgDraw.js", "../../../src/diagrams/timeline/styles.js", "../../../src/diagrams/timeline/timeline-definition.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[6,8,10,11,12,14,16,17,20,21],$V1=[1,9],$V2=[1,10],$V3=[1,11],$V4=[1,12],$V5=[1,13],$V6=[1,16],$V7=[1,17];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"timeline\":4,\"document\":5,\"EOF\":6,\"line\":7,\"SPACE\":8,\"statement\":9,\"NEWLINE\":10,\"title\":11,\"acc_title\":12,\"acc_title_value\":13,\"acc_descr\":14,\"acc_descr_value\":15,\"acc_descr_multiline_value\":16,\"section\":17,\"period_statement\":18,\"event_statement\":19,\"period\":20,\"event\":21,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"timeline\",6:\"EOF\",8:\"SPACE\",10:\"NEWLINE\",11:\"title\",12:\"acc_title\",13:\"acc_title_value\",14:\"acc_descr\",15:\"acc_descr_value\",16:\"acc_descr_multiline_value\",17:\"section\",20:\"period\",21:\"event\"},\nproductions_: [0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1:\n return $$[$0-1]; \nbreak;\ncase 2:\n this.$ = [] \nbreak;\ncase 3:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 4: case 5:\n this.$ = $$[$0] \nbreak;\ncase 6: case 7:\n this.$=[];\nbreak;\ncase 8:\nyy.getCommonDb().setDiagramTitle($$[$0].substr(6));this.$=$$[$0].substr(6);\nbreak;\ncase 9:\n this.$=$$[$0].trim();yy.getCommonDb().setAccTitle(this.$); \nbreak;\ncase 10: case 11:\n this.$=$$[$0].trim();yy.getCommonDb().setAccDescription(this.$); \nbreak;\ncase 12:\nyy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8);\nbreak;\ncase 15:\nyy.addTask($$[$0],0,'');this.$=$$[$0];\nbreak;\ncase 16:\nyy.addEvent($$[$0].substr(2));this.$=$$[$0];\nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},o($V0,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:14,19:15,20:$V6,21:$V7},o($V0,[2,7],{1:[2,1]}),o($V0,[2,3]),{9:18,11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:14,19:15,20:$V6,21:$V7},o($V0,[2,5]),o($V0,[2,6]),o($V0,[2,8]),{13:[1,19]},{15:[1,20]},o($V0,[2,11]),o($V0,[2,12]),o($V0,[2,13]),o($V0,[2,14]),o($V0,[2,15]),o($V0,[2,16]),o($V0,[2,4]),o($V0,[2,9]),o($V0,[2,10])],\ndefaultActions: {},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2:return 10;\nbreak;\ncase 3:/* skip whitespace */\nbreak;\ncase 4:/* skip comments */\nbreak;\ncase 5:return 4;\nbreak;\ncase 6:return 11;\nbreak;\ncase 7: this.begin(\"acc_title\");return 12; \nbreak;\ncase 8: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 9: this.begin(\"acc_descr\");return 14; \nbreak;\ncase 10: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 11: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 12: this.popState(); \nbreak;\ncase 13:return \"acc_descr_multiline_value\";\nbreak;\ncase 14:return 17;\nbreak;\ncase 15:return 21;\nbreak;\ncase 16:return 20;\nbreak;\ncase 17:return 6;\nbreak;\ncase 18:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:#[^\\n]*)/i,/^(?:timeline\\b)/i,/^(?:title\\s[^\\n]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:section\\s[^:\\n]+)/i,/^(?::\\s[^:\\n]+)/i,/^(?:[^#:\\n]+)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[12,13],\"inclusive\":false},\"acc_descr\":{\"rules\":[10],\"inclusive\":false},\"acc_title\":{\"rules\":[8],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import * as commonDb from '../common/commonDb.js';\nlet currentSection = '';\nlet currentTaskId = 0;\n\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\n\nexport const getCommonDb = () => commonDb;\n\nexport const clear = function () {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = '';\n  rawTasks.length = 0;\n  commonDb.clear();\n};\n\nexport const addSection = function (txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\n\nexport const getSections = function () {\n  return sections;\n};\n\nexport const getTasks = function () {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n\n  tasks.push(...rawTasks);\n\n  return tasks;\n};\n\nexport const addTask = function (period, length, event) {\n  const rawTask = {\n    id: currentTaskId++,\n    section: currentSection,\n    type: currentSection,\n    task: period,\n    score: length ? length : 0,\n    //if event is defined, then add it the events array\n    events: event ? [event] : [],\n  };\n  rawTasks.push(rawTask);\n};\n\nexport const addEvent = function (event) {\n  // fetch current task with currentTaskId\n  const currentTask = rawTasks.find((task) => task.id === currentTaskId - 1);\n  //add event to the events array\n  currentTask.events.push(event);\n};\n\nexport const addTaskOrg = function (descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: [],\n  };\n  tasks.push(newTask);\n};\n\n/**\n * Compiles the raw tasks into a list of tasks with events\n * @returns {boolean} true if all items are processed\n * @private\n * @memberof timelineDb\n */\nconst compileTasks = function () {\n  const compileTask = function (pos) {\n    return rawTasks[pos].processed;\n  };\n\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\n\nexport default {\n  clear,\n  getCommonDb,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  addEvent,\n};\n", "// @ts-nocheck - don't check until handle it\nimport type { Selection } from 'd3';\nimport { select } from 'd3';\nimport svgDraw from './svgDraw.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport type { Diagram } from '../../Diagram.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\ninterface Block<TDesc, TSection> {\n  number: number;\n  descr: TDesc;\n  section: TSection;\n  width: number;\n  padding: number;\n  maxHeight: number;\n}\n\ninterface TimelineTask {\n  id: number;\n  section: string;\n  type: string;\n  task: string;\n  score: number;\n  events: string[];\n}\nexport const draw = function (text: string, id: string, version: string, diagObj: Diagram) {\n  //1. Fetch the configuration\n  const conf = getConfig();\n  // @ts-expect-error - wrong config?\n  const LEFT_MARGIN = conf.leftMargin ?? 50;\n\n  log.debug('timeline', diagObj.db);\n\n  const securityLevel = conf.securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select('#' + id);\n\n  svg.append('g');\n\n  //4. Fetch the diagram data\n  // @ts-expect-error - db not typed yet\n  const tasks: TimelineTask[] = diagObj.db.getTasks();\n  // @ts-expect-error - db not typed yet\n  const title = diagObj.db.getCommonDb().getDiagramTitle();\n  log.debug('task', tasks);\n\n  //5. Initialize the diagram\n  svgDraw.initGraphics(svg);\n\n  // fetch Sections\n  // @ts-expect-error - db not typed yet\n  const sections: string[] = diagObj.db.getSections();\n  log.debug('sections', sections);\n\n  let maxSectionHeight = 0;\n  let maxTaskHeight = 0;\n  //let sectionBeginX = 0;\n  let depthY = 0;\n  let sectionBeginY = 0;\n  let masterX = 50 + LEFT_MARGIN;\n  //sectionBeginX = masterX;\n  let masterY = 50;\n  sectionBeginY = 50;\n  //draw sections\n  let sectionNumber = 0;\n  let hasSections = true;\n\n  //Calculate the max height of the sections\n  sections.forEach(function (section: string) {\n    const sectionNode: Block<string, number> = {\n      number: sectionNumber,\n      descr: section,\n      section: sectionNumber,\n      width: 150,\n      padding: 20,\n      maxHeight: maxSectionHeight,\n    };\n    const sectionHeight = svgDraw.getVirtualNodeHeight(svg, sectionNode, conf);\n    log.debug('sectionHeight before draw', sectionHeight);\n    maxSectionHeight = Math.max(maxSectionHeight, sectionHeight + 20);\n  });\n\n  //tasks length and maxEventCount\n  let maxEventCount = 0;\n  let maxEventLineLength = 0;\n  log.debug('tasks.length', tasks.length);\n  //calculate max task height\n  // for loop till tasks.length\n\n  for (const [i, task] of tasks.entries()) {\n    const taskNode: Block<TimelineTask, string> = {\n      number: i,\n      descr: task,\n      section: task.section,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight,\n    };\n    const taskHeight = svgDraw.getVirtualNodeHeight(svg, taskNode, conf);\n    log.debug('taskHeight before draw', taskHeight);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight + 20);\n\n    //calculate maxEventCount\n    maxEventCount = Math.max(maxEventCount, task.events.length);\n    //calculate maxEventLineLength\n    let maxEventLineLengthTemp = 0;\n    for (const event of task.events) {\n      const eventNode = {\n        descr: event,\n        section: task.section,\n        number: task.section,\n        width: 150,\n        padding: 20,\n        maxHeight: 50,\n      };\n      maxEventLineLengthTemp += svgDraw.getVirtualNodeHeight(svg, eventNode, conf);\n    }\n    maxEventLineLength = Math.max(maxEventLineLength, maxEventLineLengthTemp);\n  }\n\n  log.debug('maxSectionHeight before draw', maxSectionHeight);\n  log.debug('maxTaskHeight before draw', maxTaskHeight);\n\n  if (sections && sections.length > 0) {\n    sections.forEach((section) => {\n      //filter task where tasks.section == section\n      const tasksForSection = tasks.filter((task) => task.section === section);\n\n      const sectionNode: Block<string, number> = {\n        number: sectionNumber,\n        descr: section,\n        section: sectionNumber,\n        width: 200 * Math.max(tasksForSection.length, 1) - 50,\n        padding: 20,\n        maxHeight: maxSectionHeight,\n      };\n      log.debug('sectionNode', sectionNode);\n      const sectionNodeWrapper = svg.append('g');\n      const node = svgDraw.drawNode(sectionNodeWrapper, sectionNode, sectionNumber, conf);\n      log.debug('sectionNode output', node);\n\n      sectionNodeWrapper.attr('transform', `translate(${masterX}, ${sectionBeginY})`);\n\n      masterY += maxSectionHeight + 50;\n\n      //draw tasks for this section\n      if (tasksForSection.length > 0) {\n        drawTasks(\n          svg,\n          tasksForSection,\n          sectionNumber,\n          masterX,\n          masterY,\n          maxTaskHeight,\n          conf,\n          maxEventCount,\n          maxEventLineLength,\n          maxSectionHeight,\n          false\n        );\n      }\n      // todo replace with total width of section and its tasks\n      masterX += 200 * Math.max(tasksForSection.length, 1);\n\n      masterY = sectionBeginY;\n      sectionNumber++;\n    });\n  } else {\n    //draw tasks\n    hasSections = false;\n    drawTasks(\n      svg,\n      tasks,\n      sectionNumber,\n      masterX,\n      masterY,\n      maxTaskHeight,\n      conf,\n      maxEventCount,\n      maxEventLineLength,\n      maxSectionHeight,\n      true\n    );\n  }\n\n  // Get BBox of the diagram\n  const box = svg.node().getBBox();\n  log.debug('bounds', box);\n\n  if (title) {\n    svg\n      .append('text')\n      .text(title)\n      .attr('x', box.width / 2 - LEFT_MARGIN)\n      .attr('font-size', '4ex')\n      .attr('font-weight', 'bold')\n      .attr('y', 20);\n  }\n  //5. Draw the diagram\n  depthY = hasSections ? maxSectionHeight + maxTaskHeight + 150 : maxTaskHeight + 100;\n\n  const lineWrapper = svg.append('g').attr('class', 'lineWrapper');\n  // Draw activity line\n  lineWrapper\n    .append('line')\n    .attr('x1', LEFT_MARGIN)\n    .attr('y1', depthY) // One section head + one task + margins\n    .attr('x2', box.width + 3 * LEFT_MARGIN) // Subtract stroke width so arrow point is retained\n    .attr('y2', depthY)\n    .attr('stroke-width', 4)\n    .attr('stroke', 'black')\n    .attr('marker-end', 'url(#arrowhead)');\n\n  // Setup the view box and size of the svg element\n  setupGraphViewbox(\n    undefined,\n    svg,\n    conf.timeline?.padding ?? 50,\n    conf.timeline?.useMaxWidth ?? false\n  );\n\n  // addSVGAccessibilityFields(diagObj.db, diagram, id);\n};\n\nexport const drawTasks = function (\n  diagram: Selection<SVGElement, unknown, null, undefined>,\n  tasks: TimelineTask[],\n  sectionColor: number,\n  masterX: number,\n  masterY: number,\n  maxTaskHeight: number,\n  conf: MermaidConfig,\n  maxEventCount: number,\n  maxEventLineLength: number,\n  maxSectionHeight: number,\n  isWithoutSections: boolean\n) {\n  // Draw the tasks\n  for (const task of tasks) {\n    // create node from task\n    const taskNode = {\n      descr: task.task,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight,\n    };\n\n    log.debug('taskNode', taskNode);\n    // create task wrapper\n\n    const taskWrapper = diagram.append('g').attr('class', 'taskWrapper');\n    const node = svgDraw.drawNode(taskWrapper, taskNode, sectionColor, conf);\n    const taskHeight = node.height;\n    //log task height\n    log.debug('taskHeight after draw', taskHeight);\n    taskWrapper.attr('transform', `translate(${masterX}, ${masterY})`);\n\n    // update max task height\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight);\n\n    // if task has events, draw them\n    if (task.events) {\n      // draw a line between the task and the events\n      const lineWrapper = diagram.append('g').attr('class', 'lineWrapper');\n      let lineLength = maxTaskHeight;\n      //add margin to task\n      masterY += 100;\n      lineLength =\n        lineLength + drawEvents(diagram, task.events, sectionColor, masterX, masterY, conf);\n      masterY -= 100;\n\n      lineWrapper\n        .append('line')\n        .attr('x1', masterX + 190 / 2)\n        .attr('y1', masterY + maxTaskHeight) // One section head + one task + margins\n        .attr('x2', masterX + 190 / 2) // Subtract stroke width so arrow point is retained\n        .attr(\n          'y2',\n          masterY +\n            maxTaskHeight +\n            (isWithoutSections ? maxTaskHeight : maxSectionHeight) +\n            maxEventLineLength +\n            120\n        )\n        .attr('stroke-width', 2)\n        .attr('stroke', 'black')\n        .attr('marker-end', 'url(#arrowhead)')\n        .attr('stroke-dasharray', '5,5');\n    }\n\n    masterX = masterX + 200;\n    if (isWithoutSections && !conf.timeline?.disableMulticolor) {\n      sectionColor++;\n    }\n  }\n\n  // reset Y coordinate for next section\n  masterY = masterY - 10;\n};\n\nexport const drawEvents = function (\n  diagram: Selection<SVGElement, unknown, null, undefined>,\n  events: string[],\n  sectionColor: number,\n  masterX: number,\n  masterY: number,\n  conf: MermaidConfig\n) {\n  let maxEventHeight = 0;\n  const eventBeginY = masterY;\n  masterY = masterY + 100;\n  // Draw the events\n  for (const event of events) {\n    // create node from event\n    const eventNode: Block<string, number> = {\n      descr: event,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: 50,\n    };\n\n    //log task node\n    log.debug('eventNode', eventNode);\n    // create event wrapper\n    const eventWrapper = diagram.append('g').attr('class', 'eventWrapper');\n    const node = svgDraw.drawNode(eventWrapper, eventNode, sectionColor, conf);\n    const eventHeight = node.height;\n    maxEventHeight = maxEventHeight + eventHeight;\n    eventWrapper.attr('transform', `translate(${masterX}, ${masterY})`);\n    masterY = masterY + 10 + eventHeight;\n  }\n  // set masterY back to eventBeginY\n  masterY = eventBeginY;\n  return maxEventHeight;\n};\n\nexport default {\n  setConf: () => {\n    // no-op\n  },\n  draw,\n};\n", "import { arc as d3arc, select } from 'd3';\nconst MAX_SECTIONS = 12;\n\nexport const drawRect = function (elem, rectData) {\n  const rectElem = elem.append('rect');\n  rectElem.attr('x', rectData.x);\n  rectElem.attr('y', rectData.y);\n  rectElem.attr('fill', rectData.fill);\n  rectElem.attr('stroke', rectData.stroke);\n  rectElem.attr('width', rectData.width);\n  rectElem.attr('height', rectData.height);\n  rectElem.attr('rx', rectData.rx);\n  rectElem.attr('ry', rectData.ry);\n\n  if (rectData.class !== undefined) {\n    rectElem.attr('class', rectData.class);\n  }\n\n  return rectElem;\n};\n\nexport const drawFace = function (element, faceData) {\n  const radius = 15;\n  const circleElement = element\n    .append('circle')\n    .attr('cx', faceData.cx)\n    .attr('cy', faceData.cy)\n    .attr('class', 'face')\n    .attr('r', radius)\n    .attr('stroke-width', 2)\n    .attr('overflow', 'visible');\n\n  const face = element.append('g');\n\n  //left eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx - radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  //right eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx + radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  /** @param {any} face */\n  function smile(face) {\n    const arc = d3arc()\n      .startAngle(Math.PI / 2)\n      .endAngle(3 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 2) + ')');\n  }\n\n  /** @param {any} face */\n  function sad(face) {\n    const arc = d3arc()\n      .startAngle((3 * Math.PI) / 2)\n      .endAngle(5 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 7) + ')');\n  }\n\n  /** @param {any} face */\n  function ambivalent(face) {\n    face\n      .append('line')\n      .attr('class', 'mouth')\n      .attr('stroke', 2)\n      .attr('x1', faceData.cx - 5)\n      .attr('y1', faceData.cy + 7)\n      .attr('x2', faceData.cx + 5)\n      .attr('y2', faceData.cy + 7)\n      .attr('class', 'mouth')\n      .attr('stroke-width', '1px')\n      .attr('stroke', '#666');\n  }\n\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n\n  return circleElement;\n};\n\nexport const drawCircle = function (element, circleData) {\n  const circleElement = element.append('circle');\n  circleElement.attr('cx', circleData.cx);\n  circleElement.attr('cy', circleData.cy);\n  circleElement.attr('class', 'actor-' + circleData.pos);\n  circleElement.attr('fill', circleData.fill);\n  circleElement.attr('stroke', circleData.stroke);\n  circleElement.attr('r', circleData.r);\n\n  if (circleElement.class !== undefined) {\n    circleElement.attr('class', circleElement.class);\n  }\n\n  if (circleData.title !== undefined) {\n    circleElement.append('title').text(circleData.title);\n  }\n\n  return circleElement;\n};\n\nexport const drawText = function (elem, textData) {\n  // Remove and ignore br:s\n  const nText = textData.text.replace(/<br\\s*\\/?>/gi, ' ');\n\n  const textElem = elem.append('text');\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.attr('class', 'legend');\n\n  textElem.style('text-anchor', textData.anchor);\n\n  if (textData.class !== undefined) {\n    textElem.attr('class', textData.class);\n  }\n\n  const span = textElem.append('tspan');\n  span.attr('x', textData.x + textData.textMargin * 2);\n  span.text(nText);\n\n  return textElem;\n};\n\nexport const drawLabel = function (elem, txtObject) {\n  /**\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} cut\n   */\n  function genPoints(x, y, width, height, cut) {\n    return (\n      x +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      (y + height - cut) +\n      ' ' +\n      (x + width - cut * 1.2) +\n      ',' +\n      (y + height) +\n      ' ' +\n      x +\n      ',' +\n      (y + height)\n    );\n  }\n  const polygon = elem.append('polygon');\n  polygon.attr('points', genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr('class', 'labelBox');\n\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\n\nexport const drawSection = function (elem, section, conf) {\n  const g = elem.append('g');\n\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = 'journey-section section-type-' + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'journey-section section-type-' + section.num },\n    conf,\n    section.colour\n  );\n};\n\nlet taskCount = -1;\n/**\n * Draws an actor in the diagram with the attached line\n *\n * @param {any} elem The HTML element\n * @param {any} task The task to render\n * @param {any} conf The global configuration\n */\nexport const drawTask = function (elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append('g');\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append('line')\n    .attr('id', 'task' + taskCount)\n    .attr('x1', center)\n    .attr('y1', task.y)\n    .attr('x2', center)\n    .attr('y2', maxHeight)\n    .attr('class', 'task-line')\n    .attr('stroke-width', '1px')\n    .attr('stroke-dasharray', '4 2')\n    .attr('stroke', '#666');\n\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score,\n  });\n\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = 'task task-type-' + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'task' },\n    conf,\n    task.colour\n  );\n};\n\n/**\n * Draws a background rectangle\n *\n * @param {any} elem The html element\n * @param {any} bounds The bounds of the drawing\n */\nexport const drawBackgroundRect = function (elem, bounds) {\n  const rectElem = drawRect(elem, {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    class: 'rect',\n  });\n  rectElem.lower();\n};\n\nexport const getTextObj = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: undefined,\n    'text-anchor': 'start',\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n  };\n};\n\nexport const getNoteRect = function () {\n  return {\n    x: 0,\n    y: 0,\n    width: 100,\n    anchor: 'start',\n    height: 100,\n    rx: 0,\n    ry: 0,\n  };\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} colour\n   */\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('font-color', colour)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   * @param {any} colour\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - (taskFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .attr('fill', colour)\n        .style('text-anchor', 'middle')\n        .style('font-size', taskFontSize)\n        .style('font-family', taskFontFamily);\n      text\n        .append('tspan')\n        .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append('switch');\n    const f = body\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('position', 'fixed');\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .attr('class', 'label')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        // noinspection JSUnfilteredForInLoop\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nconst initGraphics = function (graphics) {\n  graphics\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 5)\n    .attr('refY', 2)\n    .attr('markerWidth', 6)\n    .attr('markerHeight', 4)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0,0 V 4 L6,2 Z'); // this is actual shape for arrowhead\n};\n\n/**\n * @param {string} text The text to be wrapped\n * @param {number} width The max width of the text\n */\nfunction wrap(text, width) {\n  text.each(function () {\n    var text = select(this),\n      words = text\n        .text()\n        .split(/(\\s+|<br>)/)\n        .reverse(),\n      word,\n      line = [],\n      lineHeight = 1.1, // ems\n      y = text.attr('y'),\n      dy = parseFloat(text.attr('dy')),\n      tspan = text\n        .text(null)\n        .append('tspan')\n        .attr('x', 0)\n        .attr('y', y)\n        .attr('dy', dy + 'em');\n    for (let j = 0; j < words.length; j++) {\n      word = words[words.length - 1 - j];\n      line.push(word);\n      tspan.text(line.join(' ').trim());\n      if (tspan.node().getComputedTextLength() > width || word === '<br>') {\n        line.pop();\n        tspan.text(line.join(' ').trim());\n        if (word === '<br>') {\n          line = [''];\n        } else {\n          line = [word];\n        }\n\n        tspan = text\n          .append('tspan')\n          .attr('x', 0)\n          .attr('y', y)\n          .attr('dy', lineHeight + 'em')\n          .text(word);\n      }\n    }\n  });\n}\n\nexport const drawNode = function (elem, node, fullSection, conf) {\n  const section = (fullSection % MAX_SECTIONS) - 1;\n  const nodeElem = elem.append('g');\n  node.section = section;\n  nodeElem.attr(\n    'class',\n    (node.class ? node.class + ' ' : '') + 'timeline-node ' + ('section-' + section)\n  );\n  const bkgElem = nodeElem.append('g');\n\n  // Create the wrapped text element\n  const textElem = nodeElem.append('g');\n\n  const txt = textElem\n    .append('text')\n    .text(node.descr)\n    .attr('dy', '1em')\n    .attr('alignment-baseline', 'middle')\n    .attr('dominant-baseline', 'middle')\n    .attr('text-anchor', 'middle')\n    .call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace('px', '') : conf.fontSize;\n  node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n  node.height = Math.max(node.height, node.maxHeight);\n  node.width = node.width + 2 * node.padding;\n\n  textElem.attr('transform', 'translate(' + node.width / 2 + ', ' + node.padding / 2 + ')');\n\n  // Create the background element\n  defaultBkg(bkgElem, node, section, conf);\n\n  return node;\n};\n\nexport const getVirtualNodeHeight = function (elem, node, conf) {\n  const textElem = elem.append('g');\n  const txt = textElem\n    .append('text')\n    .text(node.descr)\n    .attr('dy', '1em')\n    .attr('alignment-baseline', 'middle')\n    .attr('dominant-baseline', 'middle')\n    .attr('text-anchor', 'middle')\n    .call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace('px', '') : conf.fontSize;\n  textElem.remove();\n  return bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n};\n\nconst defaultBkg = function (elem, node, section) {\n  const rd = 5;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + node.type)\n    .attr(\n      'd',\n      `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${\n        node.width - 2 * rd\n      } q5,0 5,5 v${node.height - rd} H0 Z`\n    );\n\n  elem\n    .append('line')\n    .attr('class', 'node-line-' + section)\n    .attr('x1', 0)\n    .attr('y1', node.height)\n    .attr('x2', node.width)\n    .attr('y2', node.height);\n};\n\nexport default {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  getTextObj,\n  getNoteRect,\n  initGraphics,\n  drawNode,\n  getVirtualNodeHeight,\n};\n", "import { darken, lighten, isDark } from 'khroma';\n\nconst genSections = (options) => {\n  let sections = '';\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options['lineColor' + i] = options['lineColor' + i] || options['cScaleInv' + i];\n    if (isDark(options['lineColor' + i])) {\n      options['lineColor' + i] = lighten(options['lineColor' + i], 20);\n    } else {\n      options['lineColor' + i] = darken(options['lineColor' + i], 20);\n    }\n  }\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = '' + (17 - 3 * i);\n    sections += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${\n      i - 1\n    } path  {\n      fill: ${options['cScale' + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options['cScaleLabel' + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options['cScaleLabel' + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options['cScale' + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options['cScaleInv' + i]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${options['cScaleLabel' + i]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections;\n};\n\nconst getStyles = (options) =>\n  `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`;\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/timeline.jison';\nimport * as db from './timelineDb.js';\nimport renderer from './timelineRenderer.js';\nimport styles from './styles.js';\n\nexport const diagram = {\n  db,\n  renderer,\n  parser,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE;AACpL,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,YAAW,GAAE,YAAW,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,aAAY,GAAE,WAAU,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,oBAAmB,IAAG,mBAAkB,IAAG,UAAS,IAAG,SAAQ,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACpU,YAAY,EAAC,GAAE,SAAQ,GAAE,YAAW,GAAE,OAAM,GAAE,SAAQ,IAAG,WAAU,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,UAAS,IAAG,QAAO;AAAA,IACxN,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IAClH,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,iBAAO,GAAG,KAAG,CAAC;AACf;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC;AACX;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAE,CAAC;AACT;AAAA,QACA,KAAK;AACL,aAAG,YAAY,EAAE,gBAAgB,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACzE;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,EAAE,YAAY,KAAK,CAAC;AACzD;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,EAAE,kBAAkB,KAAK,CAAC;AAC/D;AAAA,QACA,KAAK;AACL,aAAG,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACtD;AAAA,QACA,KAAK;AACL,aAAG,QAAQ,GAAG,EAAE,GAAE,GAAE,EAAE;AAAE,eAAK,IAAE,GAAG,EAAE;AACpC;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE;AAC1C;AAAA,MACA;AAAA,IACA,GAvCe;AAAA,IAwCf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IACnb,gBAAgB,CAAC;AAAA,IACjB,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GA1Ce;AAAA,MA2Cf,OAAO,CAAC,uBAAsB,uBAAsB,eAAc,aAAY,iBAAgB,oBAAmB,uBAAsB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,0BAAyB,oBAAmB,kBAAiB,WAAU,SAAS;AAAA,MAC5W,YAAY,EAAC,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAC1O;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,mBAAQ;;;AC9oBhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAAAE;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AACA,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AAEpB,IAAM,WAAW,CAAC;AAClB,IAAM,QAAQ,CAAC;AACf,IAAM,WAAW,CAAC;AAEX,IAAM,cAAc,6BAAM,kBAAN;AAEpB,IAAMC,SAAQ,kCAAY;AAC/B,WAAS,SAAS;AAClB,QAAM,SAAS;AACf,mBAAiB;AACjB,WAAS,SAAS;AAClB,EAAS,MAAM;AACjB,GANqB;AAQd,IAAM,aAAa,gCAAU,KAAK;AACvC,mBAAiB;AACjB,WAAS,KAAK,GAAG;AACnB,GAH0B;AAKnB,IAAM,cAAc,kCAAY;AACrC,SAAO;AACT,GAF2B;AAIpB,IAAM,WAAW,kCAAY;AAClC,MAAI,oBAAoB,aAAa;AACrC,QAAM,WAAW;AACjB,MAAI,iBAAiB;AACrB,SAAO,CAAC,qBAAqB,iBAAiB,UAAU;AACtD,wBAAoB,aAAa;AACjC;AAAA,EACF;AAEA,QAAM,KAAK,GAAG,QAAQ;AAEtB,SAAO;AACT,GAZwB;AAcjB,IAAM,UAAU,gCAAU,QAAQ,QAAQ,OAAO;AACtD,QAAM,UAAU;AAAA,IACd,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO,SAAS,SAAS;AAAA;AAAA,IAEzB,QAAQ,QAAQ,CAAC,KAAK,IAAI,CAAC;AAAA,EAC7B;AACA,WAAS,KAAK,OAAO;AACvB,GAXuB;AAahB,IAAM,WAAW,gCAAU,OAAO;AAEvC,QAAM,cAAc,SAAS,KAAK,CAAC,SAAS,KAAK,OAAO,gBAAgB,CAAC;AAEzE,cAAY,OAAO,KAAK,KAAK;AAC/B,GALwB;AAOjB,IAAM,aAAa,gCAAU,OAAO;AACzC,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,EACZ;AACA,QAAM,KAAK,OAAO;AACpB,GAT0B;AAiB1B,IAAM,eAAe,kCAAY;AAC/B,QAAM,cAAc,gCAAU,KAAK;AACjC,WAAO,SAAS,GAAG,EAAE;AAAA,EACvB,GAFoB;AAIpB,MAAI,eAAe;AACnB,aAAW,CAAC,GAAG,OAAO,KAAK,SAAS,QAAQ,GAAG;AAC7C,gBAAY,CAAC;AAEb,mBAAe,gBAAgB,QAAQ;AAAA,EACzC;AACA,SAAO;AACT,GAZqB;AAcrB,IAAO,qBAAQ;AAAA,EACb,OAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACnGA,SAAS,UAAAC,eAAc;;;ACFvB,SAAS,OAAO,OAAO,cAAc;AACrC,IAAM,eAAe;AAEd,IAAM,WAAW,gCAAU,MAAM,UAAU;AAChD,QAAM,WAAW,KAAK,OAAO,MAAM;AACnC,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,QAAQ,SAAS,IAAI;AACnC,WAAS,KAAK,UAAU,SAAS,MAAM;AACvC,WAAS,KAAK,SAAS,SAAS,KAAK;AACrC,WAAS,KAAK,UAAU,SAAS,MAAM;AACvC,WAAS,KAAK,MAAM,SAAS,EAAE;AAC/B,WAAS,KAAK,MAAM,SAAS,EAAE;AAE/B,MAAI,SAAS,UAAU,QAAW;AAChC,aAAS,KAAK,SAAS,SAAS,KAAK;AAAA,EACvC;AAEA,SAAO;AACT,GAhBwB;AAkBjB,IAAM,WAAW,gCAAU,SAAS,UAAU;AACnD,QAAM,SAAS;AACf,QAAM,gBAAgB,QACnB,OAAO,QAAQ,EACf,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,SAAS,MAAM,EACpB,KAAK,KAAK,MAAM,EAChB,KAAK,gBAAgB,CAAC,EACtB,KAAK,YAAY,SAAS;AAE7B,QAAM,OAAO,QAAQ,OAAO,GAAG;AAG/B,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,KAAK,GAAG,EACb,KAAK,gBAAgB,CAAC,EACtB,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,MAAM;AAGxB,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,KAAK,GAAG,EACb,KAAK,gBAAgB,CAAC,EACtB,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,MAAM;AAGxB,WAAS,MAAMC,OAAM;AACnB,UAAM,MAAM,MAAM,EACf,WAAW,KAAK,KAAK,CAAC,EACtB,SAAS,KAAK,KAAK,KAAK,EAAE,EAC1B,YAAY,SAAS,CAAC,EACtB,YAAY,SAAS,GAAG;AAE3B,IAAAA,MACG,OAAO,MAAM,EACb,KAAK,SAAS,OAAO,EACrB,KAAK,KAAK,GAAG,EACb,KAAK,aAAa,eAAe,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,GAAG;AAAA,EACjF;AAZS;AAeT,WAAS,IAAIA,OAAM;AACjB,UAAM,MAAM,MAAM,EACf,WAAY,IAAI,KAAK,KAAM,CAAC,EAC5B,SAAS,KAAK,KAAK,KAAK,EAAE,EAC1B,YAAY,SAAS,CAAC,EACtB,YAAY,SAAS,GAAG;AAE3B,IAAAA,MACG,OAAO,MAAM,EACb,KAAK,SAAS,OAAO,EACrB,KAAK,KAAK,GAAG,EACb,KAAK,aAAa,eAAe,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,GAAG;AAAA,EACjF;AAZS;AAeT,WAAS,WAAWA,OAAM;AACxB,IAAAA,MACG,OAAO,MAAM,EACb,KAAK,SAAS,OAAO,EACrB,KAAK,UAAU,CAAC,EAChB,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,SAAS,OAAO,EACrB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,UAAU,MAAM;AAAA,EAC1B;AAZS;AAcT,MAAI,SAAS,QAAQ,GAAG;AACtB,UAAM,IAAI;AAAA,EACZ,WAAW,SAAS,QAAQ,GAAG;AAC7B,QAAI,IAAI;AAAA,EACV,OAAO;AACL,eAAW,IAAI;AAAA,EACjB;AAEA,SAAO;AACT,GAvFwB;AAyFjB,IAAM,aAAa,gCAAU,SAAS,YAAY;AACvD,QAAM,gBAAgB,QAAQ,OAAO,QAAQ;AAC7C,gBAAc,KAAK,MAAM,WAAW,EAAE;AACtC,gBAAc,KAAK,MAAM,WAAW,EAAE;AACtC,gBAAc,KAAK,SAAS,WAAW,WAAW,GAAG;AACrD,gBAAc,KAAK,QAAQ,WAAW,IAAI;AAC1C,gBAAc,KAAK,UAAU,WAAW,MAAM;AAC9C,gBAAc,KAAK,KAAK,WAAW,CAAC;AAEpC,MAAI,cAAc,UAAU,QAAW;AACrC,kBAAc,KAAK,SAAS,cAAc,KAAK;AAAA,EACjD;AAEA,MAAI,WAAW,UAAU,QAAW;AAClC,kBAAc,OAAO,OAAO,EAAE,KAAK,WAAW,KAAK;AAAA,EACrD;AAEA,SAAO;AACT,GAlB0B;AAoBnB,IAAM,WAAW,gCAAU,MAAM,UAAU;AAEhD,QAAM,QAAQ,SAAS,KAAK,QAAQ,gBAAgB,GAAG;AAEvD,QAAM,WAAW,KAAK,OAAO,MAAM;AACnC,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,SAAS,QAAQ;AAE/B,WAAS,MAAM,eAAe,SAAS,MAAM;AAE7C,MAAI,SAAS,UAAU,QAAW;AAChC,aAAS,KAAK,SAAS,SAAS,KAAK;AAAA,EACvC;AAEA,QAAM,OAAO,SAAS,OAAO,OAAO;AACpC,OAAK,KAAK,KAAK,SAAS,IAAI,SAAS,aAAa,CAAC;AACnD,OAAK,KAAK,KAAK;AAEf,SAAO;AACT,GApBwB;AAsBjB,IAAM,YAAY,gCAAU,MAAM,WAAW;AAQlD,WAAS,UAAU,GAAG,GAAG,OAAO,QAAQ,KAAK;AAC3C,WACE,IACA,MACA,IACA,OACC,IAAI,SACL,MACA,IACA,OACC,IAAI,SACL,OACC,IAAI,SAAS,OACd,OACC,IAAI,QAAQ,MAAM,OACnB,OACC,IAAI,UACL,MACA,IACA,OACC,IAAI;AAAA,EAET;AAtBS;AAuBT,QAAM,UAAU,KAAK,OAAO,SAAS;AACrC,UAAQ,KAAK,UAAU,UAAU,UAAU,GAAG,UAAU,GAAG,IAAI,IAAI,CAAC,CAAC;AACrE,UAAQ,KAAK,SAAS,UAAU;AAEhC,YAAU,IAAI,UAAU,IAAI,UAAU;AACtC,YAAU,IAAI,UAAU,IAAI,MAAM,UAAU;AAC5C,WAAS,MAAM,SAAS;AAC1B,GAtCyB;AAwClB,IAAM,cAAc,gCAAU,MAAM,SAAS,MAAM;AACxD,QAAM,IAAI,KAAK,OAAO,GAAG;AAEzB,QAAM,OAAO,YAAY;AACzB,OAAK,IAAI,QAAQ;AACjB,OAAK,IAAI,QAAQ;AACjB,OAAK,OAAO,QAAQ;AACpB,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACnB,OAAK,QAAQ,kCAAkC,QAAQ;AACvD,OAAK,KAAK;AACV,OAAK,KAAK;AACV,WAAS,GAAG,IAAI;AAEhB,yBAAuB,IAAI;AAAA,IACzB,QAAQ;AAAA,IACR;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,kCAAkC,QAAQ,IAAI;AAAA,IACvD;AAAA,IACA,QAAQ;AAAA,EACV;AACF,GAzB2B;AA2B3B,IAAI,YAAY;AAQT,IAAM,WAAW,gCAAU,MAAM,MAAM,MAAM;AAClD,QAAM,SAAS,KAAK,IAAI,KAAK,QAAQ;AACrC,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB;AACA,QAAM,YAAY,MAAM,IAAI;AAC5B,IAAE,OAAO,MAAM,EACZ,KAAK,MAAM,SAAS,SAAS,EAC7B,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,KAAK,CAAC,EACjB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,SAAS,EACpB,KAAK,SAAS,WAAW,EACzB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,oBAAoB,KAAK,EAC9B,KAAK,UAAU,MAAM;AAExB,WAAS,GAAG;AAAA,IACV,IAAI;AAAA,IACJ,IAAI,OAAO,IAAI,KAAK,SAAS;AAAA,IAC7B,OAAO,KAAK;AAAA,EACd,CAAC;AAED,QAAM,OAAO,YAAY;AACzB,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,OAAK,OAAO,KAAK;AACjB,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACnB,OAAK,QAAQ,oBAAoB,KAAK;AACtC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,WAAS,GAAG,IAAI;AAEhB,yBAAuB,IAAI;AAAA,IACzB,KAAK;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,OAAO;AAAA,IAChB;AAAA,IACA,KAAK;AAAA,EACP;AACF,GA5CwB;AAoDjB,IAAM,qBAAqB,gCAAU,MAAM,QAAQ;AACxD,QAAM,WAAW,SAAS,MAAM;AAAA,IAC9B,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,IACV,OAAO,OAAO,QAAQ,OAAO;AAAA,IAC7B,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC9B,MAAM,OAAO;AAAA,IACb,OAAO;AAAA,EACT,CAAC;AACD,WAAS,MAAM;AACjB,GAVkC;AAY3B,IAAM,aAAa,kCAAY;AACpC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,eAAe;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACF,GAZ0B;AAcnB,IAAM,cAAc,kCAAY;AACrC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACF,GAV2B;AAY3B,IAAM,yBAA0B,2BAAY;AAW1C,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,QAAQ;AAClE,UAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,EAC5B,MAAM,cAAc,MAAM,EAC1B,MAAM,eAAe,QAAQ,EAC7B,KAAK,OAAO;AACf,kBAAc,MAAM,SAAS;AAAA,EAC/B;AATS;AAsBT,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,MAAM,QAAQ;AACzE,UAAM,EAAE,cAAc,eAAe,IAAI;AAEzC,UAAM,QAAQ,QAAQ,MAAM,cAAc;AAC1C,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,eAAgB,gBAAgB,MAAM,SAAS,KAAM;AACpE,YAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,CAAC,EACX,KAAK,QAAQ,MAAM,EACnB,MAAM,eAAe,QAAQ,EAC7B,MAAM,aAAa,YAAY,EAC/B,MAAM,eAAe,cAAc;AACtC,WACG,OAAO,OAAO,EACd,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,CAAC,CAAC;AAEhB,WACG,KAAK,KAAK,IAAI,SAAS,CAAG,EAC1B,KAAK,qBAAqB,SAAS,EACnC,KAAK,sBAAsB,SAAS;AAEvC,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AA3BS;AAuCT,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,MAAM;AAC9D,UAAM,OAAO,EAAE,OAAO,QAAQ;AAC9B,UAAM,IAAI,KACP,OAAO,eAAe,EACtB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,YAAY,OAAO;AAE3B,UAAM,OAAO,EACV,OAAO,WAAW,EAClB,MAAM,WAAW,OAAO,EACxB,MAAM,UAAU,MAAM,EACtB,MAAM,SAAS,MAAM;AAExB,SACG,OAAO,KAAK,EACZ,KAAK,SAAS,OAAO,EACrB,MAAM,WAAW,YAAY,EAC7B,MAAM,cAAc,QAAQ,EAC5B,MAAM,kBAAkB,QAAQ,EAChC,KAAK,OAAO;AAEf,YAAQ,SAAS,MAAM,GAAG,GAAG,OAAO,QAAQ,WAAW,IAAI;AAC3D,kBAAc,MAAM,SAAS;AAAA,EAC/B;AA1BS;AAgCT,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,OAAO,mBAAmB;AAE5B,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAPS;AAST,SAAO,SAAU,MAAM;AACrB,WAAO,KAAK,kBAAkB,OAAO,OAAO,KAAK,kBAAkB,QAAQ,SAAS;AAAA,EACtF;AACF,EAAG;AAEH,IAAM,eAAe,gCAAU,UAAU;AACvC,WACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,WAAW,EACtB,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,CAAC,EACrB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,kBAAkB;AACjC,GAZqB;AAkBrB,SAAS,KAAK,MAAM,OAAO;AACzB,OAAK,KAAK,WAAY;AACpB,QAAIC,QAAO,OAAO,IAAI,GACpB,QAAQA,MACL,KAAK,EACL,MAAM,YAAY,EAClB,QAAQ,GACX,MACA,OAAO,CAAC,GACR,aAAa,KACb,IAAIA,MAAK,KAAK,GAAG,GACjB,KAAK,WAAWA,MAAK,KAAK,IAAI,CAAC,GAC/B,QAAQA,MACL,KAAK,IAAI,EACT,OAAO,OAAO,EACd,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,MAAM,KAAK,IAAI;AACzB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAO,MAAM,MAAM,SAAS,IAAI,CAAC;AACjC,WAAK,KAAK,IAAI;AACd,YAAM,KAAK,KAAK,KAAK,GAAG,EAAE,KAAK,CAAC;AAChC,UAAI,MAAM,KAAK,EAAE,sBAAsB,IAAI,SAAS,SAAS,QAAQ;AACnE,aAAK,IAAI;AACT,cAAM,KAAK,KAAK,KAAK,GAAG,EAAE,KAAK,CAAC;AAChC,YAAI,SAAS,QAAQ;AACnB,iBAAO,CAAC,EAAE;AAAA,QACZ,OAAO;AACL,iBAAO,CAAC,IAAI;AAAA,QACd;AAEA,gBAAQA,MACL,OAAO,OAAO,EACd,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,MAAM,aAAa,IAAI,EAC5B,KAAK,IAAI;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAxCS;AA0CF,IAAM,WAAW,gCAAU,MAAM,MAAM,aAAa,MAAM;AAC/D,QAAM,UAAW,cAAc,eAAgB;AAC/C,QAAM,WAAW,KAAK,OAAO,GAAG;AAChC,OAAK,UAAU;AACf,WAAS;AAAA,IACP;AAAA,KACC,KAAK,QAAQ,KAAK,QAAQ,MAAM,MAAM,oBAAoB,aAAa;AAAA,EAC1E;AACA,QAAM,UAAU,SAAS,OAAO,GAAG;AAGnC,QAAM,WAAW,SAAS,OAAO,GAAG;AAEpC,QAAM,MAAM,SACT,OAAO,MAAM,EACb,KAAK,KAAK,KAAK,EACf,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ,EAC5B,KAAK,MAAM,KAAK,KAAK;AACxB,QAAM,OAAO,IAAI,KAAK,EAAE,QAAQ;AAChC,QAAM,WAAW,KAAK,UAAU,UAAU,KAAK,SAAS,QAAQ,MAAM,EAAE,IAAI,KAAK;AACjF,OAAK,SAAS,KAAK,SAAS,WAAW,MAAM,MAAM,KAAK;AACxD,OAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS;AAClD,OAAK,QAAQ,KAAK,QAAQ,IAAI,KAAK;AAEnC,WAAS,KAAK,aAAa,eAAe,KAAK,QAAQ,IAAI,OAAO,KAAK,UAAU,IAAI,GAAG;AAGxF,aAAW,SAAS,MAAM,SAAS,IAAI;AAEvC,SAAO;AACT,GAjCwB;AAmCjB,IAAM,uBAAuB,gCAAU,MAAM,MAAM,MAAM;AAC9D,QAAM,WAAW,KAAK,OAAO,GAAG;AAChC,QAAM,MAAM,SACT,OAAO,MAAM,EACb,KAAK,KAAK,KAAK,EACf,KAAK,MAAM,KAAK,EAChB,KAAK,sBAAsB,QAAQ,EACnC,KAAK,qBAAqB,QAAQ,EAClC,KAAK,eAAe,QAAQ,EAC5B,KAAK,MAAM,KAAK,KAAK;AACxB,QAAM,OAAO,IAAI,KAAK,EAAE,QAAQ;AAChC,QAAM,WAAW,KAAK,UAAU,UAAU,KAAK,SAAS,QAAQ,MAAM,EAAE,IAAI,KAAK;AACjF,WAAS,OAAO;AAChB,SAAO,KAAK,SAAS,WAAW,MAAM,MAAM,KAAK;AACnD,GAdoC;AAgBpC,IAAM,aAAa,gCAAU,MAAM,MAAM,SAAS;AAChD,QAAM,KAAK;AACX,OACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,KAAK,EAAE,EAC5B,KAAK,SAAS,mBAAmB,KAAK,IAAI,EAC1C;AAAA,IACC;AAAA,IACA,MAAM,KAAK,SAAS,EAAE,KAAK,CAAC,KAAK,SAAS,IAAI,EAAE,gBAC9C,KAAK,QAAQ,IAAI,EACnB,cAAc,KAAK,SAAS,EAAE;AAAA,EAChC;AAEF,OACG,OAAO,MAAM,EACb,KAAK,SAAS,eAAe,OAAO,EACpC,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,KAAK,MAAM,EACtB,KAAK,MAAM,KAAK,KAAK,EACrB,KAAK,MAAM,KAAK,MAAM;AAC3B,GApBmB;AAsBnB,IAAO,kBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AD1iBO,IAAM,OAAO,gCAAU,MAAc,IAAY,SAAiB,SAAkB;AAEzF,QAAM,OAAO,UAAU;AAEvB,QAAM,cAAc,KAAK,cAAc;AAEvC,MAAI,MAAM,YAAY,QAAQ,EAAE;AAEhC,QAAM,gBAAgB,KAAK;AAE3B,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiBC,QAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACdA,QAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrDA,QAAO,MAAM;AAEnB,QAAM,MAAM,KAAK,OAAO,MAAM,EAAE;AAEhC,MAAI,OAAO,GAAG;AAId,QAAMC,SAAwB,QAAQ,GAAG,SAAS;AAElD,QAAM,QAAQ,QAAQ,GAAG,YAAY,EAAE,gBAAgB;AACvD,MAAI,MAAM,QAAQA,MAAK;AAGvB,kBAAQ,aAAa,GAAG;AAIxB,QAAMC,YAAqB,QAAQ,GAAG,YAAY;AAClD,MAAI,MAAM,YAAYA,SAAQ;AAE9B,MAAI,mBAAmB;AACvB,MAAI,gBAAgB;AAEpB,MAAI,SAAS;AACb,MAAI,gBAAgB;AACpB,MAAI,UAAU,KAAK;AAEnB,MAAI,UAAU;AACd,kBAAgB;AAEhB,MAAI,gBAAgB;AACpB,MAAI,cAAc;AAGlB,EAAAA,UAAS,QAAQ,SAAU,SAAiB;AAC1C,UAAM,cAAqC;AAAA,MACzC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AACA,UAAM,gBAAgB,gBAAQ,qBAAqB,KAAK,aAAa,IAAI;AACzE,QAAI,MAAM,6BAA6B,aAAa;AACpD,uBAAmB,KAAK,IAAI,kBAAkB,gBAAgB,EAAE;AAAA,EAClE,CAAC;AAGD,MAAI,gBAAgB;AACpB,MAAI,qBAAqB;AACzB,MAAI,MAAM,gBAAgBD,OAAM,MAAM;AAItC,aAAW,CAAC,GAAG,IAAI,KAAKA,OAAM,QAAQ,GAAG;AACvC,UAAM,WAAwC;AAAA,MAC5C,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS,KAAK;AAAA,MACd,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AACA,UAAM,aAAa,gBAAQ,qBAAqB,KAAK,UAAU,IAAI;AACnE,QAAI,MAAM,0BAA0B,UAAU;AAC9C,oBAAgB,KAAK,IAAI,eAAe,aAAa,EAAE;AAGvD,oBAAgB,KAAK,IAAI,eAAe,KAAK,OAAO,MAAM;AAE1D,QAAI,yBAAyB;AAC7B,eAAW,SAAS,KAAK,QAAQ;AAC/B,YAAM,YAAY;AAAA,QAChB,OAAO;AAAA,QACP,SAAS,KAAK;AAAA,QACd,QAAQ,KAAK;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AACA,gCAA0B,gBAAQ,qBAAqB,KAAK,WAAW,IAAI;AAAA,IAC7E;AACA,yBAAqB,KAAK,IAAI,oBAAoB,sBAAsB;AAAA,EAC1E;AAEA,MAAI,MAAM,gCAAgC,gBAAgB;AAC1D,MAAI,MAAM,6BAA6B,aAAa;AAEpD,MAAIC,aAAYA,UAAS,SAAS,GAAG;AACnC,IAAAA,UAAS,QAAQ,CAAC,YAAY;AAE5B,YAAM,kBAAkBD,OAAM,OAAO,CAAC,SAAS,KAAK,YAAY,OAAO;AAEvE,YAAM,cAAqC;AAAA,QACzC,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO,MAAM,KAAK,IAAI,gBAAgB,QAAQ,CAAC,IAAI;AAAA,QACnD,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AACA,UAAI,MAAM,eAAe,WAAW;AACpC,YAAM,qBAAqB,IAAI,OAAO,GAAG;AACzC,YAAM,OAAO,gBAAQ,SAAS,oBAAoB,aAAa,eAAe,IAAI;AAClF,UAAI,MAAM,sBAAsB,IAAI;AAEpC,yBAAmB,KAAK,aAAa,aAAa,OAAO,KAAK,aAAa,GAAG;AAE9E,iBAAW,mBAAmB;AAG9B,UAAI,gBAAgB,SAAS,GAAG;AAC9B;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,iBAAW,MAAM,KAAK,IAAI,gBAAgB,QAAQ,CAAC;AAEnD,gBAAU;AACV;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AAEL,kBAAc;AACd;AAAA,MACE;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAGA,QAAM,MAAM,IAAI,KAAK,EAAE,QAAQ;AAC/B,MAAI,MAAM,UAAU,GAAG;AAEvB,MAAI,OAAO;AACT,QACG,OAAO,MAAM,EACb,KAAK,KAAK,EACV,KAAK,KAAK,IAAI,QAAQ,IAAI,WAAW,EACrC,KAAK,aAAa,KAAK,EACvB,KAAK,eAAe,MAAM,EAC1B,KAAK,KAAK,EAAE;AAAA,EACjB;AAEA,WAAS,cAAc,mBAAmB,gBAAgB,MAAM,gBAAgB;AAEhF,QAAM,cAAc,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AAE/D,cACG,OAAO,MAAM,EACb,KAAK,MAAM,WAAW,EACtB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,IAAI,QAAQ,IAAI,WAAW,EACtC,KAAK,MAAM,MAAM,EACjB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,OAAO,EACtB,KAAK,cAAc,iBAAiB;AAGvC;AAAA,IACE;AAAA,IACA;AAAA,IACA,KAAK,UAAU,WAAW;AAAA,IAC1B,KAAK,UAAU,eAAe;AAAA,EAChC;AAGF,GA9MoB;AAgNb,IAAM,YAAY,gCACvBE,UACAF,QACA,cACA,SACA,SACA,eACA,MACA,eACA,oBACA,kBACA,mBACA;AAEA,aAAW,QAAQA,QAAO;AAExB,UAAM,WAAW;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAEA,QAAI,MAAM,YAAY,QAAQ;AAG9B,UAAM,cAAcE,SAAQ,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AACnE,UAAM,OAAO,gBAAQ,SAAS,aAAa,UAAU,cAAc,IAAI;AACvE,UAAM,aAAa,KAAK;AAExB,QAAI,MAAM,yBAAyB,UAAU;AAC7C,gBAAY,KAAK,aAAa,aAAa,OAAO,KAAK,OAAO,GAAG;AAGjE,oBAAgB,KAAK,IAAI,eAAe,UAAU;AAGlD,QAAI,KAAK,QAAQ;AAEf,YAAM,cAAcA,SAAQ,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AACnE,UAAI,aAAa;AAEjB,iBAAW;AACX,mBACE,aAAa,WAAWA,UAAS,KAAK,QAAQ,cAAc,SAAS,SAAS,IAAI;AACpF,iBAAW;AAEX,kBACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,MAAM,CAAC,EAC5B,KAAK,MAAM,UAAU,aAAa,EAClC,KAAK,MAAM,UAAU,MAAM,CAAC,EAC5B;AAAA,QACC;AAAA,QACA,UACE,iBACC,oBAAoB,gBAAgB,oBACrC,qBACA;AAAA,MACJ,EACC,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,OAAO,EACtB,KAAK,cAAc,iBAAiB,EACpC,KAAK,oBAAoB,KAAK;AAAA,IACnC;AAEA,cAAU,UAAU;AACpB,QAAI,qBAAqB,CAAC,KAAK,UAAU,mBAAmB;AAC1D;AAAA,IACF;AAAA,EACF;AAGA,YAAU,UAAU;AACtB,GA5EyB;AA8ElB,IAAM,aAAa,gCACxBA,UACA,QACA,cACA,SACA,SACA,MACA;AACA,MAAI,iBAAiB;AACrB,QAAM,cAAc;AACpB,YAAU,UAAU;AAEpB,aAAW,SAAS,QAAQ;AAE1B,UAAM,YAAmC;AAAA,MACvC,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAGA,QAAI,MAAM,aAAa,SAAS;AAEhC,UAAM,eAAeA,SAAQ,OAAO,GAAG,EAAE,KAAK,SAAS,cAAc;AACrE,UAAM,OAAO,gBAAQ,SAAS,cAAc,WAAW,cAAc,IAAI;AACzE,UAAM,cAAc,KAAK;AACzB,qBAAiB,iBAAiB;AAClC,iBAAa,KAAK,aAAa,aAAa,OAAO,KAAK,OAAO,GAAG;AAClE,cAAU,UAAU,KAAK;AAAA,EAC3B;AAEA,YAAU;AACV,SAAO;AACT,GApC0B;AAsC1B,IAAO,2BAAQ;AAAA,EACb,SAAS,6BAAM;AAAA,EAEf,GAFS;AAAA,EAGT;AACF;;;AEpWA,SAAS,QAAQ,SAAS,cAAc;AAExC,IAAM,cAAc,wBAAC,YAAY;AAC/B,MAAIC,YAAW;AAEf,WAAS,IAAI,GAAG,IAAI,QAAQ,mBAAmB,KAAK;AAClD,YAAQ,cAAc,CAAC,IAAI,QAAQ,cAAc,CAAC,KAAK,QAAQ,cAAc,CAAC;AAC9E,QAAI,OAAO,QAAQ,cAAc,CAAC,CAAC,GAAG;AACpC,cAAQ,cAAc,CAAC,IAAI,QAAQ,QAAQ,cAAc,CAAC,GAAG,EAAE;AAAA,IACjE,OAAO;AACL,cAAQ,cAAc,CAAC,IAAI,OAAO,QAAQ,cAAc,CAAC,GAAG,EAAE;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,mBAAmB,KAAK;AAClD,UAAM,KAAK,MAAM,KAAK,IAAI;AAC1B,IAAAA,aAAY;AAAA,eACD,IAAI,CAAC,mBAAmB,IAAI,CAAC,mBAAmB,IAAI,CAAC,qBAC9D,IAAI,CACN;AAAA,cACU,QAAQ,WAAW,CAAC,CAAC;AAAA;AAAA,eAEpB,IAAI,CAAC;AAAA,aACP,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA,iBAEtB,IAAI,CAAC;AAAA;AAAA,eAEP,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA,oBAErB,IAAI,CAAC;AAAA,gBACT,QAAQ,WAAW,CAAC,CAAC;AAAA;AAAA,kBAEnB,IAAI,CAAC;AAAA,sBACD,EAAE;AAAA;AAAA,eAET,IAAI,CAAC;AAAA,gBACJ,QAAQ,cAAc,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKxB,QAAQ,gBAAgB,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUxC;AACA,SAAOA;AACT,GAnDoB;AAqDpB,IAAM,YAAY,wBAAC,YACjB;AAAA;AAAA;AAAA;AAAA,IAIE,YAAY,OAAO,CAAC;AAAA;AAAA,YAEZ,QAAQ,IAAI;AAAA;AAAA;AAAA,YAGZ,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAVjB;AAyBlB,IAAO,iBAAQ;;;AC1ER,IAAM,UAAU;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["o", "parser", "lexer", "clear", "clear", "select", "face", "text", "select", "tasks", "sections", "diagram", "sections"]}