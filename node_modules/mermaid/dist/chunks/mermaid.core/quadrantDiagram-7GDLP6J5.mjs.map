{"version": 3, "sources": ["../../../src/diagrams/quadrant-chart/parser/quadrant.jison", "../../../src/diagrams/quadrant-chart/quadrantBuilder.ts", "../../../src/diagrams/quadrant-chart/utils.ts", "../../../src/diagrams/quadrant-chart/quadrantDb.ts", "../../../src/diagrams/quadrant-chart/quadrantRenderer.ts", "../../../src/diagrams/quadrant-chart/quadrantDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,3],$V1=[1,4],$V2=[1,5],$V3=[1,6],$V4=[1,7],$V5=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],$V6=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],$V7=[55,56,57],$V8=[2,36],$V9=[1,37],$Va=[1,36],$Vb=[1,38],$Vc=[1,35],$Vd=[1,43],$Ve=[1,41],$Vf=[1,14],$Vg=[1,23],$Vh=[1,18],$Vi=[1,19],$Vj=[1,20],$Vk=[1,21],$Vl=[1,22],$Vm=[1,24],$Vn=[1,25],$Vo=[1,26],$Vp=[1,27],$Vq=[1,28],$Vr=[1,29],$Vs=[1,32],$Vt=[1,33],$Vu=[1,34],$Vv=[1,39],$Vw=[1,40],$Vx=[1,42],$Vy=[1,44],$Vz=[1,62],$VA=[1,61],$VB=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],$VC=[1,65],$VD=[1,66],$VE=[1,67],$VF=[1,68],$VG=[1,69],$VH=[1,70],$VI=[1,71],$VJ=[1,72],$VK=[1,73],$VL=[1,74],$VM=[1,75],$VN=[1,76],$VO=[4,5,6,7,8,9,10,11,12,13,14,15,18],$VP=[1,90],$VQ=[1,91],$VR=[1,92],$VS=[1,99],$VT=[1,93],$VU=[1,96],$VV=[1,94],$VW=[1,95],$VX=[1,97],$VY=[1,98],$VZ=[1,102],$V_=[10,55,56,57],$V$=[4,5,6,8,10,11,13,17,18,19,20,55,56,57];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"idStringToken\":3,\"ALPHA\":4,\"NUM\":5,\"NODE_STRING\":6,\"DOWN\":7,\"MINUS\":8,\"DEFAULT\":9,\"COMMA\":10,\"COLON\":11,\"AMP\":12,\"BRKT\":13,\"MULT\":14,\"UNICODE_TEXT\":15,\"styleComponent\":16,\"UNIT\":17,\"SPACE\":18,\"STYLE\":19,\"PCT\":20,\"idString\":21,\"style\":22,\"stylesOpt\":23,\"classDefStatement\":24,\"CLASSDEF\":25,\"start\":26,\"eol\":27,\"QUADRANT\":28,\"document\":29,\"line\":30,\"statement\":31,\"axisDetails\":32,\"quadrantDetails\":33,\"points\":34,\"title\":35,\"title_value\":36,\"acc_title\":37,\"acc_title_value\":38,\"acc_descr\":39,\"acc_descr_value\":40,\"acc_descr_multiline_value\":41,\"section\":42,\"text\":43,\"point_start\":44,\"point_x\":45,\"point_y\":46,\"class_name\":47,\"X-AXIS\":48,\"AXIS-TEXT-DELIMITER\":49,\"Y-AXIS\":50,\"QUADRANT_1\":51,\"QUADRANT_2\":52,\"QUADRANT_3\":53,\"QUADRANT_4\":54,\"NEWLINE\":55,\"SEMI\":56,\"EOF\":57,\"alphaNumToken\":58,\"textNoTagsToken\":59,\"STR\":60,\"MD_STR\":61,\"alphaNum\":62,\"PUNCTUATION\":63,\"PLUS\":64,\"EQUALS\":65,\"DOT\":66,\"UNDERSCORE\":67,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"ALPHA\",5:\"NUM\",6:\"NODE_STRING\",7:\"DOWN\",8:\"MINUS\",9:\"DEFAULT\",10:\"COMMA\",11:\"COLON\",12:\"AMP\",13:\"BRKT\",14:\"MULT\",15:\"UNICODE_TEXT\",17:\"UNIT\",18:\"SPACE\",19:\"STYLE\",20:\"PCT\",25:\"CLASSDEF\",28:\"QUADRANT\",35:\"title\",36:\"title_value\",37:\"acc_title\",38:\"acc_title_value\",39:\"acc_descr\",40:\"acc_descr_value\",41:\"acc_descr_multiline_value\",42:\"section\",44:\"point_start\",45:\"point_x\",46:\"point_y\",47:\"class_name\",48:\"X-AXIS\",49:\"AXIS-TEXT-DELIMITER\",50:\"Y-AXIS\",51:\"QUADRANT_1\",52:\"QUADRANT_2\",53:\"QUADRANT_3\",54:\"QUADRANT_4\",55:\"NEWLINE\",56:\"SEMI\",57:\"EOF\",60:\"STR\",61:\"MD_STR\",63:\"PUNCTUATION\",64:\"PLUS\",65:\"EQUALS\",66:\"DOT\",67:\"UNDERSCORE\"},\nproductions_: [0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 23:\nthis.$=$$[$0]\nbreak;\ncase 24:\nthis.$=$$[$0-1]+''+$$[$0]\nbreak;\ncase 26:\nthis.$ = $$[$0-1] + $$[$0];\nbreak;\ncase 27:\nthis.$ = [$$[$0].trim()]\nbreak;\ncase 28:\n$$[$0-2].push($$[$0].trim());this.$ = $$[$0-2];\nbreak;\ncase 29:\nthis.$ = $$[$0-4];yy.addClass($$[$0-2],$$[$0]);\nbreak;\ncase 37:\nthis.$=[];\nbreak;\ncase 42:\n this.$=$$[$0].trim();yy.setDiagramTitle(this.$); \nbreak;\ncase 43:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 44: case 45:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 46:\nyy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8);\nbreak;\ncase 47:\nyy.addPoint($$[$0-3], \"\", $$[$0-1], $$[$0], []);\nbreak;\ncase 48:\nyy.addPoint($$[$0-4], $$[$0-3], $$[$0-1], $$[$0], []);\nbreak;\ncase 49:\nyy.addPoint($$[$0-4], \"\", $$[$0-2], $$[$0-1], $$[$0]);\nbreak;\ncase 50:\nyy.addPoint($$[$0-5], $$[$0-4], $$[$0-2], $$[$0-1], $$[$0]);\nbreak;\ncase 51:\nyy.setXAxisLeftText($$[$0-2]); yy.setXAxisRightText($$[$0]);\nbreak;\ncase 52:\n$$[$0-1].text += \" ⟶ \"; yy.setXAxisLeftText($$[$0-1]);\nbreak;\ncase 53:\nyy.setXAxisLeftText($$[$0]);\nbreak;\ncase 54:\nyy.setYAxisBottomText($$[$0-2]); yy.setYAxisTopText($$[$0]);\nbreak;\ncase 55:\n$$[$0-1].text += \" ⟶ \"; yy.setYAxisBottomText($$[$0-1]);\nbreak;\ncase 56:\nyy.setYAxisBottomText($$[$0]);\nbreak;\ncase 57:\nyy.setQuadrant1Text($$[$0])\nbreak;\ncase 58:\nyy.setQuadrant2Text($$[$0])\nbreak;\ncase 59:\nyy.setQuadrant3Text($$[$0])\nbreak;\ncase 60:\nyy.setQuadrant4Text($$[$0])\nbreak;\ncase 64:\n this.$={text:$$[$0], type: 'text'};\nbreak;\ncase 65:\n this.$={text:$$[$0-1].text+''+$$[$0], type: $$[$0-1].type};\nbreak;\ncase 66:\n this.$={text: $$[$0], type: 'text'};\nbreak;\ncase 67:\n this.$={text: $$[$0], type: 'markdown'};\nbreak;\ncase 68:\nthis.$=$$[$0];\nbreak;\ncase 69:\nthis.$=$$[$0-1]+''+$$[$0];\nbreak;\n}\n},\ntable: [{18:$V0,26:1,27:2,28:$V1,55:$V2,56:$V3,57:$V4},{1:[3]},{18:$V0,26:8,27:2,28:$V1,55:$V2,56:$V3,57:$V4},{18:$V0,26:9,27:2,28:$V1,55:$V2,56:$V3,57:$V4},o($V5,[2,33],{29:10}),o($V6,[2,61]),o($V6,[2,62]),o($V6,[2,63]),{1:[2,30]},{1:[2,31]},o($V7,$V8,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$Vf,25:$Vg,35:$Vh,37:$Vi,39:$Vj,41:$Vk,42:$Vl,48:$Vm,50:$Vn,51:$Vo,52:$Vp,53:$Vq,54:$Vr,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V5,[2,34]),{27:45,55:$V2,56:$V3,57:$V4},o($V7,[2,37]),o($V7,$V8,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$Vf,25:$Vg,35:$Vh,37:$Vi,39:$Vj,41:$Vk,42:$Vl,48:$Vm,50:$Vn,51:$Vo,52:$Vp,53:$Vq,54:$Vr,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,39]),o($V7,[2,40]),o($V7,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},o($V7,[2,45]),o($V7,[2,46]),{18:[1,50]},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:51,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:52,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:53,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:54,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:55,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,43:56,58:31,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},{4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,44:[1,57],47:[1,58],58:60,59:59,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy},o($VB,[2,64]),o($VB,[2,66]),o($VB,[2,67]),o($VB,[2,70]),o($VB,[2,71]),o($VB,[2,72]),o($VB,[2,73]),o($VB,[2,74]),o($VB,[2,75]),o($VB,[2,76]),o($VB,[2,77]),o($VB,[2,78]),o($VB,[2,79]),o($VB,[2,80]),o($V5,[2,35]),o($V7,[2,38]),o($V7,[2,42]),o($V7,[2,43]),o($V7,[2,44]),{3:64,4:$VC,5:$VD,6:$VE,7:$VF,8:$VG,9:$VH,10:$VI,11:$VJ,12:$VK,13:$VL,14:$VM,15:$VN,21:63},o($V7,[2,53],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,49:[1,77],63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,56],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,49:[1,78],63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,57],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,58],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,59],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,60],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),{45:[1,79]},{44:[1,80]},o($VB,[2,65]),o($VB,[2,81]),o($VB,[2,82]),o($VB,[2,83]),{3:82,4:$VC,5:$VD,6:$VE,7:$VF,8:$VG,9:$VH,10:$VI,11:$VJ,12:$VK,13:$VL,14:$VM,15:$VN,18:[1,81]},o($VO,[2,23]),o($VO,[2,1]),o($VO,[2,2]),o($VO,[2,3]),o($VO,[2,4]),o($VO,[2,5]),o($VO,[2,6]),o($VO,[2,7]),o($VO,[2,8]),o($VO,[2,9]),o($VO,[2,10]),o($VO,[2,11]),o($VO,[2,12]),o($V7,[2,52],{58:31,43:83,4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,55],{58:31,43:84,4:$V9,5:$Va,10:$Vb,12:$Vc,13:$Vd,14:$Ve,60:$Vs,61:$Vt,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),{46:[1,85]},{45:[1,86]},{4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,16:89,17:$VV,18:$VW,19:$VX,20:$VY,22:88,23:87},o($VO,[2,24]),o($V7,[2,51],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,54],{59:59,58:60,4:$V9,5:$Va,8:$Vz,10:$Vb,12:$Vc,13:$Vd,14:$Ve,18:$VA,63:$Vu,64:$Vv,65:$Vw,66:$Vx,67:$Vy}),o($V7,[2,47],{22:88,16:89,23:100,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY}),{46:[1,101]},o($V7,[2,29],{10:$VZ}),o($V_,[2,27],{16:103,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY}),o($V$,[2,25]),o($V$,[2,13]),o($V$,[2,14]),o($V$,[2,15]),o($V$,[2,16]),o($V$,[2,17]),o($V$,[2,18]),o($V$,[2,19]),o($V$,[2,20]),o($V$,[2,21]),o($V$,[2,22]),o($V7,[2,49],{10:$VZ}),o($V7,[2,48],{22:88,16:89,23:104,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY}),{4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,16:89,17:$VV,18:$VW,19:$VX,20:$VY,22:105},o($V$,[2,26]),o($V7,[2,50],{10:$VZ}),o($V_,[2,28],{16:103,4:$VP,5:$VQ,6:$VR,8:$VS,11:$VT,13:$VU,17:$VV,18:$VW,19:$VX,20:$VY})],\ndefaultActions: {8:[2,30],9:[2,31]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2:return 55;\nbreak;\ncase 3:/* do nothing */\nbreak;\ncase 4: this.begin(\"title\");return 35; \nbreak;\ncase 5: this.popState(); return \"title_value\"; \nbreak;\ncase 6: this.begin(\"acc_title\");return 37; \nbreak;\ncase 7: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 8: this.begin(\"acc_descr\");return 39; \nbreak;\ncase 9: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 10: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 11: this.popState(); \nbreak;\ncase 12:return \"acc_descr_multiline_value\";\nbreak;\ncase 13:return 48;\nbreak;\ncase 14:return 50;\nbreak;\ncase 15:return 49\nbreak;\ncase 16:return 51;\nbreak;\ncase 17:return 52;\nbreak;\ncase 18:return 53;\nbreak;\ncase 19:return 54;\nbreak;\ncase 20:return 25;\nbreak;\ncase 21: this.begin(\"md_string\");\nbreak;\ncase 22: return \"MD_STR\";\nbreak;\ncase 23: this.popState();\nbreak;\ncase 24:this.begin(\"string\");\nbreak;\ncase 25:this.popState();\nbreak;\ncase 26:return \"STR\";\nbreak;\ncase 27:this.begin('class_name')\nbreak;\ncase 28:this.popState(); return 47;\nbreak;\ncase 29:this.begin(\"point_start\"); return 44;\nbreak;\ncase 30:this.begin('point_x'); return 45;\nbreak;\ncase 31:this.popState();\nbreak;\ncase 32:this.popState(); this.begin('point_y');\nbreak;\ncase 33:this.popState(); return 46;\nbreak;\ncase 34:return 28;\nbreak;\ncase 35:return 4;\nbreak;\ncase 36:return 11;\nbreak;\ncase 37:return 64;\nbreak;\ncase 38:return 10;\nbreak;\ncase 39:return 65;\nbreak;\ncase 40:return 65;\nbreak;\ncase 41:return 14;\nbreak;\ncase 42:return 13;\nbreak;\ncase 43:return 67;\nbreak;\ncase 44:return 66;\nbreak;\ncase 45:return 12;\nbreak;\ncase 46:return 8;\nbreak;\ncase 47:return 5;\nbreak;\ncase 48:return 18;\nbreak;\ncase 49:return 56;\nbreak;\ncase 50:return 63;\nbreak;\ncase 51:return 57;\nbreak;\n}\n},\nrules: [/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n\\r]+)/i,/^(?:%%[^\\n]*)/i,/^(?:title\\b)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\\b)/i,/^(?:[\"][`])/i,/^(?:[^`\"]+)/i,/^(?:[`][\"])/i,/^(?:[\"])/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?::::)/i,/^(?:^\\w+)/i,/^(?:\\s*:\\s*\\[\\s*)/i,/^(?:(1)|(0(.\\d+)?))/i,/^(?:\\s*\\] *)/i,/^(?:\\s*,\\s*)/i,/^(?:(1)|(0(.\\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\\*)/i,/^(?:#)/i,/^(?:[\\_])/i,/^(?:\\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\\s)/i,/^(?:;)/i,/^(?:[!\"#$%&'*+,-.`?\\\\_/])/i,/^(?:$)/i],\nconditions: {\"class_name\":{\"rules\":[28],\"inclusive\":false},\"point_y\":{\"rules\":[33],\"inclusive\":false},\"point_x\":{\"rules\":[32],\"inclusive\":false},\"point_start\":{\"rules\":[30,31],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[11,12],\"inclusive\":false},\"acc_descr\":{\"rules\":[9],\"inclusive\":false},\"acc_title\":{\"rules\":[7],\"inclusive\":false},\"title\":{\"rules\":[5],\"inclusive\":false},\"md_string\":{\"rules\":[22,23],\"inclusive\":false},\"string\":{\"rules\":[25,26],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { scaleLinear } from 'd3';\nimport type { BaseDiagramConfig, QuadrantChartConfig } from '../../config.type.js';\nimport defaultConfig from '../../defaultConfig.js';\nimport { log } from '../../logger.js';\nimport { getThemeVariables } from '../../themes/theme-default.js';\nimport type { Point } from '../../types.js';\n\nconst defaultThemeVariables = getThemeVariables();\n\nexport type TextVerticalPos = 'left' | 'center' | 'right';\nexport type TextHorizontalPos = 'top' | 'middle' | 'bottom';\n\nexport interface StylesObject {\n  className?: string;\n  radius?: number;\n  color?: string;\n  strokeColor?: string;\n  strokeWidth?: string;\n}\n\nexport interface QuadrantPointInputType extends Point, StylesObject {\n  text: string;\n}\n\nexport interface QuadrantTextType extends Point {\n  text: string;\n  fill: string;\n  verticalPos: TextVerticalPos;\n  horizontalPos: TextHorizontalPos;\n  fontSize: number;\n  rotation: number;\n}\n\nexport interface QuadrantPointType\n  extends Point,\n    Pick<StylesObject, 'strokeColor' | 'strokeWidth'> {\n  fill: string;\n  radius: number;\n  text: QuadrantTextType;\n}\n\nexport interface QuadrantQuadrantsType extends Point {\n  text: QuadrantTextType;\n  width: number;\n  height: number;\n  fill: string;\n}\n\nexport interface QuadrantLineType {\n  strokeWidth: number;\n  strokeFill: string;\n  x1: number;\n  y1: number;\n  x2: number;\n  y2: number;\n}\n\nexport interface QuadrantBuildType {\n  points: QuadrantPointType[];\n  quadrants: QuadrantQuadrantsType[];\n  axisLabels: QuadrantTextType[];\n  title?: QuadrantTextType;\n  borderLines?: QuadrantLineType[];\n}\n\nexport interface QuadrantBuilderData {\n  titleText: string;\n  quadrant1Text: string;\n  quadrant2Text: string;\n  quadrant3Text: string;\n  quadrant4Text: string;\n  xAxisLeftText: string;\n  xAxisRightText: string;\n  yAxisBottomText: string;\n  yAxisTopText: string;\n  points: QuadrantPointInputType[];\n}\n\nexport interface QuadrantBuilderConfig\n  extends Required<Omit<QuadrantChartConfig, keyof BaseDiagramConfig>> {\n  showXAxis: boolean;\n  showYAxis: boolean;\n  showTitle: boolean;\n}\n\nexport interface QuadrantBuilderThemeConfig {\n  quadrantTitleFill: string;\n  quadrant1Fill: string;\n  quadrant2Fill: string;\n  quadrant3Fill: string;\n  quadrant4Fill: string;\n  quadrant1TextFill: string;\n  quadrant2TextFill: string;\n  quadrant3TextFill: string;\n  quadrant4TextFill: string;\n  quadrantPointFill: string;\n  quadrantPointTextFill: string;\n  quadrantXAxisTextFill: string;\n  quadrantYAxisTextFill: string;\n  quadrantInternalBorderStrokeFill: string;\n  quadrantExternalBorderStrokeFill: string;\n}\n\ninterface CalculateSpaceData {\n  xAxisSpace: {\n    top: number;\n    bottom: number;\n  };\n  yAxisSpace: {\n    left: number;\n    right: number;\n  };\n  titleSpace: {\n    top: number;\n  };\n  quadrantSpace: {\n    quadrantLeft: number;\n    quadrantTop: number;\n    quadrantWidth: number;\n    quadrantHalfWidth: number;\n    quadrantHeight: number;\n    quadrantHalfHeight: number;\n  };\n}\n\nexport class QuadrantBuilder {\n  private config: QuadrantBuilderConfig;\n  private themeConfig: QuadrantBuilderThemeConfig;\n  private data: QuadrantBuilderData;\n  private classes = new Map<string, StylesObject>();\n\n  constructor() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n\n  getDefaultData(): QuadrantBuilderData {\n    return {\n      titleText: '',\n      quadrant1Text: '',\n      quadrant2Text: '',\n      quadrant3Text: '',\n      quadrant4Text: '',\n      xAxisLeftText: '',\n      xAxisRightText: '',\n      yAxisBottomText: '',\n      yAxisTopText: '',\n      points: [],\n    };\n  }\n\n  getDefaultConfig(): QuadrantBuilderConfig {\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: defaultConfig.quadrantChart?.chartWidth || 500,\n      chartWidth: defaultConfig.quadrantChart?.chartHeight || 500,\n      titlePadding: defaultConfig.quadrantChart?.titlePadding || 10,\n      titleFontSize: defaultConfig.quadrantChart?.titleFontSize || 20,\n      quadrantPadding: defaultConfig.quadrantChart?.quadrantPadding || 5,\n      xAxisLabelPadding: defaultConfig.quadrantChart?.xAxisLabelPadding || 5,\n      yAxisLabelPadding: defaultConfig.quadrantChart?.yAxisLabelPadding || 5,\n      xAxisLabelFontSize: defaultConfig.quadrantChart?.xAxisLabelFontSize || 16,\n      yAxisLabelFontSize: defaultConfig.quadrantChart?.yAxisLabelFontSize || 16,\n      quadrantLabelFontSize: defaultConfig.quadrantChart?.quadrantLabelFontSize || 16,\n      quadrantTextTopPadding: defaultConfig.quadrantChart?.quadrantTextTopPadding || 5,\n      pointTextPadding: defaultConfig.quadrantChart?.pointTextPadding || 5,\n      pointLabelFontSize: defaultConfig.quadrantChart?.pointLabelFontSize || 12,\n      pointRadius: defaultConfig.quadrantChart?.pointRadius || 5,\n      xAxisPosition: defaultConfig.quadrantChart?.xAxisPosition || 'top',\n      yAxisPosition: defaultConfig.quadrantChart?.yAxisPosition || 'left',\n      quadrantInternalBorderStrokeWidth:\n        defaultConfig.quadrantChart?.quadrantInternalBorderStrokeWidth || 1,\n      quadrantExternalBorderStrokeWidth:\n        defaultConfig.quadrantChart?.quadrantExternalBorderStrokeWidth || 2,\n    };\n  }\n\n  getDefaultThemeConfig(): QuadrantBuilderThemeConfig {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill,\n    };\n  }\n\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    this.classes = new Map();\n    log.info('clear called');\n  }\n\n  setData(data: Partial<QuadrantBuilderData>) {\n    this.data = { ...this.data, ...data };\n  }\n\n  addPoints(points: QuadrantPointInputType[]) {\n    this.data.points = [...points, ...this.data.points];\n  }\n\n  addClass(className: string, styles: StylesObject) {\n    this.classes.set(className, styles);\n  }\n\n  setConfig(config: Partial<QuadrantBuilderConfig>) {\n    log.trace('setConfig called with: ', config);\n    this.config = { ...this.config, ...config };\n  }\n\n  setThemeConfig(themeConfig: Partial<QuadrantBuilderThemeConfig>) {\n    log.trace('setThemeConfig called with: ', themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n\n  calculateSpace(\n    xAxisPosition: typeof this.config.xAxisPosition,\n    showXAxis: boolean,\n    showYAxis: boolean,\n    showTitle: boolean\n  ): CalculateSpaceData {\n    const xAxisSpaceCalculation =\n      this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === 'top' && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === 'bottom' && showXAxis ? xAxisSpaceCalculation : 0,\n    };\n\n    const yAxisSpaceCalculation =\n      this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === 'left' && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === 'right' && showYAxis ? yAxisSpaceCalculation : 0,\n    };\n\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0,\n    };\n\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth =\n      this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight =\n      this.config.chartHeight -\n      this.config.quadrantPadding * 2 -\n      xAxisSpace.top -\n      xAxisSpace.bottom -\n      titleSpace.top;\n\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight,\n    };\n\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace,\n    };\n  }\n\n  getAxisLabels(\n    xAxisPosition: typeof this.config.xAxisPosition,\n    showXAxis: boolean,\n    showYAxis: boolean,\n    spaceData: CalculateSpaceData\n  ): QuadrantTextType[] {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth,\n    } = quadrantSpace;\n\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n\n    const axisLabels: QuadrantTextType[] = [];\n\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y:\n          xAxisPosition === 'top'\n            ? this.config.xAxisLabelPadding + titleSpace.top\n            : this.config.xAxisLabelPadding +\n              quadrantTop +\n              quadrantHeight +\n              this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: 0,\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y:\n          xAxisPosition === 'top'\n            ? this.config.xAxisLabelPadding + titleSpace.top\n            : this.config.xAxisLabelPadding +\n              quadrantTop +\n              quadrantHeight +\n              this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: 0,\n      });\n    }\n\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x:\n          this.config.yAxisPosition === 'left'\n            ? this.config.yAxisLabelPadding\n            : this.config.yAxisLabelPadding +\n              quadrantLeft +\n              quadrantWidth +\n              this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: -90,\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x:\n          this.config.yAxisPosition === 'left'\n            ? this.config.yAxisLabelPadding\n            : this.config.yAxisLabelPadding +\n              quadrantLeft +\n              quadrantWidth +\n              this.config.quadrantPadding,\n        y:\n          quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? 'center' : 'left',\n        horizontalPos: 'top',\n        rotation: -90,\n      });\n    }\n    return axisLabels;\n  }\n\n  getQuadrants(spaceData: CalculateSpaceData): QuadrantQuadrantsType[] {\n    const { quadrantSpace } = spaceData;\n\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n\n    const quadrants: QuadrantQuadrantsType[] = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill,\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill,\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill,\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: 'center',\n          horizontalPos: 'middle',\n          rotation: 0,\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill,\n      },\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      // place the text in the center of the box\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = 'middle';\n        // place the text top of the quadrant square\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = 'top';\n      }\n    }\n\n    return quadrants;\n  }\n\n  getQuadrantPoints(spaceData: CalculateSpaceData): QuadrantPointType[] {\n    const { quadrantSpace } = spaceData;\n\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n\n    const xAxis = scaleLinear()\n      .domain([0, 1])\n      .range([quadrantLeft, quadrantWidth + quadrantLeft]);\n\n    const yAxis = scaleLinear()\n      .domain([0, 1])\n      .range([quadrantHeight + quadrantTop, quadrantTop]);\n\n    const points: QuadrantPointType[] = this.data.points.map((point) => {\n      const classStyles = this.classes.get(point.className!);\n      if (classStyles) {\n        point = { ...classStyles, ...point };\n      }\n      const props: QuadrantPointType = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: point.color ?? this.themeConfig.quadrantPointFill,\n        radius: point.radius ?? this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: 'center',\n          horizontalPos: 'top',\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0,\n        },\n        strokeColor: point.strokeColor ?? this.themeConfig.quadrantPointFill,\n        strokeWidth: point.strokeWidth ?? '0px',\n      };\n      return props;\n    });\n    return points;\n  }\n\n  getBorders(spaceData: CalculateSpaceData): QuadrantLineType[] {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth,\n    } = quadrantSpace;\n\n    const borderLines: QuadrantLineType[] = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop,\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth,\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight,\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth,\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth,\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight,\n      },\n    ];\n    return borderLines;\n  }\n\n  getTitle(showTitle: boolean): QuadrantTextType | undefined {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: 'top',\n        verticalPos: 'center',\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2,\n      };\n    }\n    return;\n  }\n\n  build(): QuadrantBuildType {\n    const showXAxis =\n      this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis =\n      this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n\n    const xAxisPosition = this.data.points.length > 0 ? 'bottom' : this.config.xAxisPosition;\n\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle),\n    };\n  }\n}\n", "class InvalidStyleError extends Error {\n  constructor(style: string, value: string, type: string) {\n    super(`value for ${style} ${value} is invalid, please use a valid ${type}`);\n    this.name = 'InvalidStyleError';\n  }\n}\n\nfunction validateHexCode(value: string): boolean {\n  return !/^#?([\\dA-Fa-f]{6}|[\\dA-Fa-f]{3})$/.test(value);\n}\n\nfunction validateNumber(value: string): boolean {\n  return !/^\\d+$/.test(value);\n}\n\nfunction validateSizeInPixels(value: string): boolean {\n  return !/^\\d+px$/.test(value);\n}\n\nexport { validateHexCode, validateNumber, validateSizeInPixels, InvalidStyleError };\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n} from '../common/commonDb.js';\nimport type { StylesObject } from './quadrantBuilder.js';\nimport { QuadrantBuilder } from './quadrantBuilder.js';\nimport {\n  validateHexCode,\n  validateSizeInPixels,\n  validateNumber,\n  InvalidStyleError,\n} from './utils.js';\n\nconst config = getConfig();\n\nfunction textSanitizer(text: string) {\n  return sanitizeText(text.trim(), config);\n}\n\ninterface LexTextObj {\n  text: string;\n  type: 'text' | 'markdown';\n}\n\nconst quadrantBuilder = new QuadrantBuilder();\n\nfunction setQuadrant1Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\n\nfunction setQuadrant2Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\n\nfunction setQuadrant3Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\n\nfunction setQuadrant4Text(textObj: LexTextObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\n\nfunction setXAxisLeftText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\n\nfunction setXAxisRightText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\n\nfunction setYAxisTopText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\n\nfunction setYAxisBottomText(textObj: LexTextObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\n\nfunction parseStyles(styles: string[]): StylesObject {\n  const stylesObject: StylesObject = {};\n  for (const style of styles) {\n    const [key, value] = style.trim().split(/\\s*:\\s*/);\n    if (key === 'radius') {\n      if (validateNumber(value)) {\n        throw new InvalidStyleError(key, value, 'number');\n      }\n      stylesObject.radius = parseInt(value);\n    } else if (key === 'color') {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, 'hex code');\n      }\n      stylesObject.color = value;\n    } else if (key === 'stroke-color') {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, 'hex code');\n      }\n      stylesObject.strokeColor = value;\n    } else if (key === 'stroke-width') {\n      if (validateSizeInPixels(value)) {\n        throw new InvalidStyleError(key, value, 'number of pixels (eg. 10px)');\n      }\n      stylesObject.strokeWidth = value;\n    } else {\n      throw new Error(`style named ${key} is not supported.`);\n    }\n  }\n  return stylesObject;\n}\n\nfunction addPoint(textObj: LexTextObj, className: string, x: number, y: number, styles: string[]) {\n  const stylesObject = parseStyles(styles);\n  quadrantBuilder.addPoints([\n    {\n      x,\n      y,\n      text: textSanitizer(textObj.text),\n      className,\n      ...stylesObject,\n    },\n  ]);\n}\n\nfunction addClass(className: string, styles: string[]) {\n  quadrantBuilder.addClass(className, parseStyles(styles));\n}\n\nfunction setWidth(width: number) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\n\nfunction setHeight(height: number) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\n\nfunction getQuadrantData() {\n  const config = getConfig();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill,\n  });\n  quadrantBuilder.setData({ titleText: getDiagramTitle() });\n  return quadrantBuilder.build();\n}\n\nconst clear = function () {\n  quadrantBuilder.clear();\n  commonClear();\n};\n\nexport default {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  parseStyles,\n  addPoint,\n  addClass,\n  getQuadrantData,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n};\n", "// @ts-nocheck - don't check until handle it\nimport { select } from 'd3';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { Diagram } from '../../Diagram.js';\nimport type {\n  QuadrantBuildType,\n  QuadrantLineType,\n  QuadrantPointType,\n  QuadrantQuadrantsType,\n  QuadrantTextType,\n  TextHorizontalPos,\n  TextVerticalPos,\n} from './quadrantBuilder.js';\n\nexport const draw = (txt: string, id: string, _version: string, diagObj: Diagram) => {\n  function getDominantBaseLine(horizontalPos: TextHorizontalPos) {\n    return horizontalPos === 'top' ? 'hanging' : 'middle';\n  }\n\n  function getTextAnchor(verticalPos: TextVerticalPos) {\n    return verticalPos === 'left' ? 'start' : 'middle';\n  }\n\n  function getTransformation(data: { x: number; y: number; rotation: number }) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n\n  const conf = getConfig();\n\n  log.debug('Rendering quadrant chart\\n' + txt);\n\n  const securityLevel = conf.securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select(`[id=\"${id}\"]`);\n\n  const group = svg.append('g').attr('class', 'main');\n\n  const width = conf.quadrantChart?.chartWidth ?? 500;\n  const height = conf.quadrantChart?.chartHeight ?? 500;\n\n  configureSvgSize(svg, height, width, conf.quadrantChart?.useMaxWidth ?? true);\n\n  svg.attr('viewBox', '0 0 ' + width + ' ' + height);\n\n  // @ts-ignore: TODO Fix ts errors\n  diagObj.db.setHeight(height);\n  // @ts-ignore: TODO Fix ts errors\n  diagObj.db.setWidth(width);\n\n  // @ts-ignore: TODO Fix ts errors\n  const quadrantData: QuadrantBuildType = diagObj.db.getQuadrantData();\n\n  const quadrantsGroup = group.append('g').attr('class', 'quadrants');\n  const borderGroup = group.append('g').attr('class', 'border');\n  const dataPointGroup = group.append('g').attr('class', 'data-points');\n  const labelGroup = group.append('g').attr('class', 'labels');\n  const titleGroup = group.append('g').attr('class', 'title');\n\n  if (quadrantData.title) {\n    titleGroup\n      .append('text')\n      .attr('x', 0)\n      .attr('y', 0)\n      .attr('fill', quadrantData.title.fill)\n      .attr('font-size', quadrantData.title.fontSize)\n      .attr('dominant-baseline', getDominantBaseLine(quadrantData.title.horizontalPos))\n      .attr('text-anchor', getTextAnchor(quadrantData.title.verticalPos))\n      .attr('transform', getTransformation(quadrantData.title))\n      .text(quadrantData.title.text);\n  }\n\n  if (quadrantData.borderLines) {\n    borderGroup\n      .selectAll('line')\n      .data(quadrantData.borderLines)\n      .enter()\n      .append('line')\n      .attr('x1', (data: QuadrantLineType) => data.x1)\n      .attr('y1', (data: QuadrantLineType) => data.y1)\n      .attr('x2', (data: QuadrantLineType) => data.x2)\n      .attr('y2', (data: QuadrantLineType) => data.y2)\n      .style('stroke', (data: QuadrantLineType) => data.strokeFill)\n      .style('stroke-width', (data: QuadrantLineType) => data.strokeWidth);\n  }\n\n  const quadrants = quadrantsGroup\n    .selectAll('g.quadrant')\n    .data(quadrantData.quadrants)\n    .enter()\n    .append('g')\n    .attr('class', 'quadrant');\n\n  quadrants\n    .append('rect')\n    .attr('x', (data: QuadrantQuadrantsType) => data.x)\n    .attr('y', (data: QuadrantQuadrantsType) => data.y)\n    .attr('width', (data: QuadrantQuadrantsType) => data.width)\n    .attr('height', (data: QuadrantQuadrantsType) => data.height)\n    .attr('fill', (data: QuadrantQuadrantsType) => data.fill);\n\n  quadrants\n    .append('text')\n    .attr('x', 0)\n    .attr('y', 0)\n    .attr('fill', (data: QuadrantQuadrantsType) => data.text.fill)\n    .attr('font-size', (data: QuadrantQuadrantsType) => data.text.fontSize)\n    .attr('dominant-baseline', (data: QuadrantQuadrantsType) =>\n      getDominantBaseLine(data.text.horizontalPos)\n    )\n    .attr('text-anchor', (data: QuadrantQuadrantsType) => getTextAnchor(data.text.verticalPos))\n    .attr('transform', (data: QuadrantQuadrantsType) => getTransformation(data.text))\n    .text((data: QuadrantQuadrantsType) => data.text.text);\n\n  const labels = labelGroup\n    .selectAll('g.label')\n    .data(quadrantData.axisLabels)\n    .enter()\n    .append('g')\n    .attr('class', 'label');\n\n  labels\n    .append('text')\n    .attr('x', 0)\n    .attr('y', 0)\n    .text((data: QuadrantTextType) => data.text)\n    .attr('fill', (data: QuadrantTextType) => data.fill)\n    .attr('font-size', (data: QuadrantTextType) => data.fontSize)\n    .attr('dominant-baseline', (data: QuadrantTextType) => getDominantBaseLine(data.horizontalPos))\n    .attr('text-anchor', (data: QuadrantTextType) => getTextAnchor(data.verticalPos))\n    .attr('transform', (data: QuadrantTextType) => getTransformation(data));\n\n  const dataPoints = dataPointGroup\n    .selectAll('g.data-point')\n    .data(quadrantData.points)\n    .enter()\n    .append('g')\n    .attr('class', 'data-point');\n\n  dataPoints\n    .append('circle')\n    .attr('cx', (data: QuadrantPointType) => data.x)\n    .attr('cy', (data: QuadrantPointType) => data.y)\n    .attr('r', (data: QuadrantPointType) => data.radius)\n    .attr('fill', (data: QuadrantPointType) => data.fill)\n    .attr('stroke', (data: QuadrantPointType) => data.strokeColor)\n    .attr('stroke-width', (data: QuadrantPointType) => data.strokeWidth);\n\n  dataPoints\n    .append('text')\n    .attr('x', 0)\n    .attr('y', 0)\n    .text((data: QuadrantPointType) => data.text.text)\n    .attr('fill', (data: QuadrantPointType) => data.text.fill)\n    .attr('font-size', (data: QuadrantPointType) => data.text.fontSize)\n    .attr('dominant-baseline', (data: QuadrantPointType) =>\n      getDominantBaseLine(data.text.horizontalPos)\n    )\n    .attr('text-anchor', (data: QuadrantPointType) => getTextAnchor(data.text.verticalPos))\n    .attr('transform', (data: QuadrantPointType) => getTransformation(data.text));\n};\n\nexport default {\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/quadrant.jison';\nimport db from './quadrantDb.js';\nimport renderer from './quadrantRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles: () => '',\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAG,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AACliC,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,iBAAgB,GAAE,SAAQ,GAAE,OAAM,GAAE,eAAc,GAAE,QAAO,GAAE,SAAQ,GAAE,WAAU,GAAE,SAAQ,IAAG,SAAQ,IAAG,OAAM,IAAG,QAAO,IAAG,QAAO,IAAG,gBAAe,IAAG,kBAAiB,IAAG,QAAO,IAAG,SAAQ,IAAG,SAAQ,IAAG,OAAM,IAAG,YAAW,IAAG,SAAQ,IAAG,aAAY,IAAG,qBAAoB,IAAG,YAAW,IAAG,SAAQ,IAAG,OAAM,IAAG,YAAW,IAAG,YAAW,IAAG,QAAO,IAAG,aAAY,IAAG,eAAc,IAAG,mBAAkB,IAAG,UAAS,IAAG,SAAQ,IAAG,eAAc,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,QAAO,IAAG,eAAc,IAAG,WAAU,IAAG,WAAU,IAAG,cAAa,IAAG,UAAS,IAAG,uBAAsB,IAAG,UAAS,IAAG,cAAa,IAAG,cAAa,IAAG,cAAa,IAAG,cAAa,IAAG,WAAU,IAAG,QAAO,IAAG,OAAM,IAAG,iBAAgB,IAAG,mBAAkB,IAAG,OAAM,IAAG,UAAS,IAAG,YAAW,IAAG,eAAc,IAAG,QAAO,IAAG,UAAS,IAAG,OAAM,IAAG,cAAa,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACv7B,YAAY,EAAC,GAAE,SAAQ,GAAE,SAAQ,GAAE,OAAM,GAAE,eAAc,GAAE,QAAO,GAAE,SAAQ,GAAE,WAAU,IAAG,SAAQ,IAAG,SAAQ,IAAG,OAAM,IAAG,QAAO,IAAG,QAAO,IAAG,gBAAe,IAAG,QAAO,IAAG,SAAQ,IAAG,SAAQ,IAAG,OAAM,IAAG,YAAW,IAAG,YAAW,IAAG,SAAQ,IAAG,eAAc,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,eAAc,IAAG,WAAU,IAAG,WAAU,IAAG,cAAa,IAAG,UAAS,IAAG,uBAAsB,IAAG,UAAS,IAAG,cAAa,IAAG,cAAa,IAAG,cAAa,IAAG,cAAa,IAAG,WAAU,IAAG,QAAO,IAAG,OAAM,IAAG,OAAM,IAAG,UAAS,IAAG,eAAc,IAAG,QAAO,IAAG,UAAS,IAAG,OAAM,IAAG,aAAY;AAAA,IAClpB,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACzkB,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACL,eAAK,IAAE,GAAG,EAAE;AACZ;AAAA,QACA,KAAK;AACL,eAAK,IAAE,GAAG,KAAG,CAAC,IAAE,KAAG,GAAG,EAAE;AACxB;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC,IAAI,GAAG,EAAE;AACzB;AAAA,QACA,KAAK;AACL,eAAK,IAAI,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC;AACvB;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AAC7C;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,SAAS,GAAG,KAAG,CAAC,GAAE,GAAG,EAAE,CAAC;AAC7C;AAAA,QACA,KAAK;AACL,eAAK,IAAE,CAAC;AACR;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,gBAAgB,KAAK,CAAC;AAC/C;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACL,aAAG,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACtD;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,KAAG,CAAC,GAAG,IAAI,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9C;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AACpD;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,KAAG,CAAC,GAAG,IAAI,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AACpD;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC1D;AAAA,QACA,KAAK;AACL,aAAG,iBAAiB,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,kBAAkB,GAAG,EAAE,CAAC;AAC1D;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,QAAQ;AAAO,aAAG,iBAAiB,GAAG,KAAG,CAAC,CAAC;AACpD;AAAA,QACA,KAAK;AACL,aAAG,iBAAiB,GAAG,EAAE,CAAC;AAC1B;AAAA,QACA,KAAK;AACL,aAAG,mBAAmB,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,gBAAgB,GAAG,EAAE,CAAC;AAC1D;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,QAAQ;AAAO,aAAG,mBAAmB,GAAG,KAAG,CAAC,CAAC;AACtD;AAAA,QACA,KAAK;AACL,aAAG,mBAAmB,GAAG,EAAE,CAAC;AAC5B;AAAA,QACA,KAAK;AACL,aAAG,iBAAiB,GAAG,EAAE,CAAC;AAC1B;AAAA,QACA,KAAK;AACL,aAAG,iBAAiB,GAAG,EAAE,CAAC;AAC1B;AAAA,QACA,KAAK;AACL,aAAG,iBAAiB,GAAG,EAAE,CAAC;AAC1B;AAAA,QACA,KAAK;AACL,aAAG,iBAAiB,GAAG,EAAE,CAAC;AAC1B;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAK,GAAG,EAAE,GAAG,MAAM,OAAM;AAClC;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAK,GAAG,KAAG,CAAC,EAAE,OAAK,KAAG,GAAG,EAAE,GAAG,MAAM,GAAG,KAAG,CAAC,EAAE,KAAI;AAC1D;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAM,GAAG,EAAE,GAAG,MAAM,OAAM;AACnC;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,EAAC,MAAM,GAAG,EAAE,GAAG,MAAM,WAAU;AACvC;AAAA,QACA,KAAK;AACL,eAAK,IAAE,GAAG,EAAE;AACZ;AAAA,QACA,KAAK;AACL,eAAK,IAAE,GAAG,KAAG,CAAC,IAAE,KAAG,GAAG,EAAE;AACxB;AAAA,MACA;AAAA,IACA,GAnGe;AAAA,IAoGf,OAAO,CAAC,EAAC,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,GAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,KAAI,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,GAAE,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,GAAE,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,CAAC;AAAA,IAC5wI,gBAAgB,EAAC,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,CAAC,GAAE,EAAE,EAAC;AAAA,IAClC,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,OAAO;AAAE,mBAAO;AACnC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAC/B;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,QAAQ;AAC3B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,YAAY;AAC/B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,aAAa;AAAG,mBAAO;AAC1C;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,SAAS;AAAG,mBAAO;AACtC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,iBAAK,MAAM,SAAS;AAC7C;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GA5Ge;AAAA,MA6Gf,OAAO,CAAC,wBAAuB,uBAAsB,iBAAgB,kBAAiB,iBAAgB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,oBAAmB,oBAAmB,kBAAiB,wBAAuB,wBAAuB,wBAAuB,wBAAuB,oBAAmB,gBAAe,gBAAe,gBAAe,aAAY,aAAY,eAAc,aAAY,cAAa,sBAAqB,wBAAuB,iBAAgB,iBAAgB,wBAAuB,2BAA0B,mBAAkB,WAAU,YAAW,WAAU,WAAU,WAAU,YAAW,WAAU,cAAa,YAAW,WAAU,WAAU,gBAAe,YAAW,WAAU,8BAA6B,SAAS;AAAA,MACl3B,YAAY,EAAC,cAAa,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,eAAc,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAC3mB;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,mBAAQ;;;AC5wBhB,SAAS,mBAAmB;AAO5B,IAAM,wBAAwB,kBAAkB;AAsHzC,IAAM,kBAAN,MAAsB;AAAA,EAM3B,cAAc;AAFd,SAAQ,UAAU,oBAAI,IAA0B;AAG9C,SAAK,SAAS,KAAK,iBAAiB;AACpC,SAAK,cAAc,KAAK,sBAAsB;AAC9C,SAAK,OAAO,KAAK,eAAe;AAAA,EAClC;AAAA,EAvIF,OA6H6B;AAAA;AAAA;AAAA,EAY3B,iBAAsC;AACpC,WAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,QAAQ,CAAC;AAAA,IACX;AAAA,EACF;AAAA,EAEA,mBAA0C;AACxC,WAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa,sBAAc,eAAe,cAAc;AAAA,MACxD,YAAY,sBAAc,eAAe,eAAe;AAAA,MACxD,cAAc,sBAAc,eAAe,gBAAgB;AAAA,MAC3D,eAAe,sBAAc,eAAe,iBAAiB;AAAA,MAC7D,iBAAiB,sBAAc,eAAe,mBAAmB;AAAA,MACjE,mBAAmB,sBAAc,eAAe,qBAAqB;AAAA,MACrE,mBAAmB,sBAAc,eAAe,qBAAqB;AAAA,MACrE,oBAAoB,sBAAc,eAAe,sBAAsB;AAAA,MACvE,oBAAoB,sBAAc,eAAe,sBAAsB;AAAA,MACvE,uBAAuB,sBAAc,eAAe,yBAAyB;AAAA,MAC7E,wBAAwB,sBAAc,eAAe,0BAA0B;AAAA,MAC/E,kBAAkB,sBAAc,eAAe,oBAAoB;AAAA,MACnE,oBAAoB,sBAAc,eAAe,sBAAsB;AAAA,MACvE,aAAa,sBAAc,eAAe,eAAe;AAAA,MACzD,eAAe,sBAAc,eAAe,iBAAiB;AAAA,MAC7D,eAAe,sBAAc,eAAe,iBAAiB;AAAA,MAC7D,mCACE,sBAAc,eAAe,qCAAqC;AAAA,MACpE,mCACE,sBAAc,eAAe,qCAAqC;AAAA,IACtE;AAAA,EACF;AAAA,EAEA,wBAAoD;AAClD,WAAO;AAAA,MACL,eAAe,sBAAsB;AAAA,MACrC,eAAe,sBAAsB;AAAA,MACrC,eAAe,sBAAsB;AAAA,MACrC,eAAe,sBAAsB;AAAA,MACrC,mBAAmB,sBAAsB;AAAA,MACzC,mBAAmB,sBAAsB;AAAA,MACzC,mBAAmB,sBAAsB;AAAA,MACzC,mBAAmB,sBAAsB;AAAA,MACzC,mBAAmB,sBAAsB;AAAA,MACzC,uBAAuB,sBAAsB;AAAA,MAC7C,uBAAuB,sBAAsB;AAAA,MAC7C,uBAAuB,sBAAsB;AAAA,MAC7C,mBAAmB,sBAAsB;AAAA,MACzC,kCAAkC,sBAAsB;AAAA,MACxD,kCAAkC,sBAAsB;AAAA,IAC1D;AAAA,EACF;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS,KAAK,iBAAiB;AACpC,SAAK,cAAc,KAAK,sBAAsB;AAC9C,SAAK,OAAO,KAAK,eAAe;AAChC,SAAK,UAAU,oBAAI,IAAI;AACvB,QAAI,KAAK,cAAc;AAAA,EACzB;AAAA,EAEA,QAAQ,MAAoC;AAC1C,SAAK,OAAO,EAAE,GAAG,KAAK,MAAM,GAAG,KAAK;AAAA,EACtC;AAAA,EAEA,UAAU,QAAkC;AAC1C,SAAK,KAAK,SAAS,CAAC,GAAG,QAAQ,GAAG,KAAK,KAAK,MAAM;AAAA,EACpD;AAAA,EAEA,SAAS,WAAmB,QAAsB;AAChD,SAAK,QAAQ,IAAI,WAAW,MAAM;AAAA,EACpC;AAAA,EAEA,UAAUE,SAAwC;AAChD,QAAI,MAAM,2BAA2BA,OAAM;AAC3C,SAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,GAAGA,QAAO;AAAA,EAC5C;AAAA,EAEA,eAAe,aAAkD;AAC/D,QAAI,MAAM,gCAAgC,WAAW;AACrD,SAAK,cAAc,EAAE,GAAG,KAAK,aAAa,GAAG,YAAY;AAAA,EAC3D;AAAA,EAEA,eACE,eACA,WACA,WACA,WACoB;AACpB,UAAM,wBACJ,KAAK,OAAO,oBAAoB,IAAI,KAAK,OAAO;AAClD,UAAM,aAAa;AAAA,MACjB,KAAK,kBAAkB,SAAS,YAAY,wBAAwB;AAAA,MACpE,QAAQ,kBAAkB,YAAY,YAAY,wBAAwB;AAAA,IAC5E;AAEA,UAAM,wBACJ,KAAK,OAAO,oBAAoB,IAAI,KAAK,OAAO;AAClD,UAAM,aAAa;AAAA,MACjB,MAAM,KAAK,OAAO,kBAAkB,UAAU,YAAY,wBAAwB;AAAA,MAClF,OAAO,KAAK,OAAO,kBAAkB,WAAW,YAAY,wBAAwB;AAAA,IACtF;AAEA,UAAM,wBAAwB,KAAK,OAAO,gBAAgB,KAAK,OAAO,eAAe;AACrF,UAAM,aAAa;AAAA,MACjB,KAAK,YAAY,wBAAwB;AAAA,IAC3C;AAEA,UAAM,eAAe,KAAK,OAAO,kBAAkB,WAAW;AAC9D,UAAM,cAAc,KAAK,OAAO,kBAAkB,WAAW,MAAM,WAAW;AAC9E,UAAM,gBACJ,KAAK,OAAO,aAAa,KAAK,OAAO,kBAAkB,IAAI,WAAW,OAAO,WAAW;AAC1F,UAAM,iBACJ,KAAK,OAAO,cACZ,KAAK,OAAO,kBAAkB,IAC9B,WAAW,MACX,WAAW,SACX,WAAW;AAEb,UAAM,oBAAoB,gBAAgB;AAC1C,UAAM,qBAAqB,iBAAiB;AAC5C,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,cACE,eACA,WACA,WACA,WACoB;AACpB,UAAM,EAAE,eAAe,WAAW,IAAI;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM,0BAA0B,QAAQ,KAAK,KAAK,cAAc;AAChE,UAAM,0BAA0B,QAAQ,KAAK,KAAK,YAAY;AAE9D,UAAM,aAAiC,CAAC;AAExC,QAAI,KAAK,KAAK,iBAAiB,WAAW;AACxC,iBAAW,KAAK;AAAA,QACd,MAAM,KAAK,KAAK;AAAA,QAChB,MAAM,KAAK,YAAY;AAAA,QACvB,GAAG,gBAAgB,0BAA0B,oBAAoB,IAAI;AAAA,QACrE,GACE,kBAAkB,QACd,KAAK,OAAO,oBAAoB,WAAW,MAC3C,KAAK,OAAO,oBACZ,cACA,iBACA,KAAK,OAAO;AAAA,QAClB,UAAU,KAAK,OAAO;AAAA,QACtB,aAAa,0BAA0B,WAAW;AAAA,QAClD,eAAe;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,KAAK,kBAAkB,WAAW;AACzC,iBAAW,KAAK;AAAA,QACd,MAAM,KAAK,KAAK;AAAA,QAChB,MAAM,KAAK,YAAY;AAAA,QACvB,GAAG,eAAe,qBAAqB,0BAA0B,oBAAoB,IAAI;AAAA,QACzF,GACE,kBAAkB,QACd,KAAK,OAAO,oBAAoB,WAAW,MAC3C,KAAK,OAAO,oBACZ,cACA,iBACA,KAAK,OAAO;AAAA,QAClB,UAAU,KAAK,OAAO;AAAA,QACtB,aAAa,0BAA0B,WAAW;AAAA,QAClD,eAAe;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,QAAI,KAAK,KAAK,mBAAmB,WAAW;AAC1C,iBAAW,KAAK;AAAA,QACd,MAAM,KAAK,KAAK;AAAA,QAChB,MAAM,KAAK,YAAY;AAAA,QACvB,GACE,KAAK,OAAO,kBAAkB,SAC1B,KAAK,OAAO,oBACZ,KAAK,OAAO,oBACZ,eACA,gBACA,KAAK,OAAO;AAAA,QAClB,GAAG,cAAc,kBAAkB,0BAA0B,qBAAqB,IAAI;AAAA,QACtF,UAAU,KAAK,OAAO;AAAA,QACtB,aAAa,0BAA0B,WAAW;AAAA,QAClD,eAAe;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,QAAI,KAAK,KAAK,gBAAgB,WAAW;AACvC,iBAAW,KAAK;AAAA,QACd,MAAM,KAAK,KAAK;AAAA,QAChB,MAAM,KAAK,YAAY;AAAA,QACvB,GACE,KAAK,OAAO,kBAAkB,SAC1B,KAAK,OAAO,oBACZ,KAAK,OAAO,oBACZ,eACA,gBACA,KAAK,OAAO;AAAA,QAClB,GACE,cAAc,sBAAsB,0BAA0B,qBAAqB,IAAI;AAAA,QACzF,UAAU,KAAK,OAAO;AAAA,QACtB,aAAa,0BAA0B,WAAW;AAAA,QAClD,eAAe;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,aAAa,WAAwD;AACnE,UAAM,EAAE,cAAc,IAAI;AAE1B,UAAM,EAAE,oBAAoB,cAAc,mBAAmB,YAAY,IAAI;AAE7E,UAAM,YAAqC;AAAA,MACzC;AAAA,QACE,MAAM;AAAA,UACJ,MAAM,KAAK,KAAK;AAAA,UAChB,MAAM,KAAK,YAAY;AAAA,UACvB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,UAAU,KAAK,OAAO;AAAA,UACtB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,UAAU;AAAA,QACZ;AAAA,QACA,GAAG,eAAe;AAAA,QAClB,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM,KAAK,YAAY;AAAA,MACzB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,UACJ,MAAM,KAAK,KAAK;AAAA,UAChB,MAAM,KAAK,YAAY;AAAA,UACvB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,UAAU,KAAK,OAAO;AAAA,UACtB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,UAAU;AAAA,QACZ;AAAA,QACA,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM,KAAK,YAAY;AAAA,MACzB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,UACJ,MAAM,KAAK,KAAK;AAAA,UAChB,MAAM,KAAK,YAAY;AAAA,UACvB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,UAAU,KAAK,OAAO;AAAA,UACtB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,UAAU;AAAA,QACZ;AAAA,QACA,GAAG;AAAA,QACH,GAAG,cAAc;AAAA,QACjB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM,KAAK,YAAY;AAAA,MACzB;AAAA,MACA;AAAA,QACE,MAAM;AAAA,UACJ,MAAM,KAAK,KAAK;AAAA,UAChB,MAAM,KAAK,YAAY;AAAA,UACvB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,UAAU,KAAK,OAAO;AAAA,UACtB,aAAa;AAAA,UACb,eAAe;AAAA,UACf,UAAU;AAAA,QACZ;AAAA,QACA,GAAG,eAAe;AAAA,QAClB,GAAG,cAAc;AAAA,QACjB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM,KAAK,YAAY;AAAA,MACzB;AAAA,IACF;AACA,eAAW,YAAY,WAAW;AAChC,eAAS,KAAK,IAAI,SAAS,IAAI,SAAS,QAAQ;AAEhD,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG;AACjC,iBAAS,KAAK,IAAI,SAAS,IAAI,SAAS,SAAS;AACjD,iBAAS,KAAK,gBAAgB;AAAA,MAEhC,OAAO;AACL,iBAAS,KAAK,IAAI,SAAS,IAAI,KAAK,OAAO;AAC3C,iBAAS,KAAK,gBAAgB;AAAA,MAChC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,kBAAkB,WAAoD;AACpE,UAAM,EAAE,cAAc,IAAI;AAE1B,UAAM,EAAE,gBAAgB,cAAc,aAAa,cAAc,IAAI;AAErE,UAAM,QAAQ,YAAY,EACvB,OAAO,CAAC,GAAG,CAAC,CAAC,EACb,MAAM,CAAC,cAAc,gBAAgB,YAAY,CAAC;AAErD,UAAM,QAAQ,YAAY,EACvB,OAAO,CAAC,GAAG,CAAC,CAAC,EACb,MAAM,CAAC,iBAAiB,aAAa,WAAW,CAAC;AAEpD,UAAM,SAA8B,KAAK,KAAK,OAAO,IAAI,CAAC,UAAU;AAClE,YAAM,cAAc,KAAK,QAAQ,IAAI,MAAM,SAAU;AACrD,UAAI,aAAa;AACf,gBAAQ,EAAE,GAAG,aAAa,GAAG,MAAM;AAAA,MACrC;AACA,YAAM,QAA2B;AAAA,QAC/B,GAAG,MAAM,MAAM,CAAC;AAAA,QAChB,GAAG,MAAM,MAAM,CAAC;AAAA,QAChB,MAAM,MAAM,SAAS,KAAK,YAAY;AAAA,QACtC,QAAQ,MAAM,UAAU,KAAK,OAAO;AAAA,QACpC,MAAM;AAAA,UACJ,MAAM,MAAM;AAAA,UACZ,MAAM,KAAK,YAAY;AAAA,UACvB,GAAG,MAAM,MAAM,CAAC;AAAA,UAChB,GAAG,MAAM,MAAM,CAAC,IAAI,KAAK,OAAO;AAAA,UAChC,aAAa;AAAA,UACb,eAAe;AAAA,UACf,UAAU,KAAK,OAAO;AAAA,UACtB,UAAU;AAAA,QACZ;AAAA,QACA,aAAa,MAAM,eAAe,KAAK,YAAY;AAAA,QACnD,aAAa,MAAM,eAAe;AAAA,MACpC;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,WAAW,WAAmD;AAC5D,UAAM,0BAA0B,KAAK,OAAO,oCAAoC;AAChF,UAAM,EAAE,cAAc,IAAI;AAE1B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM,cAAkC;AAAA;AAAA,MAEtC;AAAA,QACE,YAAY,KAAK,YAAY;AAAA,QAC7B,aAAa,KAAK,OAAO;AAAA,QACzB,IAAI,eAAe;AAAA,QACnB,IAAI;AAAA,QACJ,IAAI,eAAe,gBAAgB;AAAA,QACnC,IAAI;AAAA,MACN;AAAA;AAAA,MAEA;AAAA,QACE,YAAY,KAAK,YAAY;AAAA,QAC7B,aAAa,KAAK,OAAO;AAAA,QACzB,IAAI,eAAe;AAAA,QACnB,IAAI,cAAc;AAAA,QAClB,IAAI,eAAe;AAAA,QACnB,IAAI,cAAc,iBAAiB;AAAA,MACrC;AAAA;AAAA,MAEA;AAAA,QACE,YAAY,KAAK,YAAY;AAAA,QAC7B,aAAa,KAAK,OAAO;AAAA,QACzB,IAAI,eAAe;AAAA,QACnB,IAAI,cAAc;AAAA,QAClB,IAAI,eAAe,gBAAgB;AAAA,QACnC,IAAI,cAAc;AAAA,MACpB;AAAA;AAAA,MAEA;AAAA,QACE,YAAY,KAAK,YAAY;AAAA,QAC7B,aAAa,KAAK,OAAO;AAAA,QACzB,IAAI;AAAA,QACJ,IAAI,cAAc;AAAA,QAClB,IAAI;AAAA,QACJ,IAAI,cAAc,iBAAiB;AAAA,MACrC;AAAA;AAAA,MAEA;AAAA,QACE,YAAY,KAAK,YAAY;AAAA,QAC7B,aAAa,KAAK,OAAO;AAAA,QACzB,IAAI,eAAe;AAAA,QACnB,IAAI,cAAc;AAAA,QAClB,IAAI,eAAe;AAAA,QACnB,IAAI,cAAc,iBAAiB;AAAA,MACrC;AAAA;AAAA,MAEA;AAAA,QACE,YAAY,KAAK,YAAY;AAAA,QAC7B,aAAa,KAAK,OAAO;AAAA,QACzB,IAAI,eAAe;AAAA,QACnB,IAAI,cAAc;AAAA,QAClB,IAAI,eAAe,gBAAgB;AAAA,QACnC,IAAI,cAAc;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,WAAkD;AACzD,QAAI,WAAW;AACb,aAAO;AAAA,QACL,MAAM,KAAK,KAAK;AAAA,QAChB,MAAM,KAAK,YAAY;AAAA,QACvB,UAAU,KAAK,OAAO;AAAA,QACtB,eAAe;AAAA,QACf,aAAa;AAAA,QACb,UAAU;AAAA,QACV,GAAG,KAAK,OAAO;AAAA,QACf,GAAG,KAAK,OAAO,aAAa;AAAA,MAC9B;AAAA,IACF;AACA;AAAA,EACF;AAAA,EAEA,QAA2B;AACzB,UAAM,YACJ,KAAK,OAAO,aAAa,CAAC,EAAE,KAAK,KAAK,iBAAiB,KAAK,KAAK;AACnE,UAAM,YACJ,KAAK,OAAO,aAAa,CAAC,EAAE,KAAK,KAAK,gBAAgB,KAAK,KAAK;AAClE,UAAM,YAAY,KAAK,OAAO,aAAa,CAAC,CAAC,KAAK,KAAK;AAEvD,UAAM,gBAAgB,KAAK,KAAK,OAAO,SAAS,IAAI,WAAW,KAAK,OAAO;AAE3E,UAAM,kBAAkB,KAAK,eAAe,eAAe,WAAW,WAAW,SAAS;AAE1F,WAAO;AAAA,MACL,QAAQ,KAAK,kBAAkB,eAAe;AAAA,MAC9C,WAAW,KAAK,aAAa,eAAe;AAAA,MAC5C,YAAY,KAAK,cAAc,eAAe,WAAW,WAAW,eAAe;AAAA,MACnF,aAAa,KAAK,WAAW,eAAe;AAAA,MAC5C,OAAO,KAAK,SAAS,SAAS;AAAA,IAChC;AAAA,EACF;AACF;;;AC9mBA,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAAtC,OAAsC;AAAA;AAAA;AAAA,EACpC,YAAY,OAAe,OAAe,MAAc;AACtD,UAAM,aAAa,KAAK,IAAI,KAAK,mCAAmC,IAAI,EAAE;AAC1E,SAAK,OAAO;AAAA,EACd;AACF;AAEA,SAAS,gBAAgB,OAAwB;AAC/C,SAAO,CAAC,oCAAoC,KAAK,KAAK;AACxD;AAFS;AAIT,SAAS,eAAe,OAAwB;AAC9C,SAAO,CAAC,QAAQ,KAAK,KAAK;AAC5B;AAFS;AAIT,SAAS,qBAAqB,OAAwB;AACpD,SAAO,CAAC,UAAU,KAAK,KAAK;AAC9B;AAFS;;;ACKT,IAAM,SAAS,UAAU;AAEzB,SAAS,cAAc,MAAc;AACnC,SAAO,aAAa,KAAK,KAAK,GAAG,MAAM;AACzC;AAFS;AAST,IAAM,kBAAkB,IAAI,gBAAgB;AAE5C,SAAS,iBAAiB,SAAqB;AAC7C,kBAAgB,QAAQ,EAAE,eAAe,cAAc,QAAQ,IAAI,EAAE,CAAC;AACxE;AAFS;AAIT,SAAS,iBAAiB,SAAqB;AAC7C,kBAAgB,QAAQ,EAAE,eAAe,cAAc,QAAQ,IAAI,EAAE,CAAC;AACxE;AAFS;AAIT,SAAS,iBAAiB,SAAqB;AAC7C,kBAAgB,QAAQ,EAAE,eAAe,cAAc,QAAQ,IAAI,EAAE,CAAC;AACxE;AAFS;AAIT,SAAS,iBAAiB,SAAqB;AAC7C,kBAAgB,QAAQ,EAAE,eAAe,cAAc,QAAQ,IAAI,EAAE,CAAC;AACxE;AAFS;AAIT,SAAS,iBAAiB,SAAqB;AAC7C,kBAAgB,QAAQ,EAAE,eAAe,cAAc,QAAQ,IAAI,EAAE,CAAC;AACxE;AAFS;AAIT,SAAS,kBAAkB,SAAqB;AAC9C,kBAAgB,QAAQ,EAAE,gBAAgB,cAAc,QAAQ,IAAI,EAAE,CAAC;AACzE;AAFS;AAIT,SAAS,gBAAgB,SAAqB;AAC5C,kBAAgB,QAAQ,EAAE,cAAc,cAAc,QAAQ,IAAI,EAAE,CAAC;AACvE;AAFS;AAIT,SAAS,mBAAmB,SAAqB;AAC/C,kBAAgB,QAAQ,EAAE,iBAAiB,cAAc,QAAQ,IAAI,EAAE,CAAC;AAC1E;AAFS;AAIT,SAAS,YAAY,QAAgC;AACnD,QAAM,eAA6B,CAAC;AACpC,aAAW,SAAS,QAAQ;AAC1B,UAAM,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,EAAE,MAAM,SAAS;AACjD,QAAI,QAAQ,UAAU;AACpB,UAAI,eAAe,KAAK,GAAG;AACzB,cAAM,IAAI,kBAAkB,KAAK,OAAO,QAAQ;AAAA,MAClD;AACA,mBAAa,SAAS,SAAS,KAAK;AAAA,IACtC,WAAW,QAAQ,SAAS;AAC1B,UAAI,gBAAgB,KAAK,GAAG;AAC1B,cAAM,IAAI,kBAAkB,KAAK,OAAO,UAAU;AAAA,MACpD;AACA,mBAAa,QAAQ;AAAA,IACvB,WAAW,QAAQ,gBAAgB;AACjC,UAAI,gBAAgB,KAAK,GAAG;AAC1B,cAAM,IAAI,kBAAkB,KAAK,OAAO,UAAU;AAAA,MACpD;AACA,mBAAa,cAAc;AAAA,IAC7B,WAAW,QAAQ,gBAAgB;AACjC,UAAI,qBAAqB,KAAK,GAAG;AAC/B,cAAM,IAAI,kBAAkB,KAAK,OAAO,6BAA6B;AAAA,MACvE;AACA,mBAAa,cAAc;AAAA,IAC7B,OAAO;AACL,YAAM,IAAI,MAAM,eAAe,GAAG,oBAAoB;AAAA,IACxD;AAAA,EACF;AACA,SAAO;AACT;AA7BS;AA+BT,SAAS,SAAS,SAAqB,WAAmB,GAAW,GAAW,QAAkB;AAChG,QAAM,eAAe,YAAY,MAAM;AACvC,kBAAgB,UAAU;AAAA,IACxB;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM,cAAc,QAAQ,IAAI;AAAA,MAChC;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF,CAAC;AACH;AAXS;AAaT,SAAS,SAAS,WAAmB,QAAkB;AACrD,kBAAgB,SAAS,WAAW,YAAY,MAAM,CAAC;AACzD;AAFS;AAIT,SAAS,SAAS,OAAe;AAC/B,kBAAgB,UAAU,EAAE,YAAY,MAAM,CAAC;AACjD;AAFS;AAIT,SAAS,UAAU,QAAgB;AACjC,kBAAgB,UAAU,EAAE,aAAa,OAAO,CAAC;AACnD;AAFS;AAIT,SAAS,kBAAkB;AACzB,QAAMC,UAAS,UAAU;AACzB,QAAM,EAAE,gBAAgB,eAAe,oBAAoB,IAAIA;AAC/D,MAAI,qBAAqB;AACvB,oBAAgB,UAAU,mBAAmB;AAAA,EAC/C;AACA,kBAAgB,eAAe;AAAA,IAC7B,eAAe,eAAe;AAAA,IAC9B,eAAe,eAAe;AAAA,IAC9B,eAAe,eAAe;AAAA,IAC9B,eAAe,eAAe;AAAA,IAC9B,mBAAmB,eAAe;AAAA,IAClC,mBAAmB,eAAe;AAAA,IAClC,mBAAmB,eAAe;AAAA,IAClC,mBAAmB,eAAe;AAAA,IAClC,mBAAmB,eAAe;AAAA,IAClC,uBAAuB,eAAe;AAAA,IACtC,uBAAuB,eAAe;AAAA,IACtC,uBAAuB,eAAe;AAAA,IACtC,kCAAkC,eAAe;AAAA,IACjD,kCAAkC,eAAe;AAAA,IACjD,mBAAmB,eAAe;AAAA,EACpC,CAAC;AACD,kBAAgB,QAAQ,EAAE,WAAW,gBAAgB,EAAE,CAAC;AACxD,SAAO,gBAAgB,MAAM;AAC/B;AAzBS;AA2BT,IAAMC,SAAQ,kCAAY;AACxB,kBAAgB,MAAM;AACtB,QAAY;AACd,GAHc;AAKd,IAAO,qBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC9KA,SAAS,cAAc;AAehB,IAAM,OAAO,wBAAC,KAAa,IAAY,UAAkB,YAAqB;AACnF,WAAS,oBAAoB,eAAkC;AAC7D,WAAO,kBAAkB,QAAQ,YAAY;AAAA,EAC/C;AAFS;AAIT,WAAS,cAAc,aAA8B;AACnD,WAAO,gBAAgB,SAAS,UAAU;AAAA,EAC5C;AAFS;AAIT,WAAS,kBAAkB,MAAkD;AAC3E,WAAO,aAAa,KAAK,CAAC,KAAK,KAAK,CAAC,YAAY,KAAK,YAAY,CAAC;AAAA,EACrE;AAFS;AAIT,QAAM,OAAO,UAAU;AAEvB,MAAI,MAAM,+BAA+B,GAAG;AAE5C,QAAM,gBAAgB,KAAK;AAE3B,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,OAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACd,OAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,OAAO,MAAM;AAEnB,QAAM,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AAEtC,QAAM,QAAQ,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM;AAElD,QAAM,QAAQ,KAAK,eAAe,cAAc;AAChD,QAAM,SAAS,KAAK,eAAe,eAAe;AAElD,mBAAiB,KAAK,QAAQ,OAAO,KAAK,eAAe,eAAe,IAAI;AAE5E,MAAI,KAAK,WAAW,SAAS,QAAQ,MAAM,MAAM;AAGjD,UAAQ,GAAG,UAAU,MAAM;AAE3B,UAAQ,GAAG,SAAS,KAAK;AAGzB,QAAM,eAAkC,QAAQ,GAAG,gBAAgB;AAEnE,QAAM,iBAAiB,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,WAAW;AAClE,QAAM,cAAc,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ;AAC5D,QAAM,iBAAiB,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AACpE,QAAM,aAAa,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ;AAC3D,QAAM,aAAa,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAE1D,MAAI,aAAa,OAAO;AACtB,eACG,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,QAAQ,aAAa,MAAM,IAAI,EACpC,KAAK,aAAa,aAAa,MAAM,QAAQ,EAC7C,KAAK,qBAAqB,oBAAoB,aAAa,MAAM,aAAa,CAAC,EAC/E,KAAK,eAAe,cAAc,aAAa,MAAM,WAAW,CAAC,EACjE,KAAK,aAAa,kBAAkB,aAAa,KAAK,CAAC,EACvD,KAAK,aAAa,MAAM,IAAI;AAAA,EACjC;AAEA,MAAI,aAAa,aAAa;AAC5B,gBACG,UAAU,MAAM,EAChB,KAAK,aAAa,WAAW,EAC7B,MAAM,EACN,OAAO,MAAM,EACb,KAAK,MAAM,CAAC,SAA2B,KAAK,EAAE,EAC9C,KAAK,MAAM,CAAC,SAA2B,KAAK,EAAE,EAC9C,KAAK,MAAM,CAAC,SAA2B,KAAK,EAAE,EAC9C,KAAK,MAAM,CAAC,SAA2B,KAAK,EAAE,EAC9C,MAAM,UAAU,CAAC,SAA2B,KAAK,UAAU,EAC3D,MAAM,gBAAgB,CAAC,SAA2B,KAAK,WAAW;AAAA,EACvE;AAEA,QAAM,YAAY,eACf,UAAU,YAAY,EACtB,KAAK,aAAa,SAAS,EAC3B,MAAM,EACN,OAAO,GAAG,EACV,KAAK,SAAS,UAAU;AAE3B,YACG,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,SAAgC,KAAK,CAAC,EACjD,KAAK,KAAK,CAAC,SAAgC,KAAK,CAAC,EACjD,KAAK,SAAS,CAAC,SAAgC,KAAK,KAAK,EACzD,KAAK,UAAU,CAAC,SAAgC,KAAK,MAAM,EAC3D,KAAK,QAAQ,CAAC,SAAgC,KAAK,IAAI;AAE1D,YACG,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,QAAQ,CAAC,SAAgC,KAAK,KAAK,IAAI,EAC5D,KAAK,aAAa,CAAC,SAAgC,KAAK,KAAK,QAAQ,EACrE;AAAA,IAAK;AAAA,IAAqB,CAAC,SAC1B,oBAAoB,KAAK,KAAK,aAAa;AAAA,EAC7C,EACC,KAAK,eAAe,CAAC,SAAgC,cAAc,KAAK,KAAK,WAAW,CAAC,EACzF,KAAK,aAAa,CAAC,SAAgC,kBAAkB,KAAK,IAAI,CAAC,EAC/E,KAAK,CAAC,SAAgC,KAAK,KAAK,IAAI;AAEvD,QAAM,SAAS,WACZ,UAAU,SAAS,EACnB,KAAK,aAAa,UAAU,EAC5B,MAAM,EACN,OAAO,GAAG,EACV,KAAK,SAAS,OAAO;AAExB,SACG,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,CAAC,SAA2B,KAAK,IAAI,EAC1C,KAAK,QAAQ,CAAC,SAA2B,KAAK,IAAI,EAClD,KAAK,aAAa,CAAC,SAA2B,KAAK,QAAQ,EAC3D,KAAK,qBAAqB,CAAC,SAA2B,oBAAoB,KAAK,aAAa,CAAC,EAC7F,KAAK,eAAe,CAAC,SAA2B,cAAc,KAAK,WAAW,CAAC,EAC/E,KAAK,aAAa,CAAC,SAA2B,kBAAkB,IAAI,CAAC;AAExE,QAAM,aAAa,eAChB,UAAU,cAAc,EACxB,KAAK,aAAa,MAAM,EACxB,MAAM,EACN,OAAO,GAAG,EACV,KAAK,SAAS,YAAY;AAE7B,aACG,OAAO,QAAQ,EACf,KAAK,MAAM,CAAC,SAA4B,KAAK,CAAC,EAC9C,KAAK,MAAM,CAAC,SAA4B,KAAK,CAAC,EAC9C,KAAK,KAAK,CAAC,SAA4B,KAAK,MAAM,EAClD,KAAK,QAAQ,CAAC,SAA4B,KAAK,IAAI,EACnD,KAAK,UAAU,CAAC,SAA4B,KAAK,WAAW,EAC5D,KAAK,gBAAgB,CAAC,SAA4B,KAAK,WAAW;AAErE,aACG,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,CAAC,SAA4B,KAAK,KAAK,IAAI,EAChD,KAAK,QAAQ,CAAC,SAA4B,KAAK,KAAK,IAAI,EACxD,KAAK,aAAa,CAAC,SAA4B,KAAK,KAAK,QAAQ,EACjE;AAAA,IAAK;AAAA,IAAqB,CAAC,SAC1B,oBAAoB,KAAK,KAAK,aAAa;AAAA,EAC7C,EACC,KAAK,eAAe,CAAC,SAA4B,cAAc,KAAK,KAAK,WAAW,CAAC,EACrF,KAAK,aAAa,CAAC,SAA4B,kBAAkB,KAAK,IAAI,CAAC;AAChF,GA1JoB;AA4JpB,IAAO,2BAAQ;AAAA,EACb;AACF;;;ACxKO,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,6BAAM,IAAN;AACV;", "names": ["o", "parser", "lexer", "config", "config", "clear"]}