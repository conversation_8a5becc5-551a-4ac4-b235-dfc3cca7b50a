{"version": 3, "sources": ["../../../src/diagrams/sequence/parser/sequenceDiagram.jison", "../../../src/diagrams/sequence/sequenceDb.ts", "../../../src/diagrams/sequence/styles.js", "../../../src/diagrams/sequence/sequenceRenderer.ts", "../../../src/diagrams/sequence/svgDraw.js", "../../../src/diagrams/sequence/sequenceDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,2],$V1=[1,3],$V2=[1,4],$V3=[2,4],$V4=[1,9],$V5=[1,11],$V6=[1,13],$V7=[1,14],$V8=[1,16],$V9=[1,17],$Va=[1,18],$Vb=[1,24],$Vc=[1,25],$Vd=[1,26],$Ve=[1,27],$Vf=[1,28],$Vg=[1,29],$Vh=[1,30],$Vi=[1,31],$Vj=[1,32],$Vk=[1,33],$Vl=[1,34],$Vm=[1,35],$Vn=[1,36],$Vo=[1,37],$Vp=[1,38],$Vq=[1,39],$Vr=[1,41],$Vs=[1,42],$Vt=[1,43],$Vu=[1,44],$Vv=[1,45],$Vw=[1,46],$Vx=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],$Vy=[4,5,16,50,52,53],$Vz=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],$VA=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],$VB=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],$VC=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],$VD=[68,69,70],$VE=[1,122];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"SPACE\":4,\"NEWLINE\":5,\"SD\":6,\"document\":7,\"line\":8,\"statement\":9,\"box_section\":10,\"box_line\":11,\"participant_statement\":12,\"create\":13,\"box\":14,\"restOfLine\":15,\"end\":16,\"signal\":17,\"autonumber\":18,\"NUM\":19,\"off\":20,\"activate\":21,\"actor\":22,\"deactivate\":23,\"note_statement\":24,\"links_statement\":25,\"link_statement\":26,\"properties_statement\":27,\"details_statement\":28,\"title\":29,\"legacy_title\":30,\"acc_title\":31,\"acc_title_value\":32,\"acc_descr\":33,\"acc_descr_value\":34,\"acc_descr_multiline_value\":35,\"loop\":36,\"rect\":37,\"opt\":38,\"alt\":39,\"else_sections\":40,\"par\":41,\"par_sections\":42,\"par_over\":43,\"critical\":44,\"option_sections\":45,\"break\":46,\"option\":47,\"and\":48,\"else\":49,\"participant\":50,\"AS\":51,\"participant_actor\":52,\"destroy\":53,\"note\":54,\"placement\":55,\"text2\":56,\"over\":57,\"actor_pair\":58,\"links\":59,\"link\":60,\"properties\":61,\"details\":62,\"spaceList\":63,\",\":64,\"left_of\":65,\"right_of\":66,\"signaltype\":67,\"+\":68,\"-\":69,\"ACTOR\":70,\"SOLID_OPEN_ARROW\":71,\"DOTTED_OPEN_ARROW\":72,\"SOLID_ARROW\":73,\"BIDIRECTIONAL_SOLID_ARROW\":74,\"DOTTED_ARROW\":75,\"BIDIRECTIONAL_DOTTED_ARROW\":76,\"SOLID_CROSS\":77,\"DOTTED_CROSS\":78,\"SOLID_POINT\":79,\"DOTTED_POINT\":80,\"TXT\":81,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"SPACE\",5:\"NEWLINE\",6:\"SD\",13:\"create\",14:\"box\",15:\"restOfLine\",16:\"end\",18:\"autonumber\",19:\"NUM\",20:\"off\",21:\"activate\",23:\"deactivate\",29:\"title\",30:\"legacy_title\",31:\"acc_title\",32:\"acc_title_value\",33:\"acc_descr\",34:\"acc_descr_value\",35:\"acc_descr_multiline_value\",36:\"loop\",37:\"rect\",38:\"opt\",39:\"alt\",41:\"par\",43:\"par_over\",44:\"critical\",46:\"break\",47:\"option\",48:\"and\",49:\"else\",50:\"participant\",51:\"AS\",52:\"participant_actor\",53:\"destroy\",54:\"note\",57:\"over\",59:\"links\",60:\"link\",61:\"properties\",62:\"details\",64:\",\",65:\"left_of\",66:\"right_of\",68:\"+\",69:\"-\",70:\"ACTOR\",71:\"SOLID_OPEN_ARROW\",72:\"DOTTED_OPEN_ARROW\",73:\"SOLID_ARROW\",74:\"BIDIRECTIONAL_SOLID_ARROW\",75:\"DOTTED_ARROW\",76:\"BIDIRECTIONAL_DOTTED_ARROW\",77:\"SOLID_CROSS\",78:\"DOTTED_CROSS\",79:\"SOLID_POINT\",80:\"DOTTED_POINT\",81:\"TXT\"},\nproductions_: [0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 3:\n yy.apply($$[$0]);return $$[$0]; \nbreak;\ncase 4: case 9:\n this.$ = [] \nbreak;\ncase 5: case 10:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 6: case 7: case 11: case 12:\n this.$ = $$[$0] \nbreak;\ncase 8: case 13:\n this.$=[]; \nbreak;\ncase 15:\n$$[$0].type='createParticipant'; this.$=$$[$0];\nbreak;\ncase 16:\n\n\t\t$$[$0-1].unshift({type: 'boxStart', boxData:yy.parseBoxData($$[$0-2]) });\n\t\t$$[$0-1].push({type: 'boxEnd', boxText:$$[$0-2]});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 18:\n this.$= {type:'sequenceIndex',sequenceIndex: Number($$[$0-2]), sequenceIndexStep:Number($$[$0-1]), sequenceVisible:true, signalType:yy.LINETYPE.AUTONUMBER};\nbreak;\ncase 19:\n this.$ = {type:'sequenceIndex',sequenceIndex: Number($$[$0-1]), sequenceIndexStep:1, sequenceVisible:true, signalType:yy.LINETYPE.AUTONUMBER};\nbreak;\ncase 20:\n this.$ = {type:'sequenceIndex', sequenceVisible:false, signalType:yy.LINETYPE.AUTONUMBER};\nbreak;\ncase 21:\nthis.$ = {type:'sequenceIndex', sequenceVisible:true, signalType:yy.LINETYPE.AUTONUMBER}; \nbreak;\ncase 22:\nthis.$={type: 'activeStart', signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0-1].actor};\nbreak;\ncase 23:\nthis.$={type: 'activeEnd', signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0-1].actor};\nbreak;\ncase 29:\nyy.setDiagramTitle($$[$0].substring(6));this.$=$$[$0].substring(6);\nbreak;\ncase 30:\nyy.setDiagramTitle($$[$0].substring(7));this.$=$$[$0].substring(7);\nbreak;\ncase 31:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 32: case 33:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 34:\n\n\t\t$$[$0-1].unshift({type: 'loopStart', loopText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.LOOP_START});\n\t\t$$[$0-1].push({type: 'loopEnd', loopText:$$[$0-2], signalType: yy.LINETYPE.LOOP_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 35:\n\n\t\t$$[$0-1].unshift({type: 'rectStart', color:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.RECT_START });\n\t\t$$[$0-1].push({type: 'rectEnd', color:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.RECT_END });\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 36:\n\n\t\t$$[$0-1].unshift({type: 'optStart', optText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.OPT_START});\n\t\t$$[$0-1].push({type: 'optEnd', optText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.OPT_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 37:\n\n\t\t// Alt start\n\t\t$$[$0-1].unshift({type: 'altStart', altText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.ALT_START});\n\t\t// Content in alt is already in $$[$0-1]\n\t\t// End\n\t\t$$[$0-1].push({type: 'altEnd', signalType: yy.LINETYPE.ALT_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 38:\n\n\t\t// Parallel start\n\t\t$$[$0-1].unshift({type: 'parStart', parText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.PAR_START});\n\t\t// Content in par is already in $$[$0-1]\n\t\t// End\n\t\t$$[$0-1].push({type: 'parEnd', signalType: yy.LINETYPE.PAR_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 39:\n\n\t\t// Parallel (overlapped) start\n\t\t$$[$0-1].unshift({type: 'parStart', parText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.PAR_OVER_START});\n\t\t// Content in par is already in $$[$0-1]\n\t\t// End\n\t\t$$[$0-1].push({type: 'parEnd', signalType: yy.LINETYPE.PAR_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 40:\n\n\t\t// critical start\n\t\t$$[$0-1].unshift({type: 'criticalStart', criticalText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.CRITICAL_START});\n\t\t// Content in critical is already in $$[$0-1]\n\t\t// critical end\n\t\t$$[$0-1].push({type: 'criticalEnd', signalType: yy.LINETYPE.CRITICAL_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 41:\n\n\t\t$$[$0-1].unshift({type: 'breakStart', breakText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.BREAK_START});\n\t\t$$[$0-1].push({type: 'breakEnd', optText:yy.parseMessage($$[$0-2]), signalType: yy.LINETYPE.BREAK_END});\n\t\tthis.$=$$[$0-1];\nbreak;\ncase 43:\n this.$ = $$[$0-3].concat([{type: 'option', optionText:yy.parseMessage($$[$0-1]), signalType: yy.LINETYPE.CRITICAL_OPTION}, $$[$0]]); \nbreak;\ncase 45:\n this.$ = $$[$0-3].concat([{type: 'and', parText:yy.parseMessage($$[$0-1]), signalType: yy.LINETYPE.PAR_AND}, $$[$0]]); \nbreak;\ncase 47:\n this.$ = $$[$0-3].concat([{type: 'else', altText:yy.parseMessage($$[$0-1]), signalType: yy.LINETYPE.ALT_ELSE}, $$[$0]]); \nbreak;\ncase 48:\n$$[$0-3].draw='participant'; $$[$0-3].type='addParticipant';$$[$0-3].description=yy.parseMessage($$[$0-1]); this.$=$$[$0-3];\nbreak;\ncase 49:\n$$[$0-1].draw='participant'; $$[$0-1].type='addParticipant';this.$=$$[$0-1];\nbreak;\ncase 50:\n$$[$0-3].draw='actor'; $$[$0-3].type='addParticipant';$$[$0-3].description=yy.parseMessage($$[$0-1]); this.$=$$[$0-3];\nbreak;\ncase 51:\n$$[$0-1].draw='actor'; $$[$0-1].type='addParticipant'; this.$=$$[$0-1];\nbreak;\ncase 52:\n$$[$0-1].type='destroyParticipant'; this.$=$$[$0-1];\nbreak;\ncase 53:\n\n\t\tthis.$ = [$$[$0-1], {type:'addNote', placement:$$[$0-2], actor:$$[$0-1].actor, text:$$[$0]}];\nbreak;\ncase 54:\n\n\t\t// Coerce actor_pair into a [to, from, ...] array\n\t\t$$[$0-2] = [].concat($$[$0-1], $$[$0-1]).slice(0, 2);\n\t\t$$[$0-2][0] = $$[$0-2][0].actor;\n\t\t$$[$0-2][1] = $$[$0-2][1].actor;\n\t\tthis.$ = [$$[$0-1], {type:'addNote', placement:yy.PLACEMENT.OVER, actor:$$[$0-2].slice(0, 2), text:$$[$0]}];\nbreak;\ncase 55:\n\n\t\tthis.$ = [$$[$0-1], {type:'addLinks', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 56:\n\n\t\tthis.$ = [$$[$0-1], {type:'addALink', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 57:\n\n\t\tthis.$ = [$$[$0-1], {type:'addProperties', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 58:\n\n\t\tthis.$ = [$$[$0-1], {type:'addDetails', actor:$$[$0-1].actor, text:$$[$0]}];\n  \nbreak;\ncase 61:\n this.$ = [$$[$0-2], $$[$0]]; \nbreak;\ncase 62:\n this.$ = $$[$0]; \nbreak;\ncase 63:\n this.$ = yy.PLACEMENT.LEFTOF; \nbreak;\ncase 64:\n this.$ = yy.PLACEMENT.RIGHTOF; \nbreak;\ncase 65:\n this.$ = [$$[$0-4],$$[$0-1],{type: 'addMessage', from:$$[$0-4].actor, to:$$[$0-1].actor, signalType:$$[$0-3], msg:$$[$0], activate: true},\n\t              {type: 'activeStart', signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0-1].actor}\n\t             ]\nbreak;\ncase 66:\n this.$ = [$$[$0-4],$$[$0-1],{type: 'addMessage', from:$$[$0-4].actor, to:$$[$0-1].actor, signalType:$$[$0-3], msg:$$[$0]},\n\t             {type: 'activeEnd', signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0-4].actor}\n\t             ]\nbreak;\ncase 67:\n this.$ = [$$[$0-3],$$[$0-1],{type: 'addMessage', from:$$[$0-3].actor, to:$$[$0-1].actor, signalType:$$[$0-2], msg:$$[$0]}]\nbreak;\ncase 68:\nthis.$={ type: 'addParticipant', actor:$$[$0]}\nbreak;\ncase 69:\n this.$ = yy.LINETYPE.SOLID_OPEN; \nbreak;\ncase 70:\n this.$ = yy.LINETYPE.DOTTED_OPEN; \nbreak;\ncase 71:\n this.$ = yy.LINETYPE.SOLID; \nbreak;\ncase 72:\n this.$ = yy.LINETYPE.BIDIRECTIONAL_SOLID; \nbreak;\ncase 73:\n this.$ = yy.LINETYPE.DOTTED; \nbreak;\ncase 74:\n this.$ = yy.LINETYPE.BIDIRECTIONAL_DOTTED; \nbreak;\ncase 75:\n this.$ = yy.LINETYPE.SOLID_CROSS; \nbreak;\ncase 76:\n this.$ = yy.LINETYPE.DOTTED_CROSS; \nbreak;\ncase 77:\n this.$ = yy.LINETYPE.SOLID_POINT; \nbreak;\ncase 78:\n this.$ = yy.LINETYPE.DOTTED_POINT; \nbreak;\ncase 79:\nthis.$ = yy.parseMessage($$[$0].trim().substring(1)) \nbreak;\n}\n},\ntable: [{3:1,4:$V0,5:$V1,6:$V2},{1:[3]},{3:5,4:$V0,5:$V1,6:$V2},{3:6,4:$V0,5:$V1,6:$V2},o([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],$V3,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},o($Vx,[2,5]),{9:47,12:12,13:$V6,14:$V7,17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},o($Vx,[2,7]),o($Vx,[2,8]),o($Vx,[2,14]),{12:48,50:$Vo,52:$Vp,53:$Vq},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:$Vw},{22:55,70:$Vw},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},o($Vx,[2,29]),o($Vx,[2,30]),{32:[1,61]},{34:[1,62]},o($Vx,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:$Vw},{22:72,70:$Vw},{22:73,70:$Vw},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:$Vw},{22:90,70:$Vw},{22:91,70:$Vw},{22:92,70:$Vw},o([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),o($Vx,[2,6]),o($Vx,[2,15]),o($Vy,[2,9],{10:93}),o($Vx,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},o($Vx,[2,21]),{5:[1,97]},{5:[1,98]},o($Vx,[2,24]),o($Vx,[2,25]),o($Vx,[2,26]),o($Vx,[2,27]),o($Vx,[2,28]),o($Vx,[2,31]),o($Vx,[2,32]),o($Vz,$V3,{7:99}),o($Vz,$V3,{7:100}),o($Vz,$V3,{7:101}),o($VA,$V3,{40:102,7:103}),o($VB,$V3,{42:104,7:105}),o($VB,$V3,{7:105,42:106}),o($VC,$V3,{45:107,7:108}),o($Vz,$V3,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:$Vw},o($VD,[2,69]),o($VD,[2,70]),o($VD,[2,71]),o($VD,[2,72]),o($VD,[2,73]),o($VD,[2,74]),o($VD,[2,75]),o($VD,[2,76]),o($VD,[2,77]),o($VD,[2,78]),{22:118,70:$Vw},{22:120,58:119,70:$Vw},{70:[2,63]},{70:[2,64]},{56:121,81:$VE},{56:123,81:$VE},{56:124,81:$VE},{56:125,81:$VE},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:$Vo,52:$Vp,53:$Vq},{5:[1,131]},o($Vx,[2,19]),o($Vx,[2,20]),o($Vx,[2,22]),o($Vx,[2,23]),{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,132],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,133],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,134],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{16:[1,135]},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[2,46],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,49:[1,136],50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{16:[1,137]},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[2,44],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,48:[1,138],50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{16:[1,139]},{16:[1,140]},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[2,42],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,47:[1,141],50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{4:$V4,5:$V5,8:8,9:10,12:12,13:$V6,14:$V7,16:[1,142],17:15,18:$V8,21:$V9,22:40,23:$Va,24:19,25:20,26:21,27:22,28:23,29:$Vb,30:$Vc,31:$Vd,33:$Ve,35:$Vf,36:$Vg,37:$Vh,38:$Vi,39:$Vj,41:$Vk,43:$Vl,44:$Vm,46:$Vn,50:$Vo,52:$Vp,53:$Vq,54:$Vr,59:$Vs,60:$Vt,61:$Vu,62:$Vv,70:$Vw},{15:[1,143]},o($Vx,[2,49]),{15:[1,144]},o($Vx,[2,51]),o($Vx,[2,52]),{22:145,70:$Vw},{22:146,70:$Vw},{56:147,81:$VE},{56:148,81:$VE},{56:149,81:$VE},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},o($Vx,[2,16]),o($Vy,[2,10]),{12:151,50:$Vo,52:$Vp,53:$Vq},o($Vy,[2,12]),o($Vy,[2,13]),o($Vx,[2,18]),o($Vx,[2,34]),o($Vx,[2,35]),o($Vx,[2,36]),o($Vx,[2,37]),{15:[1,152]},o($Vx,[2,38]),{15:[1,153]},o($Vx,[2,39]),o($Vx,[2,40]),{15:[1,154]},o($Vx,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:$VE},{56:158,81:$VE},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:$Vw},o($Vy,[2,11]),o($VA,$V3,{7:103,40:160}),o($VB,$V3,{7:105,42:161}),o($VC,$V3,{7:108,45:162}),o($Vx,[2,48]),o($Vx,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],\ndefaultActions: {5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 5;\nbreak;\ncase 1:/* skip all whitespace */\nbreak;\ncase 2:/* skip same-line whitespace */\nbreak;\ncase 3:/* skip comments */\nbreak;\ncase 4:/* skip comments */\nbreak;\ncase 5:/* skip comments */\nbreak;\ncase 6:return 19;\nbreak;\ncase 7: this.begin('LINE'); return 14; \nbreak;\ncase 8: this.begin('ID'); return 50; \nbreak;\ncase 9: this.begin('ID'); return 52; \nbreak;\ncase 10:return 13;\nbreak;\ncase 11: this.begin('ID'); return 53; \nbreak;\ncase 12: yy_.yytext = yy_.yytext.trim(); this.begin('ALIAS'); return 70; \nbreak;\ncase 13: this.popState(); this.popState(); this.begin('LINE'); return 51; \nbreak;\ncase 14: this.popState(); this.popState(); return 5; \nbreak;\ncase 15: this.begin('LINE'); return 36; \nbreak;\ncase 16: this.begin('LINE'); return 37; \nbreak;\ncase 17: this.begin('LINE'); return 38; \nbreak;\ncase 18: this.begin('LINE'); return 39; \nbreak;\ncase 19: this.begin('LINE'); return 49; \nbreak;\ncase 20: this.begin('LINE'); return 41; \nbreak;\ncase 21: this.begin('LINE'); return 43; \nbreak;\ncase 22: this.begin('LINE'); return 48; \nbreak;\ncase 23: this.begin('LINE'); return 44; \nbreak;\ncase 24: this.begin('LINE'); return 47; \nbreak;\ncase 25: this.begin('LINE'); return 46; \nbreak;\ncase 26: this.popState(); return 15; \nbreak;\ncase 27:return 16;\nbreak;\ncase 28:return 65;\nbreak;\ncase 29:return 66;\nbreak;\ncase 30:return 59;\nbreak;\ncase 31:return 60;\nbreak;\ncase 32:return 61;\nbreak;\ncase 33:return 62;\nbreak;\ncase 34:return 57;\nbreak;\ncase 35:return 54;\nbreak;\ncase 36: this.begin('ID'); return 21; \nbreak;\ncase 37: this.begin('ID'); return 23; \nbreak;\ncase 38:return 29;\nbreak;\ncase 39:return 30;\nbreak;\ncase 40: this.begin(\"acc_title\");return 31; \nbreak;\ncase 41: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 42: this.begin(\"acc_descr\");return 33; \nbreak;\ncase 43: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 44: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 45: this.popState(); \nbreak;\ncase 46:return \"acc_descr_multiline_value\";\nbreak;\ncase 47:return 6;\nbreak;\ncase 48:return 18;\nbreak;\ncase 49:return 20;\nbreak;\ncase 50:return 64;\nbreak;\ncase 51:return 5;\nbreak;\ncase 52: yy_.yytext = yy_.yytext.trim(); return 70; \nbreak;\ncase 53:return 73;\nbreak;\ncase 54:return 74;\nbreak;\ncase 55:return 75;\nbreak;\ncase 56:return 76;\nbreak;\ncase 57:return 71;\nbreak;\ncase 58:return 72;\nbreak;\ncase 59:return 77;\nbreak;\ncase 60:return 78;\nbreak;\ncase 61:return 79;\nbreak;\ncase 62:return 80;\nbreak;\ncase 63:return 81;\nbreak;\ncase 64:return 68;\nbreak;\ncase 65:return 69;\nbreak;\ncase 66:return 5;\nbreak;\ncase 67:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:((?!\\n)\\s)+)/i,/^(?:#[^\\n]*)/i,/^(?:%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[0-9]+(?=[ \\n]+))/i,/^(?:box\\b)/i,/^(?:participant\\b)/i,/^(?:actor\\b)/i,/^(?:create\\b)/i,/^(?:destroy\\b)/i,/^(?:[^\\<->\\->:\\n,;]+?([\\-]*[^\\<->\\->:\\n,;]+?)*?(?=((?!\\n)\\s)+as(?!\\n)\\s|[#\\n;]|$))/i,/^(?:as\\b)/i,/^(?:(?:))/i,/^(?:loop\\b)/i,/^(?:rect\\b)/i,/^(?:opt\\b)/i,/^(?:alt\\b)/i,/^(?:else\\b)/i,/^(?:par\\b)/i,/^(?:par_over\\b)/i,/^(?:and\\b)/i,/^(?:critical\\b)/i,/^(?:option\\b)/i,/^(?:break\\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\\n;]*)/i,/^(?:end\\b)/i,/^(?:left of\\b)/i,/^(?:right of\\b)/i,/^(?:links\\b)/i,/^(?:link\\b)/i,/^(?:properties\\b)/i,/^(?:details\\b)/i,/^(?:over\\b)/i,/^(?:note\\b)/i,/^(?:activate\\b)/i,/^(?:deactivate\\b)/i,/^(?:title\\s[^#\\n;]+)/i,/^(?:title:\\s[^#\\n;]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:sequenceDiagram\\b)/i,/^(?:autonumber\\b)/i,/^(?:off\\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\\+\\<->\\->:\\n,;]+((?!(-x|--x|-\\)|--\\)))[\\-]*[^\\+\\<->\\->:\\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\\)])/i,/^(?:--[\\)])/i,/^(?::(?:(?:no)?wrap)?[^#\\n;]+)/i,/^(?:\\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[45,46],\"inclusive\":false},\"acc_descr\":{\"rules\":[43],\"inclusive\":false},\"acc_title\":{\"rules\":[41],\"inclusive\":false},\"ID\":{\"rules\":[2,3,12],\"inclusive\":false},\"ALIAS\":{\"rules\":[2,3,13,14],\"inclusive\":false},\"LINE\":{\"rules\":[2,3,26],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DiagramDB } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { ImperativeState } from '../../utils/imperativeState.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type { Actor, AddMessageParams, Box, Message, Note } from './types.js';\n\ninterface SequenceState {\n  prevActor?: string;\n  actors: Map<string, Actor>;\n  createdActors: Map<string, number>;\n  destroyedActors: Map<string, number>;\n  boxes: Box[];\n  messages: Message[];\n  notes: Note[];\n  sequenceNumbersEnabled: boolean;\n  wrapEnabled?: boolean;\n  currentBox?: Box;\n  lastCreated?: Actor;\n  lastDestroyed?: Actor;\n}\n\nconst LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n  AUTONUMBER: 26,\n  CRITICAL_START: 27,\n  CRITICAL_OPTION: 28,\n  CRITICAL_END: 29,\n  BREAK_START: 30,\n  BREAK_END: 31,\n  PAR_OVER_START: 32,\n  BIDIRECTIONAL_SOLID: 33,\n  BIDIRECTIONAL_DOTTED: 34,\n} as const;\n\nconst ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1,\n} as const;\n\nconst PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2,\n} as const;\n\nexport class SequenceDB implements DiagramDB {\n  private readonly state = new ImperativeState<SequenceState>(() => ({\n    prevActor: undefined,\n    actors: new Map(),\n    createdActors: new Map(),\n    destroyedActors: new Map(),\n    boxes: [],\n    messages: [],\n    notes: [],\n    sequenceNumbersEnabled: false,\n    wrapEnabled: undefined,\n    currentBox: undefined,\n    lastCreated: undefined,\n    lastDestroyed: undefined,\n  }));\n\n  constructor() {\n    // Needed for JISON since it only supports direct properties\n    this.apply = this.apply.bind(this);\n    this.parseBoxData = this.parseBoxData.bind(this);\n    this.parseMessage = this.parseMessage.bind(this);\n\n    this.clear();\n\n    this.setWrap(getConfig().wrap);\n    this.LINETYPE = LINETYPE;\n    this.ARROWTYPE = ARROWTYPE;\n    this.PLACEMENT = PLACEMENT;\n  }\n\n  public addBox(data: { text: string; color: string; wrap: boolean }) {\n    this.state.records.boxes.push({\n      name: data.text,\n      wrap: data.wrap ?? this.autoWrap(),\n      fill: data.color,\n      actorKeys: [],\n    });\n    this.state.records.currentBox = this.state.records.boxes.slice(-1)[0];\n  }\n\n  public addActor(\n    id: string,\n    name: string,\n    description: { text: string; wrap?: boolean | null; type: string },\n    type: string\n  ) {\n    let assignedBox = this.state.records.currentBox;\n    const old = this.state.records.actors.get(id);\n    if (old) {\n      // If already set and trying to set to a new one throw error\n      if (this.state.records.currentBox && old.box && this.state.records.currentBox !== old.box) {\n        throw new Error(\n          `A same participant should only be defined in one Box: ${old.name} can't be in '${old.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`\n        );\n      }\n\n      // Don't change the box if already\n      assignedBox = old.box ? old.box : this.state.records.currentBox;\n      old.box = assignedBox;\n\n      // Don't allow description nulling\n      if (old && name === old.name && description == null) {\n        return;\n      }\n    }\n\n    // Don't allow null descriptions, either\n    if (description?.text == null) {\n      description = { text: name, type };\n    }\n    if (type == null || description.text == null) {\n      description = { text: name, type };\n    }\n\n    this.state.records.actors.set(id, {\n      box: assignedBox,\n      name: name,\n      description: description.text,\n      wrap: description.wrap ?? this.autoWrap(),\n      prevActor: this.state.records.prevActor,\n      links: {},\n      properties: {},\n      actorCnt: null,\n      rectData: null,\n      type: type ?? 'participant',\n    });\n    if (this.state.records.prevActor) {\n      const prevActorInRecords = this.state.records.actors.get(this.state.records.prevActor);\n      if (prevActorInRecords) {\n        prevActorInRecords.nextActor = id;\n      }\n    }\n\n    if (this.state.records.currentBox) {\n      this.state.records.currentBox.actorKeys.push(id);\n    }\n    this.state.records.prevActor = id;\n  }\n\n  private activationCount(part: string) {\n    let i;\n    let count = 0;\n    if (!part) {\n      return 0;\n    }\n    for (i = 0; i < this.state.records.messages.length; i++) {\n      if (\n        this.state.records.messages[i].type === this.LINETYPE.ACTIVE_START &&\n        this.state.records.messages[i].from === part\n      ) {\n        count++;\n      }\n      if (\n        this.state.records.messages[i].type === this.LINETYPE.ACTIVE_END &&\n        this.state.records.messages[i].from === part\n      ) {\n        count--;\n      }\n    }\n    return count;\n  }\n\n  public addMessage(\n    idFrom: Message['from'],\n    idTo: Message['to'],\n    message: { text: string; wrap?: boolean },\n    answer: Message['answer']\n  ) {\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      answer: answer,\n    });\n  }\n\n  public addSignal(\n    idFrom?: Message['from'],\n    idTo?: Message['to'],\n    message?: { text: string; wrap: boolean },\n    messageType?: number,\n    activate = false\n  ) {\n    if (messageType === this.LINETYPE.ACTIVE_END) {\n      const cnt = this.activationCount(idFrom ?? '');\n      if (cnt < 1) {\n        // Bail out as there is an activation signal from an inactive participant\n        const error = new Error('Trying to inactivate an inactive participant (' + idFrom + ')');\n\n        // @ts-ignore: we are passing hash param to the error object, however we should define our own custom error class to make it type safe\n        error.hash = {\n          text: '->>-',\n          token: '->>-',\n          line: '1',\n          loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n          expected: [\"'ACTIVE_PARTICIPANT'\"],\n        };\n        throw error;\n      }\n    }\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message?.text ?? '',\n      wrap: message?.wrap ?? this.autoWrap(),\n      type: messageType,\n      activate,\n    });\n    return true;\n  }\n\n  public hasAtLeastOneBox() {\n    return this.state.records.boxes.length > 0;\n  }\n\n  public hasAtLeastOneBoxWithTitle() {\n    return this.state.records.boxes.some((b) => b.name);\n  }\n\n  public getMessages() {\n    return this.state.records.messages;\n  }\n\n  public getBoxes() {\n    return this.state.records.boxes;\n  }\n  public getActors() {\n    return this.state.records.actors;\n  }\n  public getCreatedActors() {\n    return this.state.records.createdActors;\n  }\n  public getDestroyedActors() {\n    return this.state.records.destroyedActors;\n  }\n  public getActor(id: string) {\n    // TODO: do we ever use this function in a way that it might return undefined?\n    return this.state.records.actors.get(id)!;\n  }\n  public getActorKeys() {\n    return [...this.state.records.actors.keys()];\n  }\n  public enableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = true;\n  }\n  public disableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = false;\n  }\n  public showSequenceNumbers() {\n    return this.state.records.sequenceNumbersEnabled;\n  }\n\n  public setWrap(wrapSetting?: boolean) {\n    this.state.records.wrapEnabled = wrapSetting;\n  }\n\n  private extractWrap(text?: string): { cleanedText?: string; wrap?: boolean } {\n    if (text === undefined) {\n      return {};\n    }\n    text = text.trim();\n    const wrap =\n      /^:?wrap:/.exec(text) !== null ? true : /^:?nowrap:/.exec(text) !== null ? false : undefined;\n    const cleanedText = (wrap === undefined ? text : text.replace(/^:?(?:no)?wrap:/, '')).trim();\n    return { cleanedText, wrap };\n  }\n\n  public autoWrap() {\n    // if setWrap has been called, use that value, otherwise use the value from the config\n    // TODO: refactor, always use the config value let setWrap update the config value\n    if (this.state.records.wrapEnabled !== undefined) {\n      return this.state.records.wrapEnabled;\n    }\n    return getConfig().sequence?.wrap ?? false;\n  }\n\n  public clear() {\n    this.state.reset();\n    commonClear();\n  }\n\n  public parseMessage(str: string) {\n    const trimmedStr = str.trim();\n    const { wrap, cleanedText } = this.extractWrap(trimmedStr);\n    const message = {\n      text: cleanedText,\n      wrap,\n    };\n    log.debug(`parseMessage: ${JSON.stringify(message)}`);\n    return message;\n  }\n\n  // We expect the box statement to be color first then description\n  // The color can be rgb,rgba,hsl,hsla, or css code names  #hex codes are not supported for now because of the way the char # is handled\n  // We extract first segment as color, the rest of the line is considered as text\n  public parseBoxData(str: string) {\n    const match = /^((?:rgba?|hsla?)\\s*\\(.*\\)|\\w*)(.*)$/.exec(str);\n    let color = match?.[1] ? match[1].trim() : 'transparent';\n    let title = match?.[2] ? match[2].trim() : undefined;\n\n    // check that the string is a color\n    if (window?.CSS) {\n      if (!window.CSS.supports('color', color)) {\n        color = 'transparent';\n        title = str.trim();\n      }\n    } else {\n      const style = new Option().style;\n      style.color = color;\n      if (style.color !== color) {\n        color = 'transparent';\n        title = str.trim();\n      }\n    }\n    const { wrap, cleanedText } = this.extractWrap(title);\n    return {\n      text: cleanedText ? sanitizeText(cleanedText, getConfig()) : undefined,\n      color,\n      wrap,\n    };\n  }\n\n  public readonly LINETYPE: typeof LINETYPE;\n  public readonly ARROWTYPE: typeof ARROWTYPE;\n  public readonly PLACEMENT: typeof PLACEMENT;\n\n  public addNote(\n    actor: { actor: string },\n    placement: Message['placement'],\n    message: { text: string; wrap?: boolean }\n  ) {\n    const note: Note = {\n      actor: actor,\n      placement: placement,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n    };\n\n    //@ts-ignore: Coerce actor into a [to, from, ...] array\n    // eslint-disable-next-line unicorn/prefer-spread\n    const actors = [].concat(actor, actor);\n    this.state.records.notes.push(note);\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: actors[0],\n      to: actors[1],\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      type: this.LINETYPE.NOTE,\n      placement: placement,\n    });\n  }\n\n  public addLinks(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    // JSON.parse the text\n    try {\n      let sanitizedText = sanitizeText(text.text, getConfig());\n      sanitizedText = sanitizedText.replace(/&equals;/g, '=');\n      sanitizedText = sanitizedText.replace(/&amp;/g, '&');\n      const links = JSON.parse(sanitizedText);\n      // add the deserialized text to the actor's links field.\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error('error while parsing actor link text', e);\n    }\n  }\n\n  public addALink(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    try {\n      const links: Record<string, string> = {};\n      let sanitizedText = sanitizeText(text.text, getConfig());\n      const sep = sanitizedText.indexOf('@');\n      sanitizedText = sanitizedText.replace(/&equals;/g, '=');\n      sanitizedText = sanitizedText.replace(/&amp;/g, '&');\n      const label = sanitizedText.slice(0, sep - 1).trim();\n      const link = sanitizedText.slice(sep + 1).trim();\n\n      links[label] = link;\n      // add the deserialized text to the actor's links field.\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error('error while parsing actor link text', e);\n    }\n  }\n\n  private insertLinks(actor: Actor, links: Record<string, string>) {\n    if (actor.links == null) {\n      actor.links = links;\n    } else {\n      for (const key in links) {\n        actor.links[key] = links[key];\n      }\n    }\n  }\n\n  public addProperties(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    // JSON.parse the text\n    try {\n      const sanitizedText = sanitizeText(text.text, getConfig());\n      const properties: Record<string, unknown> = JSON.parse(sanitizedText);\n      // add the deserialized text to the actor's property field.\n      this.insertProperties(actor, properties);\n    } catch (e) {\n      log.error('error while parsing actor properties text', e);\n    }\n  }\n\n  private insertProperties(actor: Actor, properties: Record<string, unknown>) {\n    if (actor.properties == null) {\n      actor.properties = properties;\n    } else {\n      for (const key in properties) {\n        actor.properties[key] = properties[key];\n      }\n    }\n  }\n\n  private boxEnd() {\n    this.state.records.currentBox = undefined;\n  }\n\n  public addDetails(actorId: string, text: { text: string }) {\n    // find the actor\n    const actor = this.getActor(actorId);\n    const elem = document.getElementById(text.text)!;\n\n    // JSON.parse the text\n    try {\n      const text = elem.innerHTML;\n      const details = JSON.parse(text);\n      // add the deserialized text to the actor's property field.\n      if (details.properties) {\n        this.insertProperties(actor, details.properties);\n      }\n\n      if (details.links) {\n        this.insertLinks(actor, details.links);\n      }\n    } catch (e) {\n      log.error('error while parsing actor details text', e);\n    }\n  }\n\n  public getActorProperty(actor: Actor, key: string) {\n    if (actor?.properties !== undefined) {\n      return actor.properties[key];\n    }\n\n    return undefined;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-redundant-type-constituents\n  public apply(param: any | AddMessageParams | AddMessageParams[]) {\n    if (Array.isArray(param)) {\n      param.forEach((item) => {\n        this.apply(item);\n      });\n    } else {\n      switch (param.type) {\n        case 'sequenceIndex':\n          this.state.records.messages.push({\n            id: this.state.records.messages.length.toString(),\n            from: undefined,\n            to: undefined,\n            message: {\n              start: param.sequenceIndex,\n              step: param.sequenceIndexStep,\n              visible: param.sequenceVisible,\n            },\n            wrap: false,\n            type: param.signalType,\n          });\n          break;\n        case 'addParticipant':\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          break;\n        case 'createParticipant':\n          if (this.state.records.actors.has(param.actor)) {\n            throw new Error(\n              \"It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior\"\n            );\n          }\n          this.state.records.lastCreated = param.actor;\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          this.state.records.createdActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case 'destroyParticipant':\n          this.state.records.lastDestroyed = param.actor;\n          this.state.records.destroyedActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case 'activeStart':\n          this.addSignal(param.actor, undefined, undefined, param.signalType);\n          break;\n        case 'activeEnd':\n          this.addSignal(param.actor, undefined, undefined, param.signalType);\n          break;\n        case 'addNote':\n          this.addNote(param.actor, param.placement, param.text);\n          break;\n        case 'addLinks':\n          this.addLinks(param.actor, param.text);\n          break;\n        case 'addALink':\n          this.addALink(param.actor, param.text);\n          break;\n        case 'addProperties':\n          this.addProperties(param.actor, param.text);\n          break;\n        case 'addDetails':\n          this.addDetails(param.actor, param.text);\n          break;\n        case 'addMessage':\n          if (this.state.records.lastCreated) {\n            if (param.to !== this.state.records.lastCreated) {\n              throw new Error(\n                'The created participant ' +\n                  this.state.records.lastCreated.name +\n                  ' does not have an associated creating message after its declaration. Please check the sequence diagram.'\n              );\n            } else {\n              this.state.records.lastCreated = undefined;\n            }\n          } else if (this.state.records.lastDestroyed) {\n            if (\n              param.to !== this.state.records.lastDestroyed &&\n              param.from !== this.state.records.lastDestroyed\n            ) {\n              throw new Error(\n                'The destroyed participant ' +\n                  this.state.records.lastDestroyed.name +\n                  ' does not have an associated destroying message after its declaration. Please check the sequence diagram.'\n              );\n            } else {\n              this.state.records.lastDestroyed = undefined;\n            }\n          }\n          this.addSignal(param.from, param.to, param.msg, param.signalType, param.activate);\n          break;\n        case 'boxStart':\n          this.addBox(param.boxData);\n          break;\n        case 'boxEnd':\n          this.boxEnd();\n          break;\n        case 'loopStart':\n          this.addSignal(undefined, undefined, param.loopText, param.signalType);\n          break;\n        case 'loopEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'rectStart':\n          this.addSignal(undefined, undefined, param.color, param.signalType);\n          break;\n        case 'rectEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'optStart':\n          this.addSignal(undefined, undefined, param.optText, param.signalType);\n          break;\n        case 'optEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'altStart':\n          this.addSignal(undefined, undefined, param.altText, param.signalType);\n          break;\n        case 'else':\n          this.addSignal(undefined, undefined, param.altText, param.signalType);\n          break;\n        case 'altEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'setAccTitle':\n          setAccTitle(param.text);\n          break;\n        case 'parStart':\n          this.addSignal(undefined, undefined, param.parText, param.signalType);\n          break;\n        case 'and':\n          this.addSignal(undefined, undefined, param.parText, param.signalType);\n          break;\n        case 'parEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'criticalStart':\n          this.addSignal(undefined, undefined, param.criticalText, param.signalType);\n          break;\n        case 'option':\n          this.addSignal(undefined, undefined, param.optionText, param.signalType);\n          break;\n        case 'criticalEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n        case 'breakStart':\n          this.addSignal(undefined, undefined, param.breakText, param.signalType);\n          break;\n        case 'breakEnd':\n          this.addSignal(undefined, undefined, undefined, param.signalType);\n          break;\n      }\n    }\n  }\n\n  public setAccTitle = setAccTitle;\n  public setAccDescription = setAccDescription;\n  public setDiagramTitle = setDiagramTitle;\n  public getAccTitle = getAccTitle;\n  public getAccDescription = getAccDescription;\n  public getDiagramTitle = getDiagramTitle;\n  public getConfig() {\n    return getConfig().sequence;\n  }\n}\n", "const getStyles = (options) =>\n  `.actor {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${options.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${options.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${options.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${options.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${options.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .messageText {\n    fill: ${options.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${options.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${options.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${options.noteBorderColor};\n    fill: ${options.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${options.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${options.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n    stroke-width: 2px;\n  }\n`;\n\nexport default getStyles;\n", "// @ts-nocheck TODO: fix file\nimport { select } from 'd3';\nimport svgDraw, { drawKatex, ACTOR_TYPE_WIDTH, drawText, fixLifeLineHeights } from './svgDraw.js';\nimport { log } from '../../logger.js';\nimport common, { calculateMathMLDimensions, hasKatex } from '../common/common.js';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport assignWithDepth from '../../assignWithDepth.js';\nimport utils from '../../utils.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { Diagram } from '../../Diagram.js';\n\nlet conf = {};\n\nexport const bounds = {\n  data: {\n    startx: undefined,\n    stopx: undefined,\n    starty: undefined,\n    stopy: undefined,\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  activations: [],\n  models: {\n    getHeight: function () {\n      return (\n        Math.max.apply(\n          null,\n          this.actors.length === 0 ? [0] : this.actors.map((actor) => actor.height || 0)\n        ) +\n        (this.loops.length === 0\n          ? 0\n          : this.loops.map((it) => it.height || 0).reduce((acc, h) => acc + h)) +\n        (this.messages.length === 0\n          ? 0\n          : this.messages.map((it) => it.height || 0).reduce((acc, h) => acc + h)) +\n        (this.notes.length === 0\n          ? 0\n          : this.notes.map((it) => it.height || 0).reduce((acc, h) => acc + h))\n      );\n    },\n    clear: function () {\n      this.actors = [];\n      this.boxes = [];\n      this.loops = [];\n      this.messages = [];\n      this.notes = [];\n    },\n    addBox: function (boxModel) {\n      this.boxes.push(boxModel);\n    },\n    addActor: function (actorModel) {\n      this.actors.push(actorModel);\n    },\n    addLoop: function (loopModel) {\n      this.loops.push(loopModel);\n    },\n    addMessage: function (msgModel) {\n      this.messages.push(msgModel);\n    },\n    addNote: function (noteModel) {\n      this.notes.push(noteModel);\n    },\n    lastActor: function () {\n      return this.actors[this.actors.length - 1];\n    },\n    lastLoop: function () {\n      return this.loops[this.loops.length - 1];\n    },\n    lastMessage: function () {\n      return this.messages[this.messages.length - 1];\n    },\n    lastNote: function () {\n      return this.notes[this.notes.length - 1];\n    },\n    actors: [],\n    boxes: [],\n    loops: [],\n    messages: [],\n    notes: [],\n  },\n  init: function () {\n    this.sequenceItems = [];\n    this.activations = [];\n    this.models.clear();\n    this.data = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n    };\n    this.verticalPos = 0;\n    setConf(getConfig());\n  },\n  updateVal: function (obj, key, val, fun) {\n    if (obj[key] === undefined) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  },\n  updateBounds: function (startx, starty, stopx, stopy) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const _self = this;\n    let cnt = 0;\n    /** @param type - Either `activation` or `undefined` */\n    function updateFn(type?: 'activation') {\n      return function updateItemBounds(item) {\n        cnt++;\n        // The loop sequenceItems is a stack so the biggest margins in the beginning of the sequenceItems\n        const n = _self.sequenceItems.length - cnt + 1;\n\n        _self.updateVal(item, 'starty', starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, 'stopy', stopy + n * conf.boxMargin, Math.max);\n\n        _self.updateVal(bounds.data, 'startx', startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n        if (!(type === 'activation')) {\n          _self.updateVal(item, 'startx', startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n          _self.updateVal(bounds.data, 'starty', starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, 'stopy', stopy + n * conf.boxMargin, Math.max);\n        }\n      };\n    }\n\n    this.sequenceItems.forEach(updateFn());\n    this.activations.forEach(updateFn('activation'));\n  },\n  insert: function (startx, starty, stopx, stopy) {\n    const _startx = common.getMin(startx, stopx);\n    const _stopx = common.getMax(startx, stopx);\n    const _starty = common.getMin(starty, stopy);\n    const _stopy = common.getMax(starty, stopy);\n\n    this.updateVal(bounds.data, 'startx', _startx, Math.min);\n    this.updateVal(bounds.data, 'starty', _starty, Math.min);\n    this.updateVal(bounds.data, 'stopx', _stopx, Math.max);\n    this.updateVal(bounds.data, 'stopy', _stopy, Math.max);\n\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  },\n  newActivation: function (message, diagram, actors) {\n    const actorRect = actors.get(message.from);\n    const stackedSize = actorActivations(message.from).length || 0;\n    const x = actorRect.x + actorRect.width / 2 + ((stackedSize - 1) * conf.activationWidth) / 2;\n    this.activations.push({\n      startx: x,\n      starty: this.verticalPos + 2,\n      stopx: x + conf.activationWidth,\n      stopy: undefined,\n      actor: message.from,\n      anchored: svgDraw.anchorElement(diagram),\n    });\n  },\n  endActivation: function (message) {\n    // find most recent activation for given actor\n    const lastActorActivationIdx = this.activations\n      .map(function (activation) {\n        return activation.actor;\n      })\n      .lastIndexOf(message.from);\n    return this.activations.splice(lastActorActivationIdx, 1)[0];\n  },\n  createLoop: function (title = { message: undefined, wrap: false, width: undefined }, fill) {\n    return {\n      startx: undefined,\n      starty: this.verticalPos,\n      stopx: undefined,\n      stopy: undefined,\n      title: title.message,\n      wrap: title.wrap,\n      width: title.width,\n      height: 0,\n      fill: fill,\n    };\n  },\n  newLoop: function (title = { message: undefined, wrap: false, width: undefined }, fill) {\n    this.sequenceItems.push(this.createLoop(title, fill));\n  },\n  endLoop: function () {\n    return this.sequenceItems.pop();\n  },\n  isLoopOverlap: function () {\n    return this.sequenceItems.length\n      ? this.sequenceItems[this.sequenceItems.length - 1].overlap\n      : false;\n  },\n  addSectionToLoop: function (message) {\n    const loop = this.sequenceItems.pop();\n    loop.sections = loop.sections || [];\n    loop.sectionTitles = loop.sectionTitles || [];\n    loop.sections.push({ y: bounds.getVerticalPos(), height: 0 });\n    loop.sectionTitles.push(message);\n    this.sequenceItems.push(loop);\n  },\n  saveVerticalPos: function () {\n    if (this.isLoopOverlap()) {\n      this.savedVerticalPos = this.verticalPos;\n    }\n  },\n  resetVerticalPos: function () {\n    if (this.isLoopOverlap()) {\n      this.verticalPos = this.savedVerticalPos;\n    }\n  },\n  bumpVerticalPos: function (bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = common.getMax(this.data.stopy, this.verticalPos);\n  },\n  getVerticalPos: function () {\n    return this.verticalPos;\n  },\n  getBounds: function () {\n    return { bounds: this.data, models: this.models };\n  },\n};\n\n/** Options for drawing a note in {@link drawNote} */\ninterface NoteModel {\n  /** x axis start position */\n  startx: number;\n  /** y axis position */\n  starty: number;\n  /** the message to be shown */\n  message: string;\n  /** Set this with a custom width to override the default configured width. */\n  width: number;\n}\n\n/**\n * Draws an note in the diagram with the attached line\n *\n * @param elem - The diagram to draw to.\n * @param noteModel - Note model options.\n */\nconst drawNote = async function (elem: any, noteModel: NoteModel) {\n  bounds.bumpVerticalPos(conf.boxMargin);\n  noteModel.height = conf.boxMargin;\n  noteModel.starty = bounds.getVerticalPos();\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = noteModel.startx;\n  rect.y = noteModel.starty;\n  rect.width = noteModel.width || conf.width;\n  rect.class = 'note';\n\n  const g = elem.append('g');\n  const rectElem = svgDraw.drawRect(g, rect);\n  const textObj = svgDrawCommon.getTextObj();\n  textObj.x = noteModel.startx;\n  textObj.y = noteModel.starty;\n  textObj.width = rect.width;\n  textObj.dy = '1em';\n  textObj.text = noteModel.message;\n  textObj.class = 'noteText';\n  textObj.fontFamily = conf.noteFontFamily;\n  textObj.fontSize = conf.noteFontSize;\n  textObj.fontWeight = conf.noteFontWeight;\n  textObj.anchor = conf.noteAlign;\n  textObj.textMargin = conf.noteMargin;\n  textObj.valign = 'center';\n\n  const textElem = hasKatex(textObj.text) ? await drawKatex(g, textObj) : drawText(g, textObj);\n\n  const textHeight = Math.round(\n    textElem\n      .map((te) => (te._groups || te)[0][0].getBBox().height)\n      .reduce((acc, curr) => acc + curr)\n  );\n\n  rectElem.attr('height', textHeight + 2 * conf.noteMargin);\n  noteModel.height += textHeight + 2 * conf.noteMargin;\n  bounds.bumpVerticalPos(textHeight + 2 * conf.noteMargin);\n  noteModel.stopy = noteModel.starty + textHeight + 2 * conf.noteMargin;\n  noteModel.stopx = noteModel.startx + rect.width;\n  bounds.insert(noteModel.startx, noteModel.starty, noteModel.stopx, noteModel.stopy);\n  bounds.models.addNote(noteModel);\n};\n\nconst messageFont = (cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight,\n  };\n};\nconst noteFont = (cnf) => {\n  return {\n    fontFamily: cnf.noteFontFamily,\n    fontSize: cnf.noteFontSize,\n    fontWeight: cnf.noteFontWeight,\n  };\n};\nconst actorFont = (cnf) => {\n  return {\n    fontFamily: cnf.actorFontFamily,\n    fontSize: cnf.actorFontSize,\n    fontWeight: cnf.actorFontWeight,\n  };\n};\n\n/**\n * Process a message by adding its dimensions to the bound. It returns the Y coordinate of the\n * message so it can be drawn later. We do not draw the message at this point so the arrowhead can\n * be on top of the activation box.\n *\n * @param _diagram - The parent of the message element.\n * @param msgModel - The model containing fields describing a message\n * @returns `lineStartY` - The Y coordinate at which the message line starts\n */\nasync function boundMessage(_diagram, msgModel): Promise<number> {\n  bounds.bumpVerticalPos(10);\n  const { startx, stopx, message } = msgModel;\n  const lines = common.splitBreaks(message).length;\n  const isKatexMsg = hasKatex(message);\n  const textDims = isKatexMsg\n    ? await calculateMathMLDimensions(message, getConfig())\n    : utils.calculateTextDimensions(message, messageFont(conf));\n\n  if (!isKatexMsg) {\n    const lineHeight = textDims.height / lines;\n    msgModel.height += lineHeight;\n    bounds.bumpVerticalPos(lineHeight);\n  }\n\n  let lineStartY;\n  let totalOffset = textDims.height - 10;\n  const textWidth = textDims.width;\n\n  if (startx === stopx) {\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    if (!conf.rightAngles) {\n      totalOffset += conf.boxMargin;\n      lineStartY = bounds.getVerticalPos() + totalOffset;\n    }\n    totalOffset += 30;\n    const dx = common.getMax(textWidth / 2, conf.width / 2);\n    bounds.insert(\n      startx - dx,\n      bounds.getVerticalPos() - 10 + totalOffset,\n      stopx + dx,\n      bounds.getVerticalPos() + 30 + totalOffset\n    );\n  } else {\n    totalOffset += conf.boxMargin;\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    bounds.insert(startx, lineStartY - 10, stopx, lineStartY);\n  }\n  bounds.bumpVerticalPos(totalOffset);\n  msgModel.height += totalOffset;\n  msgModel.stopy = msgModel.starty + msgModel.height;\n  bounds.insert(msgModel.fromBounds, msgModel.starty, msgModel.toBounds, msgModel.stopy);\n\n  return lineStartY;\n}\n\n/**\n * Draws a message. Note that the bounds have previously been updated by boundMessage.\n *\n * @param diagram - The parent of the message element\n * @param msgModel - The model containing fields describing a message\n * @param lineStartY - The Y coordinate at which the message line starts\n * @param diagObj - The diagram object.\n */\nconst drawMessage = async function (diagram, msgModel, lineStartY: number, diagObj: Diagram) {\n  const { startx, stopx, starty, message, type, sequenceIndex, sequenceVisible } = msgModel;\n  const textDims = utils.calculateTextDimensions(message, messageFont(conf));\n  const textObj = svgDrawCommon.getTextObj();\n  textObj.x = startx;\n  textObj.y = starty + 10;\n  textObj.width = stopx - startx;\n  textObj.class = 'messageText';\n  textObj.dy = '1em';\n  textObj.text = message;\n  textObj.fontFamily = conf.messageFontFamily;\n  textObj.fontSize = conf.messageFontSize;\n  textObj.fontWeight = conf.messageFontWeight;\n  textObj.anchor = conf.messageAlign;\n  textObj.valign = 'center';\n  textObj.textMargin = conf.wrapPadding;\n  textObj.tspan = false;\n\n  if (hasKatex(textObj.text)) {\n    await drawKatex(diagram, textObj, { startx, stopx, starty: lineStartY });\n  } else {\n    drawText(diagram, textObj);\n  }\n\n  const textWidth = textDims.width;\n\n  let line;\n  if (startx === stopx) {\n    if (conf.rightAngles) {\n      line = diagram\n        .append('path')\n        .attr(\n          'd',\n          `M  ${startx},${lineStartY} H ${\n            startx + common.getMax(conf.width / 2, textWidth / 2)\n          } V ${lineStartY + 25} H ${startx}`\n        );\n    } else {\n      line = diagram\n        .append('path')\n        .attr(\n          'd',\n          'M ' +\n            startx +\n            ',' +\n            lineStartY +\n            ' C ' +\n            (startx + 60) +\n            ',' +\n            (lineStartY - 10) +\n            ' ' +\n            (startx + 60) +\n            ',' +\n            (lineStartY + 30) +\n            ' ' +\n            startx +\n            ',' +\n            (lineStartY + 20)\n        );\n    }\n  } else {\n    line = diagram.append('line');\n    line.attr('x1', startx);\n    line.attr('y1', lineStartY);\n    line.attr('x2', stopx);\n    line.attr('y2', lineStartY);\n  }\n  // Make an SVG Container\n  // Draw the line\n  if (\n    type === diagObj.db.LINETYPE.DOTTED ||\n    type === diagObj.db.LINETYPE.DOTTED_CROSS ||\n    type === diagObj.db.LINETYPE.DOTTED_POINT ||\n    type === diagObj.db.LINETYPE.DOTTED_OPEN ||\n    type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ) {\n    line.style('stroke-dasharray', '3, 3');\n    line.attr('class', 'messageLine1');\n  } else {\n    line.attr('class', 'messageLine0');\n  }\n\n  let url = '';\n  if (conf.arrowMarkerAbsolute) {\n    url =\n      window.location.protocol +\n      '//' +\n      window.location.host +\n      window.location.pathname +\n      window.location.search;\n    url = url.replace(/\\(/g, '\\\\(');\n    url = url.replace(/\\)/g, '\\\\)');\n  }\n\n  line.attr('stroke-width', 2);\n  line.attr('stroke', 'none'); // handled by theme/css anyway\n  line.style('fill', 'none'); // remove any fill colour\n  if (type === diagObj.db.LINETYPE.SOLID || type === diagObj.db.LINETYPE.DOTTED) {\n    line.attr('marker-end', 'url(' + url + '#arrowhead)');\n  }\n  if (\n    type === diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID ||\n    type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ) {\n    line.attr('marker-start', 'url(' + url + '#arrowhead)');\n    line.attr('marker-end', 'url(' + url + '#arrowhead)');\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_POINT || type === diagObj.db.LINETYPE.DOTTED_POINT) {\n    line.attr('marker-end', 'url(' + url + '#filled-head)');\n  }\n\n  if (type === diagObj.db.LINETYPE.SOLID_CROSS || type === diagObj.db.LINETYPE.DOTTED_CROSS) {\n    line.attr('marker-end', 'url(' + url + '#crosshead)');\n  }\n\n  // add node number\n  if (sequenceVisible || conf.showSequenceNumbers) {\n    line.attr('marker-start', 'url(' + url + '#sequencenumber)');\n    diagram\n      .append('text')\n      .attr('x', startx)\n      .attr('y', lineStartY + 4)\n      .attr('font-family', 'sans-serif')\n      .attr('font-size', '12px')\n      .attr('text-anchor', 'middle')\n      .attr('class', 'sequenceNumber')\n      .text(sequenceIndex);\n  }\n};\n\nconst addActorRenderingData = function (\n  diagram,\n  actors,\n  createdActors: Map<string, any>,\n  actorKeys,\n  verticalPos,\n  messages,\n  isFooter\n) {\n  let prevWidth = 0;\n  let prevMargin = 0;\n  let prevBox = undefined;\n  let maxHeight = 0;\n\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const box = actor.box;\n\n    // end of box\n    if (prevBox && prevBox != box) {\n      if (!isFooter) {\n        bounds.models.addBox(prevBox);\n      }\n      prevMargin += conf.boxMargin + prevBox.margin;\n    }\n\n    // new box\n    if (box && box != prevBox) {\n      if (!isFooter) {\n        box.x = prevWidth + prevMargin;\n        box.y = verticalPos;\n      }\n      prevMargin += box.margin;\n    }\n\n    // Add some rendering data to the object\n    actor.width = actor.width || conf.width;\n    actor.height = common.getMax(actor.height || conf.height, conf.height);\n    actor.margin = actor.margin || conf.actorMargin;\n\n    maxHeight = common.getMax(maxHeight, actor.height);\n\n    // if the actor is created by a message, widen margin\n    if (createdActors.get(actor.name)) {\n      prevMargin += actor.width / 2;\n    }\n\n    actor.x = prevWidth + prevMargin;\n    actor.starty = bounds.getVerticalPos();\n\n    bounds.insert(actor.x, verticalPos, actor.x + actor.width, actor.height);\n\n    prevWidth += actor.width + prevMargin;\n    if (actor.box) {\n      actor.box.width = prevWidth + box.margin - actor.box.x;\n    }\n    prevMargin = actor.margin;\n    prevBox = actor.box;\n    bounds.models.addActor(actor);\n  }\n\n  // end of box\n  if (prevBox && !isFooter) {\n    bounds.models.addBox(prevBox);\n  }\n\n  // Add a margin between the actor boxes and the first arrow\n  bounds.bumpVerticalPos(maxHeight);\n};\n\nexport const drawActors = async function (diagram, actors, actorKeys, isFooter) {\n  if (!isFooter) {\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      // Draw the box with the attached line\n      await svgDraw.drawActor(diagram, actor, conf, false);\n    }\n  } else {\n    let maxHeight = 0;\n    bounds.bumpVerticalPos(conf.boxMargin * 2);\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      if (!actor.stopy) {\n        actor.stopy = bounds.getVerticalPos();\n      }\n      const height = await svgDraw.drawActor(diagram, actor, conf, true);\n      maxHeight = common.getMax(maxHeight, height);\n    }\n    bounds.bumpVerticalPos(maxHeight + conf.boxMargin);\n  }\n};\n\nexport const drawActorsPopup = function (diagram, actors, actorKeys, doc) {\n  let maxHeight = 0;\n  let maxWidth = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const minMenuWidth = getRequiredPopupWidth(actor);\n    const menuDimensions = svgDraw.drawPopup(\n      diagram,\n      actor,\n      minMenuWidth,\n      conf,\n      conf.forceMenus,\n      doc\n    );\n    if (menuDimensions.height > maxHeight) {\n      maxHeight = menuDimensions.height;\n    }\n    if (menuDimensions.width + actor.x > maxWidth) {\n      maxWidth = menuDimensions.width + actor.x;\n    }\n  }\n\n  return { maxHeight: maxHeight, maxWidth: maxWidth };\n};\n\nexport const setConf = function (cnf) {\n  assignWithDepth(conf, cnf);\n\n  if (cnf.fontFamily) {\n    conf.actorFontFamily = conf.noteFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.actorFontSize = conf.noteFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.actorFontWeight = conf.noteFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n};\n\nconst actorActivations = function (actor) {\n  return bounds.activations.filter(function (activation) {\n    return activation.actor === actor;\n  });\n};\n\nconst activationBounds = function (actor, actors) {\n  // handle multiple stacked activations for same actor\n  const actorObj = actors.get(actor);\n  const activations = actorActivations(actor);\n\n  const left = activations.reduce(\n    function (acc, activation) {\n      return common.getMin(acc, activation.startx);\n    },\n    actorObj.x + actorObj.width / 2 - 1\n  );\n  const right = activations.reduce(\n    function (acc, activation) {\n      return common.getMax(acc, activation.stopx);\n    },\n    actorObj.x + actorObj.width / 2 + 1\n  );\n  return [left, right];\n};\n\nfunction adjustLoopHeightForWrap(loopWidths, msg, preMargin, postMargin, addLoopFn) {\n  bounds.bumpVerticalPos(preMargin);\n  let heightAdjust = postMargin;\n  if (msg.id && msg.message && loopWidths[msg.id]) {\n    const loopWidth = loopWidths[msg.id].width;\n    const textConf = messageFont(conf);\n    msg.message = utils.wrapLabel(`[${msg.message}]`, loopWidth - 2 * conf.wrapPadding, textConf);\n    msg.width = loopWidth;\n    msg.wrap = true;\n\n    // const lines = common.splitBreaks(msg.message).length;\n    const textDims = utils.calculateTextDimensions(msg.message, textConf);\n    const totalOffset = common.getMax(textDims.height, conf.labelBoxHeight);\n    heightAdjust = postMargin + totalOffset;\n    log.debug(`${totalOffset} - ${msg.message}`);\n  }\n  addLoopFn(msg);\n  bounds.bumpVerticalPos(heightAdjust);\n}\n\n/**\n * Adjust the msgModel and the actor for the rendering in case the latter is created or destroyed by the msg\n * @param msg - the potentially creating or destroying message\n * @param msgModel - the model associated with the message\n * @param lineStartY - the y position of the message line\n * @param index - the index of the current actor under consideration\n * @param actors - the array of all actors\n * @param createdActors - the array of actors created in the diagram\n * @param destroyedActors - the array of actors destroyed in the diagram\n */\nfunction adjustCreatedDestroyedData(\n  msg,\n  msgModel,\n  lineStartY,\n  index,\n  actors,\n  createdActors,\n  destroyedActors\n) {\n  function receiverAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.from).x) {\n      bounds.insert(\n        msgModel.stopx - adjustment,\n        msgModel.starty,\n        msgModel.startx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.startx,\n        msgModel.starty,\n        msgModel.stopx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx - adjustment;\n    }\n  }\n\n  function senderAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.to).x) {\n      bounds.insert(\n        msgModel.startx - adjustment,\n        msgModel.starty,\n        msgModel.stopx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.stopx,\n        msgModel.starty,\n        msgModel.startx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx - adjustment;\n    }\n  }\n\n  // if it is a create message\n  if (createdActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    const adjustment = actor.type == 'actor' ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n    receiverAdjustment(actor, adjustment);\n    actor.starty = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n  // if it is a destroy sender message\n  else if (destroyedActors.get(msg.from) == index) {\n    const actor = actors.get(msg.from);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == 'actor' ? ACTOR_TYPE_WIDTH / 2 : actor.width / 2;\n      senderAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n  // if it is a destroy receiver message\n  else if (destroyedActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == 'actor' ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n      receiverAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n}\n\n/**\n * Draws a sequenceDiagram in the tag with id: id based on the graph definition in text.\n *\n * @param _text - The text of the diagram\n * @param id - The id of the diagram which will be used as a DOM element id¨\n * @param _version - Mermaid version from package.json\n * @param diagObj - A standard diagram containing the db and the text and type etc of the diagram\n */\nexport const draw = async function (_text: string, id: string, _version: string, diagObj: Diagram) {\n  const { securityLevel, sequence } = getConfig();\n  conf = sequence;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n  bounds.init();\n  log.debug(diagObj.db);\n\n  const diagram =\n    securityLevel === 'sandbox' ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n\n  // Fetch data from the parsing\n  const actors = diagObj.db.getActors();\n  const createdActors = diagObj.db.getCreatedActors();\n  const destroyedActors = diagObj.db.getDestroyedActors();\n  const boxes = diagObj.db.getBoxes();\n  let actorKeys = diagObj.db.getActorKeys();\n  const messages = diagObj.db.getMessages();\n  const title = diagObj.db.getDiagramTitle();\n  const hasBoxes = diagObj.db.hasAtLeastOneBox();\n  const hasBoxTitles = diagObj.db.hasAtLeastOneBoxWithTitle();\n  const maxMessageWidthPerActor = await getMaxMessageWidthPerActor(actors, messages, diagObj);\n  conf.height = await calculateActorMargins(actors, maxMessageWidthPerActor, boxes);\n\n  svgDraw.insertComputerIcon(diagram);\n  svgDraw.insertDatabaseIcon(diagram);\n  svgDraw.insertClockIcon(diagram);\n\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n    if (hasBoxTitles) {\n      bounds.bumpVerticalPos(boxes[0].textMaxHeight);\n    }\n  }\n\n  if (conf.hideUnusedParticipants === true) {\n    const newActors = new Set();\n    messages.forEach((message) => {\n      newActors.add(message.from);\n      newActors.add(message.to);\n    });\n    actorKeys = actorKeys.filter((actorKey) => newActors.has(actorKey));\n  }\n\n  addActorRenderingData(diagram, actors, createdActors, actorKeys, 0, messages, false);\n  const loopWidths = await calculateLoopBounds(messages, actors, maxMessageWidthPerActor, diagObj);\n\n  // The arrow head definition is attached to the svg once\n  svgDraw.insertArrowHead(diagram);\n  svgDraw.insertArrowCrossHead(diagram);\n  svgDraw.insertArrowFilledHead(diagram);\n  svgDraw.insertSequenceNumber(diagram);\n\n  /**\n   * @param msg - The message to draw.\n   * @param verticalPos - The vertical position of the message.\n   */\n  function activeEnd(msg: any, verticalPos: number) {\n    const activationData = bounds.endActivation(msg);\n    if (activationData.starty + 18 > verticalPos) {\n      activationData.starty = verticalPos - 6;\n      verticalPos += 12;\n    }\n    svgDraw.drawActivation(\n      diagram,\n      activationData,\n      verticalPos,\n      conf,\n      actorActivations(msg.from).length\n    );\n\n    bounds.insert(activationData.startx, verticalPos - 10, activationData.stopx, verticalPos);\n  }\n\n  // Draw the messages/signals\n  let sequenceIndex = 1;\n  let sequenceIndexStep = 1;\n  const messagesToDraw = [];\n  const backgrounds = [];\n  let index = 0;\n  for (const msg of messages) {\n    let loopModel, noteModel, msgModel;\n\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.NOTE:\n        bounds.resetVerticalPos();\n        noteModel = msg.noteModel;\n        await drawNote(diagram, noteModel);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        bounds.newActivation(msg, diagram, actors);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        activeEnd(msg, bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.LOOP_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'loop', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.RECT_START:\n        adjustLoopHeightForWrap(loopWidths, msg, conf.boxMargin, conf.boxMargin, (message) =>\n          bounds.newLoop(undefined, message.message)\n        );\n        break;\n      case diagObj.db.LINETYPE.RECT_END:\n        loopModel = bounds.endLoop();\n        backgrounds.push(loopModel);\n        bounds.models.addLoop(loopModel);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.OPT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.OPT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'opt', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.ALT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'alt', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        bounds.saveVerticalPos();\n        break;\n      case diagObj.db.LINETYPE.PAR_AND:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.PAR_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'par', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.AUTONUMBER:\n        sequenceIndex = msg.message.start || sequenceIndex;\n        sequenceIndexStep = msg.message.step || sequenceIndexStep;\n        if (msg.message.visible) {\n          diagObj.db.enableSequenceNumbers();\n        } else {\n          diagObj.db.disableSequenceNumbers();\n        }\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'critical', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.BREAK_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.BREAK_END:\n        loopModel = bounds.endLoop();\n        await svgDraw.drawLoop(diagram, loopModel, 'break', conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      default:\n        try {\n          msgModel = msg.msgModel;\n          msgModel.starty = bounds.getVerticalPos();\n          msgModel.sequenceIndex = sequenceIndex;\n          msgModel.sequenceVisible = diagObj.db.showSequenceNumbers();\n          const lineStartY = await boundMessage(diagram, msgModel);\n          adjustCreatedDestroyedData(\n            msg,\n            msgModel,\n            lineStartY,\n            index,\n            actors,\n            createdActors,\n            destroyedActors\n          );\n          messagesToDraw.push({ messageModel: msgModel, lineStartY: lineStartY });\n          bounds.models.addMessage(msgModel);\n        } catch (e) {\n          log.error('error while drawing message', e);\n        }\n    }\n\n    // Increment sequence counter if msg.type is a line (and not another event like activation or note, etc)\n    if (\n      [\n        diagObj.db.LINETYPE.SOLID_OPEN,\n        diagObj.db.LINETYPE.DOTTED_OPEN,\n        diagObj.db.LINETYPE.SOLID,\n        diagObj.db.LINETYPE.DOTTED,\n        diagObj.db.LINETYPE.SOLID_CROSS,\n        diagObj.db.LINETYPE.DOTTED_CROSS,\n        diagObj.db.LINETYPE.SOLID_POINT,\n        diagObj.db.LINETYPE.DOTTED_POINT,\n        diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n        diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED,\n      ].includes(msg.type)\n    ) {\n      sequenceIndex = sequenceIndex + sequenceIndexStep;\n    }\n    index++;\n  }\n\n  log.debug('createdActors', createdActors);\n  log.debug('destroyedActors', destroyedActors);\n  await drawActors(diagram, actors, actorKeys, false);\n\n  for (const e of messagesToDraw) {\n    await drawMessage(diagram, e.messageModel, e.lineStartY, diagObj);\n  }\n  if (conf.mirrorActors) {\n    await drawActors(diagram, actors, actorKeys, true);\n  }\n  backgrounds.forEach((e) => svgDraw.drawBackgroundRect(diagram, e));\n  fixLifeLineHeights(diagram, actors, actorKeys, conf);\n\n  for (const box of bounds.models.boxes) {\n    box.height = bounds.getVerticalPos() - box.y;\n    bounds.insert(box.x, box.y, box.x + box.width, box.height);\n    box.startx = box.x;\n    box.starty = box.y;\n    box.stopx = box.startx + box.width;\n    box.stopy = box.starty + box.height;\n    box.stroke = 'rgb(0,0,0, 0.5)';\n    svgDraw.drawBox(diagram, box, conf);\n  }\n\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n  }\n\n  // only draw popups for the top row of actors.\n  const requiredBoxSize = drawActorsPopup(diagram, actors, actorKeys, doc);\n\n  const { bounds: box } = bounds.getBounds();\n\n  if (box.startx === undefined) {\n    box.startx = 0;\n  }\n  if (box.starty === undefined) {\n    box.starty = 0;\n  }\n  if (box.stopx === undefined) {\n    box.stopx = 0;\n  }\n  if (box.stopy === undefined) {\n    box.stopy = 0;\n  }\n\n  // Make sure the height of the diagram supports long menus.\n  let boxHeight = box.stopy - box.starty;\n  if (boxHeight < requiredBoxSize.maxHeight) {\n    boxHeight = requiredBoxSize.maxHeight;\n  }\n\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  if (conf.mirrorActors) {\n    height = height - conf.boxMargin + conf.bottomMarginAdj;\n  }\n\n  // Make sure the width of the diagram supports wide menus.\n  let boxWidth = box.stopx - box.startx;\n  if (boxWidth < requiredBoxSize.maxWidth) {\n    boxWidth = requiredBoxSize.maxWidth;\n  }\n  const width = boxWidth + 2 * conf.diagramMarginX;\n\n  if (title) {\n    diagram\n      .append('text')\n      .text(title)\n      .attr('x', (box.stopx - box.startx) / 2 - 2 * conf.diagramMarginX)\n      .attr('y', -25);\n  }\n\n  configureSvgSize(diagram, height, width, conf.useMaxWidth);\n\n  const extraVertForTitle = title ? 40 : 0;\n  diagram.attr(\n    'viewBox',\n    box.startx -\n      conf.diagramMarginX +\n      ' -' +\n      (conf.diagramMarginY + extraVertForTitle) +\n      ' ' +\n      width +\n      ' ' +\n      (height + extraVertForTitle)\n  );\n\n  log.debug(`models:`, bounds.models);\n};\n\n/**\n * Retrieves the max message width of each actor, supports signals (messages, loops) and notes.\n *\n * It will enumerate each given message, and will determine its text width, in relation to the actor\n * it originates from, and destined to.\n *\n * @param actors - The actors map\n * @param messages - A list of message objects to iterate\n * @param diagObj - The diagram object.\n * @returns The max message width of each actor.\n */\nasync function getMaxMessageWidthPerActor(\n  actors: Map<string, any>,\n  messages: any[],\n  diagObj: Diagram\n): Promise<Record<string, number>> {\n  const maxMessageWidthPerActor = {};\n\n  for (const msg of messages) {\n    if (actors.get(msg.to) && actors.get(msg.from)) {\n      const actor = actors.get(msg.to);\n\n      // If this is the first actor, and the message is left of it, no need to calculate the margin\n      if (msg.placement === diagObj.db.PLACEMENT.LEFTOF && !actor.prevActor) {\n        continue;\n      }\n\n      // If this is the last actor, and the message is right of it, no need to calculate the margin\n      if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF && !actor.nextActor) {\n        continue;\n      }\n\n      const isNote = msg.placement !== undefined;\n      const isMessage = !isNote;\n\n      const textFont = isNote ? noteFont(conf) : messageFont(conf);\n      const wrappedMessage = msg.wrap\n        ? utils.wrapLabel(msg.message, conf.width - 2 * conf.wrapPadding, textFont)\n        : msg.message;\n      const messageDimensions = hasKatex(wrappedMessage)\n        ? await calculateMathMLDimensions(msg.message, getConfig())\n        : utils.calculateTextDimensions(wrappedMessage, textFont);\n      const messageWidth = messageDimensions.width + 2 * conf.wrapPadding;\n\n      /*\n       * The following scenarios should be supported:\n       *\n       * - There's a message (non-note) between fromActor and toActor\n       *   - If fromActor is on the right and toActor is on the left, we should\n       *     define the toActor's margin\n       *   - If fromActor is on the left and toActor is on the right, we should\n       *     define the fromActor's margin\n       * - There's a note, in which case fromActor == toActor\n       *   - If the note is to the left of the actor, we should define the previous actor\n       *     margin\n       *   - If the note is on the actor, we should define both the previous and next actor\n       *     margins, each being the half of the note size\n       *   - If the note is on the right of the actor, we should define the current actor\n       *     margin\n       */\n      if (isMessage && msg.from === actor.nextActor) {\n        maxMessageWidthPerActor[msg.to] = common.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === actor.prevActor) {\n        maxMessageWidthPerActor[msg.from] = common.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === msg.to) {\n        maxMessageWidthPerActor[msg.from] = common.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth / 2\n        );\n\n        maxMessageWidthPerActor[msg.to] = common.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth / 2\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n        maxMessageWidthPerActor[msg.from] = common.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n        maxMessageWidthPerActor[actor.prevActor] = common.getMax(\n          maxMessageWidthPerActor[actor.prevActor] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.OVER) {\n        if (actor.prevActor) {\n          maxMessageWidthPerActor[actor.prevActor] = common.getMax(\n            maxMessageWidthPerActor[actor.prevActor] || 0,\n            messageWidth / 2\n          );\n        }\n\n        if (actor.nextActor) {\n          maxMessageWidthPerActor[msg.from] = common.getMax(\n            maxMessageWidthPerActor[msg.from] || 0,\n            messageWidth / 2\n          );\n        }\n      }\n    }\n  }\n\n  log.debug('maxMessageWidthPerActor:', maxMessageWidthPerActor);\n  return maxMessageWidthPerActor;\n}\n\nconst getRequiredPopupWidth = function (actor) {\n  let requiredPopupWidth = 0;\n  const textFont = actorFont(conf);\n  for (const key in actor.links) {\n    const labelDimensions = utils.calculateTextDimensions(key, textFont);\n    const labelWidth = labelDimensions.width + 2 * conf.wrapPadding + 2 * conf.boxMargin;\n    if (requiredPopupWidth < labelWidth) {\n      requiredPopupWidth = labelWidth;\n    }\n  }\n\n  return requiredPopupWidth;\n};\n\n/**\n * This will calculate the optimal margin for each given actor,\n * for a given actor → messageWidth map.\n *\n * An actor's margin is determined by the width of the actor, the width of the largest message that\n * originates from it, and the configured conf.actorMargin.\n *\n * @param actors - The actors map to calculate margins for\n * @param actorToMessageWidth - A map of actor key → max message width it holds\n * @param boxes - The boxes around the actors if any\n */\nasync function calculateActorMargins(\n  actors: Map<string, any>,\n  actorToMessageWidth: Awaited<ReturnType<typeof getMaxMessageWidthPerActor>>,\n  boxes\n) {\n  let maxHeight = 0;\n  for (const prop of actors.keys()) {\n    const actor = actors.get(prop);\n    if (actor.wrap) {\n      actor.description = utils.wrapLabel(\n        actor.description,\n        conf.width - 2 * conf.wrapPadding,\n        actorFont(conf)\n      );\n    }\n    const actDims = hasKatex(actor.description)\n      ? await calculateMathMLDimensions(actor.description, getConfig())\n      : utils.calculateTextDimensions(actor.description, actorFont(conf));\n\n    actor.width = actor.wrap\n      ? conf.width\n      : common.getMax(conf.width, actDims.width + 2 * conf.wrapPadding);\n\n    actor.height = actor.wrap ? common.getMax(actDims.height, conf.height) : conf.height;\n    maxHeight = common.getMax(maxHeight, actor.height);\n  }\n\n  for (const actorKey in actorToMessageWidth) {\n    const actor = actors.get(actorKey);\n\n    if (!actor) {\n      continue;\n    }\n\n    const nextActor = actors.get(actor.nextActor);\n\n    // No need to space out an actor that doesn't have a next link\n    if (!nextActor) {\n      const messageWidth = actorToMessageWidth[actorKey];\n      const actorWidth = messageWidth + conf.actorMargin - actor.width / 2;\n      actor.margin = common.getMax(actorWidth, conf.actorMargin);\n      continue;\n    }\n\n    const messageWidth = actorToMessageWidth[actorKey];\n    const actorWidth = messageWidth + conf.actorMargin - actor.width / 2 - nextActor.width / 2;\n\n    actor.margin = common.getMax(actorWidth, conf.actorMargin);\n  }\n\n  let maxBoxHeight = 0;\n  boxes.forEach((box) => {\n    const textFont = messageFont(conf);\n    let totalWidth = box.actorKeys.reduce((total, aKey) => {\n      return (total += actors.get(aKey).width + (actors.get(aKey).margin || 0));\n    }, 0);\n\n    totalWidth -= 2 * conf.boxTextMargin;\n    if (box.wrap) {\n      box.name = utils.wrapLabel(box.name, totalWidth - 2 * conf.wrapPadding, textFont);\n    }\n\n    const boxMsgDimensions = utils.calculateTextDimensions(box.name, textFont);\n    maxBoxHeight = common.getMax(boxMsgDimensions.height, maxBoxHeight);\n    const minWidth = common.getMax(totalWidth, boxMsgDimensions.width + 2 * conf.wrapPadding);\n    box.margin = conf.boxTextMargin;\n    if (totalWidth < minWidth) {\n      const missing = (minWidth - totalWidth) / 2;\n      box.margin += missing;\n    }\n  });\n  boxes.forEach((box) => (box.textMaxHeight = maxBoxHeight));\n\n  return common.getMax(maxHeight, conf.height);\n}\n\nconst buildNoteModel = async function (msg, actors, diagObj) {\n  const fromActor = actors.get(msg.from);\n  const toActor = actors.get(msg.to);\n  const startx = fromActor.x;\n  const stopx = toActor.x;\n  const shouldWrap = msg.wrap && msg.message;\n\n  let textDimensions: { width: number; height: number; lineHeight?: number } = hasKatex(msg.message)\n    ? await calculateMathMLDimensions(msg.message, getConfig())\n    : utils.calculateTextDimensions(\n        shouldWrap ? utils.wrapLabel(msg.message, conf.width, noteFont(conf)) : msg.message,\n        noteFont(conf)\n      );\n  const noteModel = {\n    width: shouldWrap\n      ? conf.width\n      : common.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin),\n    height: 0,\n    startx: fromActor.x,\n    stopx: 0,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n  };\n  if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n    noteModel.width = shouldWrap\n      ? common.getMax(conf.width, textDimensions.width)\n      : common.getMax(\n          fromActor.width / 2 + toActor.width / 2,\n          textDimensions.width + 2 * conf.noteMargin\n        );\n    noteModel.startx = startx + (fromActor.width + conf.actorMargin) / 2;\n  } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n    noteModel.width = shouldWrap\n      ? common.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin)\n      : common.getMax(\n          fromActor.width / 2 + toActor.width / 2,\n          textDimensions.width + 2 * conf.noteMargin\n        );\n    noteModel.startx = startx - noteModel.width + (fromActor.width - conf.actorMargin) / 2;\n  } else if (msg.to === msg.from) {\n    textDimensions = utils.calculateTextDimensions(\n      shouldWrap\n        ? utils.wrapLabel(msg.message, common.getMax(conf.width, fromActor.width), noteFont(conf))\n        : msg.message,\n      noteFont(conf)\n    );\n    noteModel.width = shouldWrap\n      ? common.getMax(conf.width, fromActor.width)\n      : common.getMax(fromActor.width, conf.width, textDimensions.width + 2 * conf.noteMargin);\n    noteModel.startx = startx + (fromActor.width - noteModel.width) / 2;\n  } else {\n    noteModel.width =\n      Math.abs(startx + fromActor.width / 2 - (stopx + toActor.width / 2)) + conf.actorMargin;\n    noteModel.startx =\n      startx < stopx\n        ? startx + fromActor.width / 2 - conf.actorMargin / 2\n        : stopx + toActor.width / 2 - conf.actorMargin / 2;\n  }\n  if (shouldWrap) {\n    noteModel.message = utils.wrapLabel(\n      msg.message,\n      noteModel.width - 2 * conf.wrapPadding,\n      noteFont(conf)\n    );\n  }\n  log.debug(\n    `NM:[${noteModel.startx},${noteModel.stopx},${noteModel.starty},${noteModel.stopy}:${noteModel.width},${noteModel.height}=${msg.message}]`\n  );\n  return noteModel;\n};\n\nconst buildMessageModel = function (msg, actors, diagObj) {\n  if (\n    ![\n      diagObj.db.LINETYPE.SOLID_OPEN,\n      diagObj.db.LINETYPE.DOTTED_OPEN,\n      diagObj.db.LINETYPE.SOLID,\n      diagObj.db.LINETYPE.DOTTED,\n      diagObj.db.LINETYPE.SOLID_CROSS,\n      diagObj.db.LINETYPE.DOTTED_CROSS,\n      diagObj.db.LINETYPE.SOLID_POINT,\n      diagObj.db.LINETYPE.DOTTED_POINT,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED,\n    ].includes(msg.type)\n  ) {\n    return {};\n  }\n  const [fromLeft, fromRight] = activationBounds(msg.from, actors);\n  const [toLeft, toRight] = activationBounds(msg.to, actors);\n  const isArrowToRight = fromLeft <= toLeft;\n  let startx = isArrowToRight ? fromRight : fromLeft;\n  let stopx = isArrowToRight ? toLeft : toRight;\n\n  // As the line width is considered, the left and right values will be off by 2.\n  const isArrowToActivation = Math.abs(toLeft - toRight) > 2;\n\n  /**\n   * Adjust the value based on the arrow direction\n   * @param value - The value to adjust\n   * @returns The adjustment with correct sign to be added to the actual value.\n   */\n  const adjustValue = (value: number) => {\n    return isArrowToRight ? -value : value;\n  };\n\n  if (msg.from === msg.to) {\n    // This is a self reference, so we need to make sure the arrow is drawn correctly\n    // There are many checks in the downstream rendering that checks for equality.\n    // The lines on loops will be off by few pixels, but that's fine for now.\n    stopx = startx;\n  } else {\n    /**\n     * This is an edge case for the first activation.\n     * Proper fix would require significant changes.\n     * So, we set an activate flag in the message, and cross check that with isToActivation\n     * In cases where the message is to an activation that was properly detected, we don't want to move the arrow head\n     * The activation will not be detected on the first message, so we need to move the arrow head\n     */\n    if (msg.activate && !isArrowToActivation) {\n      stopx += adjustValue(conf.activationWidth / 2 - 1);\n    }\n\n    /**\n     * Shorten the length of arrow at the end and move the marker forward (using refX) to have a clean arrowhead\n     * This is not required for open arrows that don't have arrowheads\n     */\n    if (![diagObj.db.LINETYPE.SOLID_OPEN, diagObj.db.LINETYPE.DOTTED_OPEN].includes(msg.type)) {\n      stopx += adjustValue(3);\n    }\n\n    /**\n     * Shorten start position of bidirectional arrow to accommodate for second arrowhead\n     */\n    if (\n      [diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID, diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(\n        msg.type\n      )\n    ) {\n      startx -= adjustValue(3);\n    }\n  }\n\n  const allBounds = [fromLeft, fromRight, toLeft, toRight];\n  const boundedWidth = Math.abs(startx - stopx);\n  if (msg.wrap && msg.message) {\n    msg.message = utils.wrapLabel(\n      msg.message,\n      common.getMax(boundedWidth + 2 * conf.wrapPadding, conf.width),\n      messageFont(conf)\n    );\n  }\n  const msgDims = utils.calculateTextDimensions(msg.message, messageFont(conf));\n\n  return {\n    width: common.getMax(\n      msg.wrap ? 0 : msgDims.width + 2 * conf.wrapPadding,\n      boundedWidth + 2 * conf.wrapPadding,\n      conf.width\n    ),\n    height: 0,\n    startx,\n    stopx,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n    type: msg.type,\n    wrap: msg.wrap,\n    fromBounds: Math.min.apply(null, allBounds),\n    toBounds: Math.max.apply(null, allBounds),\n  };\n};\n\nconst calculateLoopBounds = async function (messages, actors, _maxWidthPerActor, diagObj) {\n  const loops = {};\n  const stack = [];\n  let current, noteModel, msgModel;\n\n  for (const msg of messages) {\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.LOOP_START:\n      case diagObj.db.LINETYPE.ALT_START:\n      case diagObj.db.LINETYPE.OPT_START:\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n      case diagObj.db.LINETYPE.CRITICAL_START:\n      case diagObj.db.LINETYPE.BREAK_START:\n        stack.push({\n          id: msg.id,\n          msg: msg.message,\n          from: Number.MAX_SAFE_INTEGER,\n          to: Number.MIN_SAFE_INTEGER,\n          width: 0,\n        });\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n      case diagObj.db.LINETYPE.PAR_AND:\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        if (msg.message) {\n          current = stack.pop();\n          loops[current.id] = current;\n          loops[msg.id] = current;\n          stack.push(current);\n        }\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n      case diagObj.db.LINETYPE.ALT_END:\n      case diagObj.db.LINETYPE.OPT_END:\n      case diagObj.db.LINETYPE.PAR_END:\n      case diagObj.db.LINETYPE.CRITICAL_END:\n      case diagObj.db.LINETYPE.BREAK_END:\n        current = stack.pop();\n        loops[current.id] = current;\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        {\n          const actorRect = actors.get(msg.from ? msg.from : msg.to.actor);\n          const stackedSize = actorActivations(msg.from ? msg.from : msg.to.actor).length;\n          const x =\n            actorRect.x + actorRect.width / 2 + ((stackedSize - 1) * conf.activationWidth) / 2;\n          const toAdd = {\n            startx: x,\n            stopx: x + conf.activationWidth,\n            actor: msg.from,\n            enabled: true,\n          };\n          bounds.activations.push(toAdd);\n        }\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        {\n          const lastActorActivationIdx = bounds.activations\n            .map((a) => a.actor)\n            .lastIndexOf(msg.from);\n          bounds.activations.splice(lastActorActivationIdx, 1).splice(0, 1);\n        }\n        break;\n    }\n    const isNote = msg.placement !== undefined;\n    if (isNote) {\n      noteModel = await buildNoteModel(msg, actors, diagObj);\n      msg.noteModel = noteModel;\n      stack.forEach((stk) => {\n        current = stk;\n        current.from = common.getMin(current.from, noteModel.startx);\n        current.to = common.getMax(current.to, noteModel.startx + noteModel.width);\n        current.width =\n          common.getMax(current.width, Math.abs(current.from - current.to)) - conf.labelBoxWidth;\n      });\n    } else {\n      msgModel = buildMessageModel(msg, actors, diagObj);\n      msg.msgModel = msgModel;\n      if (msgModel.startx && msgModel.stopx && stack.length > 0) {\n        stack.forEach((stk) => {\n          current = stk;\n          if (msgModel.startx === msgModel.stopx) {\n            const from = actors.get(msg.from);\n            const to = actors.get(msg.to);\n            current.from = common.getMin(\n              from.x - msgModel.width / 2,\n              from.x - from.width / 2,\n              current.from\n            );\n            current.to = common.getMax(\n              to.x + msgModel.width / 2,\n              to.x + from.width / 2,\n              current.to\n            );\n            current.width =\n              common.getMax(current.width, Math.abs(current.to - current.from)) -\n              conf.labelBoxWidth;\n          } else {\n            current.from = common.getMin(msgModel.startx, current.from);\n            current.to = common.getMax(msgModel.stopx, current.to);\n            current.width = common.getMax(current.width, msgModel.width) - conf.labelBoxWidth;\n          }\n        });\n      }\n    }\n  }\n  bounds.activations = [];\n  log.debug('Loop type widths:', loops);\n  return loops;\n};\n\nexport default {\n  bounds,\n  drawActors,\n  drawActorsPopup,\n  setConf,\n  draw,\n};\n", "import common, { calculateMathMLDimensions, hasKatex, renderKatex } from '../common/common.js';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\nimport { ZERO_WIDTH_SPACE, parseFontSize } from '../../utils.js';\nimport { sanitizeUrl } from '@braintree/sanitize-url';\nimport * as configApi from '../../config.js';\n\nexport const ACTOR_TYPE_WIDTH = 18 * 2;\nconst TOP_ACTOR_CLASS = 'actor-top';\nconst BOTTOM_ACTOR_CLASS = 'actor-bottom';\nconst ACTOR_BOX_CLASS = 'actor-box';\nconst ACTOR_MAN_FIGURE_CLASS = 'actor-man';\n\nexport const drawRect = function (elem, rectData) {\n  return svgDrawCommon.drawRect(elem, rectData);\n};\n\nexport const drawPopup = function (elem, actor, minMenuWidth, textAttrs, forceMenus) {\n  if (actor.links === undefined || actor.links === null || Object.keys(actor.links).length === 0) {\n    return { height: 0, width: 0 };\n  }\n\n  const links = actor.links;\n  const actorCnt = actor.actorCnt;\n  const rectData = actor.rectData;\n\n  var displayValue = 'none';\n  if (forceMenus) {\n    displayValue = 'block !important';\n  }\n\n  const g = elem.append('g');\n  g.attr('id', 'actor' + actorCnt + '_popup');\n  g.attr('class', 'actorPopupMenu');\n  g.attr('display', displayValue);\n  var actorClass = '';\n  if (rectData.class !== undefined) {\n    actorClass = ' ' + rectData.class;\n  }\n\n  let menuWidth = rectData.width > minMenuWidth ? rectData.width : minMenuWidth;\n\n  const rectElem = g.append('rect');\n  rectElem.attr('class', 'actorPopupMenuPanel' + actorClass);\n  rectElem.attr('x', rectData.x);\n  rectElem.attr('y', rectData.height);\n  rectElem.attr('fill', rectData.fill);\n  rectElem.attr('stroke', rectData.stroke);\n  rectElem.attr('width', menuWidth);\n  rectElem.attr('height', rectData.height);\n  rectElem.attr('rx', rectData.rx);\n  rectElem.attr('ry', rectData.ry);\n  if (links != null) {\n    var linkY = 20;\n    for (let key in links) {\n      var linkElem = g.append('a');\n      var sanitizedLink = sanitizeUrl(links[key]);\n      linkElem.attr('xlink:href', sanitizedLink);\n      linkElem.attr('target', '_blank');\n\n      _drawMenuItemTextCandidateFunc(textAttrs)(\n        key,\n        linkElem,\n        rectData.x + 10,\n        rectData.height + linkY,\n        menuWidth,\n        20,\n        { class: 'actor' },\n        textAttrs\n      );\n\n      linkY += 30;\n    }\n  }\n\n  rectElem.attr('height', linkY);\n\n  return { height: rectData.height + linkY, width: menuWidth };\n};\n\nconst popupMenuToggle = function (popId) {\n  return (\n    \"var pu = document.getElementById('\" +\n    popId +\n    \"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }\"\n  );\n};\n\nexport const drawKatex = async function (elem, textData, msgModel = null) {\n  let textElem = elem.append('foreignObject');\n  const lines = await renderKatex(textData.text, configApi.getConfig());\n\n  const divElem = textElem\n    .append('xhtml:div')\n    .attr('style', 'width: fit-content;')\n    .attr('xmlns', 'http://www.w3.org/1999/xhtml')\n    .html(lines);\n  const dim = divElem.node().getBoundingClientRect();\n\n  textElem.attr('height', Math.round(dim.height)).attr('width', Math.round(dim.width));\n\n  if (textData.class === 'noteText') {\n    const rectElem = elem.node().firstChild;\n\n    rectElem.setAttribute('height', dim.height + 2 * textData.textMargin);\n    const rectDim = rectElem.getBBox();\n\n    textElem\n      .attr('x', Math.round(rectDim.x + rectDim.width / 2 - dim.width / 2))\n      .attr('y', Math.round(rectDim.y + rectDim.height / 2 - dim.height / 2));\n  } else if (msgModel) {\n    let { startx, stopx, starty } = msgModel;\n    if (startx > stopx) {\n      const temp = startx;\n      startx = stopx;\n      stopx = temp;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n    textElem.attr('x', Math.round(startx + Math.abs(startx - stopx) / 2 - dim.width / 2));\n    if (textData.class === 'loopText') {\n      textElem.attr('y', Math.round(starty));\n    } else {\n      textElem.attr('y', Math.round(starty - dim.height));\n    }\n  }\n\n  return [textElem];\n};\n\nexport const drawText = function (elem, textData) {\n  let prevTextHeight = 0;\n  let textHeight = 0;\n  const lines = textData.text.split(common.lineBreakRegex);\n\n  const [_textFontSize, _textFontSizePx] = parseFontSize(textData.fontSize);\n\n  let textElems = [];\n  let dy = 0;\n  let yfunc = () => textData.y;\n  if (\n    textData.valign !== undefined &&\n    textData.textMargin !== undefined &&\n    textData.textMargin > 0\n  ) {\n    switch (textData.valign) {\n      case 'top':\n      case 'start':\n        yfunc = () => Math.round(textData.y + textData.textMargin);\n        break;\n      case 'middle':\n      case 'center':\n        yfunc = () =>\n          Math.round(textData.y + (prevTextHeight + textHeight + textData.textMargin) / 2);\n        break;\n      case 'bottom':\n      case 'end':\n        yfunc = () =>\n          Math.round(\n            textData.y +\n              (prevTextHeight + textHeight + 2 * textData.textMargin) -\n              textData.textMargin\n          );\n        break;\n    }\n  }\n\n  if (\n    textData.anchor !== undefined &&\n    textData.textMargin !== undefined &&\n    textData.width !== undefined\n  ) {\n    switch (textData.anchor) {\n      case 'left':\n      case 'start':\n        textData.x = Math.round(textData.x + textData.textMargin);\n        textData.anchor = 'start';\n        textData.dominantBaseline = 'middle';\n        textData.alignmentBaseline = 'middle';\n        break;\n      case 'middle':\n      case 'center':\n        textData.x = Math.round(textData.x + textData.width / 2);\n        textData.anchor = 'middle';\n        textData.dominantBaseline = 'middle';\n        textData.alignmentBaseline = 'middle';\n        break;\n      case 'right':\n      case 'end':\n        textData.x = Math.round(textData.x + textData.width - textData.textMargin);\n        textData.anchor = 'end';\n        textData.dominantBaseline = 'middle';\n        textData.alignmentBaseline = 'middle';\n        break;\n    }\n  }\n\n  for (let [i, line] of lines.entries()) {\n    if (\n      textData.textMargin !== undefined &&\n      textData.textMargin === 0 &&\n      _textFontSize !== undefined\n    ) {\n      dy = i * _textFontSize;\n    }\n\n    const textElem = elem.append('text');\n    textElem.attr('x', textData.x);\n    textElem.attr('y', yfunc());\n    if (textData.anchor !== undefined) {\n      textElem\n        .attr('text-anchor', textData.anchor)\n        .attr('dominant-baseline', textData.dominantBaseline)\n        .attr('alignment-baseline', textData.alignmentBaseline);\n    }\n    if (textData.fontFamily !== undefined) {\n      textElem.style('font-family', textData.fontFamily);\n    }\n    if (_textFontSizePx !== undefined) {\n      textElem.style('font-size', _textFontSizePx);\n    }\n    if (textData.fontWeight !== undefined) {\n      textElem.style('font-weight', textData.fontWeight);\n    }\n    if (textData.fill !== undefined) {\n      textElem.attr('fill', textData.fill);\n    }\n    if (textData.class !== undefined) {\n      textElem.attr('class', textData.class);\n    }\n    if (textData.dy !== undefined) {\n      textElem.attr('dy', textData.dy);\n    } else if (dy !== 0) {\n      textElem.attr('dy', dy);\n    }\n\n    const text = line || ZERO_WIDTH_SPACE;\n    if (textData.tspan) {\n      const span = textElem.append('tspan');\n      span.attr('x', textData.x);\n      if (textData.fill !== undefined) {\n        span.attr('fill', textData.fill);\n      }\n      span.text(text);\n    } else {\n      textElem.text(text);\n    }\n    if (\n      textData.valign !== undefined &&\n      textData.textMargin !== undefined &&\n      textData.textMargin > 0\n    ) {\n      textHeight += (textElem._groups || textElem)[0][0].getBBox().height;\n      prevTextHeight = textHeight;\n    }\n\n    textElems.push(textElem);\n  }\n\n  return textElems;\n};\n\nexport const drawLabel = function (elem, txtObject) {\n  /**\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} cut\n   * @returns {any}\n   */\n  function genPoints(x, y, width, height, cut) {\n    return (\n      x +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      (y + height - cut) +\n      ' ' +\n      (x + width - cut * 1.2) +\n      ',' +\n      (y + height) +\n      ' ' +\n      x +\n      ',' +\n      (y + height)\n    );\n  }\n  const polygon = elem.append('polygon');\n  polygon.attr('points', genPoints(txtObject.x, txtObject.y, txtObject.width, txtObject.height, 7));\n  polygon.attr('class', 'labelBox');\n\n  txtObject.y = txtObject.y + txtObject.height / 2;\n\n  drawText(elem, txtObject);\n  return polygon;\n};\n\nlet actorCnt = -1;\n\nexport const fixLifeLineHeights = (diagram, actors, actorKeys, conf) => {\n  if (!diagram.select) {\n    return;\n  }\n  actorKeys.forEach((actorKey) => {\n    const actor = actors.get(actorKey);\n    const actorDOM = diagram.select('#actor' + actor.actorCnt);\n    if (!conf.mirrorActors && actor.stopy) {\n      actorDOM.attr('y2', actor.stopy + actor.height / 2);\n    } else if (conf.mirrorActors) {\n      actorDOM.attr('y2', actor.stopy);\n    }\n  });\n};\n\n/**\n * Draws an actor in the diagram with the attached line\n *\n * @param {any} elem - The diagram we'll draw to.\n * @param {any} actor - The actor to draw.\n * @param {any} conf - DrawText implementation discriminator object\n * @param {boolean} isFooter - If the actor is the footer one\n */\nconst drawActorTypeParticipant = function (elem, actor, conf, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + actor.height;\n\n  const boxplusLineGroup = elem.append('g').lower();\n  var g = boxplusLineGroup;\n\n  if (!isFooter) {\n    actorCnt++;\n    if (Object.keys(actor.links || {}).length && !conf.forceMenus) {\n      g.attr('onclick', popupMenuToggle(`actor${actorCnt}_popup`)).attr('cursor', 'pointer');\n    }\n    g.append('line')\n      .attr('id', 'actor' + actorCnt)\n      .attr('x1', center)\n      .attr('y1', centerY)\n      .attr('x2', center)\n      .attr('y2', 2000)\n      .attr('class', 'actor-line 200')\n      .attr('stroke-width', '0.5px')\n      .attr('stroke', '#999')\n      .attr('name', actor.name);\n\n    g = boxplusLineGroup.append('g');\n    actor.actorCnt = actorCnt;\n\n    if (actor.links != null) {\n      g.attr('id', 'root-' + actorCnt);\n    }\n  }\n\n  const rect = svgDrawCommon.getNoteRect();\n  var cssclass = 'actor';\n  if (actor.properties?.class) {\n    cssclass = actor.properties.class;\n  } else {\n    rect.fill = '#eaeaea';\n  }\n  if (isFooter) {\n    cssclass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssclass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = cssclass;\n  rect.rx = 3;\n  rect.ry = 3;\n  rect.name = actor.name;\n  const rectElem = drawRect(g, rect);\n  actor.rectData = rect;\n\n  if (actor.properties?.icon) {\n    const iconSrc = actor.properties.icon.trim();\n    if (iconSrc.charAt(0) === '@') {\n      svgDrawCommon.drawEmbeddedImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc.substr(1));\n    } else {\n      svgDrawCommon.drawImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc);\n    }\n  }\n\n  _drawTextCandidateFunc(conf, hasKatex(actor.description))(\n    actor.description,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_BOX_CLASS}` },\n    conf\n  );\n\n  let height = actor.height;\n  if (rectElem.node) {\n    const bounds = rectElem.node().getBBox();\n    actor.height = bounds.height;\n    height = bounds.height;\n  }\n\n  return height;\n};\n\nconst drawActorTypeActor = function (elem, actor, conf, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + 80;\n\n  const line = elem.append('g').lower();\n\n  if (!isFooter) {\n    actorCnt++;\n    line\n      .append('line')\n      .attr('id', 'actor' + actorCnt)\n      .attr('x1', center)\n      .attr('y1', centerY)\n      .attr('x2', center)\n      .attr('y2', 2000)\n      .attr('class', 'actor-line 200')\n      .attr('stroke-width', '0.5px')\n      .attr('stroke', '#999')\n      .attr('name', actor.name);\n\n    actor.actorCnt = actorCnt;\n  }\n  const actElem = elem.append('g');\n  let cssClass = ACTOR_MAN_FIGURE_CLASS;\n  if (isFooter) {\n    cssClass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssClass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  actElem.attr('class', cssClass);\n  actElem.attr('name', actor.name);\n\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.fill = '#eaeaea';\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = 'actor';\n  rect.rx = 3;\n  rect.ry = 3;\n\n  actElem\n    .append('line')\n    .attr('id', 'actor-man-torso' + actorCnt)\n    .attr('x1', center)\n    .attr('y1', actorY + 25)\n    .attr('x2', center)\n    .attr('y2', actorY + 45);\n\n  actElem\n    .append('line')\n    .attr('id', 'actor-man-arms' + actorCnt)\n    .attr('x1', center - ACTOR_TYPE_WIDTH / 2)\n    .attr('y1', actorY + 33)\n    .attr('x2', center + ACTOR_TYPE_WIDTH / 2)\n    .attr('y2', actorY + 33);\n  actElem\n    .append('line')\n    .attr('x1', center - ACTOR_TYPE_WIDTH / 2)\n    .attr('y1', actorY + 60)\n    .attr('x2', center)\n    .attr('y2', actorY + 45);\n  actElem\n    .append('line')\n    .attr('x1', center)\n    .attr('y1', actorY + 45)\n    .attr('x2', center + ACTOR_TYPE_WIDTH / 2 - 2)\n    .attr('y2', actorY + 60);\n\n  const circle = actElem.append('circle');\n  circle.attr('cx', actor.x + actor.width / 2);\n  circle.attr('cy', actorY + 10);\n  circle.attr('r', 15);\n  circle.attr('width', actor.width);\n  circle.attr('height', actor.height);\n\n  const bounds = actElem.node().getBBox();\n  actor.height = bounds.height;\n\n  _drawTextCandidateFunc(conf, hasKatex(actor.description))(\n    actor.description,\n    actElem,\n    rect.x,\n    rect.y + 35,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_MAN_FIGURE_CLASS}` },\n    conf\n  );\n\n  return actor.height;\n};\n\nexport const drawActor = async function (elem, actor, conf, isFooter) {\n  switch (actor.type) {\n    case 'actor':\n      return await drawActorTypeActor(elem, actor, conf, isFooter);\n    case 'participant':\n      return await drawActorTypeParticipant(elem, actor, conf, isFooter);\n  }\n};\n\nexport const drawBox = function (elem, box, conf) {\n  const boxplusTextGroup = elem.append('g');\n  const g = boxplusTextGroup;\n  drawBackgroundRect(g, box);\n  if (box.name) {\n    _drawTextCandidateFunc(conf)(\n      box.name,\n      g,\n      box.x,\n      box.y + (box.textMaxHeight || 0) / 2,\n      box.width,\n      0,\n      { class: 'text' },\n      conf\n    );\n  }\n  g.lower();\n};\n\nexport const anchorElement = function (elem) {\n  return elem.append('g');\n};\n\n/**\n * Draws an activation in the diagram\n *\n * @param {any} elem - Element to append activation rect.\n * @param {any} bounds - Activation box bounds.\n * @param {any} verticalPos - Precise y coordinate of bottom activation box edge.\n * @param {any} conf - Sequence diagram config object.\n * @param {any} actorActivations - Number of activations on the actor.\n */\nexport const drawActivation = function (elem, bounds, verticalPos, conf, actorActivations) {\n  const rect = svgDrawCommon.getNoteRect();\n  const g = bounds.anchored;\n  rect.x = bounds.startx;\n  rect.y = bounds.starty;\n  rect.class = 'activation' + (actorActivations % 3); // Will evaluate to 0, 1 or 2\n  rect.width = bounds.stopx - bounds.startx;\n  rect.height = verticalPos - bounds.starty;\n  drawRect(g, rect);\n};\n\n/**\n * Draws a loop in the diagram\n *\n * @param {any} elem - Element to append the loop to.\n * @param {any} loopModel - LoopModel of the given loop.\n * @param {any} labelText - Text within the loop.\n * @param {any} conf - Diagram configuration\n * @returns {any}\n */\nexport const drawLoop = async function (elem, loopModel, labelText, conf) {\n  const {\n    boxMargin,\n    boxTextMargin,\n    labelBoxHeight,\n    labelBoxWidth,\n    messageFontFamily: fontFamily,\n    messageFontSize: fontSize,\n    messageFontWeight: fontWeight,\n  } = conf;\n  const g = elem.append('g');\n  const drawLoopLine = function (startx, starty, stopx, stopy) {\n    return g\n      .append('line')\n      .attr('x1', startx)\n      .attr('y1', starty)\n      .attr('x2', stopx)\n      .attr('y2', stopy)\n      .attr('class', 'loopLine');\n  };\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.stopx, loopModel.starty);\n  drawLoopLine(loopModel.stopx, loopModel.starty, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.stopy, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.startx, loopModel.stopy);\n  if (loopModel.sections !== undefined) {\n    loopModel.sections.forEach(function (item) {\n      drawLoopLine(loopModel.startx, item.y, loopModel.stopx, item.y).style(\n        'stroke-dasharray',\n        '3, 3'\n      );\n    });\n  }\n\n  let txt = svgDrawCommon.getTextObj();\n  txt.text = labelText;\n  txt.x = loopModel.startx;\n  txt.y = loopModel.starty;\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.anchor = 'middle';\n  txt.valign = 'middle';\n  txt.tspan = false;\n  txt.width = labelBoxWidth || 50;\n  txt.height = labelBoxHeight || 20;\n  txt.textMargin = boxTextMargin;\n  txt.class = 'labelText';\n\n  drawLabel(g, txt);\n  txt = getTextObj();\n  txt.text = loopModel.title;\n  txt.x = loopModel.startx + labelBoxWidth / 2 + (loopModel.stopx - loopModel.startx) / 2;\n  txt.y = loopModel.starty + boxMargin + boxTextMargin;\n  txt.anchor = 'middle';\n  txt.valign = 'middle';\n  txt.textMargin = boxTextMargin;\n  txt.class = 'loopText';\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.wrap = true;\n\n  let textElem = hasKatex(txt.text) ? await drawKatex(g, txt, loopModel) : drawText(g, txt);\n\n  if (loopModel.sectionTitles !== undefined) {\n    for (const [idx, item] of Object.entries(loopModel.sectionTitles)) {\n      if (item.message) {\n        txt.text = item.message;\n        txt.x = loopModel.startx + (loopModel.stopx - loopModel.startx) / 2;\n        txt.y = loopModel.sections[idx].y + boxMargin + boxTextMargin;\n        txt.class = 'loopText';\n        txt.anchor = 'middle';\n        txt.valign = 'middle';\n        txt.tspan = false;\n        txt.fontFamily = fontFamily;\n        txt.fontSize = fontSize;\n        txt.fontWeight = fontWeight;\n        txt.wrap = loopModel.wrap;\n\n        if (hasKatex(txt.text)) {\n          loopModel.starty = loopModel.sections[idx].y;\n          await drawKatex(g, txt, loopModel);\n        } else {\n          drawText(g, txt);\n        }\n        let sectionHeight = Math.round(\n          textElem\n            .map((te) => (te._groups || te)[0][0].getBBox().height)\n            .reduce((acc, curr) => acc + curr)\n        );\n        loopModel.sections[idx].height += sectionHeight - (boxMargin + boxTextMargin);\n      }\n    }\n  }\n\n  loopModel.height = Math.round(loopModel.stopy - loopModel.starty);\n  return g;\n};\n\n/**\n * Draws a background rectangle\n *\n * @param {any} elem Diagram (reference for bounds)\n * @param {any} bounds Shape of the rectangle\n */\nexport const drawBackgroundRect = function (elem, bounds) {\n  svgDrawCommon.drawBackgroundRect(elem, bounds);\n};\n\nexport const insertDatabaseIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'database')\n    .attr('fill-rule', 'evenodd')\n    .attr('clip-rule', 'evenodd')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z'\n    );\n};\n\nexport const insertComputerIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'computer')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z'\n    );\n};\n\nexport const insertClockIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'clock')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z'\n    );\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param elem\n */\nexport const insertArrowHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 7.9)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto-start-reverse')\n    .append('path')\n    .attr('d', 'M -1 0 L 10 5 L 0 10 z'); // this is actual shape for arrowhead\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowFilledHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'filled-head')\n    .attr('refX', 15.5)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\n\n/**\n * Setup node number. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertSequenceNumber = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'sequencenumber')\n    .attr('refX', 15)\n    .attr('refY', 15)\n    .attr('markerWidth', 60)\n    .attr('markerHeight', 40)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', 15)\n    .attr('cy', 15)\n    .attr('r', 6);\n  // .style(\"fill\", '#f00');\n};\n\n/**\n * Setup cross head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowCrossHead = function (elem) {\n  const defs = elem.append('defs');\n  const marker = defs\n    .append('marker')\n    .attr('id', 'crosshead')\n    .attr('markerWidth', 15)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .attr('refX', 4)\n    .attr('refY', 4.5);\n  // The cross\n  marker\n    .append('path')\n    .attr('fill', 'none')\n    .attr('stroke', '#000000')\n    .style('stroke-dasharray', '0, 0')\n    .attr('stroke-width', '1pt')\n    .attr('d', 'M 1,2 L 6,7 M 6,2 L 1,7');\n  // this is actual shape for arrowhead\n};\n\nexport const getTextObj = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: undefined,\n    anchor: undefined,\n    style: '#666',\n    width: undefined,\n    height: undefined,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n    valign: undefined,\n  };\n};\n\nexport const getNoteRect = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: '#EDF2AE',\n    stroke: '#666',\n    width: 100,\n    anchor: 'start',\n    height: 100,\n    rx: 0,\n    ry: 0,\n  };\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   */\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf;\n\n    const [_actorFontSize, _actorFontSizePx] = parseFontSize(actorFontSize);\n\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * _actorFontSize - (_actorFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .style('text-anchor', 'middle')\n        .style('font-size', _actorFontSizePx)\n        .style('font-weight', actorFontWeight)\n        .style('font-family', actorFontFamily);\n      text\n        .append('tspan')\n        .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   *\n   * @param content\n   * @param g\n   * @param x\n   * @param y\n   * @param width\n   * @param height\n   * @param textAttrs\n   * @param conf\n   */\n  async function byKatex(content, g, x, y, width, height, textAttrs, conf) {\n    // TODO duplicate render calls, optimize\n\n    const dim = await calculateMathMLDimensions(content, configApi.getConfig());\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x + width / 2 - dim.width / 2)\n      .attr('y', y + height / 2 - dim.height / 2)\n      .attr('width', dim.width)\n      .attr('height', dim.height);\n\n    const text = f.append('xhtml:div').style('height', '100%').style('width', '100%');\n\n    text\n      .append('div')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .html(await renderKatex(content, configApi.getConfig()));\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf, hasKatex = false) {\n    if (hasKatex) {\n      return byKatex;\n    }\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nconst _drawMenuItemTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   */\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g\n      .append('text')\n      .attr('x', x)\n      .attr('y', y)\n      .style('text-anchor', 'start')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf;\n\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * actorFontSize - (actorFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x)\n        .attr('y', y)\n        .style('text-anchor', 'start')\n        .style('font-size', actorFontSize)\n        .style('font-weight', actorFontWeight)\n        .style('font-family', actorFontFamily);\n      text.append('tspan').attr('x', x).attr('dy', dy).text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nexport default {\n  drawRect,\n  drawText,\n  drawLabel,\n  drawActor,\n  drawBox,\n  drawPopup,\n  anchorElement,\n  drawActivation,\n  drawLoop,\n  drawBackgroundRect,\n  insertArrowHead,\n  insertArrowFilledHead,\n  insertSequenceNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n  getTextObj,\n  getNoteRect,\n  fixLifeLineHeights,\n  sanitizeUrl,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/sequenceDiagram.jison';\nimport { SequenceDB } from './sequenceDb.js';\nimport styles from './styles.js';\nimport { setConfig } from '../../diagram-api/diagramAPI.js';\nimport renderer from './sequenceRenderer.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  get db() {\n    return new SequenceDB();\n  },\n  renderer,\n  styles,\n  init: (cnf: MermaidConfig) => {\n    if (!cnf.sequence) {\n      cnf.sequence = {};\n    }\n    if (cnf.wrap) {\n      cnf.sequence.wrap = cnf.wrap;\n      setConfig({ sequence: { wrap: cnf.wrap } });\n    }\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,GAAG;AACv8B,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,SAAQ,GAAE,WAAU,GAAE,MAAK,GAAE,YAAW,GAAE,QAAO,GAAE,aAAY,GAAE,eAAc,IAAG,YAAW,IAAG,yBAAwB,IAAG,UAAS,IAAG,OAAM,IAAG,cAAa,IAAG,OAAM,IAAG,UAAS,IAAG,cAAa,IAAG,OAAM,IAAG,OAAM,IAAG,YAAW,IAAG,SAAQ,IAAG,cAAa,IAAG,kBAAiB,IAAG,mBAAkB,IAAG,kBAAiB,IAAG,wBAAuB,IAAG,qBAAoB,IAAG,SAAQ,IAAG,gBAAe,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,QAAO,IAAG,QAAO,IAAG,OAAM,IAAG,OAAM,IAAG,iBAAgB,IAAG,OAAM,IAAG,gBAAe,IAAG,YAAW,IAAG,YAAW,IAAG,mBAAkB,IAAG,SAAQ,IAAG,UAAS,IAAG,OAAM,IAAG,QAAO,IAAG,eAAc,IAAG,MAAK,IAAG,qBAAoB,IAAG,WAAU,IAAG,QAAO,IAAG,aAAY,IAAG,SAAQ,IAAG,QAAO,IAAG,cAAa,IAAG,SAAQ,IAAG,QAAO,IAAG,cAAa,IAAG,WAAU,IAAG,aAAY,IAAG,KAAI,IAAG,WAAU,IAAG,YAAW,IAAG,cAAa,IAAG,KAAI,IAAG,KAAI,IAAG,SAAQ,IAAG,oBAAmB,IAAG,qBAAoB,IAAG,eAAc,IAAG,6BAA4B,IAAG,gBAAe,IAAG,8BAA6B,IAAG,eAAc,IAAG,gBAAe,IAAG,eAAc,IAAG,gBAAe,IAAG,OAAM,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACzrC,YAAY,EAAC,GAAE,SAAQ,GAAE,SAAQ,GAAE,WAAU,GAAE,MAAK,IAAG,UAAS,IAAG,OAAM,IAAG,cAAa,IAAG,OAAM,IAAG,cAAa,IAAG,OAAM,IAAG,OAAM,IAAG,YAAW,IAAG,cAAa,IAAG,SAAQ,IAAG,gBAAe,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,QAAO,IAAG,QAAO,IAAG,OAAM,IAAG,OAAM,IAAG,OAAM,IAAG,YAAW,IAAG,YAAW,IAAG,SAAQ,IAAG,UAAS,IAAG,OAAM,IAAG,QAAO,IAAG,eAAc,IAAG,MAAK,IAAG,qBAAoB,IAAG,WAAU,IAAG,QAAO,IAAG,QAAO,IAAG,SAAQ,IAAG,QAAO,IAAG,cAAa,IAAG,WAAU,IAAG,KAAI,IAAG,WAAU,IAAG,YAAW,IAAG,KAAI,IAAG,KAAI,IAAG,SAAQ,IAAG,oBAAmB,IAAG,qBAAoB,IAAG,eAAc,IAAG,6BAA4B,IAAG,gBAAe,IAAG,8BAA6B,IAAG,eAAc,IAAG,gBAAe,IAAG,eAAc,IAAG,gBAAe,IAAG,MAAK;AAAA,IACtzB,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACrhB,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,aAAG,MAAM,GAAG,EAAE,CAAC;AAAE,iBAAO,GAAG,EAAE;AAC9B;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAI,CAAC;AACX;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACb,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AAAA,QAAI,KAAK;AAC7B,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAE,CAAC;AACT;AAAA,QACA,KAAK;AACL,aAAG,EAAE,EAAE,OAAK;AAAqB,eAAK,IAAE,GAAG,EAAE;AAC7C;AAAA,QACA,KAAK;AAEH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,YAAY,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,EAAE,CAAC;AACvE,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,UAAU,SAAQ,GAAG,KAAG,CAAC,EAAC,CAAC;AAChD,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AACJ,eAAK,IAAG,EAAC,MAAK,iBAAgB,eAAe,OAAO,GAAG,KAAG,CAAC,CAAC,GAAG,mBAAkB,OAAO,GAAG,KAAG,CAAC,CAAC,GAAG,iBAAgB,MAAM,YAAW,GAAG,SAAS,WAAU;AAC3J;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAC,MAAK,iBAAgB,eAAe,OAAO,GAAG,KAAG,CAAC,CAAC,GAAG,mBAAkB,GAAG,iBAAgB,MAAM,YAAW,GAAG,SAAS,WAAU;AAC7I;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAC,MAAK,iBAAiB,iBAAgB,OAAO,YAAW,GAAG,SAAS,WAAU;AACzF;AAAA,QACA,KAAK;AACL,eAAK,IAAI,EAAC,MAAK,iBAAiB,iBAAgB,MAAM,YAAW,GAAG,SAAS,WAAU;AACvF;AAAA,QACA,KAAK;AACL,eAAK,IAAE,EAAC,MAAM,eAAe,YAAY,GAAG,SAAS,cAAc,OAAO,GAAG,KAAG,CAAC,EAAE,MAAK;AACxF;AAAA,QACA,KAAK;AACL,eAAK,IAAE,EAAC,MAAM,aAAa,YAAY,GAAG,SAAS,YAAY,OAAO,GAAG,KAAG,CAAC,EAAE,MAAK;AACpF;AAAA,QACA,KAAK;AACL,aAAG,gBAAgB,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,UAAU,CAAC;AACjE;AAAA,QACA,KAAK;AACL,aAAG,gBAAgB,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,UAAU,CAAC;AACjE;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AAEH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,aAAa,UAAS,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,WAAU,CAAC;AAC5G,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,WAAW,UAAS,GAAG,KAAG,CAAC,GAAG,YAAY,GAAG,SAAS,SAAQ,CAAC;AACpF,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAEH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,aAAa,OAAM,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,WAAW,CAAC;AAC1G,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,WAAW,OAAM,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,SAAS,CAAC;AACnG,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAEH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,YAAY,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAS,CAAC;AACzG,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,UAAU,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,QAAO,CAAC;AAClG,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAGH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,YAAY,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAS,CAAC;AAGzG,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,UAAU,YAAY,GAAG,SAAS,QAAO,CAAC;AAC/D,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAGH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,YAAY,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAS,CAAC;AAGzG,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,UAAU,YAAY,GAAG,SAAS,QAAO,CAAC;AAC/D,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAGH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,YAAY,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,eAAc,CAAC;AAG9G,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,UAAU,YAAY,GAAG,SAAS,QAAO,CAAC;AAC/D,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAGH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,iBAAiB,cAAa,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,eAAc,CAAC;AAGxH,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,eAAe,YAAY,GAAG,SAAS,aAAY,CAAC;AACzE,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AAEH,aAAG,KAAG,CAAC,EAAE,QAAQ,EAAC,MAAM,cAAc,WAAU,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,YAAW,CAAC;AAC/G,aAAG,KAAG,CAAC,EAAE,KAAK,EAAC,MAAM,YAAY,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAS,CAAC;AACtG,eAAK,IAAE,GAAG,KAAG,CAAC;AAChB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,KAAG,CAAC,EAAE,OAAO,CAAC,EAAC,MAAM,UAAU,YAAW,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,gBAAe,GAAG,GAAG,EAAE,CAAC,CAAC;AACnI;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,KAAG,CAAC,EAAE,OAAO,CAAC,EAAC,MAAM,OAAO,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,QAAO,GAAG,GAAG,EAAE,CAAC,CAAC;AACrH;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,KAAG,CAAC,EAAE,OAAO,CAAC,EAAC,MAAM,QAAQ,SAAQ,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,SAAQ,GAAG,GAAG,EAAE,CAAC,CAAC;AACvH;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,OAAK;AAAe,aAAG,KAAG,CAAC,EAAE,OAAK;AAAiB,aAAG,KAAG,CAAC,EAAE,cAAY,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAE,GAAG,KAAG,CAAC;AAC1H;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,OAAK;AAAe,aAAG,KAAG,CAAC,EAAE,OAAK;AAAiB,eAAK,IAAE,GAAG,KAAG,CAAC;AAC1E;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,OAAK;AAAS,aAAG,KAAG,CAAC,EAAE,OAAK;AAAiB,aAAG,KAAG,CAAC,EAAE,cAAY,GAAG,aAAa,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAE,GAAG,KAAG,CAAC;AACpH;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,OAAK;AAAS,aAAG,KAAG,CAAC,EAAE,OAAK;AAAkB,eAAK,IAAE,GAAG,KAAG,CAAC;AACrE;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,OAAK;AAAsB,eAAK,IAAE,GAAG,KAAG,CAAC;AAClD;AAAA,QACA,KAAK;AAEH,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,EAAC,MAAK,WAAW,WAAU,GAAG,KAAG,CAAC,GAAG,OAAM,GAAG,KAAG,CAAC,EAAE,OAAO,MAAK,GAAG,EAAE,EAAC,CAAC;AAC7F;AAAA,QACA,KAAK;AAGH,aAAG,KAAG,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC;AACnD,aAAG,KAAG,CAAC,EAAE,CAAC,IAAI,GAAG,KAAG,CAAC,EAAE,CAAC,EAAE;AAC1B,aAAG,KAAG,CAAC,EAAE,CAAC,IAAI,GAAG,KAAG,CAAC,EAAE,CAAC,EAAE;AAC1B,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,EAAC,MAAK,WAAW,WAAU,GAAG,UAAU,MAAM,OAAM,GAAG,KAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAK,GAAG,EAAE,EAAC,CAAC;AAC5G;AAAA,QACA,KAAK;AAEH,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,EAAC,MAAK,YAAY,OAAM,GAAG,KAAG,CAAC,EAAE,OAAO,MAAK,GAAG,EAAE,EAAC,CAAC;AAE1E;AAAA,QACA,KAAK;AAEH,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,EAAC,MAAK,YAAY,OAAM,GAAG,KAAG,CAAC,EAAE,OAAO,MAAK,GAAG,EAAE,EAAC,CAAC;AAE1E;AAAA,QACA,KAAK;AAEH,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,EAAC,MAAK,iBAAiB,OAAM,GAAG,KAAG,CAAC,EAAE,OAAO,MAAK,GAAG,EAAE,EAAC,CAAC;AAE/E;AAAA,QACA,KAAK;AAEH,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,EAAC,MAAK,cAAc,OAAM,GAAG,KAAG,CAAC,EAAE,OAAO,MAAK,GAAG,EAAE,EAAC,CAAC;AAE5E;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC3B;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,UAAU;AACvB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,UAAU;AACvB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI;AAAA,YAAC,GAAG,KAAG,CAAC;AAAA,YAAE,GAAG,KAAG,CAAC;AAAA,YAAE,EAAC,MAAM,cAAc,MAAK,GAAG,KAAG,CAAC,EAAE,OAAO,IAAG,GAAG,KAAG,CAAC,EAAE,OAAO,YAAW,GAAG,KAAG,CAAC,GAAG,KAAI,GAAG,EAAE,GAAG,UAAU,KAAI;AAAA,YAC1H,EAAC,MAAM,eAAe,YAAY,GAAG,SAAS,cAAc,OAAO,GAAG,KAAG,CAAC,EAAE,MAAK;AAAA,UAClF;AACd;AAAA,QACA,KAAK;AACJ,eAAK,IAAI;AAAA,YAAC,GAAG,KAAG,CAAC;AAAA,YAAE,GAAG,KAAG,CAAC;AAAA,YAAE,EAAC,MAAM,cAAc,MAAK,GAAG,KAAG,CAAC,EAAE,OAAO,IAAG,GAAG,KAAG,CAAC,EAAE,OAAO,YAAW,GAAG,KAAG,CAAC,GAAG,KAAI,GAAG,EAAE,EAAC;AAAA,YAC3G,EAAC,MAAM,aAAa,YAAY,GAAG,SAAS,YAAY,OAAO,GAAG,KAAG,CAAC,EAAE,MAAK;AAAA,UAC7E;AACd;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,GAAG,KAAG,CAAC,GAAE,GAAG,KAAG,CAAC,GAAE,EAAC,MAAM,cAAc,MAAK,GAAG,KAAG,CAAC,EAAE,OAAO,IAAG,GAAG,KAAG,CAAC,EAAE,OAAO,YAAW,GAAG,KAAG,CAAC,GAAG,KAAI,GAAG,EAAE,EAAC,CAAC;AAC1H;AAAA,QACA,KAAK;AACL,eAAK,IAAE,EAAE,MAAM,kBAAkB,OAAM,GAAG,EAAE,EAAC;AAC7C;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,SAAS;AACtB;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,aAAa,GAAG,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACnD;AAAA,MACA;AAAA,IACA,GA7Oe;AAAA,IA8Of,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,IAAG,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,IAAG,GAAE,EAAC,GAAE,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,IAAG,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,KAAI,EAAC,GAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,GAAE,EAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,IAAG,GAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,GAAE,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,IAAG,KAAI,GAAE,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,IAAG,KAAI,GAAE,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,IAAG,KAAI,GAAE,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,IAAG,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,GAAE,GAAE,CAAC,GAAE,GAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,KAAI,GAAE,KAAI,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,KAAI,EAAC,GAAE,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC;AAAA,IACz8J,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,EAAC;AAAA,IAC9M,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACnC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,IAAI;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,IAAI;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,IAAI;AAAG,mBAAO;AAClC;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,KAAK;AAAG,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,SAAS;AAAG,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACtE;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAClD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,IAAI;AAAG,mBAAO;AAClC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,IAAI;AAAG,mBAAO;AAClC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,gBAAI,SAAS,IAAI,OAAO,KAAK;AAAG,mBAAO;AAChD;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GA5Ie;AAAA,MA6If,OAAO,CAAC,eAAc,aAAY,qBAAoB,iBAAgB,uBAAsB,uBAAsB,0BAAyB,eAAc,uBAAsB,iBAAgB,kBAAiB,mBAAkB,uFAAsF,cAAa,cAAa,gBAAe,gBAAe,eAAc,eAAc,gBAAe,eAAc,oBAAmB,eAAc,oBAAmB,kBAAiB,iBAAgB,sCAAqC,eAAc,mBAAkB,oBAAmB,iBAAgB,gBAAe,sBAAqB,mBAAkB,gBAAe,gBAAe,oBAAmB,sBAAqB,yBAAwB,0BAAyB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,2BAA0B,sBAAqB,eAAc,WAAU,WAAU,2EAA0E,aAAY,eAAc,cAAa,gBAAe,YAAW,aAAY,cAAa,eAAc,eAAc,gBAAe,mCAAkC,YAAW,WAAU,WAAU,SAAS;AAAA,MAClxC,YAAY,EAAC,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,MAAK,EAAC,SAAQ,CAAC,GAAE,GAAE,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,GAAE,GAAE,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,GAAE,GAAE,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IACrf;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,0BAAQ;;;ACv5BhB,IAAM,WAAW;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,sBAAsB;AACxB;AAEA,IAAM,YAAY;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AACR;AAEA,IAAM,YAAY;AAAA,EAChB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;AAEO,IAAM,aAAN,MAAsC;AAAA,EAgB3C,cAAc;AAfd,SAAiB,QAAQ,IAAI,gBAA+B,OAAO;AAAA,MACjE,WAAW;AAAA,MACX,QAAQ,oBAAI,IAAI;AAAA,MAChB,eAAe,oBAAI,IAAI;AAAA,MACvB,iBAAiB,oBAAI,IAAI;AAAA,MACzB,OAAO,CAAC;AAAA,MACR,UAAU,CAAC;AAAA,MACX,OAAO,CAAC;AAAA,MACR,wBAAwB;AAAA,MACxB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,eAAe;AAAA,IACjB,EAAE;AA6iBF,SAAO,cAAc;AACrB,SAAO,oBAAoB;AAC3B,SAAO,kBAAkB;AACzB,SAAO,cAAc;AACrB,SAAO,oBAAoB;AAC3B,SAAO,kBAAkB;AA9iBvB,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAE/C,SAAK,MAAM;AAEX,SAAK,QAAQE,WAAU,EAAE,IAAI;AAC7B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACnB;AAAA,EAzGF,OA6E6C;AAAA;AAAA;AAAA,EA8BpC,OAAO,MAAsD;AAClE,SAAK,MAAM,QAAQ,MAAM,KAAK;AAAA,MAC5B,MAAM,KAAK;AAAA,MACX,MAAM,KAAK,QAAQ,KAAK,SAAS;AAAA,MACjC,MAAM,KAAK;AAAA,MACX,WAAW,CAAC;AAAA,IACd,CAAC;AACD,SAAK,MAAM,QAAQ,aAAa,KAAK,MAAM,QAAQ,MAAM,MAAM,EAAE,EAAE,CAAC;AAAA,EACtE;AAAA,EAEO,SACL,IACA,MACA,aACA,MACA;AACA,QAAI,cAAc,KAAK,MAAM,QAAQ;AACrC,UAAM,MAAM,KAAK,MAAM,QAAQ,OAAO,IAAI,EAAE;AAC5C,QAAI,KAAK;AAEP,UAAI,KAAK,MAAM,QAAQ,cAAc,IAAI,OAAO,KAAK,MAAM,QAAQ,eAAe,IAAI,KAAK;AACzF,cAAM,IAAI;AAAA,UACR,yDAAyD,IAAI,IAAI,iBAAiB,IAAI,IAAI,IAAI,aAAa,KAAK,MAAM,QAAQ,WAAW,IAAI;AAAA,QAC/I;AAAA,MACF;AAGA,oBAAc,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,QAAQ;AACrD,UAAI,MAAM;AAGV,UAAI,OAAO,SAAS,IAAI,QAAQ,eAAe,MAAM;AACnD;AAAA,MACF;AAAA,IACF;AAGA,QAAI,aAAa,QAAQ,MAAM;AAC7B,oBAAc,EAAE,MAAM,MAAM,KAAK;AAAA,IACnC;AACA,QAAI,QAAQ,QAAQ,YAAY,QAAQ,MAAM;AAC5C,oBAAc,EAAE,MAAM,MAAM,KAAK;AAAA,IACnC;AAEA,SAAK,MAAM,QAAQ,OAAO,IAAI,IAAI;AAAA,MAChC,KAAK;AAAA,MACL;AAAA,MACA,aAAa,YAAY;AAAA,MACzB,MAAM,YAAY,QAAQ,KAAK,SAAS;AAAA,MACxC,WAAW,KAAK,MAAM,QAAQ;AAAA,MAC9B,OAAO,CAAC;AAAA,MACR,YAAY,CAAC;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM,QAAQ;AAAA,IAChB,CAAC;AACD,QAAI,KAAK,MAAM,QAAQ,WAAW;AAChC,YAAM,qBAAqB,KAAK,MAAM,QAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,SAAS;AACrF,UAAI,oBAAoB;AACtB,2BAAmB,YAAY;AAAA,MACjC;AAAA,IACF;AAEA,QAAI,KAAK,MAAM,QAAQ,YAAY;AACjC,WAAK,MAAM,QAAQ,WAAW,UAAU,KAAK,EAAE;AAAA,IACjD;AACA,SAAK,MAAM,QAAQ,YAAY;AAAA,EACjC;AAAA,EAEQ,gBAAgB,MAAc;AACpC,QAAI;AACJ,QAAI,QAAQ;AACZ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,SAAK,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,SAAS,QAAQ,KAAK;AACvD,UACE,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,KAAK,SAAS,gBACtD,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,MACxC;AACA;AAAA,MACF;AACA,UACE,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,KAAK,SAAS,cACtD,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,MACxC;AACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEO,WACL,QACA,MACA,SACA,QACA;AACA,SAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,MAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ,QAAQ,KAAK,SAAS;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,UACL,QACA,MACA,SACA,aACA,WAAW,OACX;AACA,QAAI,gBAAgB,KAAK,SAAS,YAAY;AAC5C,YAAM,MAAM,KAAK,gBAAgB,UAAU,EAAE;AAC7C,UAAI,MAAM,GAAG;AAEX,cAAM,QAAQ,IAAI,MAAM,mDAAmD,SAAS,GAAG;AAGvF,cAAM,OAAO;AAAA,UACX,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK,EAAE,YAAY,GAAG,WAAW,GAAG,cAAc,GAAG,aAAa,EAAE;AAAA,UACpE,UAAU,CAAC,sBAAsB;AAAA,QACnC;AACA,cAAM;AAAA,MACR;AAAA,IACF;AACA,SAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,MAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,SAAS,SAAS,QAAQ;AAAA,MAC1B,MAAM,SAAS,QAAQ,KAAK,SAAS;AAAA,MACrC,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEO,mBAAmB;AACxB,WAAO,KAAK,MAAM,QAAQ,MAAM,SAAS;AAAA,EAC3C;AAAA,EAEO,4BAA4B;AACjC,WAAO,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,MAAM,EAAE,IAAI;AAAA,EACpD;AAAA,EAEO,cAAc;AACnB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EAEO,WAAW;AAChB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACO,YAAY;AACjB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACO,mBAAmB;AACxB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACO,qBAAqB;AAC1B,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACO,SAAS,IAAY;AAE1B,WAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,EAAE;AAAA,EACzC;AAAA,EACO,eAAe;AACpB,WAAO,CAAC,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAC;AAAA,EAC7C;AAAA,EACO,wBAAwB;AAC7B,SAAK,MAAM,QAAQ,yBAAyB;AAAA,EAC9C;AAAA,EACO,yBAAyB;AAC9B,SAAK,MAAM,QAAQ,yBAAyB;AAAA,EAC9C;AAAA,EACO,sBAAsB;AAC3B,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EAEO,QAAQ,aAAuB;AACpC,SAAK,MAAM,QAAQ,cAAc;AAAA,EACnC;AAAA,EAEQ,YAAY,MAAyD;AAC3E,QAAI,SAAS,QAAW;AACtB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,KAAK,KAAK;AACjB,UAAM,OACJ,WAAW,KAAK,IAAI,MAAM,OAAO,OAAO,aAAa,KAAK,IAAI,MAAM,OAAO,QAAQ;AACrF,UAAM,eAAe,SAAS,SAAY,OAAO,KAAK,QAAQ,mBAAmB,EAAE,GAAG,KAAK;AAC3F,WAAO,EAAE,aAAa,KAAK;AAAA,EAC7B;AAAA,EAEO,WAAW;AAGhB,QAAI,KAAK,MAAM,QAAQ,gBAAgB,QAAW;AAChD,aAAO,KAAK,MAAM,QAAQ;AAAA,IAC5B;AACA,WAAOA,WAAU,EAAE,UAAU,QAAQ;AAAA,EACvC;AAAA,EAEO,QAAQ;AACb,SAAK,MAAM,MAAM;AACjB,UAAY;AAAA,EACd;AAAA,EAEO,aAAa,KAAa;AAC/B,UAAM,aAAa,IAAI,KAAK;AAC5B,UAAM,EAAE,MAAM,YAAY,IAAI,KAAK,YAAY,UAAU;AACzD,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN;AAAA,IACF;AACA,QAAI,MAAM,iBAAiB,KAAK,UAAU,OAAO,CAAC,EAAE;AACpD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKO,aAAa,KAAa;AAC/B,UAAM,QAAQ,uCAAuC,KAAK,GAAG;AAC7D,QAAI,QAAQ,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI;AAC3C,QAAI,QAAQ,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI;AAG3C,QAAI,QAAQ,KAAK;AACf,UAAI,CAAC,OAAO,IAAI,SAAS,SAAS,KAAK,GAAG;AACxC,gBAAQ;AACR,gBAAQ,IAAI,KAAK;AAAA,MACnB;AAAA,IACF,OAAO;AACL,YAAM,QAAQ,IAAI,OAAO,EAAE;AAC3B,YAAM,QAAQ;AACd,UAAI,MAAM,UAAU,OAAO;AACzB,gBAAQ;AACR,gBAAQ,IAAI,KAAK;AAAA,MACnB;AAAA,IACF;AACA,UAAM,EAAE,MAAM,YAAY,IAAI,KAAK,YAAY,KAAK;AACpD,WAAO;AAAA,MACL,MAAM,cAAc,aAAa,aAAaA,WAAU,CAAC,IAAI;AAAA,MAC7D;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAMO,QACL,OACA,WACA,SACA;AACA,UAAM,OAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ,QAAQ,KAAK,SAAS;AAAA,IACtC;AAIA,UAAM,SAAS,CAAC,EAAE,OAAO,OAAO,KAAK;AACrC,SAAK,MAAM,QAAQ,MAAM,KAAK,IAAI;AAClC,SAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,MAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,MAAM,OAAO,CAAC;AAAA,MACd,IAAI,OAAO,CAAC;AAAA,MACZ,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ,QAAQ,KAAK,SAAS;AAAA,MACpC,MAAM,KAAK,SAAS;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,SAAS,SAAiB,MAAwB;AAEvD,UAAM,QAAQ,KAAK,SAAS,OAAO;AAEnC,QAAI;AACF,UAAI,gBAAgB,aAAa,KAAK,MAAMA,WAAU,CAAC;AACvD,sBAAgB,cAAc,QAAQ,aAAa,GAAG;AACtD,sBAAgB,cAAc,QAAQ,UAAU,GAAG;AACnD,YAAM,QAAQ,KAAK,MAAM,aAAa;AAEtC,WAAK,YAAY,OAAO,KAAK;AAAA,IAC/B,SAAS,GAAG;AACV,UAAI,MAAM,uCAAuC,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EAEO,SAAS,SAAiB,MAAwB;AAEvD,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,QAAI;AACF,YAAM,QAAgC,CAAC;AACvC,UAAI,gBAAgB,aAAa,KAAK,MAAMA,WAAU,CAAC;AACvD,YAAM,MAAM,cAAc,QAAQ,GAAG;AACrC,sBAAgB,cAAc,QAAQ,aAAa,GAAG;AACtD,sBAAgB,cAAc,QAAQ,UAAU,GAAG;AACnD,YAAM,QAAQ,cAAc,MAAM,GAAG,MAAM,CAAC,EAAE,KAAK;AACnD,YAAM,OAAO,cAAc,MAAM,MAAM,CAAC,EAAE,KAAK;AAE/C,YAAM,KAAK,IAAI;AAEf,WAAK,YAAY,OAAO,KAAK;AAAA,IAC/B,SAAS,GAAG;AACV,UAAI,MAAM,uCAAuC,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EAEQ,YAAY,OAAc,OAA+B;AAC/D,QAAI,MAAM,SAAS,MAAM;AACvB,YAAM,QAAQ;AAAA,IAChB,OAAO;AACL,iBAAW,OAAO,OAAO;AACvB,cAAM,MAAM,GAAG,IAAI,MAAM,GAAG;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EAEO,cAAc,SAAiB,MAAwB;AAE5D,UAAM,QAAQ,KAAK,SAAS,OAAO;AAEnC,QAAI;AACF,YAAM,gBAAgB,aAAa,KAAK,MAAMA,WAAU,CAAC;AACzD,YAAM,aAAsC,KAAK,MAAM,aAAa;AAEpE,WAAK,iBAAiB,OAAO,UAAU;AAAA,IACzC,SAAS,GAAG;AACV,UAAI,MAAM,6CAA6C,CAAC;AAAA,IAC1D;AAAA,EACF;AAAA,EAEQ,iBAAiB,OAAc,YAAqC;AAC1E,QAAI,MAAM,cAAc,MAAM;AAC5B,YAAM,aAAa;AAAA,IACrB,OAAO;AACL,iBAAW,OAAO,YAAY;AAC5B,cAAM,WAAW,GAAG,IAAI,WAAW,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,SAAS;AACf,SAAK,MAAM,QAAQ,aAAa;AAAA,EAClC;AAAA,EAEO,WAAW,SAAiB,MAAwB;AAEzD,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,UAAM,OAAO,SAAS,eAAe,KAAK,IAAI;AAG9C,QAAI;AACF,YAAMC,QAAO,KAAK;AAClB,YAAM,UAAU,KAAK,MAAMA,KAAI;AAE/B,UAAI,QAAQ,YAAY;AACtB,aAAK,iBAAiB,OAAO,QAAQ,UAAU;AAAA,MACjD;AAEA,UAAI,QAAQ,OAAO;AACjB,aAAK,YAAY,OAAO,QAAQ,KAAK;AAAA,MACvC;AAAA,IACF,SAAS,GAAG;AACV,UAAI,MAAM,0CAA0C,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EAEO,iBAAiB,OAAc,KAAa;AACjD,QAAI,OAAO,eAAe,QAAW;AACnC,aAAO,MAAM,WAAW,GAAG;AAAA,IAC7B;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGO,MAAM,OAAoD;AAC/D,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAM,QAAQ,CAAC,SAAS;AACtB,aAAK,MAAM,IAAI;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,eAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,YAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,YAChD,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,SAAS;AAAA,cACP,OAAO,MAAM;AAAA,cACb,MAAM,MAAM;AAAA,cACZ,SAAS,MAAM;AAAA,YACjB;AAAA,YACA,MAAM;AAAA,YACN,MAAM,MAAM;AAAA,UACd,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM,aAAa,MAAM,IAAI;AACrE;AAAA,QACF,KAAK;AACH,cAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG;AAC9C,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,eAAK,MAAM,QAAQ,cAAc,MAAM;AACvC,eAAK,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM,aAAa,MAAM,IAAI;AACrE,eAAK,MAAM,QAAQ,cAAc,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,SAAS,MAAM;AACpF;AAAA,QACF,KAAK;AACH,eAAK,MAAM,QAAQ,gBAAgB,MAAM;AACzC,eAAK,MAAM,QAAQ,gBAAgB,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,SAAS,MAAM;AACtF;AAAA,QACF,KAAK;AACH,eAAK,UAAU,MAAM,OAAO,QAAW,QAAW,MAAM,UAAU;AAClE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,MAAM,OAAO,QAAW,QAAW,MAAM,UAAU;AAClE;AAAA,QACF,KAAK;AACH,eAAK,QAAQ,MAAM,OAAO,MAAM,WAAW,MAAM,IAAI;AACrD;AAAA,QACF,KAAK;AACH,eAAK,SAAS,MAAM,OAAO,MAAM,IAAI;AACrC;AAAA,QACF,KAAK;AACH,eAAK,SAAS,MAAM,OAAO,MAAM,IAAI;AACrC;AAAA,QACF,KAAK;AACH,eAAK,cAAc,MAAM,OAAO,MAAM,IAAI;AAC1C;AAAA,QACF,KAAK;AACH,eAAK,WAAW,MAAM,OAAO,MAAM,IAAI;AACvC;AAAA,QACF,KAAK;AACH,cAAI,KAAK,MAAM,QAAQ,aAAa;AAClC,gBAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,aAAa;AAC/C,oBAAM,IAAI;AAAA,gBACR,6BACE,KAAK,MAAM,QAAQ,YAAY,OAC/B;AAAA,cACJ;AAAA,YACF,OAAO;AACL,mBAAK,MAAM,QAAQ,cAAc;AAAA,YACnC;AAAA,UACF,WAAW,KAAK,MAAM,QAAQ,eAAe;AAC3C,gBACE,MAAM,OAAO,KAAK,MAAM,QAAQ,iBAChC,MAAM,SAAS,KAAK,MAAM,QAAQ,eAClC;AACA,oBAAM,IAAI;AAAA,gBACR,+BACE,KAAK,MAAM,QAAQ,cAAc,OACjC;AAAA,cACJ;AAAA,YACF,OAAO;AACL,mBAAK,MAAM,QAAQ,gBAAgB;AAAA,YACrC;AAAA,UACF;AACA,eAAK,UAAU,MAAM,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,YAAY,MAAM,QAAQ;AAChF;AAAA,QACF,KAAK;AACH,eAAK,OAAO,MAAM,OAAO;AACzB;AAAA,QACF,KAAK;AACH,eAAK,OAAO;AACZ;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,UAAU,MAAM,UAAU;AACrE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,OAAO,MAAM,UAAU;AAClE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,SAAS,MAAM,UAAU;AACpE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,SAAS,MAAM,UAAU;AACpE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,SAAS,MAAM,UAAU;AACpE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,IAAI;AACtB;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,SAAS,MAAM,UAAU;AACpE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,SAAS,MAAM,UAAU;AACpE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,cAAc,MAAM,UAAU;AACzE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,YAAY,MAAM,UAAU;AACvE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,MAAM,WAAW,MAAM,UAAU;AACtE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAW,QAAW,QAAW,MAAM,UAAU;AAChE;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EAQO,YAAY;AACjB,WAAOD,WAAU,EAAE;AAAA,EACrB;AACF;;;ACjpBA,IAAM,YAAY,wBAAC,YACjB;AAAA,cACY,QAAQ,WAAW;AAAA,YACrB,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,YAIhB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,cAKpB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMtB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMnB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,WAAW;AAAA,cACjB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA,YAI3B,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAInB,QAAQ,WAAW;AAAA,cACjB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,cAKrB,QAAQ,mBAAmB;AAAA,YAC7B,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA,YAIxB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,YAKtB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOnB,QAAQ,mBAAmB;AAAA,YAC7B,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,cAKzB,QAAQ,eAAe;AAAA,YACzB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA,YAIpB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,YAKrB,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,YAI/B,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,YAI/B,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS/B,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,cAKd,QAAQ,WAAW;AAAA,YACrB,QAAQ,QAAQ;AAAA;AAAA;AAAA,cAGd,QAAQ,WAAW;AAAA,YACrB,QAAQ,QAAQ;AAAA;AAAA;AAAA,GAlHV;AAuHlB,IAAO,iBAAQ;;;ACtHf,SAAS,cAAc;;;ACEvB,SAAS,mBAAmB;AAGrB,IAAM,mBAAmB,KAAK;AACrC,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AACxB,IAAM,yBAAyB;AAExB,IAAME,YAAW,gCAAU,MAAM,UAAU;AAChD,SAAqB,SAAS,MAAM,QAAQ;AAC9C,GAFwB;AAIjB,IAAM,YAAY,gCAAU,MAAM,OAAO,cAAc,WAAW,YAAY;AACnF,MAAI,MAAM,UAAU,UAAa,MAAM,UAAU,QAAQ,OAAO,KAAK,MAAM,KAAK,EAAE,WAAW,GAAG;AAC9F,WAAO,EAAE,QAAQ,GAAG,OAAO,EAAE;AAAA,EAC/B;AAEA,QAAM,QAAQ,MAAM;AACpB,QAAMC,YAAW,MAAM;AACvB,QAAM,WAAW,MAAM;AAEvB,MAAI,eAAe;AACnB,MAAI,YAAY;AACd,mBAAe;AAAA,EACjB;AAEA,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB,IAAE,KAAK,MAAM,UAAUA,YAAW,QAAQ;AAC1C,IAAE,KAAK,SAAS,gBAAgB;AAChC,IAAE,KAAK,WAAW,YAAY;AAC9B,MAAI,aAAa;AACjB,MAAI,SAAS,UAAU,QAAW;AAChC,iBAAa,MAAM,SAAS;AAAA,EAC9B;AAEA,MAAI,YAAY,SAAS,QAAQ,eAAe,SAAS,QAAQ;AAEjE,QAAM,WAAW,EAAE,OAAO,MAAM;AAChC,WAAS,KAAK,SAAS,wBAAwB,UAAU;AACzD,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,MAAM;AAClC,WAAS,KAAK,QAAQ,SAAS,IAAI;AACnC,WAAS,KAAK,UAAU,SAAS,MAAM;AACvC,WAAS,KAAK,SAAS,SAAS;AAChC,WAAS,KAAK,UAAU,SAAS,MAAM;AACvC,WAAS,KAAK,MAAM,SAAS,EAAE;AAC/B,WAAS,KAAK,MAAM,SAAS,EAAE;AAC/B,MAAI,SAAS,MAAM;AACjB,QAAI,QAAQ;AACZ,aAAS,OAAO,OAAO;AACrB,UAAI,WAAW,EAAE,OAAO,GAAG;AAC3B,UAAI,gBAAgB,YAAY,MAAM,GAAG,CAAC;AAC1C,eAAS,KAAK,cAAc,aAAa;AACzC,eAAS,KAAK,UAAU,QAAQ;AAEhC,qCAA+B,SAAS;AAAA,QACtC;AAAA,QACA;AAAA,QACA,SAAS,IAAI;AAAA,QACb,SAAS,SAAS;AAAA,QAClB;AAAA,QACA;AAAA,QACA,EAAE,OAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AAEA,eAAS;AAAA,IACX;AAAA,EACF;AAEA,WAAS,KAAK,UAAU,KAAK;AAE7B,SAAO,EAAE,QAAQ,SAAS,SAAS,OAAO,OAAO,UAAU;AAC7D,GA7DyB;AA+DzB,IAAM,kBAAkB,gCAAU,OAAO;AACvC,SACE,uCACA,QACA;AAEJ,GANwB;AAQjB,IAAM,YAAY,sCAAgB,MAAM,UAAU,WAAW,MAAM;AACxE,MAAI,WAAW,KAAK,OAAO,eAAe;AAC1C,QAAM,QAAQ,MAAM,YAAY,SAAS,MAAgB,UAAU,CAAC;AAEpE,QAAM,UAAU,SACb,OAAO,WAAW,EAClB,KAAK,SAAS,qBAAqB,EACnC,KAAK,SAAS,8BAA8B,EAC5C,KAAK,KAAK;AACb,QAAM,MAAM,QAAQ,KAAK,EAAE,sBAAsB;AAEjD,WAAS,KAAK,UAAU,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC;AAEnF,MAAI,SAAS,UAAU,YAAY;AACjC,UAAM,WAAW,KAAK,KAAK,EAAE;AAE7B,aAAS,aAAa,UAAU,IAAI,SAAS,IAAI,SAAS,UAAU;AACpE,UAAM,UAAU,SAAS,QAAQ;AAEjC,aACG,KAAK,KAAK,KAAK,MAAM,QAAQ,IAAI,QAAQ,QAAQ,IAAI,IAAI,QAAQ,CAAC,CAAC,EACnE,KAAK,KAAK,KAAK,MAAM,QAAQ,IAAI,QAAQ,SAAS,IAAI,IAAI,SAAS,CAAC,CAAC;AAAA,EAC1E,WAAW,UAAU;AACnB,QAAI,EAAE,QAAQ,OAAO,OAAO,IAAI;AAChC,QAAI,SAAS,OAAO;AAClB,YAAM,OAAO;AACb,eAAS;AACT,cAAQ;AAAA,IACV;AAGA,aAAS,KAAK,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AACpF,QAAI,SAAS,UAAU,YAAY;AACjC,eAAS,KAAK,KAAK,KAAK,MAAM,MAAM,CAAC;AAAA,IACvC,OAAO;AACL,eAAS,KAAK,KAAK,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,SAAO,CAAC,QAAQ;AAClB,GAxCyB;AA0ClB,IAAM,WAAW,gCAAU,MAAM,UAAU;AAChD,MAAI,iBAAiB;AACrB,MAAI,aAAa;AACjB,QAAM,QAAQ,SAAS,KAAK,MAAM,eAAO,cAAc;AAEvD,QAAM,CAAC,eAAe,eAAe,IAAI,cAAc,SAAS,QAAQ;AAExE,MAAI,YAAY,CAAC;AACjB,MAAI,KAAK;AACT,MAAI,QAAQ,6BAAM,SAAS,GAAf;AACZ,MACE,SAAS,WAAW,UACpB,SAAS,eAAe,UACxB,SAAS,aAAa,GACtB;AACA,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AAAA,MACL,KAAK;AACH,gBAAQ,6BAAM,KAAK,MAAM,SAAS,IAAI,SAAS,UAAU,GAAjD;AACR;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,gBAAQ,6BACN,KAAK,MAAM,SAAS,KAAK,iBAAiB,aAAa,SAAS,cAAc,CAAC,GADzE;AAER;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,gBAAQ,6BACN,KAAK;AAAA,UACH,SAAS,KACN,iBAAiB,aAAa,IAAI,SAAS,cAC5C,SAAS;AAAA,QACb,GALM;AAMR;AAAA,IACJ;AAAA,EACF;AAEA,MACE,SAAS,WAAW,UACpB,SAAS,eAAe,UACxB,SAAS,UAAU,QACnB;AACA,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,UAAU;AACxD,iBAAS,SAAS;AAClB,iBAAS,mBAAmB;AAC5B,iBAAS,oBAAoB;AAC7B;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,QAAQ,CAAC;AACvD,iBAAS,SAAS;AAClB,iBAAS,mBAAmB;AAC5B,iBAAS,oBAAoB;AAC7B;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,QAAQ,SAAS,UAAU;AACzE,iBAAS,SAAS;AAClB,iBAAS,mBAAmB;AAC5B,iBAAS,oBAAoB;AAC7B;AAAA,IACJ;AAAA,EACF;AAEA,WAAS,CAAC,GAAG,IAAI,KAAK,MAAM,QAAQ,GAAG;AACrC,QACE,SAAS,eAAe,UACxB,SAAS,eAAe,KACxB,kBAAkB,QAClB;AACA,WAAK,IAAI;AAAA,IACX;AAEA,UAAM,WAAW,KAAK,OAAO,MAAM;AACnC,aAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,aAAS,KAAK,KAAK,MAAM,CAAC;AAC1B,QAAI,SAAS,WAAW,QAAW;AACjC,eACG,KAAK,eAAe,SAAS,MAAM,EACnC,KAAK,qBAAqB,SAAS,gBAAgB,EACnD,KAAK,sBAAsB,SAAS,iBAAiB;AAAA,IAC1D;AACA,QAAI,SAAS,eAAe,QAAW;AACrC,eAAS,MAAM,eAAe,SAAS,UAAU;AAAA,IACnD;AACA,QAAI,oBAAoB,QAAW;AACjC,eAAS,MAAM,aAAa,eAAe;AAAA,IAC7C;AACA,QAAI,SAAS,eAAe,QAAW;AACrC,eAAS,MAAM,eAAe,SAAS,UAAU;AAAA,IACnD;AACA,QAAI,SAAS,SAAS,QAAW;AAC/B,eAAS,KAAK,QAAQ,SAAS,IAAI;AAAA,IACrC;AACA,QAAI,SAAS,UAAU,QAAW;AAChC,eAAS,KAAK,SAAS,SAAS,KAAK;AAAA,IACvC;AACA,QAAI,SAAS,OAAO,QAAW;AAC7B,eAAS,KAAK,MAAM,SAAS,EAAE;AAAA,IACjC,WAAW,OAAO,GAAG;AACnB,eAAS,KAAK,MAAM,EAAE;AAAA,IACxB;AAEA,UAAM,OAAO,QAAQ;AACrB,QAAI,SAAS,OAAO;AAClB,YAAM,OAAO,SAAS,OAAO,OAAO;AACpC,WAAK,KAAK,KAAK,SAAS,CAAC;AACzB,UAAI,SAAS,SAAS,QAAW;AAC/B,aAAK,KAAK,QAAQ,SAAS,IAAI;AAAA,MACjC;AACA,WAAK,KAAK,IAAI;AAAA,IAChB,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,QACE,SAAS,WAAW,UACpB,SAAS,eAAe,UACxB,SAAS,aAAa,GACtB;AACA,qBAAe,SAAS,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE;AAC7D,uBAAiB;AAAA,IACnB;AAEA,cAAU,KAAK,QAAQ;AAAA,EACzB;AAEA,SAAO;AACT,GAlIwB;AAoIjB,IAAM,YAAY,gCAAU,MAAM,WAAW;AASlD,WAAS,UAAU,GAAG,GAAG,OAAO,QAAQ,KAAK;AAC3C,WACE,IACA,MACA,IACA,OACC,IAAI,SACL,MACA,IACA,OACC,IAAI,SACL,OACC,IAAI,SAAS,OACd,OACC,IAAI,QAAQ,MAAM,OACnB,OACC,IAAI,UACL,MACA,IACA,OACC,IAAI;AAAA,EAET;AAtBS;AAuBT,QAAM,UAAU,KAAK,OAAO,SAAS;AACrC,UAAQ,KAAK,UAAU,UAAU,UAAU,GAAG,UAAU,GAAG,UAAU,OAAO,UAAU,QAAQ,CAAC,CAAC;AAChG,UAAQ,KAAK,SAAS,UAAU;AAEhC,YAAU,IAAI,UAAU,IAAI,UAAU,SAAS;AAE/C,WAAS,MAAM,SAAS;AACxB,SAAO;AACT,GAxCyB;AA0CzB,IAAI,WAAW;AAER,IAAM,qBAAqB,wBAACC,UAAS,QAAQ,WAAWC,UAAS;AACtE,MAAI,CAACD,SAAQ,QAAQ;AACnB;AAAA,EACF;AACA,YAAU,QAAQ,CAAC,aAAa;AAC9B,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAM,WAAWA,SAAQ,OAAO,WAAW,MAAM,QAAQ;AACzD,QAAI,CAACC,MAAK,gBAAgB,MAAM,OAAO;AACrC,eAAS,KAAK,MAAM,MAAM,QAAQ,MAAM,SAAS,CAAC;AAAA,IACpD,WAAWA,MAAK,cAAc;AAC5B,eAAS,KAAK,MAAM,MAAM,KAAK;AAAA,IACjC;AAAA,EACF,CAAC;AACH,GAbkC;AAuBlC,IAAM,2BAA2B,gCAAU,MAAM,OAAOA,OAAM,UAAU;AACtE,QAAM,SAAS,WAAW,MAAM,QAAQ,MAAM;AAC9C,QAAM,SAAS,MAAM,IAAI,MAAM,QAAQ;AACvC,QAAM,UAAU,SAAS,MAAM;AAE/B,QAAM,mBAAmB,KAAK,OAAO,GAAG,EAAE,MAAM;AAChD,MAAI,IAAI;AAER,MAAI,CAAC,UAAU;AACb;AACA,QAAI,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC,EAAE,UAAU,CAACA,MAAK,YAAY;AAC7D,QAAE,KAAK,WAAW,gBAAgB,QAAQ,QAAQ,QAAQ,CAAC,EAAE,KAAK,UAAU,SAAS;AAAA,IACvF;AACA,MAAE,OAAO,MAAM,EACZ,KAAK,MAAM,UAAU,QAAQ,EAC7B,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,OAAO,EAClB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,GAAI,EACf,KAAK,SAAS,gBAAgB,EAC9B,KAAK,gBAAgB,OAAO,EAC5B,KAAK,UAAU,MAAM,EACrB,KAAK,QAAQ,MAAM,IAAI;AAE1B,QAAI,iBAAiB,OAAO,GAAG;AAC/B,UAAM,WAAW;AAEjB,QAAI,MAAM,SAAS,MAAM;AACvB,QAAE,KAAK,MAAM,UAAU,QAAQ;AAAA,IACjC;AAAA,EACF;AAEA,QAAM,OAAqB,YAAY;AACvC,MAAI,WAAW;AACf,MAAI,MAAM,YAAY,OAAO;AAC3B,eAAW,MAAM,WAAW;AAAA,EAC9B,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AACA,MAAI,UAAU;AACZ,gBAAY,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,gBAAY,IAAI,eAAe;AAAA,EACjC;AACA,OAAK,IAAI,MAAM;AACf,OAAK,IAAI;AACT,OAAK,QAAQ,MAAM;AACnB,OAAK,SAAS,MAAM;AACpB,OAAK,QAAQ;AACb,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,OAAO,MAAM;AAClB,QAAM,WAAWH,UAAS,GAAG,IAAI;AACjC,QAAM,WAAW;AAEjB,MAAI,MAAM,YAAY,MAAM;AAC1B,UAAM,UAAU,MAAM,WAAW,KAAK,KAAK;AAC3C,QAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC7B,MAAc,kBAAkB,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,QAAQ,OAAO,CAAC,CAAC;AAAA,IAC7F,OAAO;AACL,MAAc,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,OAAO;AAAA,IAC3E;AAAA,EACF;AAEA,yBAAuBG,OAAM,SAAS,MAAM,WAAW,CAAC;AAAA,IACtD,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,SAAS,eAAe,GAAG;AAAA,IACpCA;AAAA,EACF;AAEA,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,MAAM;AACjB,UAAMC,UAAS,SAAS,KAAK,EAAE,QAAQ;AACvC,UAAM,SAASA,QAAO;AACtB,aAASA,QAAO;AAAA,EAClB;AAEA,SAAO;AACT,GAnFiC;AAqFjC,IAAM,qBAAqB,gCAAU,MAAM,OAAOD,OAAM,UAAU;AAChE,QAAM,SAAS,WAAW,MAAM,QAAQ,MAAM;AAC9C,QAAM,SAAS,MAAM,IAAI,MAAM,QAAQ;AACvC,QAAM,UAAU,SAAS;AAEzB,QAAM,OAAO,KAAK,OAAO,GAAG,EAAE,MAAM;AAEpC,MAAI,CAAC,UAAU;AACb;AACA,SACG,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,QAAQ,EAC7B,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,OAAO,EAClB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,GAAI,EACf,KAAK,SAAS,gBAAgB,EAC9B,KAAK,gBAAgB,OAAO,EAC5B,KAAK,UAAU,MAAM,EACrB,KAAK,QAAQ,MAAM,IAAI;AAE1B,UAAM,WAAW;AAAA,EACnB;AACA,QAAM,UAAU,KAAK,OAAO,GAAG;AAC/B,MAAI,WAAW;AACf,MAAI,UAAU;AACZ,gBAAY,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,gBAAY,IAAI,eAAe;AAAA,EACjC;AACA,UAAQ,KAAK,SAAS,QAAQ;AAC9B,UAAQ,KAAK,QAAQ,MAAM,IAAI;AAE/B,QAAM,OAAqB,YAAY;AACvC,OAAK,IAAI,MAAM;AACf,OAAK,IAAI;AACT,OAAK,OAAO;AACZ,OAAK,QAAQ,MAAM;AACnB,OAAK,SAAS,MAAM;AACpB,OAAK,QAAQ;AACb,OAAK,KAAK;AACV,OAAK,KAAK;AAEV,UACG,OAAO,MAAM,EACb,KAAK,MAAM,oBAAoB,QAAQ,EACvC,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,SAAS,EAAE;AAEzB,UACG,OAAO,MAAM,EACb,KAAK,MAAM,mBAAmB,QAAQ,EACtC,KAAK,MAAM,SAAS,mBAAmB,CAAC,EACxC,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,MAAM,SAAS,mBAAmB,CAAC,EACxC,KAAK,MAAM,SAAS,EAAE;AACzB,UACG,OAAO,MAAM,EACb,KAAK,MAAM,SAAS,mBAAmB,CAAC,EACxC,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,SAAS,EAAE;AACzB,UACG,OAAO,MAAM,EACb,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,MAAM,SAAS,mBAAmB,IAAI,CAAC,EAC5C,KAAK,MAAM,SAAS,EAAE;AAEzB,QAAM,SAAS,QAAQ,OAAO,QAAQ;AACtC,SAAO,KAAK,MAAM,MAAM,IAAI,MAAM,QAAQ,CAAC;AAC3C,SAAO,KAAK,MAAM,SAAS,EAAE;AAC7B,SAAO,KAAK,KAAK,EAAE;AACnB,SAAO,KAAK,SAAS,MAAM,KAAK;AAChC,SAAO,KAAK,UAAU,MAAM,MAAM;AAElC,QAAMC,UAAS,QAAQ,KAAK,EAAE,QAAQ;AACtC,QAAM,SAASA,QAAO;AAEtB,yBAAuBD,OAAM,SAAS,MAAM,WAAW,CAAC;AAAA,IACtD,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,IACL,KAAK,IAAI;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,SAAS,sBAAsB,GAAG;AAAA,IAC3CA;AAAA,EACF;AAEA,SAAO,MAAM;AACf,GA7F2B;AA+FpB,IAAM,YAAY,sCAAgB,MAAM,OAAOA,OAAM,UAAU;AACpE,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK;AACH,aAAO,MAAM,mBAAmB,MAAM,OAAOA,OAAM,QAAQ;AAAA,IAC7D,KAAK;AACH,aAAO,MAAM,yBAAyB,MAAM,OAAOA,OAAM,QAAQ;AAAA,EACrE;AACF,GAPyB;AASlB,IAAM,UAAU,gCAAU,MAAM,KAAKA,OAAM;AAChD,QAAM,mBAAmB,KAAK,OAAO,GAAG;AACxC,QAAM,IAAI;AACV,EAAAE,oBAAmB,GAAG,GAAG;AACzB,MAAI,IAAI,MAAM;AACZ,2BAAuBF,KAAI;AAAA,MACzB,IAAI;AAAA,MACJ;AAAA,MACA,IAAI;AAAA,MACJ,IAAI,KAAK,IAAI,iBAAiB,KAAK;AAAA,MACnC,IAAI;AAAA,MACJ;AAAA,MACA,EAAE,OAAO,OAAO;AAAA,MAChBA;AAAA,IACF;AAAA,EACF;AACA,IAAE,MAAM;AACV,GAjBuB;AAmBhB,IAAM,gBAAgB,gCAAU,MAAM;AAC3C,SAAO,KAAK,OAAO,GAAG;AACxB,GAF6B;AAatB,IAAM,iBAAiB,gCAAU,MAAMC,SAAQ,aAAaD,OAAMG,mBAAkB;AACzF,QAAM,OAAqB,YAAY;AACvC,QAAM,IAAIF,QAAO;AACjB,OAAK,IAAIA,QAAO;AAChB,OAAK,IAAIA,QAAO;AAChB,OAAK,QAAQ,eAAgBE,oBAAmB;AAChD,OAAK,QAAQF,QAAO,QAAQA,QAAO;AACnC,OAAK,SAAS,cAAcA,QAAO;AACnC,EAAAJ,UAAS,GAAG,IAAI;AAClB,GAT8B;AAoBvB,IAAM,WAAW,sCAAgB,MAAM,WAAW,WAAWG,OAAM;AACxE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,EACrB,IAAIA;AACJ,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB,QAAM,eAAe,gCAAU,QAAQ,QAAQ,OAAO,OAAO;AAC3D,WAAO,EACJ,OAAO,MAAM,EACb,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,KAAK,EAChB,KAAK,MAAM,KAAK,EAChB,KAAK,SAAS,UAAU;AAAA,EAC7B,GARqB;AASrB,eAAa,UAAU,QAAQ,UAAU,QAAQ,UAAU,OAAO,UAAU,MAAM;AAClF,eAAa,UAAU,OAAO,UAAU,QAAQ,UAAU,OAAO,UAAU,KAAK;AAChF,eAAa,UAAU,QAAQ,UAAU,OAAO,UAAU,OAAO,UAAU,KAAK;AAChF,eAAa,UAAU,QAAQ,UAAU,QAAQ,UAAU,QAAQ,UAAU,KAAK;AAClF,MAAI,UAAU,aAAa,QAAW;AACpC,cAAU,SAAS,QAAQ,SAAU,MAAM;AACzC,mBAAa,UAAU,QAAQ,KAAK,GAAG,UAAU,OAAO,KAAK,CAAC,EAAE;AAAA,QAC9D;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,MAAoB,WAAW;AACnC,MAAI,OAAO;AACX,MAAI,IAAI,UAAU;AAClB,MAAI,IAAI,UAAU;AAClB,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,QAAQ,iBAAiB;AAC7B,MAAI,SAAS,kBAAkB;AAC/B,MAAI,aAAa;AACjB,MAAI,QAAQ;AAEZ,YAAU,GAAG,GAAG;AAChB,QAAMI,YAAW;AACjB,MAAI,OAAO,UAAU;AACrB,MAAI,IAAI,UAAU,SAAS,gBAAgB,KAAK,UAAU,QAAQ,UAAU,UAAU;AACtF,MAAI,IAAI,UAAU,SAAS,YAAY;AACvC,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,aAAa;AACjB,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,aAAa;AACjB,MAAI,OAAO;AAEX,MAAI,WAAW,SAAS,IAAI,IAAI,IAAI,MAAM,UAAU,GAAG,KAAK,SAAS,IAAI,SAAS,GAAG,GAAG;AAExF,MAAI,UAAU,kBAAkB,QAAW;AACzC,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,UAAU,aAAa,GAAG;AACjE,UAAI,KAAK,SAAS;AAChB,YAAI,OAAO,KAAK;AAChB,YAAI,IAAI,UAAU,UAAU,UAAU,QAAQ,UAAU,UAAU;AAClE,YAAI,IAAI,UAAU,SAAS,GAAG,EAAE,IAAI,YAAY;AAChD,YAAI,QAAQ;AACZ,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,OAAO,UAAU;AAErB,YAAI,SAAS,IAAI,IAAI,GAAG;AACtB,oBAAU,SAAS,UAAU,SAAS,GAAG,EAAE;AAC3C,gBAAM,UAAU,GAAG,KAAK,SAAS;AAAA,QACnC,OAAO;AACL,mBAAS,GAAG,GAAG;AAAA,QACjB;AACA,YAAI,gBAAgB,KAAK;AAAA,UACvB,SACG,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EACrD,OAAO,CAAC,KAAK,SAAS,MAAM,IAAI;AAAA,QACrC;AACA,kBAAU,SAAS,GAAG,EAAE,UAAU,iBAAiB,YAAY;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAEA,YAAU,SAAS,KAAK,MAAM,UAAU,QAAQ,UAAU,MAAM;AAChE,SAAO;AACT,GAjGwB;AAyGjB,IAAMF,sBAAqB,gCAAU,MAAMD,SAAQ;AACxD,EAAc,mBAAmB,MAAMA,OAAM;AAC/C,GAFkC;AAI3B,IAAM,qBAAqB,gCAAU,MAAM;AAChD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,EACrB,KAAK,aAAa,SAAS,EAC3B,KAAK,aAAa,SAAS,EAC3B,OAAO,MAAM,EACb,KAAK,aAAa,WAAW,EAC7B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AACJ,GAbkC;AAe3B,IAAM,qBAAqB,gCAAU,MAAM;AAChD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,EACrB,KAAK,SAAS,IAAI,EAClB,KAAK,UAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,aAAa,WAAW,EAC7B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AACJ,GAbkC;AAe3B,IAAM,kBAAkB,gCAAU,MAAM;AAC7C,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,OAAO,EAClB,KAAK,SAAS,IAAI,EAClB,KAAK,UAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,aAAa,WAAW,EAC7B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AACJ,GAb+B;AAoBxB,IAAM,kBAAkB,gCAAU,MAAM;AAC7C,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,WAAW,EACtB,KAAK,QAAQ,GAAG,EAChB,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,oBAAoB,EACnC,OAAO,MAAM,EACb,KAAK,KAAK,wBAAwB;AACvC,GAb+B;AAoBxB,IAAM,wBAAwB,gCAAU,MAAM;AACnD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,aAAa,EACxB,KAAK,QAAQ,IAAI,EACjB,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GAZqC;AAmB9B,IAAM,uBAAuB,gCAAU,MAAM;AAClD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,gBAAgB,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,EAAE,EACb,KAAK,KAAK,CAAC;AAEhB,GAfoC;AAsB7B,IAAM,uBAAuB,gCAAU,MAAM;AAClD,QAAM,OAAO,KAAK,OAAO,MAAM;AAC/B,QAAM,SAAS,KACZ,OAAO,QAAQ,EACf,KAAK,MAAM,WAAW,EACtB,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,MAAM,EACrB,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,GAAG;AAEnB,SACG,OAAO,MAAM,EACb,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,SAAS,EACxB,MAAM,oBAAoB,MAAM,EAChC,KAAK,gBAAgB,KAAK,EAC1B,KAAK,KAAK,yBAAyB;AAExC,GAnBoC;AAqB7B,IAAMG,cAAa,kCAAY;AACpC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF,GAf0B;AAiBnB,IAAMC,eAAc,kCAAY;AACrC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACF,GAZ2B;AAc3B,IAAM,yBAA0B,2BAAY;AAU1C,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;AAC1D,UAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,EAC5B,MAAM,eAAe,QAAQ,EAC7B,KAAK,OAAO;AACf,kBAAc,MAAM,SAAS;AAAA,EAC/B;AARS;AAoBT,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWL,OAAM;AACjE,UAAM,EAAE,eAAe,iBAAiB,gBAAgB,IAAIA;AAE5D,UAAM,CAAC,gBAAgB,gBAAgB,IAAI,cAAc,aAAa;AAEtE,UAAM,QAAQ,QAAQ,MAAM,eAAO,cAAc;AACjD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,iBAAkB,kBAAkB,MAAM,SAAS,KAAM;AACxE,YAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,CAAC,EACX,MAAM,eAAe,QAAQ,EAC7B,MAAM,aAAa,gBAAgB,EACnC,MAAM,eAAe,eAAe,EACpC,MAAM,eAAe,eAAe;AACvC,WACG,OAAO,OAAO,EACd,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,CAAC,CAAC;AAEhB,WACG,KAAK,KAAK,IAAI,SAAS,CAAG,EAC1B,KAAK,qBAAqB,SAAS,EACnC,KAAK,sBAAsB,SAAS;AAEvC,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AA7BS;AAyCT,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AAC9D,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EACP,OAAO,eAAe,EACtB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM;AAExB,UAAM,OAAO,EACV,OAAO,WAAW,EAClB,MAAM,WAAW,OAAO,EACxB,MAAM,UAAU,MAAM,EACtB,MAAM,SAAS,MAAM;AAExB,SACG,OAAO,KAAK,EACZ,MAAM,WAAW,YAAY,EAC7B,MAAM,cAAc,QAAQ,EAC5B,MAAM,kBAAkB,QAAQ,EAChC,KAAK,OAAO;AAEf,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,KAAI;AACxD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AAxBS;AAqCT,iBAAe,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AAGvE,UAAM,MAAM,MAAM,0BAA0B,SAAmB,UAAU,CAAC;AAC1E,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EACP,OAAO,eAAe,EACtB,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,EACvC,KAAK,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,CAAC,EACzC,KAAK,SAAS,IAAI,KAAK,EACvB,KAAK,UAAU,IAAI,MAAM;AAE5B,UAAM,OAAO,EAAE,OAAO,WAAW,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,SAAS,MAAM;AAEhF,SACG,OAAO,KAAK,EACZ,MAAM,cAAc,QAAQ,EAC5B,MAAM,kBAAkB,QAAQ,EAChC,KAAK,MAAM,YAAY,SAAmB,UAAU,CAAC,CAAC;AAEzD,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,KAAI;AACxD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AAtBe;AA4Bf,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,kBAAkB,eAAe,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AANS;AAQT,SAAO,SAAUA,OAAMM,YAAW,OAAO;AACvC,QAAIA,WAAU;AACZ,aAAO;AAAA,IACT;AACA,WAAON,MAAK,kBAAkB,OAAO,OAAOA,MAAK,kBAAkB,QAAQ,SAAS;AAAA,EACtF;AACF,EAAG;AAEH,IAAM,iCAAkC,2BAAY;AAUlD,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;AAC1D,UAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,MAAM,eAAe,OAAO,EAC5B,KAAK,OAAO;AACf,kBAAc,MAAM,SAAS;AAAA,EAC/B;AARS;AAoBT,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AACjE,UAAM,EAAE,eAAe,iBAAiB,gBAAgB,IAAIA;AAE5D,UAAM,QAAQ,QAAQ,MAAM,eAAO,cAAc;AACjD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,gBAAiB,iBAAiB,MAAM,SAAS,KAAM;AACtE,YAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,MAAM,eAAe,OAAO,EAC5B,MAAM,aAAa,aAAa,EAChC,MAAM,eAAe,eAAe,EACpC,MAAM,eAAe,eAAe;AACvC,WAAK,OAAO,OAAO,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,CAAC;AAE9D,WACG,KAAK,KAAK,IAAI,SAAS,CAAG,EAC1B,KAAK,qBAAqB,SAAS,EACnC,KAAK,sBAAsB,SAAS;AAEvC,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AAvBS;AAmCT,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AAC9D,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EACP,OAAO,eAAe,EACtB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM;AAExB,UAAM,OAAO,EACV,OAAO,WAAW,EAClB,MAAM,WAAW,OAAO,EACxB,MAAM,UAAU,MAAM,EACtB,MAAM,SAAS,MAAM;AAExB,SACG,OAAO,KAAK,EACZ,MAAM,WAAW,YAAY,EAC7B,MAAM,cAAc,QAAQ,EAC5B,MAAM,kBAAkB,QAAQ,EAChC,KAAK,OAAO;AAEf,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,KAAI;AACxD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AAxBS;AA8BT,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,kBAAkB,eAAe,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AANS;AAQT,SAAO,SAAUA,OAAM;AACrB,WAAOA,MAAK,kBAAkB,OAAO,OAAOA,MAAK,kBAAkB,QAAQ,SAAS;AAAA,EACtF;AACF,EAAG;AAEH,IAAO,kBAAQ;AAAA,EACb,UAAAH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAAK;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAE;AAAA,EACA,aAAAC;AAAA,EACA;AAAA,EACA;AACF;;;ADvlCA,IAAI,OAAO,CAAC;AAEL,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,eAAe,CAAC;AAAA,EAChB,aAAa,CAAC;AAAA,EACd,QAAQ;AAAA,IACN,WAAW,kCAAY;AACrB,aACE,KAAK,IAAI;AAAA,QACP;AAAA,QACA,KAAK,OAAO,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,UAAU,CAAC;AAAA,MAC/E,KACC,KAAK,MAAM,WAAW,IACnB,IACA,KAAK,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,MACpE,KAAK,SAAS,WAAW,IACtB,IACA,KAAK,SAAS,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,MACvE,KAAK,MAAM,WAAW,IACnB,IACA,KAAK,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC;AAAA,IAEzE,GAhBW;AAAA,IAiBX,OAAO,kCAAY;AACjB,WAAK,SAAS,CAAC;AACf,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AACd,WAAK,WAAW,CAAC;AACjB,WAAK,QAAQ,CAAC;AAAA,IAChB,GANO;AAAA,IAOP,QAAQ,gCAAU,UAAU;AAC1B,WAAK,MAAM,KAAK,QAAQ;AAAA,IAC1B,GAFQ;AAAA,IAGR,UAAU,gCAAU,YAAY;AAC9B,WAAK,OAAO,KAAK,UAAU;AAAA,IAC7B,GAFU;AAAA,IAGV,SAAS,gCAAU,WAAW;AAC5B,WAAK,MAAM,KAAK,SAAS;AAAA,IAC3B,GAFS;AAAA,IAGT,YAAY,gCAAU,UAAU;AAC9B,WAAK,SAAS,KAAK,QAAQ;AAAA,IAC7B,GAFY;AAAA,IAGZ,SAAS,gCAAU,WAAW;AAC5B,WAAK,MAAM,KAAK,SAAS;AAAA,IAC3B,GAFS;AAAA,IAGT,WAAW,kCAAY;AACrB,aAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,IAC3C,GAFW;AAAA,IAGX,UAAU,kCAAY;AACpB,aAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,IACzC,GAFU;AAAA,IAGV,aAAa,kCAAY;AACvB,aAAO,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAAA,IAC/C,GAFa;AAAA,IAGb,UAAU,kCAAY;AACpB,aAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,IACzC,GAFU;AAAA,IAGV,QAAQ,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,EACV;AAAA,EACA,MAAM,kCAAY;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,cAAc,CAAC;AACpB,SAAK,OAAO,MAAM;AAClB,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AACA,SAAK,cAAc;AACnB,YAAQE,WAAU,CAAC;AAAA,EACrB,GAZM;AAAA,EAaN,WAAW,gCAAU,KAAK,KAAK,KAAK,KAAK;AACvC,QAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,EACF,GANW;AAAA,EAOX,cAAc,gCAAU,QAAQ,QAAQ,OAAO,OAAO;AAEpD,UAAM,QAAQ;AACd,QAAI,MAAM;AAEV,aAAS,SAAS,MAAqB;AACrC,aAAO,gCAAS,iBAAiB,MAAM;AACrC;AAEA,cAAM,IAAI,MAAM,cAAc,SAAS,MAAM;AAE7C,cAAM,UAAU,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AACrE,cAAM,UAAU,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AAEnE,cAAM,UAAU,OAAO,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AAC5E,cAAM,UAAU,OAAO,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AAE1E,YAAI,EAAE,SAAS,eAAe;AAC5B,gBAAM,UAAU,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AACrE,gBAAM,UAAU,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AAEnE,gBAAM,UAAU,OAAO,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AAC5E,gBAAM,UAAU,OAAO,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AAAA,QAC5E;AAAA,MACF,GAlBO;AAAA,IAmBT;AApBS;AAsBT,SAAK,cAAc,QAAQ,SAAS,CAAC;AACrC,SAAK,YAAY,QAAQ,SAAS,YAAY,CAAC;AAAA,EACjD,GA7Bc;AAAA,EA8Bd,QAAQ,gCAAU,QAAQ,QAAQ,OAAO,OAAO;AAC9C,UAAM,UAAU,eAAO,OAAO,QAAQ,KAAK;AAC3C,UAAM,SAAS,eAAO,OAAO,QAAQ,KAAK;AAC1C,UAAM,UAAU,eAAO,OAAO,QAAQ,KAAK;AAC3C,UAAM,SAAS,eAAO,OAAO,QAAQ,KAAK;AAE1C,SAAK,UAAU,OAAO,MAAM,UAAU,SAAS,KAAK,GAAG;AACvD,SAAK,UAAU,OAAO,MAAM,UAAU,SAAS,KAAK,GAAG;AACvD,SAAK,UAAU,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AACrD,SAAK,UAAU,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AAErD,SAAK,aAAa,SAAS,SAAS,QAAQ,MAAM;AAAA,EACpD,GAZQ;AAAA,EAaR,eAAe,gCAAU,SAASC,UAAS,QAAQ;AACjD,UAAM,YAAY,OAAO,IAAI,QAAQ,IAAI;AACzC,UAAM,cAAc,iBAAiB,QAAQ,IAAI,EAAE,UAAU;AAC7D,UAAM,IAAI,UAAU,IAAI,UAAU,QAAQ,KAAM,cAAc,KAAK,KAAK,kBAAmB;AAC3F,SAAK,YAAY,KAAK;AAAA,MACpB,QAAQ;AAAA,MACR,QAAQ,KAAK,cAAc;AAAA,MAC3B,OAAO,IAAI,KAAK;AAAA,MAChB,OAAO;AAAA,MACP,OAAO,QAAQ;AAAA,MACf,UAAU,gBAAQ,cAAcA,QAAO;AAAA,IACzC,CAAC;AAAA,EACH,GAZe;AAAA,EAaf,eAAe,gCAAU,SAAS;AAEhC,UAAM,yBAAyB,KAAK,YACjC,IAAI,SAAU,YAAY;AACzB,aAAO,WAAW;AAAA,IACpB,CAAC,EACA,YAAY,QAAQ,IAAI;AAC3B,WAAO,KAAK,YAAY,OAAO,wBAAwB,CAAC,EAAE,CAAC;AAAA,EAC7D,GARe;AAAA,EASf,YAAY,gCAAU,QAAQ,EAAE,SAAS,QAAW,MAAM,OAAO,OAAO,OAAU,GAAG,MAAM;AACzF,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ,KAAK;AAAA,MACb,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO,MAAM;AAAA,MACb,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,EACF,GAZY;AAAA,EAaZ,SAAS,gCAAU,QAAQ,EAAE,SAAS,QAAW,MAAM,OAAO,OAAO,OAAU,GAAG,MAAM;AACtF,SAAK,cAAc,KAAK,KAAK,WAAW,OAAO,IAAI,CAAC;AAAA,EACtD,GAFS;AAAA,EAGT,SAAS,kCAAY;AACnB,WAAO,KAAK,cAAc,IAAI;AAAA,EAChC,GAFS;AAAA,EAGT,eAAe,kCAAY;AACzB,WAAO,KAAK,cAAc,SACtB,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,EAAE,UAClD;AAAA,EACN,GAJe;AAAA,EAKf,kBAAkB,gCAAU,SAAS;AACnC,UAAM,OAAO,KAAK,cAAc,IAAI;AACpC,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,gBAAgB,KAAK,iBAAiB,CAAC;AAC5C,SAAK,SAAS,KAAK,EAAE,GAAG,OAAO,eAAe,GAAG,QAAQ,EAAE,CAAC;AAC5D,SAAK,cAAc,KAAK,OAAO;AAC/B,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B,GAPkB;AAAA,EAQlB,iBAAiB,kCAAY;AAC3B,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,mBAAmB,KAAK;AAAA,IAC/B;AAAA,EACF,GAJiB;AAAA,EAKjB,kBAAkB,kCAAY;AAC5B,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF,GAJkB;AAAA,EAKlB,iBAAiB,gCAAU,MAAM;AAC/B,SAAK,cAAc,KAAK,cAAc;AACtC,SAAK,KAAK,QAAQ,eAAO,OAAO,KAAK,KAAK,OAAO,KAAK,WAAW;AAAA,EACnE,GAHiB;AAAA,EAIjB,gBAAgB,kCAAY;AAC1B,WAAO,KAAK;AAAA,EACd,GAFgB;AAAA,EAGhB,WAAW,kCAAY;AACrB,WAAO,EAAE,QAAQ,KAAK,MAAM,QAAQ,KAAK,OAAO;AAAA,EAClD,GAFW;AAGb;AAoBA,IAAM,WAAW,sCAAgB,MAAW,WAAsB;AAChE,SAAO,gBAAgB,KAAK,SAAS;AACrC,YAAU,SAAS,KAAK;AACxB,YAAU,SAAS,OAAO,eAAe;AACzC,QAAM,OAAqB,YAAY;AACvC,OAAK,IAAI,UAAU;AACnB,OAAK,IAAI,UAAU;AACnB,OAAK,QAAQ,UAAU,SAAS,KAAK;AACrC,OAAK,QAAQ;AAEb,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB,QAAM,WAAW,gBAAQ,SAAS,GAAG,IAAI;AACzC,QAAM,UAAwB,WAAW;AACzC,UAAQ,IAAI,UAAU;AACtB,UAAQ,IAAI,UAAU;AACtB,UAAQ,QAAQ,KAAK;AACrB,UAAQ,KAAK;AACb,UAAQ,OAAO,UAAU;AACzB,UAAQ,QAAQ;AAChB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,WAAW,KAAK;AACxB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,SAAS,KAAK;AACtB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,SAAS;AAEjB,QAAM,WAAW,SAAS,QAAQ,IAAI,IAAI,MAAM,UAAU,GAAG,OAAO,IAAI,SAAS,GAAG,OAAO;AAE3F,QAAM,aAAa,KAAK;AAAA,IACtB,SACG,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EACrD,OAAO,CAAC,KAAK,SAAS,MAAM,IAAI;AAAA,EACrC;AAEA,WAAS,KAAK,UAAU,aAAa,IAAI,KAAK,UAAU;AACxD,YAAU,UAAU,aAAa,IAAI,KAAK;AAC1C,SAAO,gBAAgB,aAAa,IAAI,KAAK,UAAU;AACvD,YAAU,QAAQ,UAAU,SAAS,aAAa,IAAI,KAAK;AAC3D,YAAU,QAAQ,UAAU,SAAS,KAAK;AAC1C,SAAO,OAAO,UAAU,QAAQ,UAAU,QAAQ,UAAU,OAAO,UAAU,KAAK;AAClF,SAAO,OAAO,QAAQ,SAAS;AACjC,GAzCiB;AA2CjB,IAAM,cAAc,wBAAC,QAAQ;AAC3B,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GANoB;AAOpB,IAAM,WAAW,wBAAC,QAAQ;AACxB,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GANiB;AAOjB,IAAM,YAAY,wBAAC,QAAQ;AACzB,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GANkB;AAiBlB,eAAe,aAAa,UAAU,UAA2B;AAC/D,SAAO,gBAAgB,EAAE;AACzB,QAAM,EAAE,QAAQ,OAAO,QAAQ,IAAI;AACnC,QAAM,QAAQ,eAAO,YAAY,OAAO,EAAE;AAC1C,QAAM,aAAa,SAAS,OAAO;AACnC,QAAM,WAAW,aACb,MAAM,0BAA0B,SAASD,WAAU,CAAC,IACpD,cAAM,wBAAwB,SAAS,YAAY,IAAI,CAAC;AAE5D,MAAI,CAAC,YAAY;AACf,UAAM,aAAa,SAAS,SAAS;AACrC,aAAS,UAAU;AACnB,WAAO,gBAAgB,UAAU;AAAA,EACnC;AAEA,MAAI;AACJ,MAAI,cAAc,SAAS,SAAS;AACpC,QAAM,YAAY,SAAS;AAE3B,MAAI,WAAW,OAAO;AACpB,iBAAa,OAAO,eAAe,IAAI;AACvC,QAAI,CAAC,KAAK,aAAa;AACrB,qBAAe,KAAK;AACpB,mBAAa,OAAO,eAAe,IAAI;AAAA,IACzC;AACA,mBAAe;AACf,UAAM,KAAK,eAAO,OAAO,YAAY,GAAG,KAAK,QAAQ,CAAC;AACtD,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,eAAe,IAAI,KAAK;AAAA,MAC/B,QAAQ;AAAA,MACR,OAAO,eAAe,IAAI,KAAK;AAAA,IACjC;AAAA,EACF,OAAO;AACL,mBAAe,KAAK;AACpB,iBAAa,OAAO,eAAe,IAAI;AACvC,WAAO,OAAO,QAAQ,aAAa,IAAI,OAAO,UAAU;AAAA,EAC1D;AACA,SAAO,gBAAgB,WAAW;AAClC,WAAS,UAAU;AACnB,WAAS,QAAQ,SAAS,SAAS,SAAS;AAC5C,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,SAAS,UAAU,SAAS,KAAK;AAErF,SAAO;AACT;AA5Ce;AAsDf,IAAM,cAAc,sCAAgBC,UAAS,UAAU,YAAoB,SAAkB;AAC3F,QAAM,EAAE,QAAQ,OAAO,QAAQ,SAAS,MAAM,eAAe,gBAAgB,IAAI;AACjF,QAAM,WAAW,cAAM,wBAAwB,SAAS,YAAY,IAAI,CAAC;AACzE,QAAM,UAAwB,WAAW;AACzC,UAAQ,IAAI;AACZ,UAAQ,IAAI,SAAS;AACrB,UAAQ,QAAQ,QAAQ;AACxB,UAAQ,QAAQ;AAChB,UAAQ,KAAK;AACb,UAAQ,OAAO;AACf,UAAQ,aAAa,KAAK;AAC1B,UAAQ,WAAW,KAAK;AACxB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,SAAS,KAAK;AACtB,UAAQ,SAAS;AACjB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,QAAQ;AAEhB,MAAI,SAAS,QAAQ,IAAI,GAAG;AAC1B,UAAM,UAAUA,UAAS,SAAS,EAAE,QAAQ,OAAO,QAAQ,WAAW,CAAC;AAAA,EACzE,OAAO;AACL,aAASA,UAAS,OAAO;AAAA,EAC3B;AAEA,QAAM,YAAY,SAAS;AAE3B,MAAI;AACJ,MAAI,WAAW,OAAO;AACpB,QAAI,KAAK,aAAa;AACpB,aAAOA,SACJ,OAAO,MAAM,EACb;AAAA,QACC;AAAA,QACA,MAAM,MAAM,IAAI,UAAU,MACxB,SAAS,eAAO,OAAO,KAAK,QAAQ,GAAG,YAAY,CAAC,CACtD,MAAM,aAAa,EAAE,MAAM,MAAM;AAAA,MACnC;AAAA,IACJ,OAAO;AACL,aAAOA,SACJ,OAAO,MAAM,EACb;AAAA,QACC;AAAA,QACA,OACE,SACA,MACA,aACA,SACC,SAAS,MACV,OACC,aAAa,MACd,OACC,SAAS,MACV,OACC,aAAa,MACd,MACA,SACA,OACC,aAAa;AAAA,MAClB;AAAA,IACJ;AAAA,EACF,OAAO;AACL,WAAOA,SAAQ,OAAO,MAAM;AAC5B,SAAK,KAAK,MAAM,MAAM;AACtB,SAAK,KAAK,MAAM,UAAU;AAC1B,SAAK,KAAK,MAAM,KAAK;AACrB,SAAK,KAAK,MAAM,UAAU;AAAA,EAC5B;AAGA,MACE,SAAS,QAAQ,GAAG,SAAS,UAC7B,SAAS,QAAQ,GAAG,SAAS,gBAC7B,SAAS,QAAQ,GAAG,SAAS,gBAC7B,SAAS,QAAQ,GAAG,SAAS,eAC7B,SAAS,QAAQ,GAAG,SAAS,sBAC7B;AACA,SAAK,MAAM,oBAAoB,MAAM;AACrC,SAAK,KAAK,SAAS,cAAc;AAAA,EACnC,OAAO;AACL,SAAK,KAAK,SAAS,cAAc;AAAA,EACnC;AAEA,MAAI,MAAM;AACV,MAAI,KAAK,qBAAqB;AAC5B,UACE,OAAO,SAAS,WAChB,OACA,OAAO,SAAS,OAChB,OAAO,SAAS,WAChB,OAAO,SAAS;AAClB,UAAM,IAAI,QAAQ,OAAO,KAAK;AAC9B,UAAM,IAAI,QAAQ,OAAO,KAAK;AAAA,EAChC;AAEA,OAAK,KAAK,gBAAgB,CAAC;AAC3B,OAAK,KAAK,UAAU,MAAM;AAC1B,OAAK,MAAM,QAAQ,MAAM;AACzB,MAAI,SAAS,QAAQ,GAAG,SAAS,SAAS,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAC7E,SAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,EACtD;AACA,MACE,SAAS,QAAQ,GAAG,SAAS,uBAC7B,SAAS,QAAQ,GAAG,SAAS,sBAC7B;AACA,SAAK,KAAK,gBAAgB,SAAS,MAAM,aAAa;AACtD,SAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,EACtD;AACA,MAAI,SAAS,QAAQ,GAAG,SAAS,eAAe,SAAS,QAAQ,GAAG,SAAS,cAAc;AACzF,SAAK,KAAK,cAAc,SAAS,MAAM,eAAe;AAAA,EACxD;AAEA,MAAI,SAAS,QAAQ,GAAG,SAAS,eAAe,SAAS,QAAQ,GAAG,SAAS,cAAc;AACzF,SAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,EACtD;AAGA,MAAI,mBAAmB,KAAK,qBAAqB;AAC/C,SAAK,KAAK,gBAAgB,SAAS,MAAM,kBAAkB;AAC3D,IAAAA,SACG,OAAO,MAAM,EACb,KAAK,KAAK,MAAM,EAChB,KAAK,KAAK,aAAa,CAAC,EACxB,KAAK,eAAe,YAAY,EAChC,KAAK,aAAa,MAAM,EACxB,KAAK,eAAe,QAAQ,EAC5B,KAAK,SAAS,gBAAgB,EAC9B,KAAK,aAAa;AAAA,EACvB;AACF,GAhIoB;AAkIpB,IAAM,wBAAwB,gCAC5BA,UACA,QACA,eACA,WACA,aACA,UACA,UACA;AACA,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,MAAI,UAAU;AACd,MAAI,YAAY;AAEhB,aAAW,YAAY,WAAW;AAChC,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAM,MAAM,MAAM;AAGlB,QAAI,WAAW,WAAW,KAAK;AAC7B,UAAI,CAAC,UAAU;AACb,eAAO,OAAO,OAAO,OAAO;AAAA,MAC9B;AACA,oBAAc,KAAK,YAAY,QAAQ;AAAA,IACzC;AAGA,QAAI,OAAO,OAAO,SAAS;AACzB,UAAI,CAAC,UAAU;AACb,YAAI,IAAI,YAAY;AACpB,YAAI,IAAI;AAAA,MACV;AACA,oBAAc,IAAI;AAAA,IACpB;AAGA,UAAM,QAAQ,MAAM,SAAS,KAAK;AAClC,UAAM,SAAS,eAAO,OAAO,MAAM,UAAU,KAAK,QAAQ,KAAK,MAAM;AACrE,UAAM,SAAS,MAAM,UAAU,KAAK;AAEpC,gBAAY,eAAO,OAAO,WAAW,MAAM,MAAM;AAGjD,QAAI,cAAc,IAAI,MAAM,IAAI,GAAG;AACjC,oBAAc,MAAM,QAAQ;AAAA,IAC9B;AAEA,UAAM,IAAI,YAAY;AACtB,UAAM,SAAS,OAAO,eAAe;AAErC,WAAO,OAAO,MAAM,GAAG,aAAa,MAAM,IAAI,MAAM,OAAO,MAAM,MAAM;AAEvE,iBAAa,MAAM,QAAQ;AAC3B,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,QAAQ,YAAY,IAAI,SAAS,MAAM,IAAI;AAAA,IACvD;AACA,iBAAa,MAAM;AACnB,cAAU,MAAM;AAChB,WAAO,OAAO,SAAS,KAAK;AAAA,EAC9B;AAGA,MAAI,WAAW,CAAC,UAAU;AACxB,WAAO,OAAO,OAAO,OAAO;AAAA,EAC9B;AAGA,SAAO,gBAAgB,SAAS;AAClC,GApE8B;AAsEvB,IAAM,aAAa,sCAAgBA,UAAS,QAAQ,WAAW,UAAU;AAC9E,MAAI,CAAC,UAAU;AACb,eAAW,YAAY,WAAW;AAChC,YAAM,QAAQ,OAAO,IAAI,QAAQ;AAEjC,YAAM,gBAAQ,UAAUA,UAAS,OAAO,MAAM,KAAK;AAAA,IACrD;AAAA,EACF,OAAO;AACL,QAAI,YAAY;AAChB,WAAO,gBAAgB,KAAK,YAAY,CAAC;AACzC,eAAW,YAAY,WAAW;AAChC,YAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAI,CAAC,MAAM,OAAO;AAChB,cAAM,QAAQ,OAAO,eAAe;AAAA,MACtC;AACA,YAAM,SAAS,MAAM,gBAAQ,UAAUA,UAAS,OAAO,MAAM,IAAI;AACjE,kBAAY,eAAO,OAAO,WAAW,MAAM;AAAA,IAC7C;AACA,WAAO,gBAAgB,YAAY,KAAK,SAAS;AAAA,EACnD;AACF,GApB0B;AAsBnB,IAAM,kBAAkB,gCAAUA,UAAS,QAAQ,WAAW,KAAK;AACxE,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,aAAW,YAAY,WAAW;AAChC,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAM,eAAe,sBAAsB,KAAK;AAChD,UAAM,iBAAiB,gBAAQ;AAAA,MAC7BA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF;AACA,QAAI,eAAe,SAAS,WAAW;AACrC,kBAAY,eAAe;AAAA,IAC7B;AACA,QAAI,eAAe,QAAQ,MAAM,IAAI,UAAU;AAC7C,iBAAW,eAAe,QAAQ,MAAM;AAAA,IAC1C;AAAA,EACF;AAEA,SAAO,EAAE,WAAsB,SAAmB;AACpD,GAvB+B;AAyBxB,IAAM,UAAU,gCAAU,KAAK;AACpC,0BAAgB,MAAM,GAAG;AAEzB,MAAI,IAAI,YAAY;AAClB,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,oBAAoB,IAAI;AAAA,EAC5E;AACA,MAAI,IAAI,UAAU;AAChB,SAAK,gBAAgB,KAAK,eAAe,KAAK,kBAAkB,IAAI;AAAA,EACtE;AACA,MAAI,IAAI,YAAY;AAClB,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,oBAAoB,IAAI;AAAA,EAC5E;AACF,GAZuB;AAcvB,IAAM,mBAAmB,gCAAU,OAAO;AACxC,SAAO,OAAO,YAAY,OAAO,SAAU,YAAY;AACrD,WAAO,WAAW,UAAU;AAAA,EAC9B,CAAC;AACH,GAJyB;AAMzB,IAAM,mBAAmB,gCAAU,OAAO,QAAQ;AAEhD,QAAM,WAAW,OAAO,IAAI,KAAK;AACjC,QAAM,cAAc,iBAAiB,KAAK;AAE1C,QAAM,OAAO,YAAY;AAAA,IACvB,SAAU,KAAK,YAAY;AACzB,aAAO,eAAO,OAAO,KAAK,WAAW,MAAM;AAAA,IAC7C;AAAA,IACA,SAAS,IAAI,SAAS,QAAQ,IAAI;AAAA,EACpC;AACA,QAAM,QAAQ,YAAY;AAAA,IACxB,SAAU,KAAK,YAAY;AACzB,aAAO,eAAO,OAAO,KAAK,WAAW,KAAK;AAAA,IAC5C;AAAA,IACA,SAAS,IAAI,SAAS,QAAQ,IAAI;AAAA,EACpC;AACA,SAAO,CAAC,MAAM,KAAK;AACrB,GAlByB;AAoBzB,SAAS,wBAAwB,YAAY,KAAK,WAAW,YAAY,WAAW;AAClF,SAAO,gBAAgB,SAAS;AAChC,MAAI,eAAe;AACnB,MAAI,IAAI,MAAM,IAAI,WAAW,WAAW,IAAI,EAAE,GAAG;AAC/C,UAAM,YAAY,WAAW,IAAI,EAAE,EAAE;AACrC,UAAM,WAAW,YAAY,IAAI;AACjC,QAAI,UAAU,cAAM,UAAU,IAAI,IAAI,OAAO,KAAK,YAAY,IAAI,KAAK,aAAa,QAAQ;AAC5F,QAAI,QAAQ;AACZ,QAAI,OAAO;AAGX,UAAM,WAAW,cAAM,wBAAwB,IAAI,SAAS,QAAQ;AACpE,UAAM,cAAc,eAAO,OAAO,SAAS,QAAQ,KAAK,cAAc;AACtE,mBAAe,aAAa;AAC5B,QAAI,MAAM,GAAG,WAAW,MAAM,IAAI,OAAO,EAAE;AAAA,EAC7C;AACA,YAAU,GAAG;AACb,SAAO,gBAAgB,YAAY;AACrC;AAlBS;AA8BT,SAAS,2BACP,KACA,UACA,YACA,OACA,QACA,eACA,iBACA;AACA,WAAS,mBAAmB,OAAO,YAAY;AAC7C,QAAI,MAAM,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,GAAG;AACpC,aAAO;AAAA,QACL,SAAS,QAAQ;AAAA,QACjB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,QAAQ,SAAS,QAAQ;AAAA,IACpC,OAAO;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,QAAQ,SAAS,QAAQ;AAAA,IACpC;AAAA,EACF;AAlBS;AAoBT,WAAS,iBAAiB,OAAO,YAAY;AAC3C,QAAI,MAAM,IAAI,OAAO,IAAI,IAAI,EAAE,EAAE,GAAG;AAClC,aAAO;AAAA,QACL,SAAS,SAAS;AAAA,QAClB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,SAAS,SAAS,SAAS;AAAA,IACtC,OAAO;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,SAAS;AAAA,QAClB,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,SAAS,SAAS,SAAS;AAAA,IACtC;AAAA,EACF;AAlBS;AAqBT,MAAI,cAAc,IAAI,IAAI,EAAE,KAAK,OAAO;AACtC,UAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAC/B,UAAM,aAAa,MAAM,QAAQ,UAAU,mBAAmB,IAAI,IAAI,MAAM,QAAQ,IAAI;AACxF,uBAAmB,OAAO,UAAU;AACpC,UAAM,SAAS,aAAa,MAAM,SAAS;AAC3C,WAAO,gBAAgB,MAAM,SAAS,CAAC;AAAA,EACzC,WAES,gBAAgB,IAAI,IAAI,IAAI,KAAK,OAAO;AAC/C,UAAM,QAAQ,OAAO,IAAI,IAAI,IAAI;AACjC,QAAI,KAAK,cAAc;AACrB,YAAM,aAAa,MAAM,QAAQ,UAAU,mBAAmB,IAAI,MAAM,QAAQ;AAChF,uBAAiB,OAAO,UAAU;AAAA,IACpC;AACA,UAAM,QAAQ,aAAa,MAAM,SAAS;AAC1C,WAAO,gBAAgB,MAAM,SAAS,CAAC;AAAA,EACzC,WAES,gBAAgB,IAAI,IAAI,EAAE,KAAK,OAAO;AAC7C,UAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAC/B,QAAI,KAAK,cAAc;AACrB,YAAM,aAAa,MAAM,QAAQ,UAAU,mBAAmB,IAAI,IAAI,MAAM,QAAQ,IAAI;AACxF,yBAAmB,OAAO,UAAU;AAAA,IACtC;AACA,UAAM,QAAQ,aAAa,MAAM,SAAS;AAC1C,WAAO,gBAAgB,MAAM,SAAS,CAAC;AAAA,EACzC;AACF;AA7ES;AAuFF,IAAM,OAAO,sCAAgB,OAAe,IAAY,UAAkB,SAAkB;AACjG,QAAM,EAAE,eAAe,SAAS,IAAID,WAAU;AAC9C,SAAO;AAEP,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,OAAO,OAAO,EAAE;AAAA,EACnC;AAEA,QAAM,OACJ,kBAAkB,YACd,OAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,OAAO,MAAM;AACnB,QAAM,MAAM,kBAAkB,YAAY,eAAe,MAAM,EAAE,CAAC,EAAE,kBAAkB;AACtF,SAAO,KAAK;AACZ,MAAI,MAAM,QAAQ,EAAE;AAEpB,QAAMC,WACJ,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,OAAO,QAAQ,EAAE,IAAI;AAGnF,QAAM,SAAS,QAAQ,GAAG,UAAU;AACpC,QAAM,gBAAgB,QAAQ,GAAG,iBAAiB;AAClD,QAAM,kBAAkB,QAAQ,GAAG,mBAAmB;AACtD,QAAM,QAAQ,QAAQ,GAAG,SAAS;AAClC,MAAI,YAAY,QAAQ,GAAG,aAAa;AACxC,QAAM,WAAW,QAAQ,GAAG,YAAY;AACxC,QAAM,QAAQ,QAAQ,GAAG,gBAAgB;AACzC,QAAM,WAAW,QAAQ,GAAG,iBAAiB;AAC7C,QAAM,eAAe,QAAQ,GAAG,0BAA0B;AAC1D,QAAM,0BAA0B,MAAM,2BAA2B,QAAQ,UAAU,OAAO;AAC1F,OAAK,SAAS,MAAM,sBAAsB,QAAQ,yBAAyB,KAAK;AAEhF,kBAAQ,mBAAmBA,QAAO;AAClC,kBAAQ,mBAAmBA,QAAO;AAClC,kBAAQ,gBAAgBA,QAAO;AAE/B,MAAI,UAAU;AACZ,WAAO,gBAAgB,KAAK,SAAS;AACrC,QAAI,cAAc;AAChB,aAAO,gBAAgB,MAAM,CAAC,EAAE,aAAa;AAAA,IAC/C;AAAA,EACF;AAEA,MAAI,KAAK,2BAA2B,MAAM;AACxC,UAAM,YAAY,oBAAI,IAAI;AAC1B,aAAS,QAAQ,CAAC,YAAY;AAC5B,gBAAU,IAAI,QAAQ,IAAI;AAC1B,gBAAU,IAAI,QAAQ,EAAE;AAAA,IAC1B,CAAC;AACD,gBAAY,UAAU,OAAO,CAAC,aAAa,UAAU,IAAI,QAAQ,CAAC;AAAA,EACpE;AAEA,wBAAsBA,UAAS,QAAQ,eAAe,WAAW,GAAG,UAAU,KAAK;AACnF,QAAM,aAAa,MAAM,oBAAoB,UAAU,QAAQ,yBAAyB,OAAO;AAG/F,kBAAQ,gBAAgBA,QAAO;AAC/B,kBAAQ,qBAAqBA,QAAO;AACpC,kBAAQ,sBAAsBA,QAAO;AACrC,kBAAQ,qBAAqBA,QAAO;AAMpC,WAAS,UAAU,KAAU,aAAqB;AAChD,UAAM,iBAAiB,OAAO,cAAc,GAAG;AAC/C,QAAI,eAAe,SAAS,KAAK,aAAa;AAC5C,qBAAe,SAAS,cAAc;AACtC,qBAAe;AAAA,IACjB;AACA,oBAAQ;AAAA,MACNA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,IAAI,IAAI,EAAE;AAAA,IAC7B;AAEA,WAAO,OAAO,eAAe,QAAQ,cAAc,IAAI,eAAe,OAAO,WAAW;AAAA,EAC1F;AAfS;AAkBT,MAAI,gBAAgB;AACpB,MAAI,oBAAoB;AACxB,QAAM,iBAAiB,CAAC;AACxB,QAAM,cAAc,CAAC;AACrB,MAAI,QAAQ;AACZ,aAAW,OAAO,UAAU;AAC1B,QAAI,WAAW,WAAW;AAE1B,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,QAAQ,GAAG,SAAS;AACvB,eAAO,iBAAiB;AACxB,oBAAY,IAAI;AAChB,cAAM,SAASA,UAAS,SAAS;AACjC;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,eAAO,cAAc,KAAKA,UAAS,MAAM;AACzC;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,kBAAU,KAAK,OAAO,eAAe,CAAC;AACtC;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAQ,SAASA,UAAS,WAAW,QAAQ,IAAI;AACvD,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UAAwB;AAAA,UAAY;AAAA,UAAK,KAAK;AAAA,UAAW,KAAK;AAAA,UAAW,CAAC,YACxE,OAAO,QAAQ,QAAW,QAAQ,OAAO;AAAA,QAC3C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,oBAAY,KAAK,SAAS;AAC1B,eAAO,OAAO,QAAQ,SAAS;AAC/B,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAQ,SAASA,UAAS,WAAW,OAAO,IAAI;AACtD,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK,YAAY,KAAK;AAAA,UACtB,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,iBAAiB,OAAO;AAAA,QAC9C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAQ,SAASA,UAAS,WAAW,OAAO,IAAI;AACtD,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA,eAAO,gBAAgB;AACvB;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK,YAAY,KAAK;AAAA,UACtB,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,iBAAiB,OAAO;AAAA,QAC9C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAQ,SAASA,UAAS,WAAW,OAAO,IAAI;AACtD,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,wBAAgB,IAAI,QAAQ,SAAS;AACrC,4BAAoB,IAAI,QAAQ,QAAQ;AACxC,YAAI,IAAI,QAAQ,SAAS;AACvB,kBAAQ,GAAG,sBAAsB;AAAA,QACnC,OAAO;AACL,kBAAQ,GAAG,uBAAuB;AAAA,QACpC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK,YAAY,KAAK;AAAA,UACtB,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,iBAAiB,OAAO;AAAA,QAC9C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAQ,SAASA,UAAS,WAAW,YAAY,IAAI;AAC3D,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAQ,SAASA,UAAS,WAAW,SAAS,IAAI;AACxD,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF;AACE,YAAI;AACF,qBAAW,IAAI;AACf,mBAAS,SAAS,OAAO,eAAe;AACxC,mBAAS,gBAAgB;AACzB,mBAAS,kBAAkB,QAAQ,GAAG,oBAAoB;AAC1D,gBAAM,aAAa,MAAM,aAAaA,UAAS,QAAQ;AACvD;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,yBAAe,KAAK,EAAE,cAAc,UAAU,WAAuB,CAAC;AACtE,iBAAO,OAAO,WAAW,QAAQ;AAAA,QACnC,SAAS,GAAG;AACV,cAAI,MAAM,+BAA+B,CAAC;AAAA,QAC5C;AAAA,IACJ;AAGA,QACE;AAAA,MACE,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,IACtB,EAAE,SAAS,IAAI,IAAI,GACnB;AACA,sBAAgB,gBAAgB;AAAA,IAClC;AACA;AAAA,EACF;AAEA,MAAI,MAAM,iBAAiB,aAAa;AACxC,MAAI,MAAM,mBAAmB,eAAe;AAC5C,QAAM,WAAWA,UAAS,QAAQ,WAAW,KAAK;AAElD,aAAW,KAAK,gBAAgB;AAC9B,UAAM,YAAYA,UAAS,EAAE,cAAc,EAAE,YAAY,OAAO;AAAA,EAClE;AACA,MAAI,KAAK,cAAc;AACrB,UAAM,WAAWA,UAAS,QAAQ,WAAW,IAAI;AAAA,EACnD;AACA,cAAY,QAAQ,CAAC,MAAM,gBAAQ,mBAAmBA,UAAS,CAAC,CAAC;AACjE,qBAAmBA,UAAS,QAAQ,WAAW,IAAI;AAEnD,aAAWC,QAAO,OAAO,OAAO,OAAO;AACrC,IAAAA,KAAI,SAAS,OAAO,eAAe,IAAIA,KAAI;AAC3C,WAAO,OAAOA,KAAI,GAAGA,KAAI,GAAGA,KAAI,IAAIA,KAAI,OAAOA,KAAI,MAAM;AACzD,IAAAA,KAAI,SAASA,KAAI;AACjB,IAAAA,KAAI,SAASA,KAAI;AACjB,IAAAA,KAAI,QAAQA,KAAI,SAASA,KAAI;AAC7B,IAAAA,KAAI,QAAQA,KAAI,SAASA,KAAI;AAC7B,IAAAA,KAAI,SAAS;AACb,oBAAQ,QAAQD,UAASC,MAAK,IAAI;AAAA,EACpC;AAEA,MAAI,UAAU;AACZ,WAAO,gBAAgB,KAAK,SAAS;AAAA,EACvC;AAGA,QAAM,kBAAkB,gBAAgBD,UAAS,QAAQ,WAAW,GAAG;AAEvE,QAAM,EAAE,QAAQ,IAAI,IAAI,OAAO,UAAU;AAEzC,MAAI,IAAI,WAAW,QAAW;AAC5B,QAAI,SAAS;AAAA,EACf;AACA,MAAI,IAAI,WAAW,QAAW;AAC5B,QAAI,SAAS;AAAA,EACf;AACA,MAAI,IAAI,UAAU,QAAW;AAC3B,QAAI,QAAQ;AAAA,EACd;AACA,MAAI,IAAI,UAAU,QAAW;AAC3B,QAAI,QAAQ;AAAA,EACd;AAGA,MAAI,YAAY,IAAI,QAAQ,IAAI;AAChC,MAAI,YAAY,gBAAgB,WAAW;AACzC,gBAAY,gBAAgB;AAAA,EAC9B;AAEA,MAAI,SAAS,YAAY,IAAI,KAAK;AAClC,MAAI,KAAK,cAAc;AACrB,aAAS,SAAS,KAAK,YAAY,KAAK;AAAA,EAC1C;AAGA,MAAI,WAAW,IAAI,QAAQ,IAAI;AAC/B,MAAI,WAAW,gBAAgB,UAAU;AACvC,eAAW,gBAAgB;AAAA,EAC7B;AACA,QAAM,QAAQ,WAAW,IAAI,KAAK;AAElC,MAAI,OAAO;AACT,IAAAA,SACG,OAAO,MAAM,EACb,KAAK,KAAK,EACV,KAAK,MAAM,IAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,KAAK,cAAc,EAChE,KAAK,KAAK,GAAG;AAAA,EAClB;AAEA,mBAAiBA,UAAS,QAAQ,OAAO,KAAK,WAAW;AAEzD,QAAM,oBAAoB,QAAQ,KAAK;AACvC,EAAAA,SAAQ;AAAA,IACN;AAAA,IACA,IAAI,SACF,KAAK,iBACL,QACC,KAAK,iBAAiB,qBACvB,MACA,QACA,OACC,SAAS;AAAA,EACd;AAEA,MAAI,MAAM,WAAW,OAAO,MAAM;AACpC,GAtXoB;AAmYpB,eAAe,2BACb,QACA,UACA,SACiC;AACjC,QAAM,0BAA0B,CAAC;AAEjC,aAAW,OAAO,UAAU;AAC1B,QAAI,OAAO,IAAI,IAAI,EAAE,KAAK,OAAO,IAAI,IAAI,IAAI,GAAG;AAC9C,YAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAG/B,UAAI,IAAI,cAAc,QAAQ,GAAG,UAAU,UAAU,CAAC,MAAM,WAAW;AACrE;AAAA,MACF;AAGA,UAAI,IAAI,cAAc,QAAQ,GAAG,UAAU,WAAW,CAAC,MAAM,WAAW;AACtE;AAAA,MACF;AAEA,YAAM,SAAS,IAAI,cAAc;AACjC,YAAM,YAAY,CAAC;AAEnB,YAAM,WAAW,SAAS,SAAS,IAAI,IAAI,YAAY,IAAI;AAC3D,YAAM,iBAAiB,IAAI,OACvB,cAAM,UAAU,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,aAAa,QAAQ,IACxE,IAAI;AACR,YAAM,oBAAoB,SAAS,cAAc,IAC7C,MAAM,0BAA0B,IAAI,SAASD,WAAU,CAAC,IACxD,cAAM,wBAAwB,gBAAgB,QAAQ;AAC1D,YAAM,eAAe,kBAAkB,QAAQ,IAAI,KAAK;AAkBxD,UAAI,aAAa,IAAI,SAAS,MAAM,WAAW;AAC7C,gCAAwB,IAAI,EAAE,IAAI,eAAO;AAAA,UACvC,wBAAwB,IAAI,EAAE,KAAK;AAAA,UACnC;AAAA,QACF;AAAA,MACF,WAAW,aAAa,IAAI,SAAS,MAAM,WAAW;AACpD,gCAAwB,IAAI,IAAI,IAAI,eAAO;AAAA,UACzC,wBAAwB,IAAI,IAAI,KAAK;AAAA,UACrC;AAAA,QACF;AAAA,MACF,WAAW,aAAa,IAAI,SAAS,IAAI,IAAI;AAC3C,gCAAwB,IAAI,IAAI,IAAI,eAAO;AAAA,UACzC,wBAAwB,IAAI,IAAI,KAAK;AAAA,UACrC,eAAe;AAAA,QACjB;AAEA,gCAAwB,IAAI,EAAE,IAAI,eAAO;AAAA,UACvC,wBAAwB,IAAI,EAAE,KAAK;AAAA,UACnC,eAAe;AAAA,QACjB;AAAA,MACF,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,SAAS;AACzD,gCAAwB,IAAI,IAAI,IAAI,eAAO;AAAA,UACzC,wBAAwB,IAAI,IAAI,KAAK;AAAA,UACrC;AAAA,QACF;AAAA,MACF,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,QAAQ;AACxD,gCAAwB,MAAM,SAAS,IAAI,eAAO;AAAA,UAChD,wBAAwB,MAAM,SAAS,KAAK;AAAA,UAC5C;AAAA,QACF;AAAA,MACF,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,MAAM;AACtD,YAAI,MAAM,WAAW;AACnB,kCAAwB,MAAM,SAAS,IAAI,eAAO;AAAA,YAChD,wBAAwB,MAAM,SAAS,KAAK;AAAA,YAC5C,eAAe;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,MAAM,WAAW;AACnB,kCAAwB,IAAI,IAAI,IAAI,eAAO;AAAA,YACzC,wBAAwB,IAAI,IAAI,KAAK;AAAA,YACrC,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM,4BAA4B,uBAAuB;AAC7D,SAAO;AACT;AAnGe;AAqGf,IAAM,wBAAwB,gCAAU,OAAO;AAC7C,MAAI,qBAAqB;AACzB,QAAM,WAAW,UAAU,IAAI;AAC/B,aAAW,OAAO,MAAM,OAAO;AAC7B,UAAM,kBAAkB,cAAM,wBAAwB,KAAK,QAAQ;AACnE,UAAM,aAAa,gBAAgB,QAAQ,IAAI,KAAK,cAAc,IAAI,KAAK;AAC3E,QAAI,qBAAqB,YAAY;AACnC,2BAAqB;AAAA,IACvB;AAAA,EACF;AAEA,SAAO;AACT,GAZ8B;AAyB9B,eAAe,sBACb,QACA,qBACA,OACA;AACA,MAAI,YAAY;AAChB,aAAW,QAAQ,OAAO,KAAK,GAAG;AAChC,UAAM,QAAQ,OAAO,IAAI,IAAI;AAC7B,QAAI,MAAM,MAAM;AACd,YAAM,cAAc,cAAM;AAAA,QACxB,MAAM;AAAA,QACN,KAAK,QAAQ,IAAI,KAAK;AAAA,QACtB,UAAU,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM,UAAU,SAAS,MAAM,WAAW,IACtC,MAAM,0BAA0B,MAAM,aAAaA,WAAU,CAAC,IAC9D,cAAM,wBAAwB,MAAM,aAAa,UAAU,IAAI,CAAC;AAEpE,UAAM,QAAQ,MAAM,OAChB,KAAK,QACL,eAAO,OAAO,KAAK,OAAO,QAAQ,QAAQ,IAAI,KAAK,WAAW;AAElE,UAAM,SAAS,MAAM,OAAO,eAAO,OAAO,QAAQ,QAAQ,KAAK,MAAM,IAAI,KAAK;AAC9E,gBAAY,eAAO,OAAO,WAAW,MAAM,MAAM;AAAA,EACnD;AAEA,aAAW,YAAY,qBAAqB;AAC1C,UAAM,QAAQ,OAAO,IAAI,QAAQ;AAEjC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAEA,UAAM,YAAY,OAAO,IAAI,MAAM,SAAS;AAG5C,QAAI,CAAC,WAAW;AACd,YAAMG,gBAAe,oBAAoB,QAAQ;AACjD,YAAMC,cAAaD,gBAAe,KAAK,cAAc,MAAM,QAAQ;AACnE,YAAM,SAAS,eAAO,OAAOC,aAAY,KAAK,WAAW;AACzD;AAAA,IACF;AAEA,UAAM,eAAe,oBAAoB,QAAQ;AACjD,UAAM,aAAa,eAAe,KAAK,cAAc,MAAM,QAAQ,IAAI,UAAU,QAAQ;AAEzF,UAAM,SAAS,eAAO,OAAO,YAAY,KAAK,WAAW;AAAA,EAC3D;AAEA,MAAI,eAAe;AACnB,QAAM,QAAQ,CAAC,QAAQ;AACrB,UAAM,WAAW,YAAY,IAAI;AACjC,QAAI,aAAa,IAAI,UAAU,OAAO,CAAC,OAAO,SAAS;AACrD,aAAQ,SAAS,OAAO,IAAI,IAAI,EAAE,SAAS,OAAO,IAAI,IAAI,EAAE,UAAU;AAAA,IACxE,GAAG,CAAC;AAEJ,kBAAc,IAAI,KAAK;AACvB,QAAI,IAAI,MAAM;AACZ,UAAI,OAAO,cAAM,UAAU,IAAI,MAAM,aAAa,IAAI,KAAK,aAAa,QAAQ;AAAA,IAClF;AAEA,UAAM,mBAAmB,cAAM,wBAAwB,IAAI,MAAM,QAAQ;AACzE,mBAAe,eAAO,OAAO,iBAAiB,QAAQ,YAAY;AAClE,UAAM,WAAW,eAAO,OAAO,YAAY,iBAAiB,QAAQ,IAAI,KAAK,WAAW;AACxF,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,UAAU;AACzB,YAAM,WAAW,WAAW,cAAc;AAC1C,UAAI,UAAU;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,CAAC,QAAS,IAAI,gBAAgB,YAAa;AAEzD,SAAO,eAAO,OAAO,WAAW,KAAK,MAAM;AAC7C;AA1Ee;AA4Ef,IAAM,iBAAiB,sCAAgB,KAAK,QAAQ,SAAS;AAC3D,QAAM,YAAY,OAAO,IAAI,IAAI,IAAI;AACrC,QAAM,UAAU,OAAO,IAAI,IAAI,EAAE;AACjC,QAAM,SAAS,UAAU;AACzB,QAAM,QAAQ,QAAQ;AACtB,QAAM,aAAa,IAAI,QAAQ,IAAI;AAEnC,MAAI,iBAAyE,SAAS,IAAI,OAAO,IAC7F,MAAM,0BAA0B,IAAI,SAASJ,WAAU,CAAC,IACxD,cAAM;AAAA,IACJ,aAAa,cAAM,UAAU,IAAI,SAAS,KAAK,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI;AAAA,IAC5E,SAAS,IAAI;AAAA,EACf;AACJ,QAAM,YAAY;AAAA,IAChB,OAAO,aACH,KAAK,QACL,eAAO,OAAO,KAAK,OAAO,eAAe,QAAQ,IAAI,KAAK,UAAU;AAAA,IACxE,QAAQ;AAAA,IACR,QAAQ,UAAU;AAAA,IAClB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS,IAAI;AAAA,EACf;AACA,MAAI,IAAI,cAAc,QAAQ,GAAG,UAAU,SAAS;AAClD,cAAU,QAAQ,aACd,eAAO,OAAO,KAAK,OAAO,eAAe,KAAK,IAC9C,eAAO;AAAA,MACL,UAAU,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACtC,eAAe,QAAQ,IAAI,KAAK;AAAA,IAClC;AACJ,cAAU,SAAS,UAAU,UAAU,QAAQ,KAAK,eAAe;AAAA,EACrE,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,QAAQ;AACxD,cAAU,QAAQ,aACd,eAAO,OAAO,KAAK,OAAO,eAAe,QAAQ,IAAI,KAAK,UAAU,IACpE,eAAO;AAAA,MACL,UAAU,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACtC,eAAe,QAAQ,IAAI,KAAK;AAAA,IAClC;AACJ,cAAU,SAAS,SAAS,UAAU,SAAS,UAAU,QAAQ,KAAK,eAAe;AAAA,EACvF,WAAW,IAAI,OAAO,IAAI,MAAM;AAC9B,qBAAiB,cAAM;AAAA,MACrB,aACI,cAAM,UAAU,IAAI,SAAS,eAAO,OAAO,KAAK,OAAO,UAAU,KAAK,GAAG,SAAS,IAAI,CAAC,IACvF,IAAI;AAAA,MACR,SAAS,IAAI;AAAA,IACf;AACA,cAAU,QAAQ,aACd,eAAO,OAAO,KAAK,OAAO,UAAU,KAAK,IACzC,eAAO,OAAO,UAAU,OAAO,KAAK,OAAO,eAAe,QAAQ,IAAI,KAAK,UAAU;AACzF,cAAU,SAAS,UAAU,UAAU,QAAQ,UAAU,SAAS;AAAA,EACpE,OAAO;AACL,cAAU,QACR,KAAK,IAAI,SAAS,UAAU,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,EAAE,IAAI,KAAK;AAC9E,cAAU,SACR,SAAS,QACL,SAAS,UAAU,QAAQ,IAAI,KAAK,cAAc,IAClD,QAAQ,QAAQ,QAAQ,IAAI,KAAK,cAAc;AAAA,EACvD;AACA,MAAI,YAAY;AACd,cAAU,UAAU,cAAM;AAAA,MACxB,IAAI;AAAA,MACJ,UAAU,QAAQ,IAAI,KAAK;AAAA,MAC3B,SAAS,IAAI;AAAA,IACf;AAAA,EACF;AACA,MAAI;AAAA,IACF,OAAO,UAAU,MAAM,IAAI,UAAU,KAAK,IAAI,UAAU,MAAM,IAAI,UAAU,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU,MAAM,IAAI,IAAI,OAAO;AAAA,EACzI;AACA,SAAO;AACT,GAtEuB;AAwEvB,IAAM,oBAAoB,gCAAU,KAAK,QAAQ,SAAS;AACxD,MACE,CAAC;AAAA,IACC,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,EACtB,EAAE,SAAS,IAAI,IAAI,GACnB;AACA,WAAO,CAAC;AAAA,EACV;AACA,QAAM,CAAC,UAAU,SAAS,IAAI,iBAAiB,IAAI,MAAM,MAAM;AAC/D,QAAM,CAAC,QAAQ,OAAO,IAAI,iBAAiB,IAAI,IAAI,MAAM;AACzD,QAAM,iBAAiB,YAAY;AACnC,MAAI,SAAS,iBAAiB,YAAY;AAC1C,MAAI,QAAQ,iBAAiB,SAAS;AAGtC,QAAM,sBAAsB,KAAK,IAAI,SAAS,OAAO,IAAI;AAOzD,QAAM,cAAc,wBAAC,UAAkB;AACrC,WAAO,iBAAiB,CAAC,QAAQ;AAAA,EACnC,GAFoB;AAIpB,MAAI,IAAI,SAAS,IAAI,IAAI;AAIvB,YAAQ;AAAA,EACV,OAAO;AAQL,QAAI,IAAI,YAAY,CAAC,qBAAqB;AACxC,eAAS,YAAY,KAAK,kBAAkB,IAAI,CAAC;AAAA,IACnD;AAMA,QAAI,CAAC,CAAC,QAAQ,GAAG,SAAS,YAAY,QAAQ,GAAG,SAAS,WAAW,EAAE,SAAS,IAAI,IAAI,GAAG;AACzF,eAAS,YAAY,CAAC;AAAA,IACxB;AAKA,QACE,CAAC,QAAQ,GAAG,SAAS,qBAAqB,QAAQ,GAAG,SAAS,oBAAoB,EAAE;AAAA,MAClF,IAAI;AAAA,IACN,GACA;AACA,gBAAU,YAAY,CAAC;AAAA,IACzB;AAAA,EACF;AAEA,QAAM,YAAY,CAAC,UAAU,WAAW,QAAQ,OAAO;AACvD,QAAM,eAAe,KAAK,IAAI,SAAS,KAAK;AAC5C,MAAI,IAAI,QAAQ,IAAI,SAAS;AAC3B,QAAI,UAAU,cAAM;AAAA,MAClB,IAAI;AAAA,MACJ,eAAO,OAAO,eAAe,IAAI,KAAK,aAAa,KAAK,KAAK;AAAA,MAC7D,YAAY,IAAI;AAAA,IAClB;AAAA,EACF;AACA,QAAM,UAAU,cAAM,wBAAwB,IAAI,SAAS,YAAY,IAAI,CAAC;AAE5E,SAAO;AAAA,IACL,OAAO,eAAO;AAAA,MACZ,IAAI,OAAO,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAAA,MACxC,eAAe,IAAI,KAAK;AAAA,MACxB,KAAK;AAAA,IACP;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS,IAAI;AAAA,IACb,MAAM,IAAI;AAAA,IACV,MAAM,IAAI;AAAA,IACV,YAAY,KAAK,IAAI,MAAM,MAAM,SAAS;AAAA,IAC1C,UAAU,KAAK,IAAI,MAAM,MAAM,SAAS;AAAA,EAC1C;AACF,GApG0B;AAsG1B,IAAM,sBAAsB,sCAAgB,UAAU,QAAQ,mBAAmB,SAAS;AACxF,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,CAAC;AACf,MAAI,SAAS,WAAW;AAExB,aAAW,OAAO,UAAU;AAC1B,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB,cAAM,KAAK;AAAA,UACT,IAAI,IAAI;AAAA,UACR,KAAK,IAAI;AAAA,UACT,MAAM,OAAO;AAAA,UACb,IAAI,OAAO;AAAA,UACX,OAAO;AAAA,QACT,CAAC;AACD;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB,YAAI,IAAI,SAAS;AACf,oBAAU,MAAM,IAAI;AACpB,gBAAM,QAAQ,EAAE,IAAI;AACpB,gBAAM,IAAI,EAAE,IAAI;AAChB,gBAAM,KAAK,OAAO;AAAA,QACpB;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB,kBAAU,MAAM,IAAI;AACpB,cAAM,QAAQ,EAAE,IAAI;AACpB;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AACE,gBAAM,YAAY,OAAO,IAAI,IAAI,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;AAC/D,gBAAM,cAAc,iBAAiB,IAAI,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK,EAAE;AACzE,gBAAM,IACJ,UAAU,IAAI,UAAU,QAAQ,KAAM,cAAc,KAAK,KAAK,kBAAmB;AACnF,gBAAM,QAAQ;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO,IAAI,KAAK;AAAA,YAChB,OAAO,IAAI;AAAA,YACX,SAAS;AAAA,UACX;AACA,iBAAO,YAAY,KAAK,KAAK;AAAA,QAC/B;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AACE,gBAAM,yBAAyB,OAAO,YACnC,IAAI,CAAC,MAAM,EAAE,KAAK,EAClB,YAAY,IAAI,IAAI;AACvB,iBAAO,YAAY,OAAO,wBAAwB,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,QAClE;AACA;AAAA,IACJ;AACA,UAAM,SAAS,IAAI,cAAc;AACjC,QAAI,QAAQ;AACV,kBAAY,MAAM,eAAe,KAAK,QAAQ,OAAO;AACrD,UAAI,YAAY;AAChB,YAAM,QAAQ,CAAC,QAAQ;AACrB,kBAAU;AACV,gBAAQ,OAAO,eAAO,OAAO,QAAQ,MAAM,UAAU,MAAM;AAC3D,gBAAQ,KAAK,eAAO,OAAO,QAAQ,IAAI,UAAU,SAAS,UAAU,KAAK;AACzE,gBAAQ,QACN,eAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,QAAQ,OAAO,QAAQ,EAAE,CAAC,IAAI,KAAK;AAAA,MAC7E,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,kBAAkB,KAAK,QAAQ,OAAO;AACjD,UAAI,WAAW;AACf,UAAI,SAAS,UAAU,SAAS,SAAS,MAAM,SAAS,GAAG;AACzD,cAAM,QAAQ,CAAC,QAAQ;AACrB,oBAAU;AACV,cAAI,SAAS,WAAW,SAAS,OAAO;AACtC,kBAAM,OAAO,OAAO,IAAI,IAAI,IAAI;AAChC,kBAAM,KAAK,OAAO,IAAI,IAAI,EAAE;AAC5B,oBAAQ,OAAO,eAAO;AAAA,cACpB,KAAK,IAAI,SAAS,QAAQ;AAAA,cAC1B,KAAK,IAAI,KAAK,QAAQ;AAAA,cACtB,QAAQ;AAAA,YACV;AACA,oBAAQ,KAAK,eAAO;AAAA,cAClB,GAAG,IAAI,SAAS,QAAQ;AAAA,cACxB,GAAG,IAAI,KAAK,QAAQ;AAAA,cACpB,QAAQ;AAAA,YACV;AACA,oBAAQ,QACN,eAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAChE,KAAK;AAAA,UACT,OAAO;AACL,oBAAQ,OAAO,eAAO,OAAO,SAAS,QAAQ,QAAQ,IAAI;AAC1D,oBAAQ,KAAK,eAAO,OAAO,SAAS,OAAO,QAAQ,EAAE;AACrD,oBAAQ,QAAQ,eAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,KAAK;AAAA,UACtE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO,cAAc,CAAC;AACtB,MAAI,MAAM,qBAAqB,KAAK;AACpC,SAAO;AACT,GA9G4B;AAgH5B,IAAO,2BAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AE3mDO,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,IAAI,WAAW;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,wBAAC,QAAuB;AAC5B,QAAI,CAAC,IAAI,UAAU;AACjB,UAAI,WAAW,CAAC;AAAA,IAClB;AACA,QAAI,IAAI,MAAM;AACZ,UAAI,SAAS,OAAO,IAAI;AACxB,gBAAU,EAAE,UAAU,EAAE,MAAM,IAAI,KAAK,EAAE,CAAC;AAAA,IAC5C;AAAA,EACF,GARM;AASR;", "names": ["o", "parser", "lexer", "getConfig", "text", "drawRect", "actor<PERSON>nt", "diagram", "conf", "bounds", "drawBackgroundRect", "actorActivations", "getTextObj", "getNoteRect", "hasKatex", "getConfig", "diagram", "box", "messageWidth", "<PERSON><PERSON><PERSON><PERSON>"]}