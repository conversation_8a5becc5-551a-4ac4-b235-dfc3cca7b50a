import{a as ue}from"./chunk-VSLJSFIP.mjs";import{a as ce,b as le,d as he,e as de,f as mt,g as kt}from"./chunk-DLZS2ZFX.mjs";import{a as pe,k as Wt,m as V}from"./chunk-YM3XIQPS.mjs";import{a as Me}from"./chunk-TI4EEUUG.mjs";import{A as At,F as Et,I as rt,J as gt,K as Vt,L as k,M as te,P as ee,Q as Yt,R as se,S as re,T as ae,U as ie,V as ne,X as G,Y as oe,b as F,ga as wt,k as $t}from"./chunk-ZKYS2E5M.mjs";import"./chunk-6BY5RJGC.mjs";import{a as u,e as ve}from"./chunk-GTKDMUJJ.mjs";var Ft=function(){var e=u(function(ht,w,L,P){for(L=L||{},P=ht.length;P--;L[ht[P]]=w);return L},"o"),t=[1,2],c=[1,3],s=[1,4],a=[2,4],i=[1,9],n=[1,11],h=[1,13],d=[1,14],r=[1,16],f=[1,17],T=[1,18],g=[1,24],b=[1,25],E=[1,26],I=[1,27],A=[1,28],O=[1,29],v=[1,30],B=[1,31],R=[1,32],q=[1,33],z=[1,34],J=[1,35],tt=[1,36],H=[1,37],K=[1,38],W=[1,39],D=[1,41],Z=[1,42],X=[1,43],Q=[1,44],et=[1,45],S=[1,46],m=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],_=[4,5,16,50,52,53],j=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],at=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],N=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],Xt=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],it=[68,69,70],ct=[1,122],vt={trace:u(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:u(function(w,L,P,y,M,l,yt){var p=l.length-1;switch(M){case 3:return y.apply(l[p]),l[p];break;case 4:case 9:this.$=[];break;case 5:case 10:l[p-1].push(l[p]),this.$=l[p-1];break;case 6:case 7:case 11:case 12:this.$=l[p];break;case 8:case 13:this.$=[];break;case 15:l[p].type="createParticipant",this.$=l[p];break;case 16:l[p-1].unshift({type:"boxStart",boxData:y.parseBoxData(l[p-2])}),l[p-1].push({type:"boxEnd",boxText:l[p-2]}),this.$=l[p-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(l[p-2]),sequenceIndexStep:Number(l[p-1]),sequenceVisible:!0,signalType:y.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(l[p-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:y.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:y.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:y.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:y.LINETYPE.ACTIVE_START,actor:l[p-1].actor};break;case 23:this.$={type:"activeEnd",signalType:y.LINETYPE.ACTIVE_END,actor:l[p-1].actor};break;case 29:y.setDiagramTitle(l[p].substring(6)),this.$=l[p].substring(6);break;case 30:y.setDiagramTitle(l[p].substring(7)),this.$=l[p].substring(7);break;case 31:this.$=l[p].trim(),y.setAccTitle(this.$);break;case 32:case 33:this.$=l[p].trim(),y.setAccDescription(this.$);break;case 34:l[p-1].unshift({type:"loopStart",loopText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.LOOP_START}),l[p-1].push({type:"loopEnd",loopText:l[p-2],signalType:y.LINETYPE.LOOP_END}),this.$=l[p-1];break;case 35:l[p-1].unshift({type:"rectStart",color:y.parseMessage(l[p-2]),signalType:y.LINETYPE.RECT_START}),l[p-1].push({type:"rectEnd",color:y.parseMessage(l[p-2]),signalType:y.LINETYPE.RECT_END}),this.$=l[p-1];break;case 36:l[p-1].unshift({type:"optStart",optText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.OPT_START}),l[p-1].push({type:"optEnd",optText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.OPT_END}),this.$=l[p-1];break;case 37:l[p-1].unshift({type:"altStart",altText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.ALT_START}),l[p-1].push({type:"altEnd",signalType:y.LINETYPE.ALT_END}),this.$=l[p-1];break;case 38:l[p-1].unshift({type:"parStart",parText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.PAR_START}),l[p-1].push({type:"parEnd",signalType:y.LINETYPE.PAR_END}),this.$=l[p-1];break;case 39:l[p-1].unshift({type:"parStart",parText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.PAR_OVER_START}),l[p-1].push({type:"parEnd",signalType:y.LINETYPE.PAR_END}),this.$=l[p-1];break;case 40:l[p-1].unshift({type:"criticalStart",criticalText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.CRITICAL_START}),l[p-1].push({type:"criticalEnd",signalType:y.LINETYPE.CRITICAL_END}),this.$=l[p-1];break;case 41:l[p-1].unshift({type:"breakStart",breakText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.BREAK_START}),l[p-1].push({type:"breakEnd",optText:y.parseMessage(l[p-2]),signalType:y.LINETYPE.BREAK_END}),this.$=l[p-1];break;case 43:this.$=l[p-3].concat([{type:"option",optionText:y.parseMessage(l[p-1]),signalType:y.LINETYPE.CRITICAL_OPTION},l[p]]);break;case 45:this.$=l[p-3].concat([{type:"and",parText:y.parseMessage(l[p-1]),signalType:y.LINETYPE.PAR_AND},l[p]]);break;case 47:this.$=l[p-3].concat([{type:"else",altText:y.parseMessage(l[p-1]),signalType:y.LINETYPE.ALT_ELSE},l[p]]);break;case 48:l[p-3].draw="participant",l[p-3].type="addParticipant",l[p-3].description=y.parseMessage(l[p-1]),this.$=l[p-3];break;case 49:l[p-1].draw="participant",l[p-1].type="addParticipant",this.$=l[p-1];break;case 50:l[p-3].draw="actor",l[p-3].type="addParticipant",l[p-3].description=y.parseMessage(l[p-1]),this.$=l[p-3];break;case 51:l[p-1].draw="actor",l[p-1].type="addParticipant",this.$=l[p-1];break;case 52:l[p-1].type="destroyParticipant",this.$=l[p-1];break;case 53:this.$=[l[p-1],{type:"addNote",placement:l[p-2],actor:l[p-1].actor,text:l[p]}];break;case 54:l[p-2]=[].concat(l[p-1],l[p-1]).slice(0,2),l[p-2][0]=l[p-2][0].actor,l[p-2][1]=l[p-2][1].actor,this.$=[l[p-1],{type:"addNote",placement:y.PLACEMENT.OVER,actor:l[p-2].slice(0,2),text:l[p]}];break;case 55:this.$=[l[p-1],{type:"addLinks",actor:l[p-1].actor,text:l[p]}];break;case 56:this.$=[l[p-1],{type:"addALink",actor:l[p-1].actor,text:l[p]}];break;case 57:this.$=[l[p-1],{type:"addProperties",actor:l[p-1].actor,text:l[p]}];break;case 58:this.$=[l[p-1],{type:"addDetails",actor:l[p-1].actor,text:l[p]}];break;case 61:this.$=[l[p-2],l[p]];break;case 62:this.$=l[p];break;case 63:this.$=y.PLACEMENT.LEFTOF;break;case 64:this.$=y.PLACEMENT.RIGHTOF;break;case 65:this.$=[l[p-4],l[p-1],{type:"addMessage",from:l[p-4].actor,to:l[p-1].actor,signalType:l[p-3],msg:l[p],activate:!0},{type:"activeStart",signalType:y.LINETYPE.ACTIVE_START,actor:l[p-1].actor}];break;case 66:this.$=[l[p-4],l[p-1],{type:"addMessage",from:l[p-4].actor,to:l[p-1].actor,signalType:l[p-3],msg:l[p]},{type:"activeEnd",signalType:y.LINETYPE.ACTIVE_END,actor:l[p-4].actor}];break;case 67:this.$=[l[p-3],l[p-1],{type:"addMessage",from:l[p-3].actor,to:l[p-1].actor,signalType:l[p-2],msg:l[p]}];break;case 68:this.$={type:"addParticipant",actor:l[p]};break;case 69:this.$=y.LINETYPE.SOLID_OPEN;break;case 70:this.$=y.LINETYPE.DOTTED_OPEN;break;case 71:this.$=y.LINETYPE.SOLID;break;case 72:this.$=y.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=y.LINETYPE.DOTTED;break;case 74:this.$=y.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=y.LINETYPE.SOLID_CROSS;break;case 76:this.$=y.LINETYPE.DOTTED_CROSS;break;case 77:this.$=y.LINETYPE.SOLID_POINT;break;case 78:this.$=y.LINETYPE.DOTTED_POINT;break;case 79:this.$=y.parseMessage(l[p].trim().substring(1));break}},"anonymous"),table:[{3:1,4:t,5:c,6:s},{1:[3]},{3:5,4:t,5:c,6:s},{3:6,4:t,5:c,6:s},e([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],a,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:n,8:8,9:10,12:12,13:h,14:d,17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},e(m,[2,5]),{9:47,12:12,13:h,14:d,17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},e(m,[2,7]),e(m,[2,8]),e(m,[2,14]),{12:48,50:H,52:K,53:W},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:S},{22:55,70:S},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},e(m,[2,29]),e(m,[2,30]),{32:[1,61]},{34:[1,62]},e(m,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:S},{22:72,70:S},{22:73,70:S},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:S},{22:90,70:S},{22:91,70:S},{22:92,70:S},e([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),e(m,[2,6]),e(m,[2,15]),e(_,[2,9],{10:93}),e(m,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},e(m,[2,21]),{5:[1,97]},{5:[1,98]},e(m,[2,24]),e(m,[2,25]),e(m,[2,26]),e(m,[2,27]),e(m,[2,28]),e(m,[2,31]),e(m,[2,32]),e(j,a,{7:99}),e(j,a,{7:100}),e(j,a,{7:101}),e(at,a,{40:102,7:103}),e(N,a,{42:104,7:105}),e(N,a,{7:105,42:106}),e(Xt,a,{45:107,7:108}),e(j,a,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:S},e(it,[2,69]),e(it,[2,70]),e(it,[2,71]),e(it,[2,72]),e(it,[2,73]),e(it,[2,74]),e(it,[2,75]),e(it,[2,76]),e(it,[2,77]),e(it,[2,78]),{22:118,70:S},{22:120,58:119,70:S},{70:[2,63]},{70:[2,64]},{56:121,81:ct},{56:123,81:ct},{56:124,81:ct},{56:125,81:ct},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:H,52:K,53:W},{5:[1,131]},e(m,[2,19]),e(m,[2,20]),e(m,[2,22]),e(m,[2,23]),{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[1,132],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[1,133],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[1,134],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{16:[1,135]},{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[2,46],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,49:[1,136],50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{16:[1,137]},{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[2,44],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,48:[1,138],50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{16:[1,139]},{16:[1,140]},{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[2,42],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,47:[1,141],50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{4:i,5:n,8:8,9:10,12:12,13:h,14:d,16:[1,142],17:15,18:r,21:f,22:40,23:T,24:19,25:20,26:21,27:22,28:23,29:g,30:b,31:E,33:I,35:A,36:O,37:v,38:B,39:R,41:q,43:z,44:J,46:tt,50:H,52:K,53:W,54:D,59:Z,60:X,61:Q,62:et,70:S},{15:[1,143]},e(m,[2,49]),{15:[1,144]},e(m,[2,51]),e(m,[2,52]),{22:145,70:S},{22:146,70:S},{56:147,81:ct},{56:148,81:ct},{56:149,81:ct},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},e(m,[2,16]),e(_,[2,10]),{12:151,50:H,52:K,53:W},e(_,[2,12]),e(_,[2,13]),e(m,[2,18]),e(m,[2,34]),e(m,[2,35]),e(m,[2,36]),e(m,[2,37]),{15:[1,152]},e(m,[2,38]),{15:[1,153]},e(m,[2,39]),e(m,[2,40]),{15:[1,154]},e(m,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:ct},{56:158,81:ct},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:S},e(_,[2,11]),e(at,a,{7:103,40:160}),e(N,a,{7:105,42:161}),e(Xt,a,{7:108,45:162}),e(m,[2,48]),e(m,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:u(function(w,L){if(L.recoverable)this.trace(w);else{var P=new Error(w);throw P.hash=L,P}},"parseError"),parse:u(function(w){var L=this,P=[0],y=[],M=[null],l=[],yt=this.table,p="",Lt=0,Jt=0,Zt=0,Pe=2,Qt=1,Ae=l.slice.call(arguments,1),Y=Object.create(this.lexer),pt={yy:{}};for(var Dt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Dt)&&(pt.yy[Dt]=this.yy[Dt]);Y.setInput(w,pt.yy),pt.yy.lexer=Y,pt.yy.parser=this,typeof Y.yylloc>"u"&&(Y.yylloc={});var Rt=Y.yylloc;l.push(Rt);var Ne=Y.options&&Y.options.ranges;typeof pt.yy.parseError=="function"?this.parseError=pt.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function fs($){P.length=P.length-2*$,M.length=M.length-$,l.length=l.length-$}u(fs,"popStack");function Se(){var $;return $=y.pop()||Y.lex()||Qt,typeof $!="number"&&($ instanceof Array&&(y=$,$=y.pop()),$=L.symbols_[$]||$),$}u(Se,"lex");for(var U,Ct,ut,st,xs,Ot,bt={},_t,lt,jt,Pt;;){if(ut=P[P.length-1],this.defaultActions[ut]?st=this.defaultActions[ut]:((U===null||typeof U>"u")&&(U=Se()),st=yt[ut]&&yt[ut][U]),typeof st>"u"||!st.length||!st[0]){var Bt="";Pt=[];for(_t in yt[ut])this.terminals_[_t]&&_t>Pe&&Pt.push("'"+this.terminals_[_t]+"'");Y.showPosition?Bt="Parse error on line "+(Lt+1)+`:
`+Y.showPosition()+`
Expecting `+Pt.join(", ")+", got '"+(this.terminals_[U]||U)+"'":Bt="Parse error on line "+(Lt+1)+": Unexpected "+(U==Qt?"end of input":"'"+(this.terminals_[U]||U)+"'"),this.parseError(Bt,{text:Y.match,token:this.terminals_[U]||U,line:Y.yylineno,loc:Rt,expected:Pt})}if(st[0]instanceof Array&&st.length>1)throw new Error("Parse Error: multiple actions possible at state: "+ut+", token: "+U);switch(st[0]){case 1:P.push(U),M.push(Y.yytext),l.push(Y.yylloc),P.push(st[1]),U=null,Ct?(U=Ct,Ct=null):(Jt=Y.yyleng,p=Y.yytext,Lt=Y.yylineno,Rt=Y.yylloc,Zt>0&&Zt--);break;case 2:if(lt=this.productions_[st[1]][1],bt.$=M[M.length-lt],bt._$={first_line:l[l.length-(lt||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(lt||1)].first_column,last_column:l[l.length-1].last_column},Ne&&(bt._$.range=[l[l.length-(lt||1)].range[0],l[l.length-1].range[1]]),Ot=this.performAction.apply(bt,[p,Jt,Lt,pt.yy,st[1],M,l].concat(Ae)),typeof Ot<"u")return Ot;lt&&(P=P.slice(0,-1*lt*2),M=M.slice(0,-1*lt),l=l.slice(0,-1*lt)),P.push(this.productions_[st[1]][0]),M.push(bt.$),l.push(bt._$),jt=yt[P[P.length-2]][P[P.length-1]],P.push(jt);break;case 3:return!0}}return!0},"parse")},_e=function(){var ht={EOF:1,parseError:u(function(L,P){if(this.yy.parser)this.yy.parser.parseError(L,P);else throw new Error(L)},"parseError"),setInput:u(function(w,L){return this.yy=L||this.yy||{},this._input=w,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:u(function(){var w=this._input[0];this.yytext+=w,this.yyleng++,this.offset++,this.match+=w,this.matched+=w;var L=w.match(/(?:\r\n?|\n).*/g);return L?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),w},"input"),unput:u(function(w){var L=w.length,P=w.split(/(?:\r\n?|\n)/g);this._input=w+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-L),this.offset-=L;var y=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),P.length-1&&(this.yylineno-=P.length-1);var M=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:P?(P.length===y.length?this.yylloc.first_column:0)+y[y.length-P.length].length-P[0].length:this.yylloc.first_column-L},this.options.ranges&&(this.yylloc.range=[M[0],M[0]+this.yyleng-L]),this.yyleng=this.yytext.length,this},"unput"),more:u(function(){return this._more=!0,this},"more"),reject:u(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:u(function(w){this.unput(this.match.slice(w))},"less"),pastInput:u(function(){var w=this.matched.substr(0,this.matched.length-this.match.length);return(w.length>20?"...":"")+w.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:u(function(){var w=this.match;return w.length<20&&(w+=this._input.substr(0,20-w.length)),(w.substr(0,20)+(w.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:u(function(){var w=this.pastInput(),L=new Array(w.length+1).join("-");return w+this.upcomingInput()+`
`+L+"^"},"showPosition"),test_match:u(function(w,L){var P,y,M;if(this.options.backtrack_lexer&&(M={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(M.yylloc.range=this.yylloc.range.slice(0))),y=w[0].match(/(?:\r\n?|\n).*/g),y&&(this.yylineno+=y.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:y?y[y.length-1].length-y[y.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+w[0].length},this.yytext+=w[0],this.match+=w[0],this.matches=w,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(w[0].length),this.matched+=w[0],P=this.performAction.call(this,this.yy,this,L,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),P)return P;if(this._backtrack){for(var l in M)this[l]=M[l];return!1}return!1},"test_match"),next:u(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var w,L,P,y;this._more||(this.yytext="",this.match="");for(var M=this._currentRules(),l=0;l<M.length;l++)if(P=this._input.match(this.rules[M[l]]),P&&(!L||P[0].length>L[0].length)){if(L=P,y=l,this.options.backtrack_lexer){if(w=this.test_match(P,M[l]),w!==!1)return w;if(this._backtrack){L=!1;continue}else return!1}else if(!this.options.flex)break}return L?(w=this.test_match(L,M[y]),w!==!1?w:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:u(function(){var L=this.next();return L||this.lex()},"lex"),begin:u(function(L){this.conditionStack.push(L)},"begin"),popState:u(function(){var L=this.conditionStack.length-1;return L>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:u(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:u(function(L){return L=this.conditionStack.length-1-Math.abs(L||0),L>=0?this.conditionStack[L]:"INITIAL"},"topState"),pushState:u(function(L){this.begin(L)},"pushState"),stateStackSize:u(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:u(function(L,P,y,M){var l=M;switch(y){case 0:return 5;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;break;case 8:return this.begin("ID"),50;break;case 9:return this.begin("ID"),52;break;case 10:return 13;case 11:return this.begin("ID"),53;break;case 12:return P.yytext=P.yytext.trim(),this.begin("ALIAS"),70;break;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;break;case 14:return this.popState(),this.popState(),5;break;case 15:return this.begin("LINE"),36;break;case 16:return this.begin("LINE"),37;break;case 17:return this.begin("LINE"),38;break;case 18:return this.begin("LINE"),39;break;case 19:return this.begin("LINE"),49;break;case 20:return this.begin("LINE"),41;break;case 21:return this.begin("LINE"),43;break;case 22:return this.begin("LINE"),48;break;case 23:return this.begin("LINE"),44;break;case 24:return this.begin("LINE"),47;break;case 25:return this.begin("LINE"),46;break;case 26:return this.popState(),15;break;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;break;case 37:return this.begin("ID"),23;break;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;break;case 41:return this.popState(),"acc_title_value";break;case 42:return this.begin("acc_descr"),33;break;case 43:return this.popState(),"acc_descr_value";break;case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 51:return 5;case 52:return P.yytext=P.yytext.trim(),70;break;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 66:return 5;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}};return ht}();vt.lexer=_e;function Mt(){this.yy={}}return u(Mt,"Parser"),Mt.prototype=vt,vt.Parser=Mt,new Mt}();Ft.parser=Ft;var ge=Ft;var De={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},Re={FILLED:0,OPEN:1},Ce={LEFTOF:0,RIGHTOF:1,OVER:2},Nt=class{constructor(){this.state=new ue(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0}));this.setAccTitle=Yt;this.setAccDescription=re;this.setDiagramTitle=ie;this.getAccTitle=se;this.getAccDescription=ae;this.getDiagramTitle=ne;this.apply=this.apply.bind(this),this.parseBoxData=this.parseBoxData.bind(this),this.parseMessage=this.parseMessage.bind(this),this.clear(),this.setWrap(G().wrap),this.LINETYPE=De,this.ARROWTYPE=Re,this.PLACEMENT=Ce}static{u(this,"SequenceDB")}addBox(t){this.state.records.boxes.push({name:t.text,wrap:t.wrap??this.autoWrap(),fill:t.color,actorKeys:[]}),this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,c,s,a){let i=this.state.records.currentBox,n=this.state.records.actors.get(t);if(n){if(this.state.records.currentBox&&n.box&&this.state.records.currentBox!==n.box)throw new Error(`A same participant should only be defined in one Box: ${n.name} can't be in '${n.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`);if(i=n.box?n.box:this.state.records.currentBox,n.box=i,n&&c===n.name&&s==null)return}if(s?.text==null&&(s={text:c,type:a}),(a==null||s.text==null)&&(s={text:c,type:a}),this.state.records.actors.set(t,{box:i,name:c,description:s.text,wrap:s.wrap??this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:a??"participant"}),this.state.records.prevActor){let h=this.state.records.actors.get(this.state.records.prevActor);h&&(h.nextActor=t)}this.state.records.currentBox&&this.state.records.currentBox.actorKeys.push(t),this.state.records.prevActor=t}activationCount(t){let c,s=0;if(!t)return 0;for(c=0;c<this.state.records.messages.length;c++)this.state.records.messages[c].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[c].from===t&&s++,this.state.records.messages[c].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[c].from===t&&s--;return s}addMessage(t,c,s,a){this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:c,message:s.text,wrap:s.wrap??this.autoWrap(),answer:a})}addSignal(t,c,s,a,i=!1){if(a===this.LINETYPE.ACTIVE_END&&this.activationCount(t??"")<1){let h=new Error("Trying to inactivate an inactive participant ("+t+")");throw h.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},h}return this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:c,message:s?.text??"",wrap:s?.wrap??this.autoWrap(),type:a,activate:i}),!0}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some(t=>t.name)}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!0}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!1}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(t===void 0)return{};t=t.trim();let c=/^:?wrap:/.exec(t)!==null?!0:/^:?nowrap:/.exec(t)!==null?!1:void 0;return{cleanedText:(c===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:c}}autoWrap(){return this.state.records.wrapEnabled!==void 0?this.state.records.wrapEnabled:G().sequence?.wrap??!1}clear(){this.state.reset(),ee()}parseMessage(t){let c=t.trim(),{wrap:s,cleanedText:a}=this.extractWrap(c),i={text:a,wrap:s};return F.debug(`parseMessage: ${JSON.stringify(i)}`),i}parseBoxData(t){let c=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t),s=c?.[1]?c[1].trim():"transparent",a=c?.[2]?c[2].trim():void 0;if(window?.CSS)window.CSS.supports("color",s)||(s="transparent",a=t.trim());else{let h=new Option().style;h.color=s,h.color!==s&&(s="transparent",a=t.trim())}let{wrap:i,cleanedText:n}=this.extractWrap(a);return{text:n?Et(n,G()):void 0,color:s,wrap:i}}addNote(t,c,s){let a={actor:t,placement:c,message:s.text,wrap:s.wrap??this.autoWrap()},i=[].concat(t,t);this.state.records.notes.push(a),this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:i[0],to:i[1],message:s.text,wrap:s.wrap??this.autoWrap(),type:this.LINETYPE.NOTE,placement:c})}addLinks(t,c){let s=this.getActor(t);try{let a=Et(c.text,G());a=a.replace(/&equals;/g,"="),a=a.replace(/&amp;/g,"&");let i=JSON.parse(a);this.insertLinks(s,i)}catch(a){F.error("error while parsing actor link text",a)}}addALink(t,c){let s=this.getActor(t);try{let a={},i=Et(c.text,G()),n=i.indexOf("@");i=i.replace(/&equals;/g,"="),i=i.replace(/&amp;/g,"&");let h=i.slice(0,n-1).trim(),d=i.slice(n+1).trim();a[h]=d,this.insertLinks(s,a)}catch(a){F.error("error while parsing actor link text",a)}}insertLinks(t,c){if(t.links==null)t.links=c;else for(let s in c)t.links[s]=c[s]}addProperties(t,c){let s=this.getActor(t);try{let a=Et(c.text,G()),i=JSON.parse(a);this.insertProperties(s,i)}catch(a){F.error("error while parsing actor properties text",a)}}insertProperties(t,c){if(t.properties==null)t.properties=c;else for(let s in c)t.properties[s]=c[s]}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,c){let s=this.getActor(t),a=document.getElementById(c.text);try{let i=a.innerHTML,n=JSON.parse(i);n.properties&&this.insertProperties(s,n.properties),n.links&&this.insertLinks(s,n.links)}catch(i){F.error("error while parsing actor details text",i)}}getActorProperty(t,c){if(t?.properties!==void 0)return t.properties[c]}apply(t){if(Array.isArray(t))t.forEach(c=>{this.apply(c)});else switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");this.state.records.lastCreated=t.actor,this.addActor(t.actor,t.actor,t.description,t.draw),this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor,this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated)throw new Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");this.state.records.lastCreated=void 0}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed)throw new Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");this.state.records.lastDestroyed=void 0}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"rectEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"optEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"altStart":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"altEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":Yt(t.text);break;case"parStart":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"parEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break}}getConfig(){return G().sequence}};var Oe=u(e=>`.actor {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
  }

  text.actor > tspan {
    fill: ${e.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${e.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${e.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${e.signalColor};
  }

  #arrowhead path {
    fill: ${e.signalColor};
    stroke: ${e.signalColor};
  }

  .sequenceNumber {
    fill: ${e.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${e.signalColor};
  }

  #crosshead path {
    fill: ${e.signalColor};
    stroke: ${e.signalColor};
  }

  .messageText {
    fill: ${e.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${e.labelBoxBorderColor};
    fill: ${e.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${e.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${e.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${e.labelBoxBorderColor};
    fill: ${e.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${e.noteBorderColor};
    fill: ${e.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${e.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .activation1 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .activation2 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${e.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),fe=Oe;var qt=ve(Me(),1);var dt=18*2,be="actor-top",me="actor-bottom",Ve="actor-box",xe="actor-man",zt=u(function(e,t){return ce(e,t)},"drawRect"),Ye=u(function(e,t,c,s,a){if(t.links===void 0||t.links===null||Object.keys(t.links).length===0)return{height:0,width:0};let i=t.links,n=t.actorCnt,h=t.rectData;var d="none";a&&(d="block !important");let r=e.append("g");r.attr("id","actor"+n+"_popup"),r.attr("class","actorPopupMenu"),r.attr("display",d);var f="";h.class!==void 0&&(f=" "+h.class);let T=h.width>c?h.width:c,g=r.append("rect");if(g.attr("class","actorPopupMenuPanel"+f),g.attr("x",h.x),g.attr("y",h.height),g.attr("fill",h.fill),g.attr("stroke",h.stroke),g.attr("width",T),g.attr("height",h.height),g.attr("rx",h.rx),g.attr("ry",h.ry),i!=null){var b=20;for(let A in i){var E=r.append("a"),I=(0,qt.sanitizeUrl)(i[A]);E.attr("xlink:href",I),E.attr("target","_blank"),ss(s)(A,E,h.x+10,h.height+b,T,20,{class:"actor"},s),b+=30}}return g.attr("height",b),{height:h.height+b,width:T}},"drawPopup"),We=u(function(e){return"var pu = document.getElementById('"+e+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),It=u(async function(e,t,c=null){let s=e.append("foreignObject"),a=await Vt(t.text,At()),n=s.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(a).node().getBoundingClientRect();if(s.attr("height",Math.round(n.height)).attr("width",Math.round(n.width)),t.class==="noteText"){let h=e.node().firstChild;h.setAttribute("height",n.height+2*t.textMargin);let d=h.getBBox();s.attr("x",Math.round(d.x+d.width/2-n.width/2)).attr("y",Math.round(d.y+d.height/2-n.height/2))}else if(c){let{startx:h,stopx:d,starty:r}=c;if(h>d){let f=h;h=d,d=f}s.attr("x",Math.round(h+Math.abs(h-d)/2-n.width/2)),t.class==="loopText"?s.attr("y",Math.round(r)):s.attr("y",Math.round(r-n.height))}return[s]},"drawKatex"),ft=u(function(e,t){let c=0,s=0,a=t.text.split(k.lineBreakRegex),[i,n]=Wt(t.fontSize),h=[],d=0,r=u(()=>t.y,"yfunc");if(t.valign!==void 0&&t.textMargin!==void 0&&t.textMargin>0)switch(t.valign){case"top":case"start":r=u(()=>Math.round(t.y+t.textMargin),"yfunc");break;case"middle":case"center":r=u(()=>Math.round(t.y+(c+s+t.textMargin)/2),"yfunc");break;case"bottom":case"end":r=u(()=>Math.round(t.y+(c+s+2*t.textMargin)-t.textMargin),"yfunc");break}if(t.anchor!==void 0&&t.textMargin!==void 0&&t.width!==void 0)switch(t.anchor){case"left":case"start":t.x=Math.round(t.x+t.textMargin),t.anchor="start",t.dominantBaseline="middle",t.alignmentBaseline="middle";break;case"middle":case"center":t.x=Math.round(t.x+t.width/2),t.anchor="middle",t.dominantBaseline="middle",t.alignmentBaseline="middle";break;case"right":case"end":t.x=Math.round(t.x+t.width-t.textMargin),t.anchor="end",t.dominantBaseline="middle",t.alignmentBaseline="middle";break}for(let[f,T]of a.entries()){t.textMargin!==void 0&&t.textMargin===0&&i!==void 0&&(d=f*i);let g=e.append("text");g.attr("x",t.x),g.attr("y",r()),t.anchor!==void 0&&g.attr("text-anchor",t.anchor).attr("dominant-baseline",t.dominantBaseline).attr("alignment-baseline",t.alignmentBaseline),t.fontFamily!==void 0&&g.style("font-family",t.fontFamily),n!==void 0&&g.style("font-size",n),t.fontWeight!==void 0&&g.style("font-weight",t.fontWeight),t.fill!==void 0&&g.attr("fill",t.fill),t.class!==void 0&&g.attr("class",t.class),t.dy!==void 0?g.attr("dy",t.dy):d!==0&&g.attr("dy",d);let b=T||pe;if(t.tspan){let E=g.append("tspan");E.attr("x",t.x),t.fill!==void 0&&E.attr("fill",t.fill),E.text(b)}else g.text(b);t.valign!==void 0&&t.textMargin!==void 0&&t.textMargin>0&&(s+=(g._groups||g)[0][0].getBBox().height,c=s),h.push(g)}return h},"drawText"),Te=u(function(e,t){function c(a,i,n,h,d){return a+","+i+" "+(a+n)+","+i+" "+(a+n)+","+(i+h-d)+" "+(a+n-d*1.2)+","+(i+h)+" "+a+","+(i+h)}u(c,"genPoints");let s=e.append("polygon");return s.attr("points",c(t.x,t.y,t.width,t.height,7)),s.attr("class","labelBox"),t.y=t.y+t.height/2,ft(e,t),s},"drawLabel"),nt=-1,Ht=u((e,t,c,s)=>{e.select&&c.forEach(a=>{let i=t.get(a),n=e.select("#actor"+i.actorCnt);!s.mirrorActors&&i.stopy?n.attr("y2",i.stopy+i.height/2):s.mirrorActors&&n.attr("y2",i.stopy)})},"fixLifeLineHeights"),Fe=u(function(e,t,c,s){let a=s?t.stopy:t.starty,i=t.x+t.width/2,n=a+t.height,h=e.append("g").lower();var d=h;s||(nt++,Object.keys(t.links||{}).length&&!c.forceMenus&&d.attr("onclick",We(`actor${nt}_popup`)).attr("cursor","pointer"),d.append("line").attr("id","actor"+nt).attr("x1",i).attr("y1",n).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",t.name),d=h.append("g"),t.actorCnt=nt,t.links!=null&&d.attr("id","root-"+nt));let r=mt();var f="actor";t.properties?.class?f=t.properties.class:r.fill="#eaeaea",s?f+=` ${me}`:f+=` ${be}`,r.x=t.x,r.y=a,r.width=t.width,r.height=t.height,r.class=f,r.rx=3,r.ry=3,r.name=t.name;let T=zt(d,r);if(t.rectData=r,t.properties?.icon){let b=t.properties.icon.trim();b.charAt(0)==="@"?de(d,r.x+r.width-20,r.y+10,b.substr(1)):he(d,r.x+r.width-20,r.y+10,b)}Kt(c,rt(t.description))(t.description,d,r.x,r.y,r.width,r.height,{class:`actor ${Ve}`},c);let g=t.height;if(T.node){let b=T.node().getBBox();t.height=b.height,g=b.height}return g},"drawActorTypeParticipant"),qe=u(function(e,t,c,s){let a=s?t.stopy:t.starty,i=t.x+t.width/2,n=a+80,h=e.append("g").lower();s||(nt++,h.append("line").attr("id","actor"+nt).attr("x1",i).attr("y1",n).attr("x2",i).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",t.name),t.actorCnt=nt);let d=e.append("g"),r=xe;s?r+=` ${me}`:r+=` ${be}`,d.attr("class",r),d.attr("name",t.name);let f=mt();f.x=t.x,f.y=a,f.fill="#eaeaea",f.width=t.width,f.height=t.height,f.class="actor",f.rx=3,f.ry=3,d.append("line").attr("id","actor-man-torso"+nt).attr("x1",i).attr("y1",a+25).attr("x2",i).attr("y2",a+45),d.append("line").attr("id","actor-man-arms"+nt).attr("x1",i-dt/2).attr("y1",a+33).attr("x2",i+dt/2).attr("y2",a+33),d.append("line").attr("x1",i-dt/2).attr("y1",a+60).attr("x2",i).attr("y2",a+45),d.append("line").attr("x1",i).attr("y1",a+45).attr("x2",i+dt/2-2).attr("y2",a+60);let T=d.append("circle");T.attr("cx",t.x+t.width/2),T.attr("cy",a+10),T.attr("r",15),T.attr("width",t.width),T.attr("height",t.height);let g=d.node().getBBox();return t.height=g.height,Kt(c,rt(t.description))(t.description,d,f.x,f.y+35,f.width,f.height,{class:`actor ${xe}`},c),t.height},"drawActorTypeActor"),ze=u(async function(e,t,c,s){switch(t.type){case"actor":return await qe(e,t,c,s);case"participant":return await Fe(e,t,c,s)}},"drawActor"),He=u(function(e,t,c){let a=e.append("g");ye(a,t),t.name&&Kt(c)(t.name,a,t.x,t.y+(t.textMaxHeight||0)/2,t.width,0,{class:"text"},c),a.lower()},"drawBox"),Ke=u(function(e){return e.append("g")},"anchorElement"),Ue=u(function(e,t,c,s,a){let i=mt(),n=t.anchored;i.x=t.startx,i.y=t.starty,i.class="activation"+a%3,i.width=t.stopx-t.startx,i.height=c-t.starty,zt(n,i)},"drawActivation"),Ge=u(async function(e,t,c,s){let{boxMargin:a,boxTextMargin:i,labelBoxHeight:n,labelBoxWidth:h,messageFontFamily:d,messageFontSize:r,messageFontWeight:f}=s,T=e.append("g"),g=u(function(I,A,O,v){return T.append("line").attr("x1",I).attr("y1",A).attr("x2",O).attr("y2",v).attr("class","loopLine")},"drawLoopLine");g(t.startx,t.starty,t.stopx,t.starty),g(t.stopx,t.starty,t.stopx,t.stopy),g(t.startx,t.stopy,t.stopx,t.stopy),g(t.startx,t.starty,t.startx,t.stopy),t.sections!==void 0&&t.sections.forEach(function(I){g(t.startx,I.y,t.stopx,I.y).style("stroke-dasharray","3, 3")});let b=kt();b.text=c,b.x=t.startx,b.y=t.starty,b.fontFamily=d,b.fontSize=r,b.fontWeight=f,b.anchor="middle",b.valign="middle",b.tspan=!1,b.width=h||50,b.height=n||20,b.textMargin=i,b.class="labelText",Te(T,b),b=Ee(),b.text=t.title,b.x=t.startx+h/2+(t.stopx-t.startx)/2,b.y=t.starty+a+i,b.anchor="middle",b.valign="middle",b.textMargin=i,b.class="loopText",b.fontFamily=d,b.fontSize=r,b.fontWeight=f,b.wrap=!0;let E=rt(b.text)?await It(T,b,t):ft(T,b);if(t.sectionTitles!==void 0){for(let[I,A]of Object.entries(t.sectionTitles))if(A.message){b.text=A.message,b.x=t.startx+(t.stopx-t.startx)/2,b.y=t.sections[I].y+a+i,b.class="loopText",b.anchor="middle",b.valign="middle",b.tspan=!1,b.fontFamily=d,b.fontSize=r,b.fontWeight=f,b.wrap=t.wrap,rt(b.text)?(t.starty=t.sections[I].y,await It(T,b,t)):ft(T,b);let O=Math.round(E.map(v=>(v._groups||v)[0][0].getBBox().height).reduce((v,B)=>v+B));t.sections[I].height+=O-(a+i)}}return t.height=Math.round(t.stopy-t.starty),T},"drawLoop"),ye=u(function(e,t){le(e,t)},"drawBackgroundRect"),Xe=u(function(e){e.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),Je=u(function(e){e.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),Ze=u(function(e){e.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),Qe=u(function(e){e.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),je=u(function(e){e.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),$e=u(function(e){e.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),ts=u(function(e){e.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),Ee=u(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),es=u(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),Kt=function(){function e(i,n,h,d,r,f,T){let g=n.append("text").attr("x",h+r/2).attr("y",d+f/2+5).style("text-anchor","middle").text(i);a(g,T)}u(e,"byText");function t(i,n,h,d,r,f,T,g){let{actorFontSize:b,actorFontFamily:E,actorFontWeight:I}=g,[A,O]=Wt(b),v=i.split(k.lineBreakRegex);for(let B=0;B<v.length;B++){let R=B*A-A*(v.length-1)/2,q=n.append("text").attr("x",h+r/2).attr("y",d).style("text-anchor","middle").style("font-size",O).style("font-weight",I).style("font-family",E);q.append("tspan").attr("x",h+r/2).attr("dy",R).text(v[B]),q.attr("y",d+f/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),a(q,T)}}u(t,"byTspan");function c(i,n,h,d,r,f,T,g){let b=n.append("switch"),I=b.append("foreignObject").attr("x",h).attr("y",d).attr("width",r).attr("height",f).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");I.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),t(i,b,h,d,r,f,T,g),a(I,T)}u(c,"byFo");async function s(i,n,h,d,r,f,T,g){let b=await gt(i,At()),E=n.append("switch"),A=E.append("foreignObject").attr("x",h+r/2-b.width/2).attr("y",d+f/2-b.height/2).attr("width",b.width).attr("height",b.height).append("xhtml:div").style("height","100%").style("width","100%");A.append("div").style("text-align","center").style("vertical-align","middle").html(await Vt(i,At())),t(i,E,h,d,r,f,T,g),a(A,T)}u(s,"byKatex");function a(i,n){for(let h in n)n.hasOwnProperty(h)&&i.attr(h,n[h])}return u(a,"_setTextAttrs"),function(i,n=!1){return n?s:i.textPlacement==="fo"?c:i.textPlacement==="old"?e:t}}(),ss=function(){function e(a,i,n,h,d,r,f){let T=i.append("text").attr("x",n).attr("y",h).style("text-anchor","start").text(a);s(T,f)}u(e,"byText");function t(a,i,n,h,d,r,f,T){let{actorFontSize:g,actorFontFamily:b,actorFontWeight:E}=T,I=a.split(k.lineBreakRegex);for(let A=0;A<I.length;A++){let O=A*g-g*(I.length-1)/2,v=i.append("text").attr("x",n).attr("y",h).style("text-anchor","start").style("font-size",g).style("font-weight",E).style("font-family",b);v.append("tspan").attr("x",n).attr("dy",O).text(I[A]),v.attr("y",h+r/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(v,f)}}u(t,"byTspan");function c(a,i,n,h,d,r,f,T){let g=i.append("switch"),E=g.append("foreignObject").attr("x",n).attr("y",h).attr("width",d).attr("height",r).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");E.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(a),t(a,g,n,h,d,r,f,T),s(E,f)}u(c,"byFo");function s(a,i){for(let n in i)i.hasOwnProperty(n)&&a.attr(n,i[n])}return u(s,"_setTextAttrs"),function(a){return a.textPlacement==="fo"?c:a.textPlacement==="old"?e:t}}(),C={drawRect:zt,drawText:ft,drawLabel:Te,drawActor:ze,drawBox:He,drawPopup:Ye,anchorElement:Ke,drawActivation:Ue,drawLoop:Ge,drawBackgroundRect:ye,insertArrowHead:Qe,insertArrowFilledHead:je,insertSequenceNumber:$e,insertArrowCrossHead:ts,insertDatabaseIcon:Xe,insertComputerIcon:Je,insertClockIcon:Ze,getTextObj:Ee,getNoteRect:es,fixLifeLineHeights:Ht,sanitizeUrl:qt.sanitizeUrl};var o={},x={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:u(function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(e=>e.height||0))+(this.loops.length===0?0:this.loops.map(e=>e.height||0).reduce((e,t)=>e+t))+(this.messages.length===0?0:this.messages.map(e=>e.height||0).reduce((e,t)=>e+t))+(this.notes.length===0?0:this.notes.map(e=>e.height||0).reduce((e,t)=>e+t))},"getHeight"),clear:u(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:u(function(e){this.boxes.push(e)},"addBox"),addActor:u(function(e){this.actors.push(e)},"addActor"),addLoop:u(function(e){this.loops.push(e)},"addLoop"),addMessage:u(function(e){this.messages.push(e)},"addMessage"),addNote:u(function(e){this.notes.push(e)},"addNote"),lastActor:u(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:u(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:u(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:u(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:u(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,Ie(G())},"init"),updateVal:u(function(e,t,c,s){e[t]===void 0?e[t]=c:e[t]=s(c,e[t])},"updateVal"),updateBounds:u(function(e,t,c,s){let a=this,i=0;function n(h){return u(function(r){i++;let f=a.sequenceItems.length-i+1;a.updateVal(r,"starty",t-f*o.boxMargin,Math.min),a.updateVal(r,"stopy",s+f*o.boxMargin,Math.max),a.updateVal(x.data,"startx",e-f*o.boxMargin,Math.min),a.updateVal(x.data,"stopx",c+f*o.boxMargin,Math.max),h!=="activation"&&(a.updateVal(r,"startx",e-f*o.boxMargin,Math.min),a.updateVal(r,"stopx",c+f*o.boxMargin,Math.max),a.updateVal(x.data,"starty",t-f*o.boxMargin,Math.min),a.updateVal(x.data,"stopy",s+f*o.boxMargin,Math.max))},"updateItemBounds")}u(n,"updateFn"),this.sequenceItems.forEach(n()),this.activations.forEach(n("activation"))},"updateBounds"),insert:u(function(e,t,c,s){let a=k.getMin(e,c),i=k.getMax(e,c),n=k.getMin(t,s),h=k.getMax(t,s);this.updateVal(x.data,"startx",a,Math.min),this.updateVal(x.data,"starty",n,Math.min),this.updateVal(x.data,"stopx",i,Math.max),this.updateVal(x.data,"stopy",h,Math.max),this.updateBounds(a,n,i,h)},"insert"),newActivation:u(function(e,t,c){let s=c.get(e.from),a=St(e.from).length||0,i=s.x+s.width/2+(a-1)*o.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+o.activationWidth,stopy:void 0,actor:e.from,anchored:C.anchorElement(t)})},"newActivation"),endActivation:u(function(e){let t=this.activations.map(function(c){return c.actor}).lastIndexOf(e.from);return this.activations.splice(t,1)[0]},"endActivation"),createLoop:u(function(e={message:void 0,wrap:!1,width:void 0},t){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:e.message,wrap:e.wrap,width:e.width,height:0,fill:t}},"createLoop"),newLoop:u(function(e={message:void 0,wrap:!1,width:void 0},t){this.sequenceItems.push(this.createLoop(e,t))},"newLoop"),endLoop:u(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:u(function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:!1},"isLoopOverlap"),addSectionToLoop:u(function(e){let t=this.sequenceItems.pop();t.sections=t.sections||[],t.sectionTitles=t.sectionTitles||[],t.sections.push({y:x.getVerticalPos(),height:0}),t.sectionTitles.push(e),this.sequenceItems.push(t)},"addSectionToLoop"),saveVerticalPos:u(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:u(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:u(function(e){this.verticalPos=this.verticalPos+e,this.data.stopy=k.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:u(function(){return this.verticalPos},"getVerticalPos"),getBounds:u(function(){return{bounds:this.data,models:this.models}},"getBounds")},rs=u(async function(e,t){x.bumpVerticalPos(o.boxMargin),t.height=o.boxMargin,t.starty=x.getVerticalPos();let c=mt();c.x=t.startx,c.y=t.starty,c.width=t.width||o.width,c.class="note";let s=e.append("g"),a=C.drawRect(s,c),i=kt();i.x=t.startx,i.y=t.starty,i.width=c.width,i.dy="1em",i.text=t.message,i.class="noteText",i.fontFamily=o.noteFontFamily,i.fontSize=o.noteFontSize,i.fontWeight=o.noteFontWeight,i.anchor=o.noteAlign,i.textMargin=o.noteMargin,i.valign="center";let n=rt(i.text)?await It(s,i):ft(s,i),h=Math.round(n.map(d=>(d._groups||d)[0][0].getBBox().height).reduce((d,r)=>d+r));a.attr("height",h+2*o.noteMargin),t.height+=h+2*o.noteMargin,x.bumpVerticalPos(h+2*o.noteMargin),t.stopy=t.starty+h+2*o.noteMargin,t.stopx=t.startx+c.width,x.insert(t.startx,t.starty,t.stopx,t.stopy),x.models.addNote(t)},"drawNote"),xt=u(e=>({fontFamily:e.messageFontFamily,fontSize:e.messageFontSize,fontWeight:e.messageFontWeight}),"messageFont"),Tt=u(e=>({fontFamily:e.noteFontFamily,fontSize:e.noteFontSize,fontWeight:e.noteFontWeight}),"noteFont"),Ut=u(e=>({fontFamily:e.actorFontFamily,fontSize:e.actorFontSize,fontWeight:e.actorFontWeight}),"actorFont");async function as(e,t){x.bumpVerticalPos(10);let{startx:c,stopx:s,message:a}=t,i=k.splitBreaks(a).length,n=rt(a),h=n?await gt(a,G()):V.calculateTextDimensions(a,xt(o));if(!n){let T=h.height/i;t.height+=T,x.bumpVerticalPos(T)}let d,r=h.height-10,f=h.width;if(c===s){d=x.getVerticalPos()+r,o.rightAngles||(r+=o.boxMargin,d=x.getVerticalPos()+r),r+=30;let T=k.getMax(f/2,o.width/2);x.insert(c-T,x.getVerticalPos()-10+r,s+T,x.getVerticalPos()+30+r)}else r+=o.boxMargin,d=x.getVerticalPos()+r,x.insert(c,d-10,s,d);return x.bumpVerticalPos(r),t.height+=r,t.stopy=t.starty+t.height,x.insert(t.fromBounds,t.starty,t.toBounds,t.stopy),d}u(as,"boundMessage");var is=u(async function(e,t,c,s){let{startx:a,stopx:i,starty:n,message:h,type:d,sequenceIndex:r,sequenceVisible:f}=t,T=V.calculateTextDimensions(h,xt(o)),g=kt();g.x=a,g.y=n+10,g.width=i-a,g.class="messageText",g.dy="1em",g.text=h,g.fontFamily=o.messageFontFamily,g.fontSize=o.messageFontSize,g.fontWeight=o.messageFontWeight,g.anchor=o.messageAlign,g.valign="center",g.textMargin=o.wrapPadding,g.tspan=!1,rt(g.text)?await It(e,g,{startx:a,stopx:i,starty:c}):ft(e,g);let b=T.width,E;a===i?o.rightAngles?E=e.append("path").attr("d",`M  ${a},${c} H ${a+k.getMax(o.width/2,b/2)} V ${c+25} H ${a}`):E=e.append("path").attr("d","M "+a+","+c+" C "+(a+60)+","+(c-10)+" "+(a+60)+","+(c+30)+" "+a+","+(c+20)):(E=e.append("line"),E.attr("x1",a),E.attr("y1",c),E.attr("x2",i),E.attr("y2",c)),d===s.db.LINETYPE.DOTTED||d===s.db.LINETYPE.DOTTED_CROSS||d===s.db.LINETYPE.DOTTED_POINT||d===s.db.LINETYPE.DOTTED_OPEN||d===s.db.LINETYPE.BIDIRECTIONAL_DOTTED?(E.style("stroke-dasharray","3, 3"),E.attr("class","messageLine1")):E.attr("class","messageLine0");let I="";o.arrowMarkerAbsolute&&(I=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,I=I.replace(/\(/g,"\\("),I=I.replace(/\)/g,"\\)")),E.attr("stroke-width",2),E.attr("stroke","none"),E.style("fill","none"),(d===s.db.LINETYPE.SOLID||d===s.db.LINETYPE.DOTTED)&&E.attr("marker-end","url("+I+"#arrowhead)"),(d===s.db.LINETYPE.BIDIRECTIONAL_SOLID||d===s.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(E.attr("marker-start","url("+I+"#arrowhead)"),E.attr("marker-end","url("+I+"#arrowhead)")),(d===s.db.LINETYPE.SOLID_POINT||d===s.db.LINETYPE.DOTTED_POINT)&&E.attr("marker-end","url("+I+"#filled-head)"),(d===s.db.LINETYPE.SOLID_CROSS||d===s.db.LINETYPE.DOTTED_CROSS)&&E.attr("marker-end","url("+I+"#crosshead)"),(f||o.showSequenceNumbers)&&(E.attr("marker-start","url("+I+"#sequencenumber)"),e.append("text").attr("x",a).attr("y",c+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(r))},"drawMessage"),ns=u(function(e,t,c,s,a,i,n){let h=0,d=0,r,f=0;for(let T of s){let g=t.get(T),b=g.box;r&&r!=b&&(n||x.models.addBox(r),d+=o.boxMargin+r.margin),b&&b!=r&&(n||(b.x=h+d,b.y=a),d+=b.margin),g.width=g.width||o.width,g.height=k.getMax(g.height||o.height,o.height),g.margin=g.margin||o.actorMargin,f=k.getMax(f,g.height),c.get(g.name)&&(d+=g.width/2),g.x=h+d,g.starty=x.getVerticalPos(),x.insert(g.x,a,g.x+g.width,g.height),h+=g.width+d,g.box&&(g.box.width=h+b.margin-g.box.x),d=g.margin,r=g.box,x.models.addActor(g)}r&&!n&&x.models.addBox(r),x.bumpVerticalPos(f)},"addActorRenderingData"),Gt=u(async function(e,t,c,s){if(s){let a=0;x.bumpVerticalPos(o.boxMargin*2);for(let i of c){let n=t.get(i);n.stopy||(n.stopy=x.getVerticalPos());let h=await C.drawActor(e,n,o,!0);a=k.getMax(a,h)}x.bumpVerticalPos(a+o.boxMargin)}else for(let a of c){let i=t.get(a);await C.drawActor(e,i,o,!1)}},"drawActors"),ke=u(function(e,t,c,s){let a=0,i=0;for(let n of c){let h=t.get(n),d=hs(h),r=C.drawPopup(e,h,d,o,o.forceMenus,s);r.height>a&&(a=r.height),r.width+h.x>i&&(i=r.width+h.x)}return{maxHeight:a,maxWidth:i}},"drawActorsPopup"),Ie=u(function(e){$t(o,e),e.fontFamily&&(o.actorFontFamily=o.noteFontFamily=o.messageFontFamily=e.fontFamily),e.fontSize&&(o.actorFontSize=o.noteFontSize=o.messageFontSize=e.fontSize),e.fontWeight&&(o.actorFontWeight=o.noteFontWeight=o.messageFontWeight=e.fontWeight)},"setConf"),St=u(function(e){return x.activations.filter(function(t){return t.actor===e})},"actorActivations"),we=u(function(e,t){let c=t.get(e),s=St(e),a=s.reduce(function(n,h){return k.getMin(n,h.startx)},c.x+c.width/2-1),i=s.reduce(function(n,h){return k.getMax(n,h.stopx)},c.x+c.width/2+1);return[a,i]},"activationBounds");function ot(e,t,c,s,a){x.bumpVerticalPos(c);let i=s;if(t.id&&t.message&&e[t.id]){let n=e[t.id].width,h=xt(o);t.message=V.wrapLabel(`[${t.message}]`,n-2*o.wrapPadding,h),t.width=n,t.wrap=!0;let d=V.calculateTextDimensions(t.message,h),r=k.getMax(d.height,o.labelBoxHeight);i=s+r,F.debug(`${r} - ${t.message}`)}a(t),x.bumpVerticalPos(i)}u(ot,"adjustLoopHeightForWrap");function os(e,t,c,s,a,i,n){function h(r,f){r.x<a.get(e.from).x?(x.insert(t.stopx-f,t.starty,t.startx,t.stopy+r.height/2+o.noteMargin),t.stopx=t.stopx+f):(x.insert(t.startx,t.starty,t.stopx+f,t.stopy+r.height/2+o.noteMargin),t.stopx=t.stopx-f)}u(h,"receiverAdjustment");function d(r,f){r.x<a.get(e.to).x?(x.insert(t.startx-f,t.starty,t.stopx,t.stopy+r.height/2+o.noteMargin),t.startx=t.startx+f):(x.insert(t.stopx,t.starty,t.startx+f,t.stopy+r.height/2+o.noteMargin),t.startx=t.startx-f)}if(u(d,"senderAdjustment"),i.get(e.to)==s){let r=a.get(e.to),f=r.type=="actor"?dt/2+3:r.width/2+3;h(r,f),r.starty=c-r.height/2,x.bumpVerticalPos(r.height/2)}else if(n.get(e.from)==s){let r=a.get(e.from);if(o.mirrorActors){let f=r.type=="actor"?dt/2:r.width/2;d(r,f)}r.stopy=c-r.height/2,x.bumpVerticalPos(r.height/2)}else if(n.get(e.to)==s){let r=a.get(e.to);if(o.mirrorActors){let f=r.type=="actor"?dt/2+3:r.width/2+3;h(r,f)}r.stopy=c-r.height/2,x.bumpVerticalPos(r.height/2)}}u(os,"adjustCreatedDestroyedData");var cs=u(async function(e,t,c,s){let{securityLevel:a,sequence:i}=G();o=i;let n;a==="sandbox"&&(n=wt("#i"+t));let h=a==="sandbox"?wt(n.nodes()[0].contentDocument.body):wt("body"),d=a==="sandbox"?n.nodes()[0].contentDocument:document;x.init(),F.debug(s.db);let r=a==="sandbox"?h.select(`[id="${t}"]`):wt(`[id="${t}"]`),f=s.db.getActors(),T=s.db.getCreatedActors(),g=s.db.getDestroyedActors(),b=s.db.getBoxes(),E=s.db.getActorKeys(),I=s.db.getMessages(),A=s.db.getDiagramTitle(),O=s.db.hasAtLeastOneBox(),v=s.db.hasAtLeastOneBoxWithTitle(),B=await ls(f,I,s);if(o.height=await ds(f,B,b),C.insertComputerIcon(r),C.insertDatabaseIcon(r),C.insertClockIcon(r),O&&(x.bumpVerticalPos(o.boxMargin),v&&x.bumpVerticalPos(b[0].textMaxHeight)),o.hideUnusedParticipants===!0){let m=new Set;I.forEach(_=>{m.add(_.from),m.add(_.to)}),E=E.filter(_=>m.has(_))}ns(r,f,T,E,0,I,!1);let R=await gs(I,f,B,s);C.insertArrowHead(r),C.insertArrowCrossHead(r),C.insertArrowFilledHead(r),C.insertSequenceNumber(r);function q(m,_){let j=x.endActivation(m);j.starty+18>_&&(j.starty=_-6,_+=12),C.drawActivation(r,j,_,o,St(m.from).length),x.insert(j.startx,_-10,j.stopx,_)}u(q,"activeEnd");let z=1,J=1,tt=[],H=[],K=0;for(let m of I){let _,j,at;switch(m.type){case s.db.LINETYPE.NOTE:x.resetVerticalPos(),j=m.noteModel,await rs(r,j);break;case s.db.LINETYPE.ACTIVE_START:x.newActivation(m,r,f);break;case s.db.LINETYPE.ACTIVE_END:q(m,x.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:ot(R,m,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.LOOP_END:_=x.endLoop(),await C.drawLoop(r,_,"loop",o),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.RECT_START:ot(R,m,o.boxMargin,o.boxMargin,N=>x.newLoop(void 0,N.message));break;case s.db.LINETYPE.RECT_END:_=x.endLoop(),H.push(_),x.models.addLoop(_),x.bumpVerticalPos(_.stopy-x.getVerticalPos());break;case s.db.LINETYPE.OPT_START:ot(R,m,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.OPT_END:_=x.endLoop(),await C.drawLoop(r,_,"opt",o),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.ALT_START:ot(R,m,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.ALT_ELSE:ot(R,m,o.boxMargin+o.boxTextMargin,o.boxMargin,N=>x.addSectionToLoop(N));break;case s.db.LINETYPE.ALT_END:_=x.endLoop(),await C.drawLoop(r,_,"alt",o),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:ot(R,m,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N)),x.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:ot(R,m,o.boxMargin+o.boxTextMargin,o.boxMargin,N=>x.addSectionToLoop(N));break;case s.db.LINETYPE.PAR_END:_=x.endLoop(),await C.drawLoop(r,_,"par",o),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.AUTONUMBER:z=m.message.start||z,J=m.message.step||J,m.message.visible?s.db.enableSequenceNumbers():s.db.disableSequenceNumbers();break;case s.db.LINETYPE.CRITICAL_START:ot(R,m,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.CRITICAL_OPTION:ot(R,m,o.boxMargin+o.boxTextMargin,o.boxMargin,N=>x.addSectionToLoop(N));break;case s.db.LINETYPE.CRITICAL_END:_=x.endLoop(),await C.drawLoop(r,_,"critical",o),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;case s.db.LINETYPE.BREAK_START:ot(R,m,o.boxMargin,o.boxMargin+o.boxTextMargin,N=>x.newLoop(N));break;case s.db.LINETYPE.BREAK_END:_=x.endLoop(),await C.drawLoop(r,_,"break",o),x.bumpVerticalPos(_.stopy-x.getVerticalPos()),x.models.addLoop(_);break;default:try{at=m.msgModel,at.starty=x.getVerticalPos(),at.sequenceIndex=z,at.sequenceVisible=s.db.showSequenceNumbers();let N=await as(r,at);os(m,at,N,K,f,T,g),tt.push({messageModel:at,lineStartY:N}),x.models.addMessage(at)}catch(N){F.error("error while drawing message",N)}}[s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT,s.db.LINETYPE.BIDIRECTIONAL_SOLID,s.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(m.type)&&(z=z+J),K++}F.debug("createdActors",T),F.debug("destroyedActors",g),await Gt(r,f,E,!1);for(let m of tt)await is(r,m.messageModel,m.lineStartY,s);o.mirrorActors&&await Gt(r,f,E,!0),H.forEach(m=>C.drawBackgroundRect(r,m)),Ht(r,f,E,o);for(let m of x.models.boxes)m.height=x.getVerticalPos()-m.y,x.insert(m.x,m.y,m.x+m.width,m.height),m.startx=m.x,m.starty=m.y,m.stopx=m.startx+m.width,m.stopy=m.starty+m.height,m.stroke="rgb(0,0,0, 0.5)",C.drawBox(r,m,o);O&&x.bumpVerticalPos(o.boxMargin);let W=ke(r,f,E,d),{bounds:D}=x.getBounds();D.startx===void 0&&(D.startx=0),D.starty===void 0&&(D.starty=0),D.stopx===void 0&&(D.stopx=0),D.stopy===void 0&&(D.stopy=0);let Z=D.stopy-D.starty;Z<W.maxHeight&&(Z=W.maxHeight);let X=Z+2*o.diagramMarginY;o.mirrorActors&&(X=X-o.boxMargin+o.bottomMarginAdj);let Q=D.stopx-D.startx;Q<W.maxWidth&&(Q=W.maxWidth);let et=Q+2*o.diagramMarginX;A&&r.append("text").text(A).attr("x",(D.stopx-D.startx)/2-2*o.diagramMarginX).attr("y",-25),te(r,X,et,o.useMaxWidth);let S=A?40:0;r.attr("viewBox",D.startx-o.diagramMarginX+" -"+(o.diagramMarginY+S)+" "+et+" "+(X+S)),F.debug("models:",x.models)},"draw");async function ls(e,t,c){let s={};for(let a of t)if(e.get(a.to)&&e.get(a.from)){let i=e.get(a.to);if(a.placement===c.db.PLACEMENT.LEFTOF&&!i.prevActor||a.placement===c.db.PLACEMENT.RIGHTOF&&!i.nextActor)continue;let n=a.placement!==void 0,h=!n,d=n?Tt(o):xt(o),r=a.wrap?V.wrapLabel(a.message,o.width-2*o.wrapPadding,d):a.message,T=(rt(r)?await gt(a.message,G()):V.calculateTextDimensions(r,d)).width+2*o.wrapPadding;h&&a.from===i.nextActor?s[a.to]=k.getMax(s[a.to]||0,T):h&&a.from===i.prevActor?s[a.from]=k.getMax(s[a.from]||0,T):h&&a.from===a.to?(s[a.from]=k.getMax(s[a.from]||0,T/2),s[a.to]=k.getMax(s[a.to]||0,T/2)):a.placement===c.db.PLACEMENT.RIGHTOF?s[a.from]=k.getMax(s[a.from]||0,T):a.placement===c.db.PLACEMENT.LEFTOF?s[i.prevActor]=k.getMax(s[i.prevActor]||0,T):a.placement===c.db.PLACEMENT.OVER&&(i.prevActor&&(s[i.prevActor]=k.getMax(s[i.prevActor]||0,T/2)),i.nextActor&&(s[a.from]=k.getMax(s[a.from]||0,T/2)))}return F.debug("maxMessageWidthPerActor:",s),s}u(ls,"getMaxMessageWidthPerActor");var hs=u(function(e){let t=0,c=Ut(o);for(let s in e.links){let i=V.calculateTextDimensions(s,c).width+2*o.wrapPadding+2*o.boxMargin;t<i&&(t=i)}return t},"getRequiredPopupWidth");async function ds(e,t,c){let s=0;for(let i of e.keys()){let n=e.get(i);n.wrap&&(n.description=V.wrapLabel(n.description,o.width-2*o.wrapPadding,Ut(o)));let h=rt(n.description)?await gt(n.description,G()):V.calculateTextDimensions(n.description,Ut(o));n.width=n.wrap?o.width:k.getMax(o.width,h.width+2*o.wrapPadding),n.height=n.wrap?k.getMax(h.height,o.height):o.height,s=k.getMax(s,n.height)}for(let i in t){let n=e.get(i);if(!n)continue;let h=e.get(n.nextActor);if(!h){let T=t[i]+o.actorMargin-n.width/2;n.margin=k.getMax(T,o.actorMargin);continue}let r=t[i]+o.actorMargin-n.width/2-h.width/2;n.margin=k.getMax(r,o.actorMargin)}let a=0;return c.forEach(i=>{let n=xt(o),h=i.actorKeys.reduce((f,T)=>f+=e.get(T).width+(e.get(T).margin||0),0);h-=2*o.boxTextMargin,i.wrap&&(i.name=V.wrapLabel(i.name,h-2*o.wrapPadding,n));let d=V.calculateTextDimensions(i.name,n);a=k.getMax(d.height,a);let r=k.getMax(h,d.width+2*o.wrapPadding);if(i.margin=o.boxTextMargin,h<r){let f=(r-h)/2;i.margin+=f}}),c.forEach(i=>i.textMaxHeight=a),k.getMax(s,o.height)}u(ds,"calculateActorMargins");var ps=u(async function(e,t,c){let s=t.get(e.from),a=t.get(e.to),i=s.x,n=a.x,h=e.wrap&&e.message,d=rt(e.message)?await gt(e.message,G()):V.calculateTextDimensions(h?V.wrapLabel(e.message,o.width,Tt(o)):e.message,Tt(o)),r={width:h?o.width:k.getMax(o.width,d.width+2*o.noteMargin),height:0,startx:s.x,stopx:0,starty:0,stopy:0,message:e.message};return e.placement===c.db.PLACEMENT.RIGHTOF?(r.width=h?k.getMax(o.width,d.width):k.getMax(s.width/2+a.width/2,d.width+2*o.noteMargin),r.startx=i+(s.width+o.actorMargin)/2):e.placement===c.db.PLACEMENT.LEFTOF?(r.width=h?k.getMax(o.width,d.width+2*o.noteMargin):k.getMax(s.width/2+a.width/2,d.width+2*o.noteMargin),r.startx=i-r.width+(s.width-o.actorMargin)/2):e.to===e.from?(d=V.calculateTextDimensions(h?V.wrapLabel(e.message,k.getMax(o.width,s.width),Tt(o)):e.message,Tt(o)),r.width=h?k.getMax(o.width,s.width):k.getMax(s.width,o.width,d.width+2*o.noteMargin),r.startx=i+(s.width-r.width)/2):(r.width=Math.abs(i+s.width/2-(n+a.width/2))+o.actorMargin,r.startx=i<n?i+s.width/2-o.actorMargin/2:n+a.width/2-o.actorMargin/2),h&&(r.message=V.wrapLabel(e.message,r.width-2*o.wrapPadding,Tt(o))),F.debug(`NM:[${r.startx},${r.stopx},${r.starty},${r.stopy}:${r.width},${r.height}=${e.message}]`),r},"buildNoteModel"),us=u(function(e,t,c){if(![c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN,c.db.LINETYPE.SOLID,c.db.LINETYPE.DOTTED,c.db.LINETYPE.SOLID_CROSS,c.db.LINETYPE.DOTTED_CROSS,c.db.LINETYPE.SOLID_POINT,c.db.LINETYPE.DOTTED_POINT,c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(e.type))return{};let[s,a]=we(e.from,t),[i,n]=we(e.to,t),h=s<=i,d=h?a:s,r=h?i:n,f=Math.abs(i-n)>2,T=u(I=>h?-I:I,"adjustValue");e.from===e.to?r=d:(e.activate&&!f&&(r+=T(o.activationWidth/2-1)),[c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN].includes(e.type)||(r+=T(3)),[c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(e.type)&&(d-=T(3)));let g=[s,a,i,n],b=Math.abs(d-r);e.wrap&&e.message&&(e.message=V.wrapLabel(e.message,k.getMax(b+2*o.wrapPadding,o.width),xt(o)));let E=V.calculateTextDimensions(e.message,xt(o));return{width:k.getMax(e.wrap?0:E.width+2*o.wrapPadding,b+2*o.wrapPadding,o.width),height:0,startx:d,stopx:r,starty:0,stopy:0,message:e.message,type:e.type,wrap:e.wrap,fromBounds:Math.min.apply(null,g),toBounds:Math.max.apply(null,g)}},"buildMessageModel"),gs=u(async function(e,t,c,s){let a={},i=[],n,h,d;for(let r of e){switch(r.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:i.push({id:r.id,msg:r.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:r.message&&(n=i.pop(),a[n.id]=n,a[r.id]=n,i.push(n));break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:n=i.pop(),a[n.id]=n;break;case s.db.LINETYPE.ACTIVE_START:{let T=t.get(r.from?r.from:r.to.actor),g=St(r.from?r.from:r.to.actor).length,b=T.x+T.width/2+(g-1)*o.activationWidth/2,E={startx:b,stopx:b+o.activationWidth,actor:r.from,enabled:!0};x.activations.push(E)}break;case s.db.LINETYPE.ACTIVE_END:{let T=x.activations.map(g=>g.actor).lastIndexOf(r.from);x.activations.splice(T,1).splice(0,1)}break}r.placement!==void 0?(h=await ps(r,t,s),r.noteModel=h,i.forEach(T=>{n=T,n.from=k.getMin(n.from,h.startx),n.to=k.getMax(n.to,h.startx+h.width),n.width=k.getMax(n.width,Math.abs(n.from-n.to))-o.labelBoxWidth})):(d=us(r,t,s),r.msgModel=d,d.startx&&d.stopx&&i.length>0&&i.forEach(T=>{if(n=T,d.startx===d.stopx){let g=t.get(r.from),b=t.get(r.to);n.from=k.getMin(g.x-d.width/2,g.x-g.width/2,n.from),n.to=k.getMax(b.x+d.width/2,b.x+g.width/2,n.to),n.width=k.getMax(n.width,Math.abs(n.to-n.from))-o.labelBoxWidth}else n.from=k.getMin(d.startx,n.from),n.to=k.getMax(d.stopx,n.to),n.width=k.getMax(n.width,d.width)-o.labelBoxWidth}))}return x.activations=[],F.debug("Loop type widths:",a),a},"calculateLoopBounds"),Le={bounds:x,drawActors:Gt,drawActorsPopup:ke,setConf:Ie,draw:cs};var Xs={parser:ge,get db(){return new Nt},renderer:Le,styles:fe,init:u(e=>{e.sequence||(e.sequence={}),e.wrap&&(e.sequence.wrap=e.wrap,oe({sequence:{wrap:e.wrap}}))},"init")};export{Xs as diagram};
