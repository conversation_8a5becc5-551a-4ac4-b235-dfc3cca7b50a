{"version": 3, "sources": ["../../../src/diagrams/state/id-cache.js", "../../../src/diagrams/state/shapes.js", "../../../src/diagrams/state/stateRenderer.js", "../../../src/diagrams/state/stateDiagram.ts"], "sourcesContent": ["const idCache = {};\n\nexport const set = (key, val) => {\n  idCache[key] = val;\n};\n\nexport const get = (k) => idCache[k];\nexport const keys = () => Object.keys(idCache);\nexport const size = () => keys().length;\n\nexport default {\n  get,\n  set,\n  keys,\n  size,\n};\n", "import { line, curveBasis } from 'd3';\nimport idCache from './id-cache.js';\nimport { StateDB } from './stateDb.js';\nimport utils from '../../utils.js';\nimport common from '../common/common.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\n\n/**\n * Draws a start state as a black circle\n *\n * @param {any} g\n */\nexport const drawStartState = (g) =>\n  g\n    .append('circle')\n    // .style('stroke', 'black')\n    // .style('fill', 'black')\n    .attr('class', 'start-state')\n    .attr('r', getConfig().state.sizeUnit)\n    .attr('cx', getConfig().state.padding + getConfig().state.sizeUnit)\n    .attr('cy', getConfig().state.padding + getConfig().state.sizeUnit);\n\n/**\n * Draws a start state as a black circle\n *\n * @param {any} g\n */\nexport const drawDivider = (g) =>\n  g\n    .append('line')\n    .style('stroke', 'grey')\n    .style('stroke-dasharray', '3')\n    .attr('x1', getConfig().state.textHeight)\n    .attr('class', 'divider')\n    .attr('x2', getConfig().state.textHeight * 2)\n    .attr('y1', 0)\n    .attr('y2', 0);\n\n/**\n * Draws a an end state as a black circle\n *\n * @param {any} g\n * @param {any} stateDef\n */\nexport const drawSimpleState = (g, stateDef) => {\n  const state = g\n    .append('text')\n    .attr('x', 2 * getConfig().state.padding)\n    .attr('y', getConfig().state.textHeight + 2 * getConfig().state.padding)\n    .attr('font-size', getConfig().state.fontSize)\n    .attr('class', 'state-title')\n    .text(stateDef.id);\n\n  const classBox = state.node().getBBox();\n  g.insert('rect', ':first-child')\n    .attr('x', getConfig().state.padding)\n    .attr('y', getConfig().state.padding)\n    .attr('width', classBox.width + 2 * getConfig().state.padding)\n    .attr('height', classBox.height + 2 * getConfig().state.padding)\n    .attr('rx', getConfig().state.radius);\n\n  return state;\n};\n\n/**\n * Draws a state with descriptions\n *\n * @param {any} g The d3 svg object to add the state to\n * @param {any} stateDef\n * @returns {any} The d3 svg state\n */\nexport const drawDescrState = (g, stateDef) => {\n  const addTspan = function (textEl, txt, isFirst) {\n    const tSpan = textEl\n      .append('tspan')\n      .attr('x', 2 * getConfig().state.padding)\n      .text(txt);\n    if (!isFirst) {\n      tSpan.attr('dy', getConfig().state.textHeight);\n    }\n  };\n  const title = g\n    .append('text')\n    .attr('x', 2 * getConfig().state.padding)\n    .attr('y', getConfig().state.textHeight + 1.3 * getConfig().state.padding)\n    .attr('font-size', getConfig().state.fontSize)\n    .attr('class', 'state-title')\n    .text(stateDef.descriptions[0]);\n\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n\n  const description = g\n    .append('text') // text label for the x axis\n    .attr('x', getConfig().state.padding)\n    .attr(\n      'y',\n      titleHeight +\n        getConfig().state.padding * 0.4 +\n        getConfig().state.dividerMargin +\n        getConfig().state.textHeight\n    )\n    .attr('class', 'state-description');\n\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function (descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n\n  const descrLine = g\n    .append('line') // text label for the x axis\n    .attr('x1', getConfig().state.padding)\n    .attr('y1', getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2)\n    .attr('y2', getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2)\n    .attr('class', 'descr-divider');\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n\n  descrLine.attr('x2', width + 3 * getConfig().state.padding);\n  // const classBox = title.node().getBBox();\n\n  g.insert('rect', ':first-child')\n    .attr('x', getConfig().state.padding)\n    .attr('y', getConfig().state.padding)\n    .attr('width', width + 2 * getConfig().state.padding)\n    .attr('height', descrBox.height + titleHeight + 2 * getConfig().state.padding)\n    .attr('rx', getConfig().state.radius);\n\n  return g;\n};\n\n/** Adds the creates a box around the existing content and adds a panel for the id on top of the content. */\n/**\n * Function that creates an title row and a frame around a substate for a composite state diagram.\n * The function returns a new d3 svg object with updated width and height properties;\n *\n * @param {any} g The d3 svg object for the substate to framed\n * @param {any} stateDef The info about the\n * @param {any} altBkg\n */\nexport const addTitleAndBox = (g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n\n  const title = g\n    .append('text')\n    .attr('x', 0)\n    .attr('y', getConfig().state.titleShift)\n    .attr('font-size', getConfig().state.fontSize)\n    .attr('class', 'state-title')\n    .text(stateDef.id);\n\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth); // + dblPad;\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  // const lineY = 1 - getConfig().state.textHeight;\n  // const descrLine = g\n  //   .append('line') // text label for the x axis\n  //   .attr('x1', 0)\n  //   .attr('y1', lineY)\n  //   .attr('y2', lineY)\n  //   .attr('class', 'descr-divider');\n\n  const graphBox = g.node().getBBox();\n  // descrLine.attr('x2', graphBox.width + getConfig().state.padding);\n\n  if (stateDef.doc) {\n    // console.warn(\n    //   stateDef.id,\n    //   'orgX: ',\n    //   orgX,\n    //   'width: ',\n    //   width,\n    //   'titleWidth: ',\n    //   titleWidth,\n    //   'orgWidth: ',\n    //   orgWidth,\n    //   'width',\n    //   width\n    // );\n  }\n\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n\n  const lineY = 1 - getConfig().state.textHeight;\n  // White color\n  g.insert('rect', ':first-child')\n    .attr('x', startX)\n    .attr('y', lineY)\n    .attr('class', altBkg ? 'alt-composit' : 'composit') // cspell:disable-line\n    .attr('width', width)\n    .attr(\n      'height',\n      graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n    )\n    .attr('rx', '0');\n\n  title.attr('x', startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr('x', orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n\n  // Title background\n  g.insert('rect', ':first-child')\n    .attr('x', startX)\n    .attr(\n      'y',\n      getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n    )\n    .attr('width', width)\n    // Just needs to be higher then the descr line, will be clipped by the white color box\n    .attr('height', getConfig().state.textHeight * 3)\n    .attr('rx', getConfig().state.radius);\n\n  // Full background\n  g.insert('rect', ':first-child')\n    .attr('x', startX)\n    .attr(\n      'y',\n      getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n    )\n    .attr('width', width)\n    .attr('height', graphBox.height + 3 + 2 * getConfig().state.textHeight)\n    .attr('rx', getConfig().state.radius);\n\n  return g;\n};\n\nconst drawEndState = (g) => {\n  g.append('circle')\n    // .style('stroke', 'black')\n    // .style('fill', 'white')\n    .attr('class', 'end-state-outer')\n    .attr('r', getConfig().state.sizeUnit + getConfig().state.miniPadding)\n    .attr(\n      'cx',\n      getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n    )\n    .attr(\n      'cy',\n      getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n    );\n\n  return (\n    g\n      .append('circle')\n      // .style('stroke', 'black')\n      // .style('fill', 'black')\n      .attr('class', 'end-state-inner')\n      .attr('r', getConfig().state.sizeUnit)\n      .attr('cx', getConfig().state.padding + getConfig().state.sizeUnit + 2)\n      .attr('cy', getConfig().state.padding + getConfig().state.sizeUnit + 2)\n  );\n};\nconst drawForkJoinState = (g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g\n    .append('rect')\n    .style('stroke', 'black')\n    .style('fill', 'black')\n    .attr('width', width)\n    .attr('height', height)\n    .attr('x', getConfig().state.padding)\n    .attr('y', getConfig().state.padding);\n};\n\nexport const drawText = function (elem, textData) {\n  // Remove and ignore br:s\n  const nText = textData.text.replace(common.lineBreakRegex, ' ');\n\n  const textElem = elem.append('text');\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.style('text-anchor', textData.anchor);\n  textElem.attr('fill', textData.fill);\n  if (textData.class !== undefined) {\n    textElem.attr('class', textData.class);\n  }\n\n  const span = textElem.append('tspan');\n  span.attr('x', textData.x + textData.textMargin * 2);\n  span.attr('fill', textData.fill);\n  span.text(nText);\n\n  return textElem;\n};\n\nconst _drawLongText = (_text, x, y, g) => {\n  let textHeight = 0;\n\n  const textElem = g.append('text');\n  textElem.style('text-anchor', 'start');\n  textElem.attr('class', 'noteText');\n\n  let text = _text.replace(/\\r\\n/g, '<br/>');\n  text = text.replace(/\\n/g, '<br/>');\n  const lines = text.split(common.lineBreakRegex);\n\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line of lines) {\n    const txt = line.trim();\n\n    if (txt.length > 0) {\n      const span = textElem.append('tspan');\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr('x', x + getConfig().state.noteMargin);\n      span.attr('y', y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n};\n\n/**\n * Draws a note to the diagram\n *\n * @param text - The text of the given note.\n * @param g - The element the note is attached to.\n */\n\nexport const drawNote = (text, g) => {\n  g.attr('class', 'state-note');\n  const note = g.append('rect').attr('x', 0).attr('y', getConfig().state.padding);\n  const rectElem = g.append('g');\n\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr('height', textHeight + 2 * getConfig().state.noteMargin);\n  note.attr('width', textWidth + getConfig().state.noteMargin * 2);\n\n  return note;\n};\n\n/**\n * Starting point for drawing a state. The function finds out the specifics about the state and\n * renders with appropriate function.\n *\n * @param {any} elem\n * @param {any} stateDef\n */\n\nexport const drawState = function (elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id: id,\n    label: stateDef.id,\n    width: 0,\n    height: 0,\n  };\n\n  const g = elem.append('g').attr('id', id).attr('class', 'stateGroup');\n\n  if (stateDef.type === 'start') {\n    drawStartState(g);\n  }\n  if (stateDef.type === 'end') {\n    drawEndState(g);\n  }\n  if (stateDef.type === 'fork' || stateDef.type === 'join') {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === 'note') {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === 'divider') {\n    drawDivider(g);\n  }\n  if (stateDef.type === 'default' && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === 'default' && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n\n  idCache.set(id, stateInfo);\n  // stateCnt++;\n  return stateInfo;\n};\n\nlet edgeCount = 0;\nexport const drawEdge = function (elem, path, relation) {\n  const getRelationType = function (type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return 'aggregation';\n      case StateDB.relationType.EXTENSION:\n        return 'extension';\n      case StateDB.relationType.COMPOSITION:\n        return 'composition';\n      case StateDB.relationType.DEPENDENCY:\n        return 'dependency';\n    }\n  };\n\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n\n  // The data for our line\n  const lineData = path.points;\n\n  // This is the accessor function we talked about above\n  const lineFunction = line()\n    .x(function (d) {\n      return d.x;\n    })\n    .y(function (d) {\n      return d.y;\n    })\n    .curve(curveBasis);\n\n  const svgPath = elem\n    .append('path')\n    .attr('d', lineFunction(lineData))\n    .attr('id', 'edge' + edgeCount)\n    .attr('class', 'transition');\n  let url = '';\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url =\n      window.location.protocol +\n      '//' +\n      window.location.host +\n      window.location.pathname +\n      window.location.search;\n    url = url.replace(/\\(/g, '\\\\(');\n    url = url.replace(/\\)/g, '\\\\)');\n  }\n\n  svgPath.attr(\n    'marker-end',\n    'url(' + url + '#' + getRelationType(StateDB.relationType.DEPENDENCY) + 'End' + ')'\n  );\n\n  if (relation.title !== undefined) {\n    const label = elem.append('g').attr('class', 'stateLabel');\n\n    const { x, y } = utils.calcLabelPosition(path.points);\n\n    const rows = common.getRows(relation.title);\n\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label\n        .append('text')\n        .attr('text-anchor', 'middle')\n        .text(rows[i])\n        .attr('x', x)\n        .attr('y', y + titleHeight);\n\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n\n      log.info(boundsTmp.x, x, y + titleHeight);\n\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info('Title height', titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n\n      titleRows.forEach((title, i) => title.attr('y', y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n\n    const bounds = label.node().getBBox();\n\n    label\n      .insert('rect', ':first-child')\n      .attr('class', 'box')\n      .attr('x', x - maxWidth / 2 - getConfig().state.padding / 2)\n      .attr('y', y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5)\n      .attr('width', maxWidth + getConfig().state.padding)\n      .attr('height', boxHeight + getConfig().state.padding);\n\n    log.info(bounds);\n\n    //label.attr('transform', '0 -' + (bounds.y / 2));\n\n    // Debug points\n    // path.points.forEach(point => {\n    //   g.append('circle')\n    //     .style('stroke', 'red')\n    //     .style('fill', 'red')\n    //     .attr('r', 1)\n    //     .attr('cx', point.x)\n    //     .attr('cy', point.y);\n    // });\n    // g.append('circle')\n    //   .style('stroke', 'blue')\n    //   .style('fill', 'blue')\n    //   .attr('r', 1)\n    //   .attr('cx', x)\n    //   .attr('cy', y);\n  }\n\n  edgeCount++;\n};\n", "import { select } from 'd3';\nimport { layout as dagreLayout } from 'dagre-d3-es/src/dagre/index.js';\nimport * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport { log } from '../../logger.js';\nimport common from '../common/common.js';\nimport { drawState, addTitleAndBox, drawEdge } from './shapes.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\n// TODO Move conf object to main conf in mermaidAPI\nlet conf;\n\nconst transformationLog = {};\n\nexport const setConf = function () {\n  //no-op\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nconst insertMarkers = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'dependencyEnd')\n    .attr('refX', 19)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 19,7 L9,13 L14,7 L9,1 Z');\n};\n\n/**\n * Draws a flowchart in the tag with id: id based on the graph definition in text.\n *\n * @param {any} text\n * @param {any} id\n * @param _version\n * @param diagObj\n */\nexport const draw = function (text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n\n  log.debug('Rendering diagram ' + text);\n\n  // Fetch the default direction, use TD if none was found\n  const diagram = root.select(`[id='${id}']`);\n  insertMarkers(diagram);\n\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram, undefined, false, root, doc, diagObj);\n\n  const padding = conf.padding;\n  const bounds = diagram.node().getBBox();\n\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n\n  // zoom in a bit\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram, height, svgWidth, conf.useMaxWidth);\n\n  diagram.attr(\n    'viewBox',\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + ' ' + height\n  );\n};\nconst getLabelWidth = (text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n};\n\nconst renderDoc = (doc, diagram, parentId, altBkg, root, domDocument, diagObj) => {\n  // Layout graph, Create a new directed graph\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true,\n  });\n\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === 'relation') {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n\n  // Set an object for the graph label\n  if (parentId) {\n    graph.setGraph({\n      rankdir: 'LR',\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: 'tight-tree',\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true,\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: 'TB',\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: 'tight-tree',\n      // ranker: 'network-simplex'\n      isMultiGraph: true,\n    });\n  }\n\n  // Default to assigning a new object as a label for each new edge.\n  graph.setDefaultEdgeLabel(function () {\n    return {};\n  });\n\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n\n  const keys = Object.keys(states);\n\n  let first = true;\n\n  for (const key of keys) {\n    const stateDef = states[key];\n\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram.append('g').attr('id', stateDef.id).attr('class', 'stateGroup');\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n\n      if (first) {\n        // first = false;\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        // sub = addIdAndBox(sub, stateDef);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n        // transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      }\n    } else {\n      node = drawState(diagram, stateDef, graph);\n    }\n\n    if (stateDef.note) {\n      // Draw note note\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + '-note',\n        note: stateDef.note,\n        type: 'note',\n      };\n      const note = drawState(diagram, noteDef, graph);\n\n      // graph.setNode(node.id, node);\n      if (stateDef.note.position === 'left of') {\n        graph.setNode(node.id + '-note', note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + '-note', note);\n      }\n      // graph.setNode(node.id);\n      graph.setParent(node.id, node.id + '-group');\n      graph.setParent(node.id + '-note', node.id + '-group');\n    } else {\n      // Add nodes to the graph. The first argument is the node id. The second is\n      // metadata about the node. In this case we're going to add labels to each of\n      // our nodes.\n      graph.setNode(node.id, node);\n    }\n  }\n\n  log.debug('Count=', graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function (relation) {\n    cnt++;\n    log.debug('Setting edge', relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation: relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common.getRows(relation.title).length,\n        labelpos: 'c',\n      },\n      'id' + cnt\n    );\n  });\n\n  dagreLayout(graph);\n\n  log.debug('Graph after layout', graph.nodes());\n  const svgElem = diagram.node();\n\n  graph.nodes().forEach(function (v) {\n    if (v !== undefined && graph.node(v) !== undefined) {\n      log.warn('Node ' + v + ': ' + JSON.stringify(graph.node(v)));\n      root\n        .select('#' + svgElem.id + ' #' + v)\n        .attr(\n          'transform',\n          'translate(' +\n            (graph.node(v).x - graph.node(v).width / 2) +\n            ',' +\n            (graph.node(v).y +\n              (transformationLog[v] ? transformationLog[v].y : 0) -\n              graph.node(v).height / 2) +\n            ' )'\n        );\n      root\n        .select('#' + svgElem.id + ' #' + v)\n        .attr('data-x-shift', graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll('#' + svgElem.id + ' #' + v + ' .divider');\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute('data-x-shift'), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute('x1', 0 - pShift + 8);\n        divider.setAttribute('x2', pWidth - pShift - 8);\n      });\n    } else {\n      log.debug('No Node ' + v + ': ' + JSON.stringify(graph.node(v)));\n    }\n  });\n\n  let stateBox = svgElem.getBBox();\n\n  graph.edges().forEach(function (e) {\n    if (e !== undefined && graph.edge(e) !== undefined) {\n      log.debug('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n\n  stateBox = svgElem.getBBox();\n\n  const stateInfo = {\n    id: parentId ? parentId : 'root',\n    label: parentId ? parentId : 'root',\n    width: 0,\n    height: 0,\n  };\n\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n\n  log.debug('Doc rendered', stateInfo, graph);\n  return stateInfo;\n};\n\nexport default {\n  setConf,\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/stateDiagram.jison';\nimport { StateDB } from './stateDb.js';\nimport styles from './styles.js';\nimport renderer from './stateRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer,\n  styles,\n  init: (cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  },\n};\n"], "mappings": "woBAAA,IAAMA,EAAU,CAAC,EAEJC,EAAMC,EAAA,CAACC,EAAKC,IAAQ,CAC/BJ,EAAQG,CAAG,EAAIC,CACjB,EAFmB,OAINC,EAAMH,EAACI,GAAMN,EAAQM,CAAC,EAAhB,OACNC,EAAOL,EAAA,IAAM,OAAO,KAAKF,CAAO,EAAzB,QACPQ,EAAON,EAAA,IAAMK,EAAK,EAAE,OAAb,QAEbE,EAAQ,CACb,IAAAJ,EACA,IAAAJ,EACA,KAAAM,EACA,KAAAC,CACF,ECFO,IAAME,EAAiBC,EAACC,GAC7BA,EACG,OAAO,QAAQ,EAGf,KAAK,QAAS,aAAa,EAC3B,KAAK,IAAKC,EAAU,EAAE,MAAM,QAAQ,EACpC,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,QAAQ,EACjE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,QAAQ,EARxC,kBAejBC,EAAcH,EAACC,GAC1BA,EACG,OAAO,MAAM,EACb,MAAM,SAAU,MAAM,EACtB,MAAM,mBAAoB,GAAG,EAC7B,KAAK,KAAMC,EAAU,EAAE,MAAM,UAAU,EACvC,KAAK,QAAS,SAAS,EACvB,KAAK,KAAMA,EAAU,EAAE,MAAM,WAAa,CAAC,EAC3C,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EATU,eAiBdE,GAAkBJ,EAAA,CAACC,EAAGI,IAAa,CAC9C,IAAMC,EAAQL,EACX,OAAO,MAAM,EACb,KAAK,IAAK,EAAIC,EAAU,EAAE,MAAM,OAAO,EACvC,KAAK,IAAKA,EAAU,EAAE,MAAM,WAAa,EAAIA,EAAU,EAAE,MAAM,OAAO,EACtE,KAAK,YAAaA,EAAU,EAAE,MAAM,QAAQ,EAC5C,KAAK,QAAS,aAAa,EAC3B,KAAKG,EAAS,EAAE,EAEbE,EAAWD,EAAM,KAAK,EAAE,QAAQ,EACtC,OAAAL,EAAE,OAAO,OAAQ,cAAc,EAC5B,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EACnC,KAAK,IAAKA,EAAU,EAAE,MAAM,OAAO,EACnC,KAAK,QAASK,EAAS,MAAQ,EAAIL,EAAU,EAAE,MAAM,OAAO,EAC5D,KAAK,SAAUK,EAAS,OAAS,EAAIL,EAAU,EAAE,MAAM,OAAO,EAC9D,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAE/BI,CACT,EAlB+B,mBA2BlBE,GAAiBR,EAAA,CAACC,EAAGI,IAAa,CAC7C,IAAMI,EAAWT,EAAA,SAAUU,EAAQC,EAAKC,EAAS,CAC/C,IAAMC,EAAQH,EACX,OAAO,OAAO,EACd,KAAK,IAAK,EAAIR,EAAU,EAAE,MAAM,OAAO,EACvC,KAAKS,CAAG,EACNC,GACHC,EAAM,KAAK,KAAMX,EAAU,EAAE,MAAM,UAAU,CAEjD,EARiB,YAiBXY,EARQb,EACX,OAAO,MAAM,EACb,KAAK,IAAK,EAAIC,EAAU,EAAE,MAAM,OAAO,EACvC,KAAK,IAAKA,EAAU,EAAE,MAAM,WAAa,IAAMA,EAAU,EAAE,MAAM,OAAO,EACxE,KAAK,YAAaA,EAAU,EAAE,MAAM,QAAQ,EAC5C,KAAK,QAAS,aAAa,EAC3B,KAAKG,EAAS,aAAa,CAAC,CAAC,EAET,KAAK,EAAE,QAAQ,EAChCU,EAAcD,EAAS,OAEvBE,EAAcf,EACjB,OAAO,MAAM,EACb,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EACnC,KACC,IACAa,EACEb,EAAU,EAAE,MAAM,QAAU,GAC5BA,EAAU,EAAE,MAAM,cAClBA,EAAU,EAAE,MAAM,UACtB,EACC,KAAK,QAAS,mBAAmB,EAEhCU,EAAU,GACVK,EAAW,GACfZ,EAAS,aAAa,QAAQ,SAAUa,EAAO,CACxCN,IACHH,EAASO,EAAaE,EAAOD,CAAQ,EACrCA,EAAW,IAEbL,EAAU,EACZ,CAAC,EAED,IAAMO,EAAYlB,EACf,OAAO,MAAM,EACb,KAAK,KAAMC,EAAU,EAAE,MAAM,OAAO,EACpC,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUa,EAAcb,EAAU,EAAE,MAAM,cAAgB,CAAC,EACxF,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUa,EAAcb,EAAU,EAAE,MAAM,cAAgB,CAAC,EACxF,KAAK,QAAS,eAAe,EAC1BkB,EAAWJ,EAAY,KAAK,EAAE,QAAQ,EACtCK,EAAQ,KAAK,IAAID,EAAS,MAAON,EAAS,KAAK,EAErD,OAAAK,EAAU,KAAK,KAAME,EAAQ,EAAInB,EAAU,EAAE,MAAM,OAAO,EAG1DD,EAAE,OAAO,OAAQ,cAAc,EAC5B,KAAK,IAAKC,EAAU,EAAE,MAAM,OAAO,EACnC,KAAK,IAAKA,EAAU,EAAE,MAAM,OAAO,EACnC,KAAK,QAASmB,EAAQ,EAAInB,EAAU,EAAE,MAAM,OAAO,EACnD,KAAK,SAAUkB,EAAS,OAASL,EAAc,EAAIb,EAAU,EAAE,MAAM,OAAO,EAC5E,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAE/BD,CACT,EA/D8B,kBA0EjBqB,EAAiBtB,EAAA,CAACC,EAAGI,EAAUkB,IAAW,CACrD,IAAMC,EAAMtB,EAAU,EAAE,MAAM,QACxBuB,EAAS,EAAIvB,EAAU,EAAE,MAAM,QAC/BwB,EAASzB,EAAE,KAAK,EAAE,QAAQ,EAC1B0B,EAAWD,EAAO,MAClBE,EAAOF,EAAO,EAEdG,EAAQ5B,EACX,OAAO,MAAM,EACb,KAAK,IAAK,CAAC,EACX,KAAK,IAAKC,EAAU,EAAE,MAAM,UAAU,EACtC,KAAK,YAAaA,EAAU,EAAE,MAAM,QAAQ,EAC5C,KAAK,QAAS,aAAa,EAC3B,KAAKG,EAAS,EAAE,EAGbyB,EADWD,EAAM,KAAK,EAAE,QAAQ,EACV,MAAQJ,EAChCJ,EAAQ,KAAK,IAAIS,EAAYH,CAAQ,EACrCN,IAAUM,IACZN,EAAQA,EAAQI,GAElB,IAAIM,EASEC,EAAW/B,EAAE,KAAK,EAAE,QAAQ,EAG9BI,EAAS,IAgBb0B,EAASH,EAAOJ,EACZM,EAAaH,IACfI,GAAUJ,EAAWN,GAAS,EAAIG,GAEhC,KAAK,IAAII,EAAOI,EAAS,CAAC,EAAIR,GAAOM,EAAaH,IACpDI,EAASH,GAAQE,EAAaH,GAAY,GAG5C,IAAMM,EAAQ,EAAI/B,EAAU,EAAE,MAAM,WAEpC,OAAAD,EAAE,OAAO,OAAQ,cAAc,EAC5B,KAAK,IAAK8B,CAAM,EAChB,KAAK,IAAKE,CAAK,EACf,KAAK,QAASV,EAAS,eAAiB,UAAU,EAClD,KAAK,QAASF,CAAK,EACnB,KACC,SACAW,EAAS,OAAS9B,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,WAAa,CAClF,EACC,KAAK,KAAM,GAAG,EAEjB2B,EAAM,KAAK,IAAKE,EAASP,CAAG,EACxBM,GAAcH,GAChBE,EAAM,KAAK,IAAKD,GAAQP,EAAQI,GAAU,EAAIK,EAAa,EAAIN,CAAG,EAIpEvB,EAAE,OAAO,OAAQ,cAAc,EAC5B,KAAK,IAAK8B,CAAM,EAChB,KACC,IACA7B,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,OAClF,EACC,KAAK,QAASmB,CAAK,EAEnB,KAAK,SAAUnB,EAAU,EAAE,MAAM,WAAa,CAAC,EAC/C,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAGtCD,EAAE,OAAO,OAAQ,cAAc,EAC5B,KAAK,IAAK8B,CAAM,EAChB,KACC,IACA7B,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,WAAaA,EAAU,EAAE,MAAM,OAClF,EACC,KAAK,QAASmB,CAAK,EACnB,KAAK,SAAUW,EAAS,OAAS,EAAI,EAAI9B,EAAU,EAAE,MAAM,UAAU,EACrE,KAAK,KAAMA,EAAU,EAAE,MAAM,MAAM,EAE/BD,CACT,EAnG8B,kBAqGxBiC,GAAelC,EAACC,IACpBA,EAAE,OAAO,QAAQ,EAGd,KAAK,QAAS,iBAAiB,EAC/B,KAAK,IAAKC,EAAU,EAAE,MAAM,SAAWA,EAAU,EAAE,MAAM,WAAW,EACpE,KACC,KACAA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAWA,EAAU,EAAE,MAAM,WAC7E,EACC,KACC,KACAA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAWA,EAAU,EAAE,MAAM,WAC7E,EAGAD,EACG,OAAO,QAAQ,EAGf,KAAK,QAAS,iBAAiB,EAC/B,KAAK,IAAKC,EAAU,EAAE,MAAM,QAAQ,EACpC,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAW,CAAC,EACrE,KAAK,KAAMA,EAAU,EAAE,MAAM,QAAUA,EAAU,EAAE,MAAM,SAAW,CAAC,GAvBvD,gBA0BfiC,GAAoBnC,EAAA,CAACC,EAAGI,IAAa,CACzC,IAAIgB,EAAQnB,EAAU,EAAE,MAAM,UAC1BkC,EAASlC,EAAU,EAAE,MAAM,WAE/B,GAAIG,EAAS,SAAU,CACrB,IAAIgC,EAAMhB,EACVA,EAAQe,EACRA,EAASC,CACX,CACA,OAAOpC,EACJ,OAAO,MAAM,EACb,MAAM,SAAU,OAAO,EACvB,MAAM,OAAQ,OAAO,EACrB,KAAK,QAASoB,CAAK,EACnB,KAAK,SAAUe,CAAM,EACrB,KAAK,IAAKlC,EAAU,EAAE,MAAM,OAAO,EACnC,KAAK,IAAKA,EAAU,EAAE,MAAM,OAAO,CACxC,EAjB0B,qBAwC1B,IAAMoC,GAAgBC,EAAA,CAACC,EAAOC,EAAGC,EAAGC,IAAM,CACxC,IAAIC,EAAa,EAEXC,EAAWF,EAAE,OAAO,MAAM,EAChCE,EAAS,MAAM,cAAe,OAAO,EACrCA,EAAS,KAAK,QAAS,UAAU,EAEjC,IAAIC,EAAON,EAAM,QAAQ,QAAS,OAAO,EACzCM,EAAOA,EAAK,QAAQ,MAAO,OAAO,EAClC,IAAMC,EAAQD,EAAK,MAAME,EAAO,cAAc,EAE1CC,EAAU,KAAOC,EAAU,EAAE,MAAM,WACvC,QAAWC,KAAQJ,EAAO,CACxB,IAAMK,EAAMD,EAAK,KAAK,EAEtB,GAAIC,EAAI,OAAS,EAAG,CAClB,IAAMC,EAAOR,EAAS,OAAO,OAAO,EAEpC,GADAQ,EAAK,KAAKD,CAAG,EACTH,IAAY,EAAG,CACjB,IAAMK,EAAaD,EAAK,KAAK,EAAE,QAAQ,EACvCJ,GAAWK,EAAW,MACxB,CACAV,GAAcK,EACdI,EAAK,KAAK,IAAKZ,EAAIS,EAAU,EAAE,MAAM,UAAU,EAC/CG,EAAK,KAAK,IAAKX,EAAIE,EAAa,KAAOM,EAAU,EAAE,MAAM,UAAU,CACrE,CACF,CACA,MAAO,CAAE,UAAWL,EAAS,KAAK,EAAE,QAAQ,EAAE,MAAO,WAAAD,CAAW,CAClE,EA5BsB,iBAqCTW,GAAWhB,EAAA,CAACO,EAAMH,IAAM,CACnCA,EAAE,KAAK,QAAS,YAAY,EAC5B,IAAMa,EAAOb,EAAE,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAKO,EAAU,EAAE,MAAM,OAAO,EACxEO,EAAWd,EAAE,OAAO,GAAG,EAEvB,CAAE,UAAAe,EAAW,WAAAd,CAAW,EAAIN,GAAcQ,EAAM,EAAG,EAAGW,CAAQ,EACpE,OAAAD,EAAK,KAAK,SAAUZ,EAAa,EAAIM,EAAU,EAAE,MAAM,UAAU,EACjEM,EAAK,KAAK,QAASE,EAAYR,EAAU,EAAE,MAAM,WAAa,CAAC,EAExDM,CACT,EAVwB,YAoBXG,EAAYpB,EAAA,SAAUqB,EAAMC,EAAU,CACjD,IAAMC,EAAKD,EAAS,GACdE,EAAY,CAChB,GAAID,EACJ,MAAOD,EAAS,GAChB,MAAO,EACP,OAAQ,CACV,EAEMlB,EAAIiB,EAAK,OAAO,GAAG,EAAE,KAAK,KAAME,CAAE,EAAE,KAAK,QAAS,YAAY,EAEhED,EAAS,OAAS,SACpBG,EAAerB,CAAC,EAEdkB,EAAS,OAAS,OACpBI,GAAatB,CAAC,GAEZkB,EAAS,OAAS,QAAUA,EAAS,OAAS,SAChDK,GAAkBvB,EAAGkB,CAAQ,EAE3BA,EAAS,OAAS,QACpBN,GAASM,EAAS,KAAK,KAAMlB,CAAC,EAE5BkB,EAAS,OAAS,WACpBM,EAAYxB,CAAC,EAEXkB,EAAS,OAAS,WAAaA,EAAS,aAAa,SAAW,GAClEO,GAAgBzB,EAAGkB,CAAQ,EAEzBA,EAAS,OAAS,WAAaA,EAAS,aAAa,OAAS,GAChEQ,GAAe1B,EAAGkB,CAAQ,EAG5B,IAAMS,EAAW3B,EAAE,KAAK,EAAE,QAAQ,EAClC,OAAAoB,EAAU,MAAQO,EAAS,MAAQ,EAAIpB,EAAU,EAAE,MAAM,QACzDa,EAAU,OAASO,EAAS,OAAS,EAAIpB,EAAU,EAAE,MAAM,QAE3DqB,EAAQ,IAAIT,EAAIC,CAAS,EAElBA,CACT,EAxCyB,aA0CrBS,EAAY,EACHC,EAAWlC,EAAA,SAAUqB,EAAMc,EAAMC,EAAU,CACtD,IAAMC,EAAkBrC,EAAA,SAAUsC,EAAM,CACtC,OAAQA,EAAM,CACZ,KAAKC,EAAQ,aAAa,YACxB,MAAO,cACT,KAAKA,EAAQ,aAAa,UACxB,MAAO,YACT,KAAKA,EAAQ,aAAa,YACxB,MAAO,cACT,KAAKA,EAAQ,aAAa,WACxB,MAAO,YACX,CACF,EAXwB,mBAaxBJ,EAAK,OAASA,EAAK,OAAO,OAAQK,GAAM,CAAC,OAAO,MAAMA,EAAE,CAAC,CAAC,EAG1D,IAAMC,EAAWN,EAAK,OAGhBO,EAAeC,EAAK,EACvB,EAAE,SAAUC,EAAG,CACd,OAAOA,EAAE,CACX,CAAC,EACA,EAAE,SAAUA,EAAG,CACd,OAAOA,EAAE,CACX,CAAC,EACA,MAAMC,CAAU,EAEbC,EAAUzB,EACb,OAAO,MAAM,EACb,KAAK,IAAKqB,EAAaD,CAAQ,CAAC,EAChC,KAAK,KAAM,OAASR,CAAS,EAC7B,KAAK,QAAS,YAAY,EACzBc,EAAM,GAiBV,GAhBIpC,EAAU,EAAE,MAAM,sBACpBoC,EACE,OAAO,SAAS,SAChB,KACA,OAAO,SAAS,KAChB,OAAO,SAAS,SAChB,OAAO,SAAS,OAClBA,EAAMA,EAAI,QAAQ,MAAO,KAAK,EAC9BA,EAAMA,EAAI,QAAQ,MAAO,KAAK,GAGhCD,EAAQ,KACN,aACA,OAASC,EAAM,IAAMV,EAAgBE,EAAQ,aAAa,UAAU,EAAI,MAC1E,EAEIH,EAAS,QAAU,OAAW,CAChC,IAAMY,EAAQ3B,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,YAAY,EAEnD,CAAE,EAAAnB,EAAG,EAAAC,CAAE,EAAI8C,EAAM,kBAAkBd,EAAK,MAAM,EAE9Ce,EAAOzC,EAAO,QAAQ2B,EAAS,KAAK,EAEtCe,EAAc,EACZC,EAAY,CAAC,EACfC,EAAW,EACXC,EAAO,EAEX,QAASC,EAAI,EAAGA,GAAKL,EAAK,OAAQK,IAAK,CACrC,IAAMC,EAAQR,EACX,OAAO,MAAM,EACb,KAAK,cAAe,QAAQ,EAC5B,KAAKE,EAAKK,CAAC,CAAC,EACZ,KAAK,IAAKrD,CAAC,EACX,KAAK,IAAKC,EAAIgD,CAAW,EAEtBM,EAAYD,EAAM,KAAK,EAAE,QAAQ,EACvCH,EAAW,KAAK,IAAIA,EAAUI,EAAU,KAAK,EAC7CH,EAAO,KAAK,IAAIA,EAAMG,EAAU,CAAC,EAEjCC,EAAI,KAAKD,EAAU,EAAGvD,EAAGC,EAAIgD,CAAW,EAEpCA,IAAgB,IAElBA,EADiBK,EAAM,KAAK,EAAE,QAAQ,EACf,OACvBE,EAAI,KAAK,eAAgBP,EAAahD,CAAC,GAEzCiD,EAAU,KAAKI,CAAK,CACtB,CAEA,IAAIG,EAAYR,EAAcD,EAAK,OACnC,GAAIA,EAAK,OAAS,EAAG,CACnB,IAAMU,GAAaV,EAAK,OAAS,GAAKC,EAAc,GAEpDC,EAAU,QAAQ,CAACI,EAAOD,IAAMC,EAAM,KAAK,IAAKrD,EAAIoD,EAAIJ,EAAcS,CAAS,CAAC,EAChFD,EAAYR,EAAcD,EAAK,MACjC,CAEA,IAAMW,EAASb,EAAM,KAAK,EAAE,QAAQ,EAEpCA,EACG,OAAO,OAAQ,cAAc,EAC7B,KAAK,QAAS,KAAK,EACnB,KAAK,IAAK9C,EAAImD,EAAW,EAAI1C,EAAU,EAAE,MAAM,QAAU,CAAC,EAC1D,KAAK,IAAKR,EAAIwD,EAAY,EAAIhD,EAAU,EAAE,MAAM,QAAU,EAAI,GAAG,EACjE,KAAK,QAAS0C,EAAW1C,EAAU,EAAE,MAAM,OAAO,EAClD,KAAK,SAAUgD,EAAYhD,EAAU,EAAE,MAAM,OAAO,EAEvD+C,EAAI,KAAKG,CAAM,CAmBjB,CAEA5B,GACF,EA7HwB,YCnZxB,IAAI6B,EAEEC,EAAoB,CAAC,EAEdC,GAAUC,EAAA,UAAY,CAEnC,EAFuB,WASjBC,GAAgBD,EAAA,SAAUE,EAAM,CACpCA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,eAAe,EAC1B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EAZsB,iBAsBTC,GAAOH,EAAA,SAAUI,EAAMC,EAAIC,EAAUC,EAAS,CACzDV,EAAOW,EAAU,EAAE,MACnB,IAAMC,EAAgBD,EAAU,EAAE,cAE9BE,EACAD,IAAkB,YACpBC,EAAiBC,EAAO,KAAON,CAAE,GAEnC,IAAMO,EACJH,IAAkB,UACdE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,EAAO,MAAM,EACbE,EAAMJ,IAAkB,UAAYC,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAkB,SAEtFI,EAAI,MAAM,qBAAuBV,CAAI,EAGrC,IAAMW,EAAUH,EAAK,OAAO,QAAQP,CAAE,IAAI,EAC1CJ,GAAcc,CAAO,EAErB,IAAMC,EAAUT,EAAQ,GAAG,WAAW,EACtCU,EAAUD,EAASD,EAAS,OAAW,GAAOH,EAAMC,EAAKN,CAAO,EAEhE,IAAMW,EAAUrB,EAAK,QACfsB,EAASJ,EAAQ,KAAK,EAAE,QAAQ,EAEhCK,EAAQD,EAAO,MAAQD,EAAU,EACjCG,EAASF,EAAO,OAASD,EAAU,EAGnCI,EAAWF,EAAQ,KACzBG,EAAiBR,EAASM,EAAQC,EAAUzB,EAAK,WAAW,EAE5DkB,EAAQ,KACN,UACA,GAAGI,EAAO,EAAItB,EAAK,OAAO,KAAKsB,EAAO,EAAItB,EAAK,OAAO,IAAMuB,EAAQ,IAAMC,CAC5E,CACF,EArCoB,QAsCdG,GAAgBxB,EAACI,GACdA,EAAOA,EAAK,OAASP,EAAK,eAAiB,EAD9B,iBAIhBoB,EAAYjB,EAAA,CAACa,EAAKE,EAASU,EAAUC,EAAQd,EAAMe,EAAapB,IAAY,CAEhF,IAAMqB,EAAQ,IAAaC,EAAM,CAC/B,SAAU,GACV,WAAY,EACd,CAAC,EAEGC,EACAC,EAAc,GAClB,IAAKD,EAAI,EAAGA,EAAIjB,EAAI,OAAQiB,IAC1B,GAAIjB,EAAIiB,CAAC,EAAE,OAAS,WAAY,CAC9BC,EAAc,GACd,KACF,CAIEN,EACFG,EAAM,SAAS,CACb,QAAS,KACT,WAAY,GACZ,SAAU,GAEV,OAAQ,aACR,QAASG,EAAc,EAAIlC,EAAK,iBAChC,QAASkC,EAAc,EAAI,GAC3B,aAAc,EAGhB,CAAC,EAEDH,EAAM,SAAS,CACb,QAAS,KACT,WAAY,GACZ,SAAU,GAIV,QAASG,EAAc,EAAIlC,EAAK,iBAChC,QAASkC,EAAc,EAAI,GAC3B,OAAQ,aAER,aAAc,EAChB,CAAC,EAIHH,EAAM,oBAAoB,UAAY,CACpC,MAAO,CAAC,CACV,CAAC,EAED,IAAMI,EAASzB,EAAQ,GAAG,UAAU,EAC9B0B,EAAY1B,EAAQ,GAAG,aAAa,EAEpC2B,EAAO,OAAO,KAAKF,CAAM,EAE3BG,EAAQ,GAEZ,QAAWC,KAAOF,EAAM,CACtB,IAAMG,EAAWL,EAAOI,CAAG,EAEvBX,IACFY,EAAS,SAAWZ,GAGtB,IAAIa,EACJ,GAAID,EAAS,IAAK,CAChB,IAAIE,EAAMxB,EAAQ,OAAO,GAAG,EAAE,KAAK,KAAMsB,EAAS,EAAE,EAAE,KAAK,QAAS,YAAY,EAGhF,GAFAC,EAAOrB,EAAUoB,EAAS,IAAKE,EAAKF,EAAS,GAAI,CAACX,EAAQd,EAAMe,EAAapB,CAAO,EAEhF4B,EAAO,CAETI,EAAMC,EAAeD,EAAKF,EAAUX,CAAM,EAC1C,IAAIe,EAAYF,EAAI,KAAK,EAAE,QAAQ,EACnCD,EAAK,MAAQG,EAAU,MACvBH,EAAK,OAASG,EAAU,OAAS5C,EAAK,QAAU,EAChDC,EAAkBuC,EAAS,EAAE,EAAI,CAAE,EAAGxC,EAAK,iBAAkB,CAC/D,KAAO,CAEL,IAAI4C,EAAYF,EAAI,KAAK,EAAE,QAAQ,EACnCD,EAAK,MAAQG,EAAU,MACvBH,EAAK,OAASG,EAAU,MAE1B,CACF,MACEH,EAAOI,EAAU3B,EAASsB,EAAUT,CAAK,EAG3C,GAAIS,EAAS,KAAM,CAEjB,IAAMM,EAAU,CACd,aAAc,CAAC,EACf,GAAIN,EAAS,GAAK,QAClB,KAAMA,EAAS,KACf,KAAM,MACR,EACMO,EAAOF,EAAU3B,EAAS4B,EAASf,CAAK,EAG1CS,EAAS,KAAK,WAAa,WAC7BT,EAAM,QAAQU,EAAK,GAAK,QAASM,CAAI,EACrChB,EAAM,QAAQU,EAAK,GAAIA,CAAI,IAE3BV,EAAM,QAAQU,EAAK,GAAIA,CAAI,EAC3BV,EAAM,QAAQU,EAAK,GAAK,QAASM,CAAI,GAGvChB,EAAM,UAAUU,EAAK,GAAIA,EAAK,GAAK,QAAQ,EAC3CV,EAAM,UAAUU,EAAK,GAAK,QAASA,EAAK,GAAK,QAAQ,CACvD,MAIEV,EAAM,QAAQU,EAAK,GAAIA,CAAI,CAE/B,CAEAxB,EAAI,MAAM,SAAUc,EAAM,UAAU,EAAGA,CAAK,EAC5C,IAAIiB,EAAM,EACVZ,EAAU,QAAQ,SAAUa,EAAU,CACpCD,IACA/B,EAAI,MAAM,eAAgBgC,CAAQ,EAClClB,EAAM,QACJkB,EAAS,IACTA,EAAS,IACT,CACE,SAAUA,EACV,MAAOtB,GAAcsB,EAAS,KAAK,EACnC,OAAQjD,EAAK,YAAckD,EAAO,QAAQD,EAAS,KAAK,EAAE,OAC1D,SAAU,GACZ,EACA,KAAOD,CACT,CACF,CAAC,EAEDG,EAAYpB,CAAK,EAEjBd,EAAI,MAAM,qBAAsBc,EAAM,MAAM,CAAC,EAC7C,IAAMqB,EAAUlC,EAAQ,KAAK,EAE7Ba,EAAM,MAAM,EAAE,QAAQ,SAAUsB,EAAG,CAC7BA,IAAM,QAAatB,EAAM,KAAKsB,CAAC,IAAM,QACvCpC,EAAI,KAAK,QAAUoC,EAAI,KAAO,KAAK,UAAUtB,EAAM,KAAKsB,CAAC,CAAC,CAAC,EAC3DtC,EACG,OAAO,IAAMqC,EAAQ,GAAK,KAAOC,CAAC,EAClC,KACC,YACA,cACGtB,EAAM,KAAKsB,CAAC,EAAE,EAAItB,EAAM,KAAKsB,CAAC,EAAE,MAAQ,GACzC,KACCtB,EAAM,KAAKsB,CAAC,EAAE,GACZpD,EAAkBoD,CAAC,EAAIpD,EAAkBoD,CAAC,EAAE,EAAI,GACjDtB,EAAM,KAAKsB,CAAC,EAAE,OAAS,GACzB,IACJ,EACFtC,EACG,OAAO,IAAMqC,EAAQ,GAAK,KAAOC,CAAC,EAClC,KAAK,eAAgBtB,EAAM,KAAKsB,CAAC,EAAE,EAAItB,EAAM,KAAKsB,CAAC,EAAE,MAAQ,CAAC,EAChDvB,EAAY,iBAAiB,IAAMsB,EAAQ,GAAK,KAAOC,EAAI,WAAW,EAC9E,QAASC,GAAY,CAC5B,IAAMC,EAASD,EAAQ,cACnBE,EAAS,EACTC,EAAS,EACTF,IACEA,EAAO,gBACTC,EAASD,EAAO,cAAc,QAAQ,EAAE,OAE1CE,EAAS,SAASF,EAAO,aAAa,cAAc,EAAG,EAAE,EACrD,OAAO,MAAME,CAAM,IACrBA,EAAS,IAGbH,EAAQ,aAAa,KAAM,EAAIG,EAAS,CAAC,EACzCH,EAAQ,aAAa,KAAME,EAASC,EAAS,CAAC,CAChD,CAAC,GAEDxC,EAAI,MAAM,WAAaoC,EAAI,KAAO,KAAK,UAAUtB,EAAM,KAAKsB,CAAC,CAAC,CAAC,CAEnE,CAAC,EAED,IAAIK,EAAWN,EAAQ,QAAQ,EAE/BrB,EAAM,MAAM,EAAE,QAAQ,SAAU4B,EAAG,CAC7BA,IAAM,QAAa5B,EAAM,KAAK4B,CAAC,IAAM,SACvC1C,EAAI,MAAM,QAAU0C,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAU5B,EAAM,KAAK4B,CAAC,CAAC,CAAC,EAC7EC,EAAS1C,EAASa,EAAM,KAAK4B,CAAC,EAAG5B,EAAM,KAAK4B,CAAC,EAAE,QAAQ,EAE3D,CAAC,EAEDD,EAAWN,EAAQ,QAAQ,EAE3B,IAAMS,EAAY,CAChB,GAAIjC,GAAsB,OAC1B,MAAOA,GAAsB,OAC7B,MAAO,EACP,OAAQ,CACV,EAEA,OAAAiC,EAAU,MAAQH,EAAS,MAAQ,EAAI1D,EAAK,QAC5C6D,EAAU,OAASH,EAAS,OAAS,EAAI1D,EAAK,QAE9CiB,EAAI,MAAM,eAAgB4C,EAAW9B,CAAK,EACnC8B,CACT,EA3MkB,aA6MXC,EAAQ,CACb,QAAA5D,GACA,KAAAI,EACF,EChSO,IAAMyD,GAA6B,CACxC,OAAAC,EACA,IAAI,IAAK,CACP,OAAO,IAAIC,EAAQ,CAAC,CACtB,EACA,SAAAC,EACA,OAAAC,EACA,KAAMC,EAACC,GAAQ,CACRA,EAAI,QACPA,EAAI,MAAQ,CAAC,GAEfA,EAAI,MAAM,oBAAsBA,EAAI,mBACtC,EALM,OAMR", "names": ["idCache", "set", "__name", "key", "val", "get", "k", "keys", "size", "id_cache_default", "drawStartState", "__name", "g", "getConfig", "drawDivider", "drawSimpleState", "stateDef", "state", "classBox", "drawDescrState", "addTspan", "textEl", "txt", "<PERSON><PERSON><PERSON><PERSON>", "tSpan", "titleBox", "titleHeight", "description", "isSecond", "descr", "descrLine", "descrBox", "width", "addTitleAndBox", "altBkg", "pad", "dblPad", "orgBox", "orgWidth", "orgX", "title", "titleWidth", "startX", "graphBox", "lineY", "drawEndState", "drawForkJoinState", "height", "tmp", "_drawLongText", "__name", "_text", "x", "y", "g", "textHeight", "textElem", "text", "lines", "common_default", "tHeight", "getConfig", "line", "txt", "span", "textBounds", "drawNote", "note", "rectElem", "textWidth", "drawState", "elem", "stateDef", "id", "stateInfo", "drawStartState", "drawEndState", "drawForkJoinState", "drawDivider", "drawSimpleState", "drawDescrState", "stateBox", "id_cache_default", "edgeCount", "drawEdge", "path", "relation", "getRelationType", "type", "StateDB", "p", "lineData", "lineFunction", "line_default", "d", "basis_default", "svgPath", "url", "label", "utils_default", "rows", "titleHeight", "titleRows", "max<PERSON><PERSON><PERSON>", "minX", "i", "title", "boundsTmp", "log", "boxHeight", "heightAdj", "bounds", "conf", "transformationLog", "setConf", "__name", "insertMarkers", "elem", "draw", "text", "id", "_version", "diagObj", "getConfig", "securityLevel", "sandboxElement", "select_default", "root", "doc", "log", "diagram", "rootDoc", "renderDoc", "padding", "bounds", "width", "height", "svgWidth", "configureSvgSize", "<PERSON><PERSON><PERSON><PERSON>", "parentId", "altBkg", "domDocument", "graph", "Graph", "i", "edgeFreeDoc", "states", "relations", "keys", "first", "key", "stateDef", "node", "sub", "addTitleAndBox", "boxBounds", "drawState", "noteDef", "note", "cnt", "relation", "common_default", "layout", "svgElem", "v", "divider", "parent", "pWidth", "pShift", "stateBox", "e", "drawEdge", "stateInfo", "stateRenderer_default", "diagram", "stateDiagram_default", "StateDB", "stateRenderer_default", "styles_default", "__name", "cnf"]}