{"name": "@vitest/browser", "type": "module", "version": "3.2.4", "description": "<PERSON><PERSON><PERSON> running for Vitest", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/browser#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/browser"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./providers": {"types": "./providers.d.ts", "default": "./dist/providers.js"}, "./context": {"types": "./context.d.ts", "default": "./context.js"}, "./client": {"default": "./dist/client.js"}, "./matchers": {"types": "./matchers.d.ts", "default": "./dummy.js"}, "./providers/webdriverio": {"types": "./providers/webdriverio.d.ts", "default": "./dummy.js"}, "./providers/playwright": {"types": "./providers/playwright.d.ts", "default": "./dummy.js"}, "./locator": {"types": "./dist/locators/index.d.ts", "default": "./dist/locators/index.js"}, "./utils": {"types": "./utils.d.ts", "default": "./dist/utils.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["*.d.ts", "context.js", "dist", "dummy.js", "providers"], "peerDependencies": {"playwright": "*", "webdriverio": "^7.0.0 || ^8.0.0 || ^9.0.0", "vitest": "3.2.4"}, "peerDependenciesMeta": {"playwright": {"optional": true}, "safaridriver": {"optional": true}, "webdriverio": {"optional": true}}, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/user-event": "^14.6.1", "magic-string": "^0.30.17", "sirv": "^3.0.1", "tinyrainbow": "^2.0.0", "ws": "^8.18.2", "@vitest/mocker": "3.2.4", "@vitest/utils": "3.2.4"}, "devDependencies": {"@types/ws": "^8.18.1", "@wdio/protocols": "^9.15.0", "@wdio/types": "^9.15.0", "birpc": "2.4.0", "flatted": "^3.3.3", "ivya": "^1.7.0", "mime": "^4.0.7", "pathe": "^2.0.3", "periscopic": "^4.0.2", "playwright": "^1.53.0", "playwright-core": "^1.53.0", "safaridriver": "^1.0.0", "webdriverio": "^9.15.0", "@vitest/ui": "3.2.4", "@vitest/ws-client": "3.2.4", "vitest": "3.2.4", "@vitest/runner": "3.2.4"}, "scripts": {"build": "rimraf dist && pnpm build:node && pnpm build:client", "build:client": "vite build src/client", "build:node": "rollup -c", "dev:client": "vite build src/client --watch", "dev:node": "rollup -c --watch --watch.include 'src/**'", "dev": "rimraf dist && pnpm run --stream '/^dev:/'"}}