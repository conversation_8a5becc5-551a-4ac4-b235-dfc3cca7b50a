# @vitest/browser

[Browser runner](https://vitest.dev/guide/browser/) for Vitest.

> ⚠️ This package is **experimental**. While this package will be released along with other packages, it will not follow SemVer for breaking changes until we mark it as ready.

## Development Setup

At project root:

```bash
cd test/browser
# runs relevant tests for the browser mode
# useful to confirm everything works fine
pnpm test
# runs tests as the browser mode
# useful during development
pnpm test-fixtures
```
